/**
 * 数据库种子数据脚本 (JavaScript版本)
 * 初始化RBAC权限管理系统的基础数据
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// 系统权限定义
const SYSTEM_PERMISSIONS = [
  // 商品管理权限
  { name: 'products.read', resource: 'products', action: 'read', description: '查看商品' },
  { name: 'products.create', resource: 'products', action: 'create', description: '创建商品' },
  { name: 'products.update', resource: 'products', action: 'update', description: '更新商品' },
  { name: 'products.delete', resource: 'products', action: 'delete', description: '删除商品' },
  { name: 'products.import', resource: 'products', action: 'import', description: '导入商品' },
  { name: 'products.export', resource: 'products', action: 'export', description: '导出商品' },

  // 订单管理权限
  { name: 'orders.read', resource: 'orders', action: 'read', description: '查看订单' },
  { name: 'orders.create', resource: 'orders', action: 'create', description: '创建订单' },
  { name: 'orders.update', resource: 'orders', action: 'update', description: '更新订单' },
  { name: 'orders.delete', resource: 'orders', action: 'delete', description: '删除订单' },
  { name: 'orders.process', resource: 'orders', action: 'process', description: '处理订单' },
  { name: 'orders.ship', resource: 'orders', action: 'ship', description: '发货' },
  { name: 'orders.cancel', resource: 'orders', action: 'cancel', description: '取消订单' },
  { name: 'orders.refund', resource: 'orders', action: 'refund', description: '退款' },
  { name: 'orders.export', resource: 'orders', action: 'export', description: '导出订单' },

  // 客户管理权限
  { name: 'customers.read', resource: 'customers', action: 'read', description: '查看客户' },
  { name: 'customers.create', resource: 'customers', action: 'create', description: '创建客户' },
  { name: 'customers.update', resource: 'customers', action: 'update', description: '更新客户' },
  { name: 'customers.delete', resource: 'customers', action: 'delete', description: '删除客户' },
  { name: 'customers.import', resource: 'customers', action: 'import', description: '导入客户' },
  { name: 'customers.export', resource: 'customers', action: 'export', description: '导出客户' },
  { name: 'customers.manage', resource: 'customers', action: 'manage', description: '管理客户' },

  // 库存管理权限
  { name: 'inventory.read', resource: 'inventory', action: 'read', description: '查看库存' },
  { name: 'inventory.create', resource: 'inventory', action: 'create', description: '创建库存' },
  { name: 'inventory.update', resource: 'inventory', action: 'update', description: '更新库存' },
  { name: 'inventory.delete', resource: 'inventory', action: 'delete', description: '删除库存' },
  { name: 'inventory.adjust', resource: 'inventory', action: 'adjust', description: '调整库存' },
  { name: 'inventory.transfer', resource: 'inventory', action: 'transfer', description: '转移库存' },
  { name: 'inventory.count', resource: 'inventory', action: 'count', description: '库存盘点' },
  { name: 'inventory.import', resource: 'inventory', action: 'import', description: '导入库存' },
  { name: 'inventory.export', resource: 'inventory', action: 'export', description: '导出库存' },

  // 用户管理权限
  { name: 'users.read', resource: 'users', action: 'read', description: '查看用户' },
  { name: 'users.create', resource: 'users', action: 'create', description: '创建用户' },
  { name: 'users.update', resource: 'users', action: 'update', description: '更新用户' },
  { name: 'users.delete', resource: 'users', action: 'delete', description: '删除用户' },
  { name: 'users.manage_roles', resource: 'users', action: 'manage_roles', description: '管理用户角色' },
  { name: 'users.export', resource: 'users', action: 'export', description: '导出用户' },

  // 角色管理权限
  { name: 'roles.read', resource: 'roles', action: 'read', description: '查看角色' },
  { name: 'roles.create', resource: 'roles', action: 'create', description: '创建角色' },
  { name: 'roles.update', resource: 'roles', action: 'update', description: '更新角色' },
  { name: 'roles.delete', resource: 'roles', action: 'delete', description: '删除角色' },
  { name: 'roles.assign_permissions', resource: 'roles', action: 'assign_permissions', description: '分配角色权限' },

  // 权限管理权限
  { name: 'permissions.read', resource: 'permissions', action: 'read', description: '查看权限' },
  { name: 'permissions.create', resource: 'permissions', action: 'create', description: '创建权限' },
  { name: 'permissions.update', resource: 'permissions', action: 'update', description: '更新权限' },
  { name: 'permissions.delete', resource: 'permissions', action: 'delete', description: '删除权限' },

  // 报表权限
  { name: 'reports.read', resource: 'reports', action: 'read', description: '查看报表' },
  { name: 'reports.create', resource: 'reports', action: 'create', description: '创建报表' },
  { name: 'reports.export', resource: 'reports', action: 'export', description: '导出报表' },

  // 系统设置权限
  { name: 'settings.read', resource: 'settings', action: 'read', description: '查看设置' },
  { name: 'settings.update', resource: 'settings', action: 'update', description: '更新设置' },
  { name: 'settings.backup', resource: 'settings', action: 'backup', description: '备份系统' },
  { name: 'settings.restore', resource: 'settings', action: 'restore', description: '恢复系统' },

  // 审计日志权限
  { name: 'audit.read', resource: 'audit', action: 'read', description: '查看审计日志' },
  { name: 'audit.export', resource: 'audit', action: 'export', description: '导出审计日志' },
];

// 系统角色定义
const SYSTEM_ROLES = [
  {
    name: 'super_admin',
    displayName: '超级管理员',
    description: '拥有系统所有权限的超级管理员',
    isActive: true,
    permissions: SYSTEM_PERMISSIONS.map(p => p.name), // 所有权限
  },
  {
    name: 'admin',
    displayName: '系统管理员',
    description: '拥有大部分管理权限的系统管理员',
    isActive: true,
    permissions: [
      'products.read', 'products.create', 'products.update', 'products.delete', 'products.import', 'products.export',
      'orders.read', 'orders.create', 'orders.update', 'orders.process', 'orders.ship', 'orders.cancel', 'orders.export',
      'customers.read', 'customers.create', 'customers.update', 'customers.delete', 'customers.import', 'customers.export',
      'inventory.read', 'inventory.create', 'inventory.update', 'inventory.adjust', 'inventory.transfer', 'inventory.count',
      'users.read', 'users.create', 'users.update', 'users.manage_roles',
      'roles.read', 'roles.create', 'roles.update', 'roles.assign_permissions',
      'permissions.read',
      'reports.read', 'reports.create', 'reports.export',
      'settings.read', 'settings.update',
      'audit.read', 'audit.export',
    ],
  },
  {
    name: 'product_manager',
    displayName: '商品管理员',
    description: '负责商品和库存管理',
    isActive: true,
    permissions: [
      'products.read', 'products.create', 'products.update', 'products.delete', 'products.import', 'products.export',
      'inventory.read', 'inventory.create', 'inventory.update', 'inventory.adjust', 'inventory.transfer', 'inventory.count',
      'orders.read',
      'customers.read',
      'reports.read',
    ],
  },
  {
    name: 'order_manager',
    displayName: '订单管理员',
    description: '负责订单处理和物流管理',
    isActive: true,
    permissions: [
      'orders.read', 'orders.create', 'orders.update', 'orders.process', 'orders.ship', 'orders.cancel', 'orders.export',
      'customers.read', 'customers.create', 'customers.update',
      'products.read',
      'inventory.read',
      'reports.read',
    ],
  },
  {
    name: 'sales_rep',
    displayName: '销售代表',
    description: '负责客户和订单管理',
    isActive: true,
    permissions: [
      'customers.read', 'customers.create', 'customers.update', 'customers.export',
      'orders.read', 'orders.create', 'orders.update',
      'products.read',
      'inventory.read',
      'reports.read',
    ],
  },
  {
    name: 'customer_service',
    displayName: '客服人员',
    description: '负责客户服务和售后处理',
    isActive: true,
    permissions: [
      'customers.read', 'customers.update',
      'orders.read', 'orders.update', 'orders.cancel', 'orders.refund',
      'products.read',
      'reports.read',
    ],
  },
  {
    name: 'warehouse_staff',
    displayName: '仓库人员',
    description: '负责库存和发货管理',
    isActive: true,
    permissions: [
      'inventory.read', 'inventory.update', 'inventory.adjust', 'inventory.transfer', 'inventory.count',
      'orders.read', 'orders.ship',
      'products.read',
    ],
  },
  {
    name: 'analyst',
    displayName: '数据分析师',
    description: '负责报表和数据分析',
    isActive: true,
    permissions: [
      'reports.read', 'reports.create', 'reports.export',
      'products.read',
      'orders.read',
      'customers.read',
      'inventory.read',
    ],
  },
  {
    name: 'viewer',
    displayName: '只读用户',
    description: '只能查看数据的用户',
    isActive: true,
    permissions: [
      'products.read',
      'orders.read',
      'customers.read',
      'inventory.read',
      'reports.read',
    ],
  },
];

async function main() {
  console.log('🌱 开始初始化数据库种子数据...');

  try {
    // 1. 创建权限
    console.log('📝 创建系统权限...');
    const createdPermissions = {};
    
    for (const permission of SYSTEM_PERMISSIONS) {
      const created = await prisma.permission.upsert({
        where: { name: permission.name },
        update: permission,
        create: permission,
      });
      createdPermissions[permission.name] = created;
      console.log(`  ✅ 权限: ${permission.name}`);
    }

    // 2. 创建角色和角色权限
    console.log('👥 创建系统角色...');
    const createdRoles = {};

    for (const role of SYSTEM_ROLES) {
      const { permissions, ...roleData } = role;
      
      const createdRole = await prisma.role.upsert({
        where: { name: role.name },
        update: roleData,
        create: roleData,
      });
      createdRoles[role.name] = createdRole;

      // 删除现有的角色权限关联
      await prisma.rolePermission.deleteMany({
        where: { roleId: createdRole.id },
      });

      // 创建新的角色权限关联
      for (const permissionName of permissions) {
        const permission = createdPermissions[permissionName];
        if (permission) {
          await prisma.rolePermission.create({
            data: {
              roleId: createdRole.id,
              permissionId: permission.id,
            },
          });
        }
      }

      console.log(`  ✅ 角色: ${role.displayName} (${permissions.length} 个权限)`);
    }

    // 3. 创建默认管理员用户
    console.log('👤 创建默认管理员用户...');
    const hashedPassword = await bcrypt.hash('admin123456', 12);
    
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        firstName: '系统',
        lastName: '管理员',
        isActive: true,
        emailVerified: true,
      },
      create: {
        email: '<EMAIL>',
        password: hashedPassword,
        firstName: '系统',
        lastName: '管理员',
        isActive: true,
        emailVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    // 分配超级管理员角色
    const superAdminRole = createdRoles['super_admin'];
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: adminUser.id,
          roleId: superAdminRole.id,
        },
      },
      update: {},
      create: {
        userId: adminUser.id,
        roleId: superAdminRole.id,
        assignedBy: adminUser.id,
      },
    });

    console.log(`  ✅ 管理员用户: ${adminUser.email}`);

    console.log('🎉 数据库种子数据初始化完成！');
    console.log('');
    console.log('📋 系统信息:');
    console.log(`  权限数量: ${SYSTEM_PERMISSIONS.length}`);
    console.log(`  角色数量: ${SYSTEM_ROLES.length}`);
    console.log('');
    console.log('🔑 默认管理员账户:');
    console.log(`  邮箱: <EMAIL>`);
    console.log(`  密码: admin123456`);
    console.log(`  角色: 超级管理员`);

  } catch (error) {
    console.error('❌ 初始化种子数据失败:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
