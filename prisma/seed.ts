/**
 * 数据库种子数据脚本
 * 用于初始化系统基础数据和测试数据
 */

import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../src/lib/auth';
import { seedRBAC } from './seeds/rbac-seed';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 开始填充数据库种子数据...');

  // 初始化RBAC权限管理系统
  console.log('🔐 初始化RBAC权限管理系统...');
  await seedRBAC();



  // 创建默认用户
  console.log('👤 创建默认用户...');
  const hashedPassword = await hashPassword('admin123456');

  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      passwordHash: hashedPassword,
      firstName: '系统',
      lastName: '管理员',
      languageCode: 'zh-CN',
      timezone: 'Asia/Shanghai',
      status: 'ACTIVE',
      emailVerified: true,
      createdBy: 'system',
    },
  });

  // 获取超级管理员角色
  const superAdminRole = await prisma.role.findUnique({
    where: { name: 'super_admin' },
  });

  if (superAdminRole) {
    // 为管理员用户分配超级管理员角色
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: adminUser.id,
          roleId: superAdminRole.id,
        },
      },
      update: {},
      create: {
        userId: adminUser.id,
        roleId: superAdminRole.id,
        assignedBy: 'system',
      },
    });
  }

  // 创建默认仓库
  console.log('🏪 创建默认仓库...');
  await prisma.warehouse.upsert({
    where: { code: 'MAIN' },
    update: {},
    create: {
      code: 'MAIN',
      name: '主仓库',
      address: {
        addressLine1: '深圳市南山区科技园',
        city: '深圳',
        stateProvince: '广东省',
        countryCode: 'CN',
        postalCode: '518000',
      },
      contactInfo: {
        phone: '+86-755-12345678',
        email: '<EMAIL>',
        manager: '仓库管理员',
      },
      isActive: true,
      isDefault: true,
    },
  });

  // 创建默认商品分类
  console.log('📦 创建默认商品分类...');
  const electronicsCategory = await prisma.category.upsert({
    where: { slug: 'electronics' },
    update: {},
    create: {
      name: '电子产品',
      slug: 'electronics',
      description: '各类电子产品和数码设备',
      sortOrder: 1,
      isActive: true,
      seoTitle: '电子产品 - CBEC ERP',
      seoDescription: '高质量的电子产品和数码设备',
    },
  });

  await prisma.category.upsert({
    where: { slug: 'smartphones' },
    update: {},
    create: {
      parentId: electronicsCategory.id,
      name: '智能手机',
      slug: 'smartphones',
      description: '各品牌智能手机',
      sortOrder: 1,
      isActive: true,
      seoTitle: '智能手机 - CBEC ERP',
      seoDescription: '最新款智能手机产品',
    },
  });

  // 创建物流承运商
  console.log('🚚 创建物流承运商...');
  await prisma.shippingCarrier.upsert({
    where: { code: 'DHL' },
    update: {},
    create: {
      name: 'DHL快递',
      code: 'DHL',
      apiEndpoint: 'https://api.dhl.com',
      isActive: true,
    },
  });

  await prisma.shippingCarrier.upsert({
    where: { code: 'FEDEX' },
    update: {},
    create: {
      name: 'FedEx联邦快递',
      code: 'FEDEX',
      apiEndpoint: 'https://api.fedex.com',
      isActive: true,
    },
  });

  console.log('🎉 数据库种子数据填充完成！');
  console.log('📧 默认管理员账户:');
  console.log('   邮箱: <EMAIL>');
  console.log('   密码: admin123456');
}

main()
  .catch((e) => {
    console.error('填充种子数据时发生错误:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
