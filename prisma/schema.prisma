// 跨境电商ERP系统数据库模型定义
// 使用Prisma ORM进行数据库管理

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "linux-musl"]
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// =============================================================================
// 用户管理模块
// =============================================================================

// 用户表
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String?   @unique
  passwordHash      String    @map("password_hash")
  firstName         String?   @map("first_name")
  lastName          String?   @map("last_name")
  phone             String?
  avatarUrl         String?   @map("avatar_url")
  languageCode      String    @default("zh-CN") @map("language_code")
  timezone          String    @default("Asia/Shanghai")
  status            UserStatus @default(ACTIVE)
  emailVerified     Boolean   @default(false) @map("email_verified")
  phoneVerified     Boolean   @default(false) @map("phone_verified")
  twoFactorEnabled  Boolean   @default(false) @map("two_factor_enabled")
  lastLoginAt       DateTime? @map("last_login_at")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  createdBy         String?   @map("created_by")
  updatedBy         String?   @map("updated_by")

  // 关联关系
  userRoles         UserRole[]
  userSessions      UserSession[]
  createdProducts   Product[] @relation("ProductCreatedBy")
  updatedProducts   Product[] @relation("ProductUpdatedBy")
  createdOrders     Order[] @relation("OrderCreatedBy")
  updatedOrders     Order[] @relation("OrderUpdatedBy")
  auditLogs         AuditLog[]

  @@map("users")
}

// 用户状态枚举
enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  DELETED
}

// 角色表
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  displayName String   @map("display_name")
  description String?
  isSystemRole Boolean @default(false) @map("is_system_role")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  userRoles       UserRole[]
  rolePermissions RolePermission[]

  @@map("roles")
}

// 权限表
model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  resource    String
  action      String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  rolePermissions RolePermission[]

  @@map("permissions")
}

// 用户角色关联表
model UserRole {
  id         String    @id @default(cuid())
  userId     String    @map("user_id")
  roleId     String    @map("role_id")
  assignedAt DateTime  @default(now()) @map("assigned_at")
  assignedBy String?   @map("assigned_by")
  expiresAt  DateTime? @map("expires_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// 角色权限关联表
model RolePermission {
  id           String   @id @default(cuid())
  roleId       String   @map("role_id")
  permissionId String   @map("permission_id")
  grantedAt    DateTime @default(now()) @map("granted_at")
  grantedBy    String?  @map("granted_by")

  // 关联关系
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

// 用户会话表
model UserSession {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  token     String   @unique
  ipAddress String   @map("ip_address")
  userAgent String   @map("user_agent")
  expiresAt DateTime @map("expires_at")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// =============================================================================
// 商品管理模块
// =============================================================================

// 商品分类表
model Category {
  id          String    @id @default(cuid())
  parentId    String?   @map("parent_id")
  name        String
  slug        String    @unique
  description String?
  imageUrl    String?   @map("image_url")
  sortOrder   Int       @default(0) @map("sort_order")
  isActive    Boolean   @default(true) @map("is_active")
  seoTitle    String?   @map("seo_title")
  seoDescription String? @map("seo_description")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // 关联关系
  parent   Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]
  translations CategoryTranslation[]

  @@map("categories")
}

// 分类多语言表
model CategoryTranslation {
  id             String   @id @default(cuid())
  categoryId     String   @map("category_id")
  languageCode   String   @map("language_code")
  name           String
  description    String?
  seoTitle       String?  @map("seo_title")
  seoDescription String?  @map("seo_description")

  // 关联关系
  category Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@unique([categoryId, languageCode])
  @@map("category_translations")
}

// 商品表
model Product {
  id               String      @id @default(cuid())
  sku              String      @unique
  name             String
  description      String?
  shortDescription String?     @map("short_description")
  categoryId       String?     @map("category_id")
  brand            String?
  model            String?
  weight           Decimal?    @db.Decimal(10, 3)
  dimensions       Json?       // 尺寸信息
  basePrice        Decimal     @map("base_price") @db.Decimal(12, 2)
  costPrice        Decimal?    @map("cost_price") @db.Decimal(12, 2)
  currency         String      @default("USD")
  taxClass         String?     @map("tax_class")
  status           ProductStatus @default(DRAFT)
  isDigital        Boolean     @default(false) @map("is_digital")
  requiresShipping Boolean     @default(true) @map("requires_shipping")
  trackInventory   Boolean     @default(true) @map("track_inventory")
  allowBackorder   Boolean     @default(false) @map("allow_backorder")
  metaData         Json?       @map("meta_data") // 自定义属性
  seoTitle         String?     @map("seo_title")
  seoDescription   String?     @map("seo_description")
  createdAt        DateTime    @default(now()) @map("created_at")
  updatedAt        DateTime    @updatedAt @map("updated_at")
  createdBy        String?     @map("created_by")
  updatedBy        String?     @map("updated_by")

  // 关联关系
  category     Category? @relation(fields: [categoryId], references: [id])
  creator      User?     @relation("ProductCreatedBy", fields: [createdBy], references: [id])
  updater      User?     @relation("ProductUpdatedBy", fields: [updatedBy], references: [id])
  variants     ProductVariant[]
  images       ProductImage[]
  translations ProductTranslation[]
  inventory    Inventory[]
  orderItems   OrderItem[]

  @@map("products")
}

// 商品状态枚举
enum ProductStatus {
  DRAFT
  ACTIVE
  INACTIVE
  DISCONTINUED
}

// 商品多语言表
model ProductTranslation {
  id               String  @id @default(cuid())
  productId        String  @map("product_id")
  languageCode     String  @map("language_code")
  name             String
  description      String?
  shortDescription String? @map("short_description")
  seoTitle         String? @map("seo_title")
  seoDescription   String? @map("seo_description")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, languageCode])
  @@map("product_translations")
}

// 商品变体表
model ProductVariant {
  id         String   @id @default(cuid())
  productId  String   @map("product_id")
  sku        String   @unique
  name       String?
  price      Decimal? @db.Decimal(12, 2)
  costPrice  Decimal? @map("cost_price") @db.Decimal(12, 2)
  weight     Decimal? @db.Decimal(10, 3)
  dimensions Json?    // 尺寸信息
  attributes Json?    // 变体属性
  imageUrl   String?  @map("image_url")
  isActive   Boolean  @default(true) @map("is_active")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // 关联关系
  product    Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  inventory  Inventory[]
  orderItems OrderItem[]

  @@map("product_variants")
}

// 商品图片表
model ProductImage {
  id        String  @id @default(cuid())
  productId String  @map("product_id")
  variantId String? @map("variant_id")
  url       String
  altText   String? @map("alt_text")
  sortOrder Int     @default(0) @map("sort_order")
  isPrimary Boolean @default(false) @map("is_primary")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  product Product        @relation(fields: [productId], references: [id], onDelete: Cascade)
  variant ProductVariant? @relation(fields: [variantId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

// =============================================================================
// 库存管理模块
// =============================================================================

// 仓库表
model Warehouse {
  id          String  @id @default(cuid())
  code        String  @unique
  name        String
  address     Json    // 地址信息
  contactInfo Json?   @map("contact_info") // 联系信息
  isActive    Boolean @default(true) @map("is_active")
  isDefault   Boolean @default(false) @map("is_default")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  inventory Inventory[]
  inventoryMovements InventoryMovement[]

  @@map("warehouses")
}

// 库存表
model Inventory {
  id               String   @id @default(cuid())
  warehouseId      String   @map("warehouse_id")
  productId        String?  @map("product_id")
  variantId        String?  @map("variant_id")
  sku              String
  quantityOnHand   Int      @default(0) @map("quantity_on_hand")
  quantityReserved Int      @default(0) @map("quantity_reserved")
  reorderPoint     Int      @default(0) @map("reorder_point")
  reorderQuantity  Int      @default(0) @map("reorder_quantity")
  costPerUnit      Decimal? @map("cost_per_unit") @db.Decimal(12, 2)
  lastCountedAt    DateTime? @map("last_counted_at")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  // 关联关系
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])
  product   Product?  @relation(fields: [productId], references: [id])
  variant   ProductVariant? @relation(fields: [variantId], references: [id])
  movements InventoryMovement[]
  reservations StockReservation[]

  @@unique([warehouseId, sku])
  @@map("inventory")
}

// 库存变动记录表
model InventoryMovement {
  id            String            @id @default(cuid())
  inventoryId   String            @map("inventory_id")
  warehouseId   String            @map("warehouse_id")
  movementType  InventoryMovementType @map("movement_type")
  quantity      Int
  referenceType String?           @map("reference_type")
  referenceId   String?           @map("reference_id")
  reason        String?
  costPerUnit   Decimal?          @map("cost_per_unit") @db.Decimal(12, 2)
  notes         String?
  createdAt     DateTime          @default(now()) @map("created_at")
  createdBy     String?           @map("created_by")

  // 关联关系
  inventory Inventory @relation(fields: [inventoryId], references: [id])
  warehouse Warehouse @relation(fields: [warehouseId], references: [id])

  @@map("inventory_movements")
}

// 库存变动类型枚举
enum InventoryMovementType {
  IN
  OUT
  ADJUSTMENT
  TRANSFER
}

// 库存预留表
model StockReservation {
  id          String   @id @default(cuid())
  inventoryId String   @map("inventory_id")
  orderId     String?  @map("order_id")
  quantity    Int
  expiresAt   DateTime @map("expires_at")
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  inventory Inventory @relation(fields: [inventoryId], references: [id])
  order     Order?    @relation(fields: [orderId], references: [id])

  @@map("stock_reservations")
}

// =============================================================================
// 客户管理模块
// =============================================================================

// 客户表
model Customer {
  id                  String      @id @default(cuid())
  email               String      @unique
  firstName           String?     @map("first_name")
  lastName            String?     @map("last_name")
  phone               String?
  dateOfBirth         DateTime?   @map("date_of_birth") @db.Date
  gender              Gender?
  languageCode        String      @default("zh-CN") @map("language_code")
  currencyCode        String      @default("USD") @map("currency_code")
  timezone            String      @default("Asia/Shanghai")
  customerGroup       String      @default("default") @map("customer_group")
  totalOrders         Int         @default(0) @map("total_orders")
  totalSpent          Decimal     @default(0) @map("total_spent") @db.Decimal(12, 2)
  averageOrderValue   Decimal     @default(0) @map("average_order_value") @db.Decimal(12, 2)
  lastOrderAt         DateTime?   @map("last_order_at")
  notes               String?
  tags                String[]    // 客户标签
  marketingConsent    Boolean     @default(false) @map("marketing_consent")
  createdAt           DateTime    @default(now()) @map("created_at")
  updatedAt           DateTime    @updatedAt @map("updated_at")

  // 关联关系
  addresses CustomerAddress[]
  orders    Order[]

  @@map("customers")
}

// 性别枚举
enum Gender {
  MALE
  FEMALE
  OTHER
}

// 客户地址表
model CustomerAddress {
  id           String      @id @default(cuid())
  customerId   String      @map("customer_id")
  type         AddressType @default(SHIPPING)
  firstName    String?     @map("first_name")
  lastName     String?     @map("last_name")
  company      String?
  addressLine1 String      @map("address_line_1")
  addressLine2 String?     @map("address_line_2")
  city         String
  stateProvince String?    @map("state_province")
  postalCode   String?     @map("postal_code")
  countryCode  String      @map("country_code")
  phone        String?
  isDefault    Boolean     @default(false) @map("is_default")
  createdAt    DateTime    @default(now()) @map("created_at")
  updatedAt    DateTime    @updatedAt @map("updated_at")

  // 关联关系
  customer Customer @relation(fields: [customerId], references: [id], onDelete: Cascade)

  @@map("customer_addresses")
}

// 地址类型枚举
enum AddressType {
  BILLING
  SHIPPING
}

// =============================================================================
// 订单管理模块
// =============================================================================

// 订单表
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique @map("order_number")
  customerId      String?     @map("customer_id")
  status          OrderStatus @default(PENDING)
  currency        String
  exchangeRate    Decimal     @default(1.0) @map("exchange_rate") @db.Decimal(10, 6)

  // 金额信息
  subtotal        Decimal     @db.Decimal(12, 2)
  taxAmount       Decimal     @default(0) @map("tax_amount") @db.Decimal(12, 2)
  shippingAmount  Decimal     @default(0) @map("shipping_amount") @db.Decimal(12, 2)
  discountAmount  Decimal     @default(0) @map("discount_amount") @db.Decimal(12, 2)
  totalAmount     Decimal     @map("total_amount") @db.Decimal(12, 2)

  // 地址信息
  billingAddress  Json        @map("billing_address")
  shippingAddress Json        @map("shipping_address")

  // 其他信息
  notes           String?
  internalNotes   String?     @map("internal_notes")
  source          String?     // 订单来源
  sourceOrderId   String?     @map("source_order_id") // 第三方平台订单ID

  // 时间信息
  orderDate       DateTime    @default(now()) @map("order_date")
  confirmedAt     DateTime?   @map("confirmed_at")
  shippedAt       DateTime?   @map("shipped_at")
  deliveredAt     DateTime?   @map("delivered_at")
  cancelledAt     DateTime?   @map("cancelled_at")

  createdAt       DateTime    @default(now()) @map("created_at")
  updatedAt       DateTime    @updatedAt @map("updated_at")
  createdBy       String?     @map("created_by")
  updatedBy       String?     @map("updated_by")

  // 关联关系
  customer        Customer?   @relation(fields: [customerId], references: [id])
  creator         User?       @relation("OrderCreatedBy", fields: [createdBy], references: [id])
  updater         User?       @relation("OrderUpdatedBy", fields: [updatedBy], references: [id])
  items           OrderItem[]
  payments        Payment[]
  shipments       Shipment[]
  statusHistory   OrderStatusHistory[]
  reservations    StockReservation[]

  @@map("orders")
}

// 订单状态枚举
enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

// 订单商品表
model OrderItem {
  id          String   @id @default(cuid())
  orderId     String   @map("order_id")
  productId   String?  @map("product_id")
  variantId   String?  @map("variant_id")
  sku         String
  name        String
  quantity    Int
  unitPrice   Decimal  @map("unit_price") @db.Decimal(12, 2)
  totalPrice  Decimal  @map("total_price") @db.Decimal(12, 2)
  costPrice   Decimal? @map("cost_price") @db.Decimal(12, 2)
  weight      Decimal? @db.Decimal(10, 3)
  productData Json?    @map("product_data") // 商品快照数据
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  order   Order           @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product?        @relation(fields: [productId], references: [id])
  variant ProductVariant? @relation(fields: [variantId], references: [id])

  @@map("order_items")
}

// 订单状态历史表
model OrderStatusHistory {
  id        String      @id @default(cuid())
  orderId   String      @map("order_id")
  status    OrderStatus
  notes     String?
  createdAt DateTime    @default(now()) @map("created_at")
  createdBy String?     @map("created_by")

  // 关联关系
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("order_status_history")
}

// =============================================================================
// 支付管理模块
// =============================================================================

// 支付表
model Payment {
  id                String        @id @default(cuid())
  orderId           String        @map("order_id")
  paymentMethod     String        @map("payment_method") // stripe, paypal, bank_transfer等
  paymentProvider   String        @map("payment_provider")
  providerPaymentId String?       @map("provider_payment_id")
  status            PaymentStatus @default(PENDING)
  amount            Decimal       @db.Decimal(12, 2)
  currency          String
  exchangeRate      Decimal       @default(1.0) @map("exchange_rate") @db.Decimal(10, 6)
  feeAmount         Decimal       @default(0) @map("fee_amount") @db.Decimal(12, 2)
  providerResponse  Json?         @map("provider_response")
  failureReason     String?       @map("failure_reason")
  processedAt       DateTime?     @map("processed_at")
  createdAt         DateTime      @default(now()) @map("created_at")
  updatedAt         DateTime      @updatedAt @map("updated_at")

  // 关联关系
  order   Order    @relation(fields: [orderId], references: [id])
  refunds Refund[]

  @@map("payments")
}

// 支付状态枚举
enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

// 退款表
model Refund {
  id               String       @id @default(cuid())
  paymentId        String       @map("payment_id")
  orderId          String       @map("order_id")
  amount           Decimal      @db.Decimal(12, 2)
  currency         String
  reason           String?
  status           RefundStatus @default(PENDING)
  providerRefundId String?      @map("provider_refund_id")
  providerResponse Json?        @map("provider_response")
  processedAt      DateTime?    @map("processed_at")
  createdAt        DateTime     @default(now()) @map("created_at")
  createdBy        String?      @map("created_by")

  // 关联关系
  payment Payment @relation(fields: [paymentId], references: [id])

  @@map("refunds")
}

// 退款状态枚举
enum RefundStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

// =============================================================================
// 物流管理模块
// =============================================================================

// 物流承运商表
model ShippingCarrier {
  id          String   @id @default(cuid())
  name        String
  code        String   @unique
  apiEndpoint String?  @map("api_endpoint")
  apiKey      String?  @map("api_key")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  shipments Shipment[]

  @@map("shipping_carriers")
}

// 发货表
model Shipment {
  id              String         @id @default(cuid())
  orderId         String         @map("order_id")
  carrierId       String         @map("carrier_id")
  trackingNumber  String?        @map("tracking_number")
  serviceType     String?        @map("service_type")
  status          ShipmentStatus @default(PENDING)
  shippingCost    Decimal?       @map("shipping_cost") @db.Decimal(12, 2)
  weight          Decimal?       @db.Decimal(10, 3)
  dimensions      Json?          // 包裹尺寸
  shippingAddress Json           @map("shipping_address")
  estimatedDelivery DateTime?    @map("estimated_delivery")
  actualDelivery  DateTime?      @map("actual_delivery")
  shippedAt       DateTime?      @map("shipped_at")
  createdAt       DateTime       @default(now()) @map("created_at")
  updatedAt       DateTime       @updatedAt @map("updated_at")

  // 关联关系
  order           Order           @relation(fields: [orderId], references: [id])
  carrier         ShippingCarrier @relation(fields: [carrierId], references: [id])
  trackingEvents  TrackingEvent[]

  @@map("shipments")
}

// 发货状态枚举
enum ShipmentStatus {
  PENDING
  PICKED_UP
  IN_TRANSIT
  OUT_FOR_DELIVERY
  DELIVERED
  EXCEPTION
  RETURNED
}

// 物流跟踪事件表
model TrackingEvent {
  id          String   @id @default(cuid())
  shipmentId  String   @map("shipment_id")
  status      String
  description String
  location    String?
  timestamp   DateTime
  createdAt   DateTime @default(now()) @map("created_at")

  // 关联关系
  shipment Shipment @relation(fields: [shipmentId], references: [id], onDelete: Cascade)

  @@map("tracking_events")
}

// 审计日志表
model AuditLog {
  id           String   @id @default(cuid())
  userId       String   @map("user_id")
  action       String
  resource     String
  resourceId   String?  @map("resource_id")
  oldValues    Json?    @map("old_values")
  newValues    Json?    @map("new_values")
  ipAddress    String   @map("ip_address")
  userAgent    String   @map("user_agent")
  timestamp    DateTime @default(now())
  success      Boolean  @default(true)
  errorMessage String?  @map("error_message")
  metadata     Json?

  // 关联关系
  user User @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}
