/**
 * RBAC权限管理系统数据种子
 * 初始化系统的基础权限和角色
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// 系统权限定义
const SYSTEM_PERMISSIONS = [
  // 仪表板权限
  { name: 'dashboard.read', resource: 'dashboard', action: 'read', description: '查看仪表板' },
  
  // 用户管理权限
  { name: 'users.read', resource: 'users', action: 'read', description: '查看用户列表' },
  { name: 'users.create', resource: 'users', action: 'create', description: '创建用户' },
  { name: 'users.update', resource: 'users', action: 'update', description: '更新用户信息' },
  { name: 'users.delete', resource: 'users', action: 'delete', description: '删除用户' },
  { name: 'users.manage_roles', resource: 'users', action: 'manage_roles', description: '管理用户角色' },
  
  // 角色管理权限
  { name: 'roles.read', resource: 'roles', action: 'read', description: '查看角色列表' },
  { name: 'roles.create', resource: 'roles', action: 'create', description: '创建角色' },
  { name: 'roles.update', resource: 'roles', action: 'update', description: '更新角色信息' },
  { name: 'roles.delete', resource: 'roles', action: 'delete', description: '删除角色' },
  { name: 'roles.assign_permissions', resource: 'roles', action: 'assign_permissions', description: '分配角色权限' },
  
  // 权限管理权限
  { name: 'permissions.read', resource: 'permissions', action: 'read', description: '查看权限列表' },
  { name: 'permissions.create', resource: 'permissions', action: 'create', description: '创建权限' },
  { name: 'permissions.update', resource: 'permissions', action: 'update', description: '更新权限信息' },
  { name: 'permissions.delete', resource: 'permissions', action: 'delete', description: '删除权限' },
  
  // 商品管理权限
  { name: 'products.read', resource: 'products', action: 'read', description: '查看商品列表' },
  { name: 'products.create', resource: 'products', action: 'create', description: '创建商品' },
  { name: 'products.update', resource: 'products', action: 'update', description: '更新商品信息' },
  { name: 'products.delete', resource: 'products', action: 'delete', description: '删除商品' },
  { name: 'products.publish', resource: 'products', action: 'publish', description: '发布商品' },
  { name: 'products.import', resource: 'products', action: 'import', description: '批量导入商品' },
  { name: 'products.export', resource: 'products', action: 'export', description: '导出商品数据' },
  
  // 分类管理权限
  { name: 'categories.read', resource: 'categories', action: 'read', description: '查看分类列表' },
  { name: 'categories.create', resource: 'categories', action: 'create', description: '创建分类' },
  { name: 'categories.update', resource: 'categories', action: 'update', description: '更新分类信息' },
  { name: 'categories.delete', resource: 'categories', action: 'delete', description: '删除分类' },
  
  // 库存管理权限
  { name: 'inventory.read', resource: 'inventory', action: 'read', description: '查看库存信息' },
  { name: 'inventory.update', resource: 'inventory', action: 'update', description: '更新库存数量' },
  { name: 'inventory.adjust', resource: 'inventory', action: 'adjust', description: '库存调整' },
  { name: 'inventory.transfer', resource: 'inventory', action: 'transfer', description: '库存调拨' },
  { name: 'inventory.count', resource: 'inventory', action: 'count', description: '库存盘点' },
  
  // 订单管理权限
  { name: 'orders.read', resource: 'orders', action: 'read', description: '查看订单列表' },
  { name: 'orders.create', resource: 'orders', action: 'create', description: '创建订单' },
  { name: 'orders.update', resource: 'orders', action: 'update', description: '更新订单信息' },
  { name: 'orders.delete', resource: 'orders', action: 'delete', description: '删除订单' },
  { name: 'orders.process', resource: 'orders', action: 'process', description: '处理订单' },
  { name: 'orders.ship', resource: 'orders', action: 'ship', description: '发货' },
  { name: 'orders.cancel', resource: 'orders', action: 'cancel', description: '取消订单' },
  { name: 'orders.refund', resource: 'orders', action: 'refund', description: '退款处理' },
  
  // 客户管理权限
  { name: 'customers.read', resource: 'customers', action: 'read', description: '查看客户列表' },
  { name: 'customers.create', resource: 'customers', action: 'create', description: '创建客户' },
  { name: 'customers.update', resource: 'customers', action: 'update', description: '更新客户信息' },
  { name: 'customers.delete', resource: 'customers', action: 'delete', description: '删除客户' },
  { name: 'customers.export', resource: 'customers', action: 'export', description: '导出客户数据' },
  
  // 支付管理权限
  { name: 'payments.read', resource: 'payments', action: 'read', description: '查看支付记录' },
  { name: 'payments.process', resource: 'payments', action: 'process', description: '处理支付' },
  { name: 'payments.refund', resource: 'payments', action: 'refund', description: '退款处理' },
  
  // 物流管理权限
  { name: 'shipping.read', resource: 'shipping', action: 'read', description: '查看物流信息' },
  { name: 'shipping.create', resource: 'shipping', action: 'create', description: '创建物流单' },
  { name: 'shipping.update', resource: 'shipping', action: 'update', description: '更新物流信息' },
  { name: 'shipping.track', resource: 'shipping', action: 'track', description: '物流跟踪' },
  
  // 报表分析权限
  { name: 'reports.read', resource: 'reports', action: 'read', description: '查看报表' },
  { name: 'reports.create', resource: 'reports', action: 'create', description: '创建报表' },
  { name: 'reports.export', resource: 'reports', action: 'export', description: '导出报表' },
  
  // 系统设置权限
  { name: 'settings.read', resource: 'settings', action: 'read', description: '查看系统设置' },
  { name: 'settings.update', resource: 'settings', action: 'update', description: '更新系统设置' },
  { name: 'settings.backup', resource: 'settings', action: 'backup', description: '系统备份' },
  { name: 'settings.restore', resource: 'settings', action: 'restore', description: '系统恢复' },
  
  // 审计日志权限
  { name: 'audit.read', resource: 'audit', action: 'read', description: '查看审计日志' },
  { name: 'audit.export', resource: 'audit', action: 'export', description: '导出审计日志' },
];

// 系统角色定义
const SYSTEM_ROLES = [
  {
    name: 'super_admin',
    displayName: '超级管理员',
    description: '拥有系统所有权限的超级管理员',
    isSystemRole: true,
    permissions: SYSTEM_PERMISSIONS.map(p => p.name), // 所有权限
  },
  {
    name: 'admin',
    displayName: '系统管理员',
    description: '系统管理员，拥有大部分管理权限',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'users.read', 'users.create', 'users.update', 'users.manage_roles',
      'roles.read', 'roles.create', 'roles.update', 'roles.assign_permissions',
      'permissions.read',
      'products.read', 'products.create', 'products.update', 'products.publish', 'products.import', 'products.export',
      'categories.read', 'categories.create', 'categories.update', 'categories.delete',
      'inventory.read', 'inventory.update', 'inventory.adjust', 'inventory.transfer', 'inventory.count',
      'orders.read', 'orders.create', 'orders.update', 'orders.process', 'orders.ship', 'orders.cancel', 'orders.refund',
      'customers.read', 'customers.create', 'customers.update', 'customers.export',
      'payments.read', 'payments.process', 'payments.refund',
      'shipping.read', 'shipping.create', 'shipping.update', 'shipping.track',
      'reports.read', 'reports.create', 'reports.export',
      'settings.read', 'settings.update',
      'audit.read', 'audit.export',
    ],
  },
  {
    name: 'product_manager',
    displayName: '商品管理员',
    description: '负责商品和库存管理',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'products.read', 'products.create', 'products.update', 'products.publish', 'products.import', 'products.export',
      'categories.read', 'categories.create', 'categories.update', 'categories.delete',
      'inventory.read', 'inventory.update', 'inventory.adjust', 'inventory.transfer', 'inventory.count',
      'reports.read',
    ],
  },
  {
    name: 'order_manager',
    displayName: '订单管理员',
    description: '负责订单处理和物流管理',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'orders.read', 'orders.create', 'orders.update', 'orders.process', 'orders.ship', 'orders.cancel', 'orders.refund',
      'customers.read', 'customers.update',
      'payments.read', 'payments.process', 'payments.refund',
      'shipping.read', 'shipping.create', 'shipping.update', 'shipping.track',
      'inventory.read',
      'products.read',
      'reports.read',
    ],
  },
  {
    name: 'sales_rep',
    displayName: '销售代表',
    description: '销售人员，负责客户和订单管理',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'orders.read', 'orders.create', 'orders.update',
      'customers.read', 'customers.create', 'customers.update',
      'products.read',
      'inventory.read',
      'reports.read',
    ],
  },
  {
    name: 'customer_service',
    displayName: '客服人员',
    description: '客服人员，负责客户服务和售后处理',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'orders.read', 'orders.update', 'orders.refund',
      'customers.read', 'customers.update',
      'products.read',
      'payments.read', 'payments.refund',
      'shipping.read', 'shipping.track',
    ],
  },
  {
    name: 'warehouse_staff',
    displayName: '仓库人员',
    description: '仓库工作人员，负责库存和发货管理',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'inventory.read', 'inventory.update', 'inventory.adjust', 'inventory.transfer', 'inventory.count',
      'orders.read', 'orders.ship',
      'products.read',
      'shipping.read', 'shipping.create', 'shipping.update',
    ],
  },
  {
    name: 'analyst',
    displayName: '数据分析师',
    description: '数据分析人员，负责报表和数据分析',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'reports.read', 'reports.create', 'reports.export',
      'orders.read',
      'customers.read',
      'products.read',
      'inventory.read',
      'payments.read',
    ],
  },
  {
    name: 'viewer',
    displayName: '只读用户',
    description: '只读权限用户，只能查看数据',
    isSystemRole: true,
    permissions: [
      'dashboard.read',
      'products.read',
      'orders.read',
      'customers.read',
      'inventory.read',
      'reports.read',
    ],
  },
];

/**
 * 初始化权限数据
 */
async function seedPermissions() {
  console.log('🔐 开始初始化权限数据...');
  
  for (const permission of SYSTEM_PERMISSIONS) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {
        resource: permission.resource,
        action: permission.action,
        description: permission.description,
      },
      create: permission,
    });
  }
  
  console.log(`✅ 成功初始化 ${SYSTEM_PERMISSIONS.length} 个权限`);
}

/**
 * 初始化角色数据
 */
async function seedRoles() {
  console.log('👥 开始初始化角色数据...');
  
  for (const roleData of SYSTEM_ROLES) {
    const { permissions, ...roleInfo } = roleData;
    
    // 创建或更新角色
    const role = await prisma.role.upsert({
      where: { name: roleInfo.name },
      update: {
        displayName: roleInfo.displayName,
        description: roleInfo.description,
        isSystemRole: roleInfo.isSystemRole,
      },
      create: roleInfo,
    });
    
    // 获取权限ID
    const permissionRecords = await prisma.permission.findMany({
      where: { name: { in: permissions } },
      select: { id: true, name: true },
    });
    
    // 删除现有的角色权限关联
    await prisma.rolePermission.deleteMany({
      where: { roleId: role.id },
    });
    
    // 创建新的角色权限关联
    if (permissionRecords.length > 0) {
      await prisma.rolePermission.createMany({
        data: permissionRecords.map(permission => ({
          roleId: role.id,
          permissionId: permission.id,
          grantedBy: 'system',
        })),
      });
    }
  }
  
  console.log(`✅ 成功初始化 ${SYSTEM_ROLES.length} 个角色`);
}

/**
 * 主函数
 */
export async function seedRBAC() {
  try {
    await seedPermissions();
    await seedRoles();
    console.log('🎉 RBAC权限管理系统初始化完成！');
  } catch (error) {
    console.error('❌ RBAC初始化失败:', error);
    throw error;
  }
}

// 如果直接运行此文件
if (require.main === module) {
  seedRBAC()
    .catch((e) => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
