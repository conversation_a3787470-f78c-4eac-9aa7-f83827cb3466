#!/bin/bash

# CBEC ERP项目初始化脚本
# 用于快速设置开发环境

set -e

echo "🚀 开始初始化CBEC ERP项目..."

# 检查Node.js版本
check_node_version() {
    echo "📋 检查Node.js版本..."
    if ! command -v node &> /dev/null; then
        echo "❌ 错误: 未找到Node.js，请先安装Node.js 18.0或更高版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="18.0.0"
    
    if ! node -e "process.exit(require('semver').gte('$NODE_VERSION', '$REQUIRED_VERSION') ? 0 : 1)" 2>/dev/null; then
        echo "❌ 错误: Node.js版本过低，当前版本: $NODE_VERSION，需要: $REQUIRED_VERSION+"
        exit 1
    fi
    
    echo "✅ Node.js版本检查通过: $NODE_VERSION"
}

# 检查包管理器
check_package_manager() {
    echo "📋 检查包管理器..."
    if command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        echo "✅ 使用npm作为包管理器"
    elif command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        echo "✅ 使用yarn作为包管理器"
    else
        echo "❌ 错误: 未找到npm或yarn包管理器"
        exit 1
    fi
}

# 安装依赖
install_dependencies() {
    echo "📦 安装项目依赖..."
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm install
    else
        yarn install
    fi
    echo "✅ 依赖安装完成"
}

# 设置环境变量
setup_environment() {
    echo "🔧 设置环境变量..."
    if [ ! -f ".env.local" ]; then
        cp .env.example .env.local
        echo "✅ 已创建.env.local文件，请根据需要修改配置"
        echo "⚠️  重要: 请在.env.local中设置正确的数据库连接信息"
    else
        echo "ℹ️  .env.local文件已存在，跳过创建"
    fi
}

# 检查数据库连接
check_database() {
    echo "🗄️  检查数据库连接..."
    if command -v psql &> /dev/null; then
        echo "✅ PostgreSQL客户端已安装"
    else
        echo "⚠️  警告: 未找到PostgreSQL客户端，请确保PostgreSQL已安装并运行"
    fi
    
    if command -v redis-cli &> /dev/null; then
        echo "✅ Redis客户端已安装"
    else
        echo "⚠️  警告: 未找到Redis客户端，请确保Redis已安装并运行"
    fi
}

# 生成Prisma客户端
generate_prisma() {
    echo "🔄 生成Prisma客户端..."
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run db:generate
    else
        yarn db:generate
    fi
    echo "✅ Prisma客户端生成完成"
}

# 运行数据库迁移
run_migrations() {
    echo "🔄 运行数据库迁移..."
    read -p "是否要运行数据库迁移? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ "$PACKAGE_MANAGER" = "npm" ]; then
            npm run db:migrate
        else
            yarn db:migrate
        fi
        echo "✅ 数据库迁移完成"
    else
        echo "⏭️  跳过数据库迁移"
    fi
}

# 填充种子数据
seed_database() {
    echo "🌱 填充种子数据..."
    read -p "是否要填充种子数据? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ "$PACKAGE_MANAGER" = "npm" ]; then
            npm run db:seed
        else
            yarn db:seed
        fi
        echo "✅ 种子数据填充完成"
    else
        echo "⏭️  跳过种子数据填充"
    fi
}

# 运行代码检查
run_linting() {
    echo "🔍 运行代码检查..."
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run lint
    else
        yarn lint
    fi
    echo "✅ 代码检查完成"
}

# 运行测试
run_tests() {
    echo "🧪 运行测试..."
    read -p "是否要运行测试? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if [ "$PACKAGE_MANAGER" = "npm" ]; then
            npm test
        else
            yarn test
        fi
        echo "✅ 测试完成"
    else
        echo "⏭️  跳过测试"
    fi
}

# 启动开发服务器
start_dev_server() {
    echo "🚀 启动开发服务器..."
    read -p "是否要启动开发服务器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🌐 开发服务器将在 http://localhost:3000 启动"
        echo "📖 API文档将在 http://localhost:3000/api/docs 可用"
        echo "💾 数据库管理界面将在 http://localhost:5555 可用 (如果启用)"
        echo ""
        echo "按 Ctrl+C 停止服务器"
        echo ""
        
        if [ "$PACKAGE_MANAGER" = "npm" ]; then
            npm run dev
        else
            yarn dev
        fi
    else
        echo "⏭️  跳过启动开发服务器"
        echo ""
        echo "🎉 项目初始化完成！"
        echo ""
        echo "📝 下一步操作:"
        echo "   1. 编辑 .env.local 文件，配置数据库和其他服务"
        echo "   2. 运行 'npm run dev' 或 'yarn dev' 启动开发服务器"
        echo "   3. 访问 http://localhost:3000 查看应用"
        echo ""
        echo "📚 更多信息请查看 README.md 文件"
    fi
}

# 主函数
main() {
    echo "==============================================="
    echo "🏢 CBEC ERP - 跨境电商ERP管理平台"
    echo "==============================================="
    echo ""
    
    check_node_version
    check_package_manager
    install_dependencies
    setup_environment
    check_database
    generate_prisma
    run_migrations
    seed_database
    run_linting
    run_tests
    start_dev_server
}

# 错误处理
trap 'echo "❌ 脚本执行失败，请检查错误信息"; exit 1' ERR

# 运行主函数
main "$@"
