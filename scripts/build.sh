#!/bin/bash

# CBEC ERP项目构建脚本
# 用于生产环境构建和部署准备

set -e

echo "🏗️  开始构建CBEC ERP项目..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查环境
check_environment() {
    log_info "检查构建环境..."
    
    # 检查Node.js版本
    if ! command -v node &> /dev/null; then
        log_error "未找到Node.js，请先安装Node.js"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    log_success "Node.js版本: $NODE_VERSION"
    
    # 检查包管理器
    if command -v npm &> /dev/null; then
        PACKAGE_MANAGER="npm"
        NPM_VERSION=$(npm -v)
        log_success "使用npm: $NPM_VERSION"
    elif command -v yarn &> /dev/null; then
        PACKAGE_MANAGER="yarn"
        YARN_VERSION=$(yarn -v)
        log_success "使用yarn: $YARN_VERSION"
    else
        log_error "未找到npm或yarn包管理器"
        exit 1
    fi
}

# 清理构建目录
clean_build() {
    log_info "清理构建目录..."
    
    # 清理Next.js构建缓存
    if [ -d ".next" ]; then
        rm -rf .next
        log_success "已清理.next目录"
    fi
    
    # 清理构建输出
    if [ -d "dist" ]; then
        rm -rf dist
        log_success "已清理dist目录"
    fi
    
    # 清理TypeScript构建信息
    if [ -f "*.tsbuildinfo" ]; then
        rm -f *.tsbuildinfo
        log_success "已清理TypeScript构建信息"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装生产依赖..."
    
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm ci --only=production
    else
        yarn install --production --frozen-lockfile
    fi
    
    log_success "生产依赖安装完成"
}

# 类型检查
type_check() {
    log_info "运行TypeScript类型检查..."
    
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run type-check
    else
        yarn type-check
    fi
    
    log_success "类型检查通过"
}

# 代码检查
lint_check() {
    log_info "运行代码质量检查..."
    
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run lint
    else
        yarn lint
    fi
    
    log_success "代码质量检查通过"
}

# 运行测试
run_tests() {
    log_info "运行测试套件..."
    
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run test:ci
    else
        yarn test:ci
    fi
    
    log_success "所有测试通过"
}

# 生成Prisma客户端
generate_prisma() {
    log_info "生成Prisma客户端..."
    
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run db:generate
    else
        yarn db:generate
    fi
    
    log_success "Prisma客户端生成完成"
}

# 构建应用
build_app() {
    log_info "构建Next.js应用..."
    
    # 设置生产环境变量
    export NODE_ENV=production
    
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run build
    else
        yarn build
    fi
    
    log_success "应用构建完成"
}

# 分析构建结果
analyze_build() {
    log_info "分析构建结果..."
    
    # 检查构建输出大小
    if [ -d ".next" ]; then
        BUILD_SIZE=$(du -sh .next | cut -f1)
        log_info "构建输出大小: $BUILD_SIZE"
    fi
    
    # 检查静态资源
    if [ -d ".next/static" ]; then
        STATIC_SIZE=$(du -sh .next/static | cut -f1)
        log_info "静态资源大小: $STATIC_SIZE"
    fi
    
    # 生成构建报告
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run analyze 2>/dev/null || log_warning "构建分析工具不可用"
    else
        yarn analyze 2>/dev/null || log_warning "构建分析工具不可用"
    fi
}

# 创建部署包
create_deployment_package() {
    log_info "创建部署包..."
    
    # 创建部署目录
    DEPLOY_DIR="deploy-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$DEPLOY_DIR"
    
    # 复制必要文件
    cp -r .next "$DEPLOY_DIR/"
    cp -r public "$DEPLOY_DIR/"
    cp package.json "$DEPLOY_DIR/"
    cp package-lock.json "$DEPLOY_DIR/" 2>/dev/null || cp yarn.lock "$DEPLOY_DIR/" 2>/dev/null || true
    cp -r prisma "$DEPLOY_DIR/"
    
    # 复制配置文件
    cp next.config.js "$DEPLOY_DIR/" 2>/dev/null || true
    cp tailwind.config.js "$DEPLOY_DIR/" 2>/dev/null || true
    cp tsconfig.json "$DEPLOY_DIR/" 2>/dev/null || true
    
    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash
echo "启动CBEC ERP应用..."
export NODE_ENV=production
npm start
EOF
    chmod +x "$DEPLOY_DIR/start.sh"
    
    # 创建部署说明
    cat > "$DEPLOY_DIR/README.md" << 'EOF'
# CBEC ERP 部署包

## 部署步骤

1. 安装Node.js 18+
2. 安装依赖: `npm ci --only=production`
3. 设置环境变量（参考.env.example）
4. 运行数据库迁移: `npx prisma migrate deploy`
5. 启动应用: `./start.sh` 或 `npm start`

## 环境要求

- Node.js 18.0+
- PostgreSQL 14+
- Redis 6+

## 端口配置

默认端口: 3000
可通过环境变量PORT修改

## 健康检查

GET /api/health
EOF
    
    # 压缩部署包
    tar -czf "${DEPLOY_DIR}.tar.gz" "$DEPLOY_DIR"
    rm -rf "$DEPLOY_DIR"
    
    log_success "部署包已创建: ${DEPLOY_DIR}.tar.gz"
}

# Docker构建
build_docker() {
    if [ "$1" = "--docker" ]; then
        log_info "构建Docker镜像..."
        
        # 检查Docker是否可用
        if ! command -v docker &> /dev/null; then
            log_warning "Docker未安装，跳过Docker构建"
            return
        fi
        
        # 构建镜像
        docker build -t cbec-erp:latest .
        docker build -t cbec-erp:$(date +%Y%m%d-%H%M%S) .
        
        log_success "Docker镜像构建完成"
        
        # 显示镜像信息
        docker images | grep cbec-erp
    fi
}

# 生成构建报告
generate_report() {
    log_info "生成构建报告..."
    
    REPORT_FILE="build-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
CBEC ERP 构建报告
================

构建时间: $(date)
Node.js版本: $(node -v)
包管理器: $PACKAGE_MANAGER

构建状态: 成功
构建输出: .next/
静态资源: .next/static/

部署包: ${DEPLOY_DIR}.tar.gz (如果创建)

下一步:
1. 部署到生产环境
2. 配置环境变量
3. 运行数据库迁移
4. 启动应用服务

EOF
    
    log_success "构建报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    echo "==============================================="
    echo "🏗️  CBEC ERP 生产构建"
    echo "==============================================="
    echo ""
    
    # 解析命令行参数
    DOCKER_BUILD=false
    SKIP_TESTS=false
    CREATE_PACKAGE=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --docker)
                DOCKER_BUILD=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --package)
                CREATE_PACKAGE=true
                shift
                ;;
            *)
                log_warning "未知参数: $1"
                shift
                ;;
        esac
    done
    
    # 执行构建步骤
    check_environment
    clean_build
    install_dependencies
    generate_prisma
    type_check
    lint_check
    
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    else
        log_warning "跳过测试"
    fi
    
    build_app
    analyze_build
    
    if [ "$CREATE_PACKAGE" = true ]; then
        create_deployment_package
    fi
    
    if [ "$DOCKER_BUILD" = true ]; then
        build_docker --docker
    fi
    
    generate_report
    
    echo ""
    log_success "🎉 构建完成！"
    echo ""
    echo "📝 使用方法:"
    echo "   ./scripts/build.sh                # 基础构建"
    echo "   ./scripts/build.sh --docker       # 包含Docker构建"
    echo "   ./scripts/build.sh --package      # 创建部署包"
    echo "   ./scripts/build.sh --skip-tests   # 跳过测试"
    echo ""
}

# 错误处理
trap 'log_error "构建失败，请检查错误信息"; exit 1' ERR

# 运行主函数
main "$@"
