# IDP集成API文档

## 概述

本文档描述了CBEC ERP系统中外部身份提供商(IDP)集成的API接口。系统支持多种IDP类型，包括Google Workspace、Microsoft Azure AD、Okta、Auth0、LDAP和通用OIDC/SAML协议。

## 支持的IDP类型

| IDP类型 | 协议支持 | 描述 |
|---------|----------|------|
| Google Workspace | OIDC | Google企业身份认证 |
| Microsoft Azure AD | OIDC, SAML 2.0 | Microsoft Azure Active Directory |
| Okta | OIDC, SAML 2.0 | Okta身份管理平台 |
| Auth0 | OIDC | Auth0身份认证服务 |
| LDAP | LDAP | 企业LDAP目录服务 |
| 通用SAML | SAML 2.0 | 标准SAML 2.0协议 |
| 通用OIDC | OIDC | 标准OpenID Connect协议 |

## API端点

### 1. IDP配置管理

#### 获取IDP配置列表
```http
GET /api/auth/idp
```

**查询参数:**
- `type` (可选): IDP类型筛选
- `enabled` (可选): 启用状态筛选 (true/false)

**响应示例:**
```json
{
  "success": true,
  "data": [
    {
      "id": "google-workspace-1",
      "name": "Google Workspace",
      "type": "google_workspace",
      "protocol": "oidc",
      "enabled": true,
      "isDefault": false,
      "description": "公司Google Workspace集成",
      "config": {
        "clientId": "xxx.apps.googleusercontent.com",
        "hostedDomain": "company.com"
      },
      "createdAt": "2024-01-28T10:00:00Z",
      "updatedAt": "2024-01-28T10:00:00Z",
      "lastTestResult": {
        "success": true,
        "message": "连接测试成功",
        "timestamp": "2024-01-28T10:30:00Z"
      }
    }
  ],
  "metadata": {
    "timestamp": "2024-01-28T10:00:00Z",
    "requestId": "req-123",
    "version": "v1"
  }
}
```

#### 创建IDP配置
```http
POST /api/auth/idp
```

**请求体:**
```json
{
  "name": "Google Workspace",
  "type": "google_workspace",
  "protocol": "oidc",
  "description": "公司Google Workspace集成",
  "enabled": true,
  "isDefault": false,
  "config": {
    "clientId": "xxx.apps.googleusercontent.com",
    "clientSecret": "xxx",
    "hostedDomain": "company.com",
    "scopes": ["openid", "email", "profile"]
  }
}
```

#### 获取单个IDP配置
```http
GET /api/auth/idp/{id}
```

#### 更新IDP配置
```http
PUT /api/auth/idp/{id}
```

#### 删除IDP配置
```http
DELETE /api/auth/idp/{id}
```

#### 测试IDP连接
```http
POST /api/auth/idp/{id}/test
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "success": true,
    "message": "OAuth/OIDC连接测试成功 (4/4 项通过)",
    "details": {
      "testResults": [
        {"name": "授权端点", "success": true},
        {"name": "令牌端点", "success": true},
        {"name": "用户信息端点", "success": true},
        {"name": "发现端点", "success": true}
      ],
      "endpoints": {
        "authorization": "https://accounts.google.com/o/oauth2/v2/auth",
        "token": "https://oauth2.googleapis.com/token",
        "userInfo": "https://openidconnect.googleapis.com/v1/userinfo"
      }
    },
    "timestamp": "2024-01-28T10:30:00Z"
  }
}
```

### 2. 认证流程

#### 发起认证
```http
GET /api/auth/idp/{id}/auth
```

**查询参数:**
- `redirect_uri`: 认证成功后的回调URL
- `state` (可选): 状态参数

**响应:**
重定向到IDP的认证页面

#### 处理认证回调
```http
POST /api/auth/idp/{id}/callback
```

**请求体 (OAuth/OIDC):**
```json
{
  "code": "authorization_code",
  "state": "state_parameter",
  "redirect_uri": "callback_url"
}
```

**请求体 (SAML):**
```json
{
  "SAMLResponse": "base64_encoded_saml_response",
  "RelayState": "state_parameter"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "google-user-123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "displayName": "John Doe",
      "avatar": "https://example.com/avatar.jpg",
      "groups": ["employees", "developers"],
      "roles": ["user"],
      "idpId": "google-workspace-1",
      "idpType": "google_workspace"
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 3600
    }
  }
}
```

### 3. 用户同步

#### 同步单个用户
```http
POST /api/auth/idp/{id}/sync/user
```

#### 批量同步用户
```http
POST /api/auth/idp/{id}/sync/users
```

#### 获取同步状态
```http
GET /api/auth/idp/{id}/sync/status
```

## 配置示例

### Google Workspace配置
```json
{
  "name": "Google Workspace",
  "type": "google_workspace",
  "protocol": "oidc",
  "config": {
    "clientId": "xxx.apps.googleusercontent.com",
    "clientSecret": "xxx",
    "hostedDomain": "company.com",
    "scopes": ["openid", "email", "profile"],
    "syncOrgStructure": true,
    "adminEmail": "<EMAIL>"
  }
}
```

### Azure AD OIDC配置
```json
{
  "name": "Azure AD",
  "type": "microsoft_azure_ad",
  "protocol": "oidc",
  "config": {
    "tenantId": "tenant-id",
    "clientId": "application-id",
    "clientSecret": "client-secret",
    "useGraphAPI": true,
    "scopes": ["openid", "profile", "email", "User.Read"]
  }
}
```

### Azure AD SAML配置
```json
{
  "name": "Azure AD SAML",
  "type": "microsoft_azure_ad",
  "protocol": "saml2",
  "config": {
    "tenantId": "tenant-id",
    "applicationId": "application-id",
    "entityId": "https://sts.windows.net/tenant-id/",
    "ssoServiceUrl": "https://login.microsoftonline.com/tenant-id/saml2",
    "certificate": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"
  }
}
```

### 通用OIDC配置
```json
{
  "name": "Custom OIDC",
  "type": "oidc",
  "protocol": "oidc",
  "config": {
    "clientId": "client-id",
    "clientSecret": "client-secret",
    "discoveryUrl": "https://idp.example.com/.well-known/openid_configuration",
    "scopes": ["openid", "profile", "email"],
    "userInfoMapping": {
      "id": "user_id",
      "email": "email_address",
      "firstName": "first_name",
      "lastName": "last_name"
    }
  }
}
```

### LDAP配置
```json
{
  "name": "Company LDAP",
  "type": "ldap",
  "protocol": "ldap",
  "config": {
    "serverUrl": "ldap://ldap.company.com:389",
    "bindDn": "cn=admin,dc=company,dc=com",
    "bindPassword": "password",
    "userBaseDn": "ou=users,dc=company,dc=com",
    "userSearchFilter": "(uid={username})",
    "groupBaseDn": "ou=groups,dc=company,dc=com",
    "useTls": true
  }
}
```

## 错误代码

| 错误代码 | 描述 |
|----------|------|
| `VALIDATION_ERROR` | 请求参数验证失败 |
| `NOT_FOUND` | IDP配置不存在 |
| `NAME_EXISTS` | 配置名称已存在 |
| `IDP_IN_USE` | IDP正在被用户使用，无法删除 |
| `CONNECTION_FAILED` | IDP连接测试失败 |
| `AUTH_FAILED` | 认证失败 |
| `INVALID_CREDENTIALS` | 无效的认证凭据 |
| `DOMAIN_MISMATCH` | 域名不匹配 |
| `TOKEN_INVALID` | 令牌无效 |
| `SYNC_FAILED` | 用户同步失败 |

## 安全考虑

1. **HTTPS要求**: 所有IDP通信必须使用HTTPS
2. **令牌安全**: JWT令牌使用强密钥签名
3. **状态验证**: 使用state参数防止CSRF攻击
4. **域名验证**: 验证用户所属域名
5. **会话管理**: 实现会话超时和并发控制
6. **审计日志**: 记录所有认证和配置变更事件

## 限制和配额

- 最大IDP配置数: 50个
- 并发认证请求: 100个/分钟
- 用户同步频率: 每小时1次
- 配置测试频率: 每分钟5次

## 监控和告警

系统提供以下监控指标:
- IDP连接成功率
- 认证成功率
- 用户同步成功率
- 响应时间
- 错误率

建议设置告警规则:
- 连接成功率 < 95%
- 认证成功率 < 90%
- 响应时间 > 5秒
- 错误率 > 5%
