# 跨境电商ERP系统API接口设计

## 文档概述

本文档详细描述了跨境电商ERP系统的RESTful API接口设计，包括接口规范、认证机制、错误处理、版本控制等，为前端开发和第三方集成提供完整的API文档。

## 1. API设计原则

### 1.1 RESTful设计规范

**1.1.1 HTTP方法使用**
- `GET` - 获取资源（幂等）
- `POST` - 创建资源
- `PUT` - 完整更新资源（幂等）
- `PATCH` - 部分更新资源
- `DELETE` - 删除资源（幂等）

**1.1.2 URL设计规范**
```
基础URL: https://api.cbec-erp.com/v1

资源命名规则:
- 使用复数名词: /users, /products, /orders
- 使用小写字母和连字符: /order-items
- 嵌套资源: /orders/{orderId}/items
- 查询参数: /products?category=electronics&status=active
```

**1.1.3 HTTP状态码标准**
```typescript
enum HttpStatus {
  // 成功响应
  OK = 200,                    // 请求成功
  CREATED = 201,               // 资源创建成功
  NO_CONTENT = 204,            // 请求成功但无返回内容
  
  // 客户端错误
  BAD_REQUEST = 400,           // 请求参数错误
  UNAUTHORIZED = 401,          // 未认证
  FORBIDDEN = 403,             // 无权限
  NOT_FOUND = 404,             // 资源不存在
  CONFLICT = 409,              // 资源冲突
  UNPROCESSABLE_ENTITY = 422,  // 请求格式正确但语义错误
  
  // 服务器错误
  INTERNAL_SERVER_ERROR = 500, // 服务器内部错误
  SERVICE_UNAVAILABLE = 503    // 服务不可用
}
```

### 1.2 统一响应格式

**1.2.1 成功响应格式**
```typescript
interface SuccessResponse<T> {
  success: true;
  data: T;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  metadata?: {
    timestamp: string;
    requestId: string;
    version: string;
    processingTime: number;
  };
}

// 示例
{
  "success": true,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "iPhone 15 Pro",
    "price": 999.99,
    "currency": "USD"
  },
  "metadata": {
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_abc123",
    "version": "v1",
    "processingTime": 45
  }
}
```

**1.2.2 错误响应格式**
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
    field?: string;
    timestamp: string;
    requestId: string;
  };
}

// 示例
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "email": ["邮箱格式不正确"],
      "password": ["密码长度至少8位"]
    },
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_abc123"
  }
}
```

## 2. 认证和授权

### 2.1 JWT认证机制

**2.1.1 认证流程**
```typescript
// 登录请求
POST /auth/login
{
  "email": "<EMAIL>",
  "password": "password123",
  "rememberMe": true
}

// 登录响应
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "roles": ["admin"]
    },
    "tokens": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expiresIn": 3600,
      "tokenType": "Bearer"
    }
  }
}
```

**2.1.2 Token刷新**
```typescript
// 刷新Token
POST /auth/refresh
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}

// 响应
{
  "success": true,
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 3600
  }
}
```

### 2.2 权限控制

**2.2.1 请求头认证**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
X-API-Key: your-api-key (可选，用于第三方集成)
```

**2.2.2 权限验证中间件**
```typescript
interface PermissionCheck {
  resource: string;
  action: string;
  conditions?: {
    ownResource?: boolean;
    departmentResource?: boolean;
  };
}

// 权限装饰器示例
@RequirePermission({
  resource: 'orders',
  action: 'read',
  conditions: { ownResource: true }
})
async getOrder(orderId: string, userId: string) {
  // 只能查看自己的订单
}
```

## 3. 核心API接口

### 3.1 用户管理API

**3.1.1 用户CRUD操作**
```typescript
// 获取用户列表
GET /users?page=1&limit=20&role=admin&status=active
Response: {
  "success": true,
  "data": [
    {
      "id": "user_123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "roles": ["admin"],
      "status": "active",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  }
}

// 获取单个用户
GET /users/{userId}
Response: {
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "phone": "+1234567890",
    "avatar": "https://cdn.example.com/avatars/user_123.jpg",
    "roles": ["admin"],
    "permissions": ["users.read", "users.write"],
    "status": "active",
    "lastLoginAt": "2024-01-15T10:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T09:00:00Z"
  }
}

// 创建用户
POST /users
{
  "email": "<EMAIL>",
  "firstName": "Jane",
  "lastName": "Smith",
  "password": "securePassword123",
  "roles": ["operator"],
  "phone": "+1234567890"
}

// 更新用户
PATCH /users/{userId}
{
  "firstName": "Jane Updated",
  "phone": "+0987654321",
  "status": "inactive"
}

// 删除用户
DELETE /users/{userId}
Response: 204 No Content
```

### 3.2 商品管理API

**3.2.1 商品操作**
```typescript
// 获取商品列表
GET /products?page=1&limit=20&category=electronics&status=active&search=iphone
Response: {
  "success": true,
  "data": [
    {
      "id": "prod_123",
      "sku": "IPH15PRO256",
      "name": "iPhone 15 Pro 256GB",
      "description": "Latest iPhone with advanced features",
      "category": {
        "id": "cat_123",
        "name": "Smartphones"
      },
      "price": 999.99,
      "currency": "USD",
      "images": [
        {
          "url": "https://cdn.example.com/products/iphone15pro_1.jpg",
          "alt": "iPhone 15 Pro Front View",
          "isPrimary": true
        }
      ],
      "inventory": {
        "totalStock": 150,
        "availableStock": 120,
        "reservedStock": 30
      },
      "status": "active",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 500,
    "totalPages": 25
  }
}

// 创建商品
POST /products
{
  "sku": "NEWPROD001",
  "name": "New Product",
  "description": "Product description",
  "categoryId": "cat_123",
  "price": 299.99,
  "currency": "USD",
  "weight": 0.5,
  "dimensions": {
    "length": 10,
    "width": 5,
    "height": 3,
    "unit": "cm"
  },
  "images": [
    {
      "url": "https://cdn.example.com/products/newprod_1.jpg",
      "alt": "New Product Image",
      "isPrimary": true
    }
  ],
  "variants": [
    {
      "sku": "NEWPROD001-RED",
      "attributes": {
        "color": "red",
        "size": "M"
      },
      "price": 299.99
    }
  ]
}

// 批量更新商品价格
PATCH /products/batch/prices
{
  "updates": [
    {
      "id": "prod_123",
      "price": 899.99
    },
    {
      "id": "prod_124",
      "price": 1299.99
    }
  ]
}
```

### 3.3 订单管理API

**3.3.1 订单操作**
```typescript
// 获取订单列表
GET /orders?page=1&limit=20&status=pending&customer=cust_123&dateFrom=2024-01-01&dateTo=2024-01-31
Response: {
  "success": true,
  "data": [
    {
      "id": "order_123",
      "orderNumber": "ORD-2024-001",
      "customer": {
        "id": "cust_123",
        "email": "<EMAIL>",
        "name": "John Customer"
      },
      "status": "pending",
      "currency": "USD",
      "subtotal": 999.99,
      "taxAmount": 80.00,
      "shippingAmount": 15.00,
      "totalAmount": 1094.99,
      "items": [
        {
          "id": "item_123",
          "productId": "prod_123",
          "sku": "IPH15PRO256",
          "name": "iPhone 15 Pro 256GB",
          "quantity": 1,
          "unitPrice": 999.99,
          "totalPrice": 999.99
        }
      ],
      "shippingAddress": {
        "firstName": "John",
        "lastName": "Customer",
        "addressLine1": "123 Main St",
        "city": "New York",
        "state": "NY",
        "postalCode": "10001",
        "country": "US"
      },
      "orderDate": "2024-01-15T10:00:00Z",
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ]
}

// 创建订单
POST /orders
{
  "customerId": "cust_123",
  "currency": "USD",
  "items": [
    {
      "productId": "prod_123",
      "variantId": "var_123",
      "quantity": 2,
      "unitPrice": 999.99
    }
  ],
  "shippingAddress": {
    "firstName": "John",
    "lastName": "Customer",
    "addressLine1": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postalCode": "10001",
    "country": "US",
    "phone": "+1234567890"
  },
  "billingAddress": {
    // 同上
  },
  "notes": "Please handle with care"
}

// 更新订单状态
PATCH /orders/{orderId}/status
{
  "status": "confirmed",
  "notes": "Order confirmed by admin"
}

// 取消订单
POST /orders/{orderId}/cancel
{
  "reason": "Customer requested cancellation",
  "refundAmount": 1094.99
}
```

### 3.4 库存管理API

**3.4.1 库存操作**
```typescript
// 获取库存信息
GET /inventory?warehouseId=wh_123&productId=prod_123&lowStock=true
Response: {
  "success": true,
  "data": [
    {
      "id": "inv_123",
      "warehouse": {
        "id": "wh_123",
        "name": "Main Warehouse",
        "code": "MAIN"
      },
      "product": {
        "id": "prod_123",
        "sku": "IPH15PRO256",
        "name": "iPhone 15 Pro 256GB"
      },
      "quantityOnHand": 150,
      "quantityReserved": 30,
      "quantityAvailable": 120,
      "reorderPoint": 50,
      "reorderQuantity": 100,
      "lastCountedAt": "2024-01-10T00:00:00Z",
      "updatedAt": "2024-01-15T10:00:00Z"
    }
  ]
}

// 调整库存
POST /inventory/{inventoryId}/adjust
{
  "quantity": 10,
  "type": "increase", // increase, decrease, set
  "reason": "Stock replenishment",
  "notes": "Received new shipment"
}

// 预留库存
POST /inventory/reserve
{
  "items": [
    {
      "productId": "prod_123",
      "variantId": "var_123",
      "quantity": 2,
      "warehouseId": "wh_123"
    }
  ],
  "orderId": "order_123",
  "expiresAt": "2024-01-16T10:00:00Z"
}

// 释放预留库存
POST /inventory/release
{
  "reservationId": "res_123"
}
```

## 4. 分页和过滤

### 4.1 分页参数
```typescript
interface PaginationParams {
  page?: number;        // 页码，从1开始，默认1
  limit?: number;       // 每页数量，默认20，最大100
  sort?: string;        // 排序字段，如 "createdAt:desc,name:asc"
}

// 示例
GET /products?page=2&limit=50&sort=createdAt:desc,name:asc
```

### 4.2 过滤参数
```typescript
interface FilterParams {
  // 基础过滤
  status?: string;
  category?: string;
  
  // 时间范围过滤
  dateFrom?: string;    // ISO 8601格式
  dateTo?: string;
  
  // 数值范围过滤
  priceMin?: number;
  priceMax?: number;
  
  // 搜索
  search?: string;      // 全文搜索
  
  // 数组过滤
  ids?: string[];       // 多个ID
  tags?: string[];      // 多个标签
}

// 示例
GET /orders?status=pending&dateFrom=2024-01-01&dateTo=2024-01-31&priceMin=100&search=iphone
```

## 5. 错误处理

### 5.1 错误代码定义
```typescript
enum ErrorCodes {
  // 通用错误
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  
  // 业务错误
  INSUFFICIENT_INVENTORY = 'INSUFFICIENT_INVENTORY',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  ORDER_ALREADY_SHIPPED = 'ORDER_ALREADY_SHIPPED',
  INVALID_COUPON = 'INVALID_COUPON',
  
  // 系统错误
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}
```

### 5.2 错误处理示例
```typescript
// 验证错误
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {
      "email": ["邮箱格式不正确"],
      "password": ["密码长度至少8位"]
    }
  }
}

// 业务错误
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_INVENTORY",
    "message": "库存不足",
    "details": {
      "productId": "prod_123",
      "requestedQuantity": 10,
      "availableQuantity": 5
    }
  }
}

// 权限错误
{
  "success": false,
  "error": {
    "code": "FORBIDDEN",
    "message": "您没有权限执行此操作",
    "details": {
      "requiredPermission": "orders.write",
      "userPermissions": ["orders.read"]
    }
  }
}
```

## 6. API版本控制

### 6.1 版本策略
```typescript
// URL版本控制（推荐）
GET /v1/products
GET /v2/products

// Header版本控制
GET /products
Headers: {
  "API-Version": "v1"
}

// 查询参数版本控制
GET /products?version=v1
```

### 6.2 版本兼容性
```typescript
interface APIVersion {
  version: string;
  deprecated: boolean;
  deprecationDate?: string;
  sunsetDate?: string;
  migrationGuide?: string;
}

// 版本信息响应
GET /version
{
  "success": true,
  "data": {
    "current": "v2",
    "supported": ["v1", "v2"],
    "versions": {
      "v1": {
        "version": "v1",
        "deprecated": true,
        "deprecationDate": "2024-06-01",
        "sunsetDate": "2024-12-01",
        "migrationGuide": "https://docs.api.com/migration/v1-to-v2"
      },
      "v2": {
        "version": "v2",
        "deprecated": false
      }
    }
  }
}
```

这个API接口设计为跨境电商ERP系统提供了完整的RESTful API规范，确保前端和第三方系统能够高效、安全地与后端服务交互。
