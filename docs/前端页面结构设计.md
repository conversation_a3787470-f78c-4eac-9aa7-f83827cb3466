# 跨境电商ERP系统前端页面结构设计

## 文档概述

本文档详细描述了跨境电商ERP系统的前端页面结构设计，包括页面布局、组件架构、用户界面原型、交互设计等，为前端开发提供完整的设计指导。

## 1. 整体布局架构

### 1.1 主布局结构

```mermaid
graph TD
    A[应用根组件 App] --> B[路由守卫 AuthGuard]
    B --> C[主布局 MainLayout]
    C --> D[顶部导航 TopNavbar]
    C --> E[侧边栏 Sidebar]
    C --> F[主内容区 MainContent]
    C --> G[底部信息 Footer]
    
    F --> H[面包屑 Breadcrumb]
    F --> I[页面标题 PageHeader]
    F --> J[页面内容 PageContent]
    F --> K[操作按钮 ActionButtons]
```

### 1.2 响应式布局设计

```typescript
// 响应式断点定义
const breakpoints = {
  xs: '0px',      // 手机竖屏
  sm: '576px',    // 手机横屏
  md: '768px',    // 平板竖屏
  lg: '992px',    // 平板横屏/小屏笔记本
  xl: '1200px',   // 桌面显示器
  xxl: '1400px'   // 大屏显示器
};

// 布局配置
interface LayoutConfig {
  sidebar: {
    width: string;
    collapsedWidth: string;
    breakpoint: string;
  };
  header: {
    height: string;
    fixed: boolean;
  };
  content: {
    padding: string;
    maxWidth: string;
  };
}

const layoutConfig: LayoutConfig = {
  sidebar: {
    width: '280px',
    collapsedWidth: '80px',
    breakpoint: 'lg'
  },
  header: {
    height: '64px',
    fixed: true
  },
  content: {
    padding: '24px',
    maxWidth: '1200px'
  }
};
```

## 2. 核心组件设计

### 2.1 顶部导航栏 (TopNavbar)

```typescript
interface TopNavbarProps {
  user: User;
  notifications: Notification[];
  onLogout: () => void;
  onToggleSidebar: () => void;
}

const TopNavbar: React.FC<TopNavbarProps> = ({
  user,
  notifications,
  onLogout,
  onToggleSidebar
}) => {
  return (
    <header className="top-navbar">
      {/* 左侧区域 */}
      <div className="navbar-left">
        <button onClick={onToggleSidebar} className="sidebar-toggle">
          <MenuIcon />
        </button>
        <div className="logo">
          <img src="/logo.svg" alt="CBEC ERP" />
          <span>CBEC ERP</span>
        </div>
      </div>
      
      {/* 中间搜索区域 */}
      <div className="navbar-center">
        <GlobalSearch placeholder="搜索订单、商品、客户..." />
      </div>
      
      {/* 右侧功能区域 */}
      <div className="navbar-right">
        <LanguageSelector />
        <NotificationDropdown notifications={notifications} />
        <UserDropdown user={user} onLogout={onLogout} />
      </div>
    </header>
  );
};
```

### 2.2 侧边栏导航 (Sidebar)

```typescript
interface MenuItem {
  key: string;
  label: string;
  icon: React.ReactNode;
  path?: string;
  children?: MenuItem[];
  permission?: string;
  badge?: number;
}

const menuItems: MenuItem[] = [
  {
    key: 'dashboard',
    label: '仪表盘',
    icon: <DashboardIcon />,
    path: '/dashboard'
  },
  {
    key: 'orders',
    label: '订单管理',
    icon: <OrderIcon />,
    children: [
      { key: 'orders-list', label: '订单列表', path: '/orders' },
      { key: 'orders-create', label: '创建订单', path: '/orders/create' },
      { key: 'orders-import', label: '批量导入', path: '/orders/import' }
    ]
  },
  {
    key: 'products',
    label: '商品管理',
    icon: <ProductIcon />,
    children: [
      { key: 'products-list', label: '商品列表', path: '/products' },
      { key: 'products-create', label: '添加商品', path: '/products/create' },
      { key: 'categories', label: '商品分类', path: '/categories' },
      { key: 'brands', label: '品牌管理', path: '/brands' }
    ]
  },
  {
    key: 'inventory',
    label: '库存管理',
    icon: <InventoryIcon />,
    children: [
      { key: 'inventory-overview', label: '库存概览', path: '/inventory' },
      { key: 'inventory-movements', label: '库存变动', path: '/inventory/movements' },
      { key: 'stock-alerts', label: '库存预警', path: '/inventory/alerts', badge: 5 },
      { key: 'warehouses', label: '仓库管理', path: '/warehouses' }
    ]
  },
  {
    key: 'customers',
    label: '客户管理',
    icon: <CustomerIcon />,
    children: [
      { key: 'customers-list', label: '客户列表', path: '/customers' },
      { key: 'customer-groups', label: '客户分组', path: '/customer-groups' },
      { key: 'customer-service', label: '客服工单', path: '/customer-service' }
    ]
  },
  {
    key: 'finance',
    label: '财务管理',
    icon: <FinanceIcon />,
    permission: 'finance.read',
    children: [
      { key: 'payments', label: '支付记录', path: '/payments' },
      { key: 'invoices', label: '发票管理', path: '/invoices' },
      { key: 'refunds', label: '退款管理', path: '/refunds' },
      { key: 'reports', label: '财务报表', path: '/finance/reports' }
    ]
  },
  {
    key: 'settings',
    label: '系统设置',
    icon: <SettingsIcon />,
    permission: 'settings.read',
    children: [
      { key: 'users', label: '用户管理', path: '/settings/users' },
      { key: 'roles', label: '角色权限', path: '/settings/roles' },
      { key: 'integrations', label: '第三方集成', path: '/settings/integrations' },
      { key: 'system', label: '系统配置', path: '/settings/system' }
    ]
  }
];
```

### 2.3 数据表格组件 (DataTable)

```typescript
interface DataTableProps<T> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: PaginationConfig;
  selection?: SelectionConfig<T>;
  actions?: ActionConfig<T>;
  filters?: FilterConfig[];
  onRowClick?: (record: T) => void;
  onSelectionChange?: (selectedRows: T[]) => void;
}

interface TableColumn<T> {
  key: string;
  title: string;
  dataIndex: keyof T;
  width?: number;
  sortable?: boolean;
  filterable?: boolean;
  render?: (value: any, record: T, index: number) => React.ReactNode;
  align?: 'left' | 'center' | 'right';
}

// 使用示例
const orderColumns: TableColumn<Order>[] = [
  {
    key: 'orderNumber',
    title: '订单号',
    dataIndex: 'orderNumber',
    width: 150,
    sortable: true,
    render: (value, record) => (
      <Link to={`/orders/${record.id}`} className="order-link">
        {value}
      </Link>
    )
  },
  {
    key: 'customer',
    title: '客户',
    dataIndex: 'customer',
    render: (customer) => (
      <div className="customer-info">
        <div className="customer-name">{customer.name}</div>
        <div className="customer-email">{customer.email}</div>
      </div>
    )
  },
  {
    key: 'status',
    title: '状态',
    dataIndex: 'status',
    width: 120,
    filterable: true,
    render: (status) => (
      <StatusBadge status={status} />
    )
  },
  {
    key: 'totalAmount',
    title: '订单金额',
    dataIndex: 'totalAmount',
    width: 120,
    align: 'right',
    sortable: true,
    render: (amount, record) => (
      <PriceDisplay amount={amount} currency={record.currency} />
    )
  },
  {
    key: 'orderDate',
    title: '下单时间',
    dataIndex: 'orderDate',
    width: 180,
    sortable: true,
    render: (date) => (
      <DateDisplay date={date} format="YYYY-MM-DD HH:mm" />
    )
  },
  {
    key: 'actions',
    title: '操作',
    dataIndex: 'actions',
    width: 150,
    render: (_, record) => (
      <ActionButtons>
        <Button size="small" onClick={() => viewOrder(record.id)}>
          查看
        </Button>
        <Button size="small" onClick={() => editOrder(record.id)}>
          编辑
        </Button>
        <Dropdown
          items={[
            { key: 'ship', label: '发货' },
            { key: 'cancel', label: '取消' },
            { key: 'refund', label: '退款' }
          ]}
          onSelect={(key) => handleAction(key, record)}
        >
          <Button size="small">更多</Button>
        </Dropdown>
      </ActionButtons>
    )
  }
];
```

## 3. 页面设计规范

### 3.1 列表页面模板

```typescript
// 通用列表页面组件
const ListPageTemplate: React.FC<ListPageProps> = ({
  title,
  description,
  createButton,
  filters,
  table,
  bulkActions
}) => {
  return (
    <div className="list-page">
      {/* 页面头部 */}
      <PageHeader
        title={title}
        description={description}
        extra={createButton}
      />
      
      {/* 过滤器区域 */}
      <div className="filters-section">
        <FilterBar filters={filters} />
        <div className="filter-actions">
          <Button onClick={resetFilters}>重置</Button>
          <Button type="primary" onClick={applyFilters}>应用</Button>
        </div>
      </div>
      
      {/* 批量操作区域 */}
      {bulkActions && (
        <div className="bulk-actions">
          <BulkActionBar actions={bulkActions} />
        </div>
      )}
      
      {/* 数据表格 */}
      <div className="table-section">
        <DataTable {...table} />
      </div>
    </div>
  );
};

// 订单列表页面
const OrderListPage: React.FC = () => {
  return (
    <ListPageTemplate
      title="订单管理"
      description="管理所有订单信息，包括订单状态跟踪、发货处理等"
      createButton={
        <Button type="primary" icon={<PlusIcon />} onClick={createOrder}>
          创建订单
        </Button>
      }
      filters={orderFilters}
      table={orderTableProps}
      bulkActions={orderBulkActions}
    />
  );
};
```

### 3.2 详情页面模板

```typescript
// 通用详情页面组件
const DetailPageTemplate: React.FC<DetailPageProps> = ({
  title,
  subtitle,
  status,
  actions,
  tabs,
  loading
}) => {
  return (
    <div className="detail-page">
      {/* 页面头部 */}
      <PageHeader
        title={title}
        subtitle={subtitle}
        status={status}
        extra={actions}
        onBack={() => history.back()}
      />
      
      {/* 标签页内容 */}
      <div className="detail-content">
        <Tabs items={tabs} />
      </div>
      
      {/* 加载状态 */}
      {loading && <LoadingSpinner />}
    </div>
  );
};

// 订单详情页面
const OrderDetailPage: React.FC = () => {
  const { orderId } = useParams();
  const { data: order, loading } = useOrder(orderId);
  
  const tabs = [
    {
      key: 'overview',
      label: '订单概览',
      content: <OrderOverviewTab order={order} />
    },
    {
      key: 'items',
      label: '商品明细',
      content: <OrderItemsTab items={order?.items} />
    },
    {
      key: 'payments',
      label: '支付记录',
      content: <OrderPaymentsTab payments={order?.payments} />
    },
    {
      key: 'shipping',
      label: '物流信息',
      content: <OrderShippingTab shipping={order?.shipping} />
    },
    {
      key: 'history',
      label: '操作历史',
      content: <OrderHistoryTab history={order?.history} />
    }
  ];
  
  return (
    <DetailPageTemplate
      title={`订单 ${order?.orderNumber}`}
      subtitle={`客户：${order?.customer?.name}`}
      status={<StatusBadge status={order?.status} />}
      actions={<OrderActions order={order} />}
      tabs={tabs}
      loading={loading}
    />
  );
};
```

### 3.3 表单页面模板

```typescript
// 通用表单页面组件
const FormPageTemplate: React.FC<FormPageProps> = ({
  title,
  description,
  form,
  onSubmit,
  onCancel,
  loading,
  mode = 'create'
}) => {
  return (
    <div className="form-page">
      {/* 页面头部 */}
      <PageHeader
        title={title}
        description={description}
        onBack={onCancel}
      />
      
      {/* 表单内容 */}
      <div className="form-content">
        <Form
          {...form}
          onFinish={onSubmit}
          layout="vertical"
          className="main-form"
        >
          {form.sections.map(section => (
            <FormSection
              key={section.key}
              title={section.title}
              description={section.description}
              fields={section.fields}
            />
          ))}
          
          {/* 表单操作按钮 */}
          <div className="form-actions">
            <Button onClick={onCancel}>
              取消
            </Button>
            <Button type="primary" htmlType="submit" loading={loading}>
              {mode === 'create' ? '创建' : '保存'}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
};

// 商品创建/编辑页面
const ProductFormPage: React.FC = () => {
  const { productId } = useParams();
  const isEdit = !!productId;
  
  const formSections = [
    {
      key: 'basic',
      title: '基本信息',
      description: '商品的基本信息和描述',
      fields: [
        { name: 'name', label: '商品名称', required: true },
        { name: 'sku', label: 'SKU', required: true },
        { name: 'description', label: '商品描述', type: 'textarea' },
        { name: 'categoryId', label: '商品分类', type: 'select' }
      ]
    },
    {
      key: 'pricing',
      title: '价格信息',
      description: '商品的价格和成本信息',
      fields: [
        { name: 'price', label: '销售价格', type: 'number', required: true },
        { name: 'costPrice', label: '成本价格', type: 'number' },
        { name: 'currency', label: '货币', type: 'select', defaultValue: 'USD' }
      ]
    },
    {
      key: 'inventory',
      title: '库存信息',
      description: '商品的库存管理设置',
      fields: [
        { name: 'trackInventory', label: '跟踪库存', type: 'switch' },
        { name: 'allowBackorder', label: '允许缺货销售', type: 'switch' },
        { name: 'reorderPoint', label: '补货点', type: 'number' }
      ]
    },
    {
      key: 'shipping',
      title: '物流信息',
      description: '商品的重量和尺寸信息',
      fields: [
        { name: 'weight', label: '重量(kg)', type: 'number' },
        { name: 'dimensions.length', label: '长度(cm)', type: 'number' },
        { name: 'dimensions.width', label: '宽度(cm)', type: 'number' },
        { name: 'dimensions.height', label: '高度(cm)', type: 'number' }
      ]
    }
  ];
  
  return (
    <FormPageTemplate
      title={isEdit ? '编辑商品' : '创建商品'}
      description={isEdit ? '修改商品信息' : '添加新的商品到系统'}
      form={{ sections: formSections }}
      onSubmit={handleSubmit}
      onCancel={() => navigate('/products')}
      loading={submitting}
      mode={isEdit ? 'edit' : 'create'}
    />
  );
};
```

## 4. 仪表盘设计

### 4.1 仪表盘布局

```typescript
const DashboardPage: React.FC = () => {
  return (
    <div className="dashboard">
      {/* 关键指标卡片 */}
      <div className="metrics-row">
        <MetricCard
          title="今日订单"
          value={156}
          change={+12.5}
          icon={<OrderIcon />}
          color="blue"
        />
        <MetricCard
          title="今日销售额"
          value="$24,580"
          change={+8.2}
          icon={<RevenueIcon />}
          color="green"
        />
        <MetricCard
          title="待处理订单"
          value={23}
          change={-5.1}
          icon={<PendingIcon />}
          color="orange"
        />
        <MetricCard
          title="库存预警"
          value={8}
          change={+2}
          icon={<AlertIcon />}
          color="red"
        />
      </div>
      
      {/* 图表区域 */}
      <div className="charts-row">
        <div className="chart-card">
          <CardHeader title="销售趋势" />
          <SalesChart data={salesData} />
        </div>
        <div className="chart-card">
          <CardHeader title="订单状态分布" />
          <OrderStatusChart data={orderStatusData} />
        </div>
      </div>
      
      {/* 数据表格区域 */}
      <div className="tables-row">
        <div className="table-card">
          <CardHeader 
            title="最近订单" 
            extra={<Link to="/orders">查看全部</Link>}
          />
          <RecentOrdersTable data={recentOrders} />
        </div>
        <div className="table-card">
          <CardHeader 
            title="热销商品" 
            extra={<Link to="/products">查看全部</Link>}
          />
          <TopProductsTable data={topProducts} />
        </div>
      </div>
    </div>
  );
};
```

## 5. 移动端适配

### 5.1 响应式设计策略

```typescript
// 移动端布局组件
const MobileLayout: React.FC = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  return (
    <div className="mobile-layout">
      {/* 移动端顶部导航 */}
      <MobileHeader onMenuClick={() => setSidebarOpen(true)} />
      
      {/* 侧边栏抽屉 */}
      <Drawer
        open={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        placement="left"
      >
        <MobileSidebar />
      </Drawer>
      
      {/* 主内容区 */}
      <div className="mobile-content">
        {children}
      </div>
      
      {/* 底部导航 */}
      <MobileBottomNav />
    </div>
  );
};

// 移动端底部导航
const MobileBottomNav: React.FC = () => {
  const location = useLocation();
  
  const navItems = [
    { key: 'dashboard', label: '首页', icon: <HomeIcon />, path: '/dashboard' },
    { key: 'orders', label: '订单', icon: <OrderIcon />, path: '/orders' },
    { key: 'products', label: '商品', icon: <ProductIcon />, path: '/products' },
    { key: 'customers', label: '客户', icon: <CustomerIcon />, path: '/customers' },
    { key: 'more', label: '更多', icon: <MoreIcon />, path: '/more' }
  ];
  
  return (
    <div className="mobile-bottom-nav">
      {navItems.map(item => (
        <Link
          key={item.key}
          to={item.path}
          className={`nav-item ${location.pathname.startsWith(item.path) ? 'active' : ''}`}
        >
          {item.icon}
          <span>{item.label}</span>
        </Link>
      ))}
    </div>
  );
};
```

## 6. 主题和样式系统

### 6.1 设计令牌 (Design Tokens)

```typescript
// 颜色系统
const colors = {
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    500: '#0ea5e9',
    600: '#0284c7',
    900: '#0c4a6e'
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    500: '#6b7280',
    900: '#111827'
  },
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  info: '#3b82f6'
};

// 间距系统
const spacing = {
  xs: '4px',
  sm: '8px',
  md: '16px',
  lg: '24px',
  xl: '32px',
  xxl: '48px'
};

// 字体系统
const typography = {
  fontFamily: {
    sans: ['Inter', 'system-ui', 'sans-serif'],
    mono: ['JetBrains Mono', 'monospace']
  },
  fontSize: {
    xs: '12px',
    sm: '14px',
    base: '16px',
    lg: '18px',
    xl: '20px',
    '2xl': '24px',
    '3xl': '30px'
  },
  fontWeight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  }
};
```

这个前端页面结构设计为跨境电商ERP系统提供了完整的用户界面框架，确保系统具备良好的用户体验和可维护性。
