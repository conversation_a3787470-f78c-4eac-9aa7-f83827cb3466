# RBAC权限管理系统

## 概述

本项目实现了一个完整的基于角色的访问控制（RBAC）权限管理系统，提供细粒度的权限控制和用户管理功能。

## 系统架构

### 数据库模型

#### 核心表结构
- **User**: 用户表，存储用户基本信息
- **Role**: 角色表，定义系统角色
- **Permission**: 权限表，定义具体权限
- **UserRole**: 用户角色关联表
- **RolePermission**: 角色权限关联表

#### 关系设计
```
User ←→ UserRole ←→ Role ←→ RolePermission ←→ Permission
```

### 权限模型

#### 权限命名规范
权限采用 `资源.操作` 的命名格式，例如：
- `products.read` - 查看商品
- `orders.create` - 创建订单
- `users.manage_roles` - 管理用户角色

#### 系统角色
1. **super_admin** - 超级管理员，拥有所有权限
2. **admin** - 系统管理员，拥有大部分管理权限
3. **product_manager** - 商品管理员，负责商品和库存管理
4. **order_manager** - 订单管理员，负责订单处理和物流管理
5. **sales_rep** - 销售代表，负责客户和订单管理
6. **customer_service** - 客服人员，负责客户服务和售后处理
7. **warehouse_staff** - 仓库人员，负责库存和发货管理
8. **analyst** - 数据分析师，负责报表和数据分析
9. **viewer** - 只读用户，只能查看数据

## API接口

### 角色管理
- `GET /api/roles` - 获取角色列表
- `POST /api/roles` - 创建角色
- `GET /api/roles/[id]` - 获取角色详情
- `PUT /api/roles/[id]` - 更新角色
- `DELETE /api/roles/[id]` - 删除角色
- `POST /api/roles/[id]/permissions` - 分配角色权限

### 权限管理
- `GET /api/permissions` - 获取权限列表
- `POST /api/permissions` - 创建权限
- `GET /api/permissions/[id]` - 获取权限详情
- `PUT /api/permissions/[id]` - 更新权限
- `DELETE /api/permissions/[id]` - 删除权限

### 用户角色管理
- `GET /api/users/[id]/roles` - 获取用户角色
- `POST /api/users/[id]/roles` - 分配用户角色
- `DELETE /api/users/[id]/roles` - 移除用户角色
- `POST /api/users/batch-assign-roles` - 批量分配角色

### 权限验证
- `POST /api/auth/check-permission` - 检查单个权限
- `PUT /api/auth/check-permission` - 批量检查权限
- `GET /api/auth/check-permission` - 获取可访问资源
- `DELETE /api/auth/check-permission` - 清除权限缓存

## 前端组件

### 权限验证Hook
```typescript
const { hasPermission, checkPermission } = usePermissions();
```

### 权限保护组件
```tsx
// 基础权限保护
<PermissionGuard permission={{ resource: 'products', action: 'read' }}>
  <ProductList />
</PermissionGuard>

// 便捷组件
<Can do="create" on="orders">
  <CreateOrderButton />
</Can>

<HasRole role="admin">
  <AdminPanel />
</HasRole>
```

### 权限验证中间件
自动检查页面级权限，支持路径权限映射和细粒度控制。

## 权限服务

### PermissionService
提供权限检查、缓存管理和资源访问控制功能：

```typescript
// 检查权限
const result = await permissionService.checkPermission({
  userId: 'user-id',
  resource: 'products',
  action: 'create',
  resourceId: 'product-id'
});

// 批量检查权限
const results = await permissionService.checkMultiplePermissions(
  userId, 
  permissions
);
```

### 权限缓存
- 5分钟TTL缓存
- 自动清理过期缓存
- 支持手动清除缓存

## 数据初始化

### 种子数据
运行以下命令初始化系统权限和角色：

```bash
npm run db:seed
```

### 默认管理员账户
- 邮箱: <EMAIL>
- 密码: admin123456
- 角色: super_admin

## 安全特性

### 权限检查层级
1. **中间件层** - 页面级权限检查
2. **API层** - 接口级权限验证
3. **组件层** - UI元素权限控制
4. **服务层** - 业务逻辑权限验证

### 资源级权限
支持基于资源ID的细粒度权限控制：
- 销售人员只能查看自己创建的订单
- 用户只能修改自己的信息
- 商品管理员可以访问所有商品

### 条件权限
支持基于条件的权限控制：
- 时间限制
- 部门限制
- 地理位置限制
- 自定义条件

## 使用示例

### 1. 创建新角色
```typescript
const newRole = await fetch('/api/roles', {
  method: 'POST',
  body: JSON.stringify({
    name: 'custom_role',
    displayName: '自定义角色',
    description: '自定义角色描述',
    permissionIds: ['perm-1', 'perm-2']
  })
});
```

### 2. 分配用户角色
```typescript
const assignRole = await fetch('/api/users/user-id/roles', {
  method: 'POST',
  body: JSON.stringify({
    roleIds: ['role-1', 'role-2'],
    expiresAt: '2024-12-31T23:59:59Z'
  })
});
```

### 3. 前端权限检查
```tsx
function ProductActions({ productId }) {
  const { hasPermission } = usePermissions();
  
  return (
    <div>
      {hasPermission('products', 'update') && (
        <EditButton productId={productId} />
      )}
      <Can do="delete" on="products">
        <DeleteButton productId={productId} />
      </Can>
    </div>
  );
}
```

## 测试

### 单元测试
```bash
npm run test:rbac
```

### 权限测试用例
- 角色创建和权限分配
- 用户角色分配和移除
- 权限验证逻辑
- 缓存机制测试
- 批量操作测试

## 性能优化

### 缓存策略
- 用户权限缓存（5分钟TTL）
- 角色权限缓存
- 权限检查结果缓存

### 数据库优化
- 权限查询索引优化
- 批量操作支持
- 分页查询优化

## 扩展性

### 自定义权限
支持动态创建和管理自定义权限，满足业务特定需求。

### 插件化架构
权限系统采用插件化设计，支持扩展新的权限验证逻辑。

### 多租户支持
预留多租户架构支持，可扩展为SaaS模式。

## 监控和审计

### 权限审计日志
记录所有权限相关操作：
- 角色创建/修改/删除
- 权限分配/移除
- 用户角色变更
- 权限检查失败记录

### 性能监控
- 权限检查响应时间
- 缓存命中率
- API调用频率

## 故障排除

### 常见问题
1. **权限检查失败** - 检查用户角色和权限配置
2. **缓存不一致** - 清除权限缓存重新加载
3. **性能问题** - 检查权限查询索引和缓存配置

### 调试工具
- 权限检查日志
- 缓存状态查看
- 权限树可视化

## 最佳实践

### 权限设计原则
1. **最小权限原则** - 用户只获得必需的最小权限
2. **职责分离** - 不同角色承担不同职责
3. **权限继承** - 合理使用角色继承减少配置复杂度

### 安全建议
1. 定期审查用户权限
2. 及时回收离职员工权限
3. 监控异常权限使用
4. 定期更新权限配置

## 更新日志

### v1.0.0 (2024-01-15)
- 完整的RBAC权限管理系统
- 前端权限保护组件
- API权限验证中间件
- 权限缓存机制
- 数据库种子初始化
- 单元测试覆盖

---

更多详细信息请参考源代码注释和API文档。
