# IDP集成用户指南

## 概述

本指南将帮助您配置和使用CBEC ERP系统的外部身份提供商(IDP)集成功能。通过IDP集成，您可以让用户使用现有的企业账户（如Google Workspace、Microsoft Azure AD等）登录系统，实现单点登录(SSO)。

## 快速开始

### 1. 访问IDP管理页面

1. 登录CBEC ERP系统
2. 导航到 **系统设置** > **身份提供商管理**
3. 您将看到IDP配置列表和管理界面

### 2. 添加第一个IDP配置

1. 点击 **添加IDP** 按钮
2. 选择您要集成的IDP类型
3. 填写必要的配置信息
4. 测试连接确保配置正确
5. 启用配置

## 支持的IDP类型详细配置

### Google Workspace集成

**适用场景**: 公司使用Google Workspace作为企业邮箱和办公套件

**配置步骤**:

1. **在Google Cloud Console中创建OAuth应用**:
   - 访问 [Google Cloud Console](https://console.cloud.google.com/)
   - 创建新项目或选择现有项目
   - 启用Google+ API
   - 创建OAuth 2.0客户端ID
   - 设置授权重定向URI: `https://your-domain.com/api/auth/idp/google-workspace-1/callback`

2. **在CBEC ERP中配置**:
   - IDP类型: Google Workspace
   - 协议: OIDC
   - 客户端ID: 从Google Cloud Console获取
   - 客户端密钥: 从Google Cloud Console获取
   - 托管域名: 您的公司域名（如 company.com）
   - 作用域: `openid email profile`

3. **高级配置**:
   - 启用组织架构同步（需要Directory API权限）
   - 配置管理员邮箱用于API访问
   - 上传服务账号密钥文件

**测试步骤**:
1. 点击 **测试连接** 按钮
2. 系统将验证各个端点的可访问性
3. 确认测试结果显示 "连接测试成功"

### Microsoft Azure AD集成

**适用场景**: 公司使用Microsoft 365或Azure AD作为身份管理

**OIDC配置步骤**:

1. **在Azure Portal中注册应用**:
   - 访问 [Azure Portal](https://portal.azure.com/)
   - 导航到 Azure Active Directory > 应用注册
   - 创建新的应用注册
   - 配置重定向URI: `https://your-domain.com/api/auth/idp/azure-ad-1/callback`
   - 生成客户端密钥

2. **在CBEC ERP中配置**:
   - IDP类型: Microsoft Azure AD
   - 协议: OIDC
   - 租户ID: 从Azure Portal获取
   - 客户端ID: 应用程序ID
   - 客户端密钥: 生成的密钥值
   - 启用Graph API: 推荐启用以获取更多用户信息

**SAML配置步骤**:

1. **在Azure Portal中配置企业应用**:
   - 创建新的企业应用程序
   - 选择 "非库应用程序"
   - 配置单点登录为SAML
   - 设置标识符和回复URL
   - 下载证书

2. **在CBEC ERP中配置**:
   - IDP类型: Microsoft Azure AD
   - 协议: SAML 2.0
   - 实体ID: 从Azure获取
   - SSO服务URL: 从Azure获取
   - X.509证书: 粘贴下载的证书内容

### 通用OIDC集成

**适用场景**: 使用支持OpenID Connect标准的任意身份提供商

**配置步骤**:

1. **获取IDP信息**:
   - 发现端点URL（通常是 `/.well-known/openid_configuration`）
   - 或手动获取各个端点URL
   - 客户端凭据

2. **在CBEC ERP中配置**:
   - IDP类型: 通用OIDC
   - 协议: OIDC
   - 发现端点URL: 如果支持自动发现
   - 或手动填写各个端点URL
   - 配置用户属性映射

**用户属性映射示例**:
```json
{
  "id": "user_id",
  "email": "email_address", 
  "firstName": "given_name",
  "lastName": "family_name",
  "groups": "user_groups"
}
```

### LDAP集成

**适用场景**: 公司使用传统的LDAP目录服务

**配置步骤**:

1. **准备LDAP信息**:
   - LDAP服务器地址和端口
   - 绑定DN和密码
   - 用户搜索基础DN
   - 组搜索基础DN（可选）

2. **在CBEC ERP中配置**:
   - IDP类型: LDAP
   - 协议: LDAP
   - 服务器URL: `ldap://ldap.company.com:389`
   - 绑定DN: `cn=admin,dc=company,dc=com`
   - 用户搜索基础DN: `ou=users,dc=company,dc=com`
   - 用户搜索过滤器: `(uid={username})`

## 用户映射和同步

### 属性映射配置

配置外部IDP用户属性到本地系统属性的映射:

1. 进入IDP配置的 **用户映射** 标签
2. 配置属性映射规则:
   - 本地属性: 系统中的用户字段
   - 外部属性: IDP返回的属性名
   - 是否必需: 该属性是否为必填
   - 默认值: 当外部属性不存在时使用的默认值

### 角色映射配置

配置外部角色/组到本地角色的映射:

1. 添加角色映射规则
2. 设置映射优先级（数字越小优先级越高）
3. 配置映射条件（可选）

**示例配置**:
- 外部角色: `admin` → 本地角色: `ADMIN` (优先级: 1)
- 外部角色: `user` → 本地角色: `USER` (优先级: 2)
- 外部组: `developers` → 本地角色: `DEVELOPER` (优先级: 3)

### 同步策略配置

配置用户同步的行为:

- **创建新用户**: 是否允许为外部用户创建本地账户
- **更新现有用户**: 是否更新现有用户的信息
- **同步用户属性**: 是否同步用户基本信息
- **同步用户角色**: 是否同步用户角色
- **冲突解决策略**: 当本地和外部信息冲突时的处理方式

## 安全最佳实践

### 1. 网络安全
- 确保所有IDP通信使用HTTPS
- 配置防火墙规则限制访问
- 使用VPN或专线连接内部LDAP服务器

### 2. 凭据管理
- 定期轮换客户端密钥
- 使用强密码策略
- 启用多因素认证（如果IDP支持）

### 3. 访问控制
- 配置适当的作用域权限
- 限制用户域名（如Google Workspace的托管域名）
- 实施最小权限原则

### 4. 监控和审计
- 启用审计日志记录
- 监控异常登录活动
- 设置安全告警规则

## 故障排除

### 常见问题

**1. 连接测试失败**
- 检查网络连接和防火墙设置
- 验证端点URL是否正确
- 确认客户端凭据有效

**2. 认证失败**
- 检查重定向URI配置
- 验证作用域权限
- 确认用户属于指定域名

**3. 用户同步失败**
- 检查属性映射配置
- 验证必需属性是否存在
- 确认角色映射规则正确

**4. 权限不足**
- 检查IDP中的应用权限
- 验证服务账号权限（如Google Directory API）
- 确认用户具有相应的角色

### 调试步骤

1. **查看审计日志**:
   - 导航到 **系统设置** > **审计日志**
   - 筛选IDP相关事件
   - 查看详细错误信息

2. **测试连接**:
   - 使用内置的连接测试功能
   - 检查各个端点的响应
   - 验证证书和签名

3. **手动测试**:
   - 使用浏览器访问IDP端点
   - 检查返回的用户信息格式
   - 验证属性名称和值

## 维护和更新

### 定期维护任务

1. **证书更新**: 定期检查和更新SAML证书
2. **密钥轮换**: 定期更换客户端密钥
3. **配置审查**: 定期审查IDP配置和权限
4. **性能监控**: 监控认证性能和成功率

### 升级注意事项

1. **备份配置**: 升级前备份所有IDP配置
2. **测试环境**: 在测试环境中验证升级
3. **用户通知**: 提前通知用户可能的服务中断
4. **回滚计划**: 准备回滚方案以防出现问题

## 支持和帮助

如果您在配置或使用IDP集成时遇到问题，请：

1. 查阅本文档和API文档
2. 检查系统审计日志
3. 联系系统管理员
4. 提交技术支持请求

**技术支持信息**:
- 邮箱: <EMAIL>
- 文档: https://docs.cbec-erp.com
- 社区论坛: https://community.cbec-erp.com
