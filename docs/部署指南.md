# CBEC ERP系统部署指南

## 概述

本指南详细说明了如何在生产环境中部署CBEC ERP系统，包括环境准备、依赖安装、配置设置和部署步骤。

## 系统要求

### 硬件要求

**最低配置**:
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 50GB SSD
- 网络: 100Mbps

**推荐配置**:
- CPU: 4核心或更多
- 内存: 8GB RAM或更多
- 存储: 100GB SSD或更多
- 网络: 1Gbps

### 软件要求

- **操作系统**: Ubuntu 20.04 LTS / CentOS 8 / RHEL 8
- **Node.js**: 18.x 或更高版本
- **数据库**: PostgreSQL 14+ 或 MySQL 8.0+
- **缓存**: Redis 6.0+
- **Web服务器**: Nginx 1.18+
- **SSL证书**: Let's Encrypt 或商业证书

## 环境准备

### 1. 更新系统

```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2. 安装Node.js

```bash
# 使用NodeSource仓库安装Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 验证安装
node --version
npm --version
```

### 3. 安装PostgreSQL

```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib -y

# 启动并启用PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE cbec_erp;
CREATE USER cbec_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE cbec_erp TO cbec_user;
\q
```

### 4. 安装Redis

```bash
# Ubuntu/Debian
sudo apt install redis-server -y

# 启动并启用Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server

# 配置Redis (可选)
sudo nano /etc/redis/redis.conf
# 设置密码: requirepass your_redis_password
sudo systemctl restart redis-server
```

### 5. 安装Nginx

```bash
# Ubuntu/Debian
sudo apt install nginx -y

# 启动并启用Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

## 应用部署

### 1. 创建部署用户

```bash
# 创建专用用户
sudo adduser cbec-erp
sudo usermod -aG sudo cbec-erp

# 切换到部署用户
sudo su - cbec-erp
```

### 2. 克隆代码仓库

```bash
# 克隆项目代码
git clone https://github.com/your-org/cbec-erp.git
cd cbec-erp

# 切换到生产分支
git checkout production
```

### 3. 安装依赖

```bash
# 安装项目依赖
npm ci --only=production

# 安装PM2进程管理器
npm install -g pm2
```

### 4. 环境配置

```bash
# 复制环境配置文件
cp .env.example .env.production

# 编辑生产环境配置
nano .env.production
```

**生产环境配置示例**:

```env
# 应用配置
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://erp.yourdomain.com
NEXT_PUBLIC_API_URL=https://erp.yourdomain.com/api

# 数据库配置
DATABASE_URL="postgresql://cbec_user:your_secure_password@localhost:5432/cbec_erp"

# JWT配置
JWT_SECRET=your-super-secure-jwt-secret-key-here
NEXTAUTH_SECRET=your-nextauth-secret-key-here
NEXTAUTH_URL=https://erp.yourdomain.com

# 会话配置
SESSION_SECRET=your-session-secret-key
SESSION_TIMEOUT=3600

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# 缓存配置
CACHE_TTL=3600
CACHE_PREFIX=cbec_erp_prod

# 安全配置
CORS_ORIGIN=https://erp.yourdomain.com
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=1000

# 加密密钥
ENCRYPTION_KEY=your-32-character-encryption-key-here

# 邮件配置
SMTP_HOST=smtp.yourdomain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_smtp_password
SMTP_FROM=CBEC ERP <<EMAIL>>

# 文件存储配置
UPLOAD_DIR=/var/www/cbec-erp/uploads
MAX_FILE_SIZE=10485760

# 日志配置
LOG_LEVEL=info
LOG_FILE=/var/log/cbec-erp/app.log

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
```

### 5. 数据库迁移

```bash
# 生成Prisma客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate deploy

# 初始化种子数据
npm run db:seed
```

### 6. 构建应用

```bash
# 构建生产版本
npm run build

# 验证构建结果
ls -la .next/
```

### 7. 配置PM2

创建PM2配置文件：

```bash
nano ecosystem.config.js
```

```javascript
module.exports = {
  apps: [
    {
      name: 'cbec-erp',
      script: 'npm',
      args: 'start',
      cwd: '/home/<USER>/cbec-erp',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_file: '.env.production',
      log_file: '/var/log/cbec-erp/combined.log',
      out_file: '/var/log/cbec-erp/out.log',
      error_file: '/var/log/cbec-erp/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      watch: false,
      ignore_watch: ['node_modules', '.next', 'logs'],
      restart_delay: 5000,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
```

### 8. 启动应用

```bash
# 创建日志目录
sudo mkdir -p /var/log/cbec-erp
sudo chown cbec-erp:cbec-erp /var/log/cbec-erp

# 启动应用
pm2 start ecosystem.config.js

# 保存PM2配置
pm2 save

# 设置开机自启
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u cbec-erp --hp /home/<USER>
```

## Nginx配置

### 1. 创建Nginx配置

```bash
sudo nano /etc/nginx/sites-available/cbec-erp
```

```nginx
server {
    listen 80;
    server_name erp.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name erp.yourdomain.com;

    # SSL配置
    ssl_certificate /etc/letsencrypt/live/erp.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/erp.yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # 日志配置
    access_log /var/log/nginx/cbec-erp.access.log;
    error_log /var/log/nginx/cbec-erp.error.log;

    # 客户端配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 静态文件缓存
    location /_next/static/ {
        alias /home/<USER>/cbec-erp/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    location /uploads/ {
        alias /var/www/cbec-erp/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }

    # API代理
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 主应用代理
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### 2. 启用站点

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/cbec-erp /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重新加载Nginx
sudo systemctl reload nginx
```

## SSL证书配置

### 使用Let's Encrypt

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取SSL证书
sudo certbot --nginx -d erp.yourdomain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 监控和日志

### 1. 系统监控

```bash
# 安装系统监控工具
sudo apt install htop iotop nethogs -y

# 监控应用状态
pm2 monit

# 查看应用日志
pm2 logs cbec-erp
```

### 2. 日志轮转

```bash
sudo nano /etc/logrotate.d/cbec-erp
```

```
/var/log/cbec-erp/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 cbec-erp cbec-erp
    postrotate
        pm2 reloadLogs
    endscript
}
```

### 3. 数据库备份

```bash
# 创建备份脚本
nano /home/<USER>/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/cbec-erp"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="cbec_erp"
DB_USER="cbec_user"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
pg_dump -U $DB_USER -h localhost $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# 删除7天前的备份
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/db_backup_$DATE.sql.gz"
```

```bash
# 设置执行权限
chmod +x /home/<USER>/backup.sh

# 添加到定时任务
crontab -e
# 添加以下行（每天凌晨2点备份）：
# 0 2 * * * /home/<USER>/backup.sh
```

## 安全配置

### 1. 防火墙设置

```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 查看状态
sudo ufw status
```

### 2. 系统安全

```bash
# 禁用root登录
sudo nano /etc/ssh/sshd_config
# 设置: PermitRootLogin no

# 重启SSH服务
sudo systemctl restart ssh

# 安装fail2ban
sudo apt install fail2ban -y
sudo systemctl enable fail2ban
```

## 性能优化

### 1. 数据库优化

```sql
-- PostgreSQL配置优化
-- 编辑 /etc/postgresql/14/main/postgresql.conf

shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
```

### 2. Redis优化

```bash
# 编辑Redis配置
sudo nano /etc/redis/redis.conf

# 设置最大内存
maxmemory 512mb
maxmemory-policy allkeys-lru

# 启用持久化
save 900 1
save 300 10
save 60 10000
```

## 故障排除

### 常见问题

1. **应用无法启动**
   ```bash
   # 检查日志
   pm2 logs cbec-erp
   
   # 检查端口占用
   sudo netstat -tlnp | grep :3000
   ```

2. **数据库连接失败**
   ```bash
   # 检查PostgreSQL状态
   sudo systemctl status postgresql
   
   # 测试连接
   psql -U cbec_user -h localhost -d cbec_erp
   ```

3. **Nginx配置错误**
   ```bash
   # 测试配置
   sudo nginx -t
   
   # 查看错误日志
   sudo tail -f /var/log/nginx/error.log
   ```

### 性能问题诊断

```bash
# 查看系统资源使用
htop
iotop
free -h
df -h

# 查看应用性能
pm2 monit

# 查看数据库性能
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

## 更新部署

### 1. 应用更新

```bash
# 拉取最新代码
git pull origin production

# 安装新依赖
npm ci --only=production

# 运行数据库迁移
npx prisma migrate deploy

# 重新构建
npm run build

# 重启应用
pm2 restart cbec-erp
```

### 2. 零停机部署

```bash
# 使用PM2的reload功能
pm2 reload cbec-erp
```

## 支持

如有部署问题，请联系：

- 技术支持: <EMAIL>
- 文档: https://docs.cbec-erp.com
- 社区: https://community.cbec-erp.com
