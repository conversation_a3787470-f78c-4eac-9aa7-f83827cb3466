# CBEC ERP系统 API文档

## 概述

本文档描述了CBEC ERP系统的RESTful API接口，包括认证、权限管理、商品管理、订单管理、客户管理和库存管理等功能模块。

## 基础信息

- **基础URL**: `https://api.cbec-erp.com/api`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时区**: UTC

## 认证方式

### JWT Token认证

所有需要认证的API都需要在请求头中包含JWT Token：

```http
Authorization: Bearer <your-jwt-token>
```

### 获取Token

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "firstName": "张",
      "lastName": "三",
      "roles": ["admin"]
    }
  }
}
```

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "data": {
    // 响应数据
  }
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": {} // 可选的详细信息
  }
}
```

### 分页响应

```json
{
  "success": true,
  "data": {
    "data": [], // 数据列表
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "totalPages": 5,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## 错误代码

| 代码 | HTTP状态码 | 描述 |
|------|------------|------|
| UNAUTHORIZED | 401 | 未认证或Token无效 |
| FORBIDDEN | 403 | 权限不足 |
| NOT_FOUND | 404 | 资源不存在 |
| VALIDATION_ERROR | 400 | 请求参数验证失败 |
| INTERNAL_ERROR | 500 | 服务器内部错误 |
| RATE_LIMIT_EXCEEDED | 429 | 请求频率超限 |

## 权限管理API

### 检查权限

检查用户是否有特定权限。

```http
POST /auth/check-permission
Authorization: Bearer <token>
Content-Type: application/json

{
  "resource": "products",
  "action": "read",
  "resourceId": "product-123" // 可选
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "allowed": true,
    "reason": null
  }
}
```

### 批量检查权限

```http
PUT /auth/check-permission
Authorization: Bearer <token>
Content-Type: application/json

{
  "permissions": [
    {"resource": "products", "action": "read"},
    {"resource": "orders", "action": "create"}
  ]
}
```

### 获取可访问资源

```http
GET /auth/check-permission?action=read
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "resources": ["products", "customers", "orders"]
  }
}
```

## 商品管理API

### 获取商品列表

```http
GET /products?page=1&limit=20&search=关键词&category=分类ID&status=ACTIVE
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20, 最大: 100)
- `search`: 搜索关键词
- `category`: 分类ID
- `status`: 商品状态 (ACTIVE, INACTIVE, DRAFT)
- `sort`: 排序字段 (name, price, createdAt)
- `order`: 排序方向 (asc, desc)

### 获取商品详情

```http
GET /products/{productId}
Authorization: Bearer <token>
```

### 创建商品

```http
POST /products
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "商品名称",
  "description": "商品描述",
  "sku": "PROD-001",
  "basePrice": 99.99,
  "costPrice": 50.00,
  "categoryId": "category-123",
  "status": "ACTIVE",
  "images": [
    {
      "url": "https://example.com/image1.jpg",
      "alt": "商品图片1",
      "isPrimary": true
    }
  ],
  "variants": [
    {
      "name": "红色-大号",
      "sku": "PROD-001-RED-L",
      "price": 109.99,
      "attributes": {
        "color": "红色",
        "size": "大号"
      }
    }
  ],
  "seoTitle": "SEO标题",
  "seoDescription": "SEO描述",
  "tags": ["标签1", "标签2"]
}
```

### 更新商品

```http
PUT /products/{productId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "更新的商品名称",
  "basePrice": 119.99,
  "status": "ACTIVE"
}
```

### 删除商品

```http
DELETE /products/{productId}
Authorization: Bearer <token>
```

### 批量操作

```http
PATCH /products
Authorization: Bearer <token>
Content-Type: application/json

{
  "productIds": ["product-1", "product-2"],
  "action": "update_status",
  "data": {
    "status": "INACTIVE"
  }
}
```

## 订单管理API

### 获取订单列表

```http
GET /orders?page=1&limit=20&status=PENDING&paymentStatus=PAID
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `status`: 订单状态 (PENDING, PROCESSING, SHIPPED, DELIVERED, CANCELLED)
- `paymentStatus`: 支付状态 (PENDING, PAID, FAILED, REFUNDED)
- `customerId`: 客户ID
- `search`: 搜索订单号或客户信息
- `dateFrom`: 开始日期 (YYYY-MM-DD)
- `dateTo`: 结束日期 (YYYY-MM-DD)

### 获取订单详情

```http
GET /orders/{orderId}
Authorization: Bearer <token>
```

### 创建订单

```http
POST /orders
Authorization: Bearer <token>
Content-Type: application/json

{
  "customerId": "customer-123", // 可选，如果不提供则使用customerInfo
  "customerInfo": { // 可选，如果customerId不存在
    "email": "<EMAIL>",
    "firstName": "张",
    "lastName": "三",
    "phone": "+86 138 0013 8000"
  },
  "items": [
    {
      "productId": "product-123",
      "variantId": "variant-456", // 可选
      "quantity": 2,
      "unitPrice": 99.99 // 可选，默认使用商品价格
    }
  ],
  "shippingAddress": {
    "firstName": "张",
    "lastName": "三",
    "addressLine1": "北京市朝阳区xxx街道xxx号",
    "city": "北京",
    "postalCode": "100000",
    "countryCode": "CN",
    "phone": "+86 138 0013 8000"
  },
  "billingAddress": { // 可选，默认使用shippingAddress
    // 同shippingAddress格式
  },
  "notes": "订单备注",
  "couponCode": "DISCOUNT10" // 可选
}
```

### 更新订单状态

```http
PUT /orders/{orderId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "PROCESSING",
  "paymentStatus": "PAID",
  "trackingNumber": "SF1234567890",
  "notes": "订单已确认，准备发货",
  "internalNotes": "内部备注"
}
```

### 订单操作

#### 处理订单
```http
POST /orders/{orderId}/process
Authorization: Bearer <token>
```

#### 发货
```http
POST /orders/{orderId}/ship
Authorization: Bearer <token>
Content-Type: application/json

{
  "trackingNumber": "SF1234567890",
  "carrier": "顺丰速运",
  "notes": "已发货"
}
```

#### 取消订单
```http
POST /orders/{orderId}/cancel
Authorization: Bearer <token>
Content-Type: application/json

{
  "reason": "客户要求取消",
  "refundAmount": 99.99 // 可选
}
```

#### 退款
```http
POST /orders/{orderId}/refund
Authorization: Bearer <token>
Content-Type: application/json

{
  "amount": 99.99,
  "reason": "商品质量问题",
  "refundMethod": "original" // original, manual
}
```

## 客户管理API

### 获取客户列表

```http
GET /customers?page=1&limit=20&search=关键词&customerGroup=VIP
Authorization: Bearer <token>
```

### 获取客户详情

```http
GET /customers/{customerId}
Authorization: Bearer <token>
```

### 创建客户

```http
POST /customers
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "firstName": "张",
  "lastName": "三",
  "phone": "+86 138 0013 8000",
  "dateOfBirth": "1990-01-01",
  "gender": "MALE",
  "customerGroup": "REGULAR",
  "marketingConsent": true,
  "addresses": [
    {
      "type": "SHIPPING",
      "firstName": "张",
      "lastName": "三",
      "addressLine1": "北京市朝阳区xxx街道xxx号",
      "city": "北京",
      "postalCode": "100000",
      "countryCode": "CN",
      "isDefault": true
    }
  ],
  "tags": ["VIP客户", "老客户"],
  "notes": "重要客户"
}
```

### 更新客户

```http
PUT /customers/{customerId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "李",
  "customerGroup": "VIP",
  "tags": ["VIP客户", "高价值客户"]
}
```

## 库存管理API

### 获取库存列表

```http
GET /inventory?page=1&limit=20&warehouseId=warehouse-123&lowStock=true
Authorization: Bearer <token>
```

### 调整库存

```http
POST /inventory/adjust
Authorization: Bearer <token>
Content-Type: application/json

{
  "productId": "product-123",
  "variantId": "variant-456", // 可选
  "warehouseId": "warehouse-123",
  "type": "INCREASE", // INCREASE, DECREASE, SET, DAMAGE, etc.
  "reason": "MANUAL_ADJUSTMENT",
  "quantity": 10,
  "unitCost": 50.00, // 可选
  "notes": "手动调整库存"
}
```

### 库存转移

```http
POST /inventory/transfer
Authorization: Bearer <token>
Content-Type: application/json

{
  "productId": "product-123",
  "variantId": "variant-456", // 可选
  "fromWarehouseId": "warehouse-123",
  "toWarehouseId": "warehouse-456",
  "quantity": 5,
  "notes": "仓库间转移",
  "expectedDate": "2024-02-01T00:00:00Z" // 可选
}
```

### 库存盘点

```http
POST /inventory/count
Authorization: Bearer <token>
Content-Type: application/json

{
  "warehouseId": "warehouse-123",
  "countDate": "2024-01-15T00:00:00Z",
  "notes": "月度盘点",
  "items": [
    {
      "productId": "product-123",
      "variantId": "variant-456", // 可选
      "expectedQuantity": 100,
      "actualQuantity": 98,
      "notes": "发现2件损坏"
    }
  ]
}
```

## 用户管理API

### 获取用户列表

```http
GET /users?page=1&limit=20&role=admin&status=active
Authorization: Bearer <token>
```

### 获取用户详情

```http
GET /users/{userId}
Authorization: Bearer <token>
```

### 创建用户

```http
POST /users
Authorization: Bearer <token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "新",
  "lastName": "用户",
  "phone": "+86 138 0013 8000",
  "roleIds": ["role-123", "role-456"]
}
```

### 更新用户

```http
PUT /users/{userId}
Authorization: Bearer <token>
Content-Type: application/json

{
  "firstName": "更新的",
  "lastName": "姓名",
  "isActive": true
}
```

### 管理用户角色

#### 分配角色
```http
POST /users/{userId}/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "roleIds": ["role-123", "role-456"],
  "expiresAt": "2024-12-31T23:59:59Z" // 可选
}
```

#### 移除角色
```http
DELETE /users/{userId}/roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "roleIds": ["role-123"]
}
```

## 角色和权限管理API

### 获取角色列表

```http
GET /roles
Authorization: Bearer <token>
```

### 创建角色

```http
POST /roles
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "custom_role",
  "displayName": "自定义角色",
  "description": "角色描述",
  "permissionIds": ["perm-1", "perm-2"]
}
```

### 获取权限列表

```http
GET /permissions
Authorization: Bearer <token>
```

## 速率限制

API实施速率限制以防止滥用：

- **认证用户**: 每分钟1000次请求
- **未认证用户**: 每分钟100次请求

超出限制时返回HTTP 429状态码。

## 版本控制

API使用URL路径进行版本控制：

- 当前版本: `/api/v1/`
- 未来版本: `/api/v2/`

## 支持

如有API使用问题，请联系：

- 邮箱: <EMAIL>
- 文档: https://docs.cbec-erp.com
- 技术支持: https://support.cbec-erp.com
