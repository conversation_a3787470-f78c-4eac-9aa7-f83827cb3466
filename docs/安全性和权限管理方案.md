# 跨境电商ERP系统安全性和权限管理方案

## 文档概述

本文档详细描述了跨境电商ERP系统的安全架构和权限管理方案，包括身份认证、授权机制、数据安全、网络安全等多层次安全防护措施，确保系统和数据的安全性。

## 1. 安全架构概览

### 1.1 多层安全防护模型

```mermaid
graph TB
    A[用户接入层] --> B[网络安全层]
    B --> C[应用安全层]
    C --> D[业务逻辑层]
    D --> E[数据访问层]
    E --> F[数据存储层]
    
    subgraph "安全控制点"
        G[WAF防火墙]
        H[API网关]
        I[身份认证]
        J[权限控制]
        K[数据加密]
        L[审计日志]
    end
    
    A -.-> G
    B -.-> H
    C -.-> I
    D -.-> J
    E -.-> K
    F -.-> L
```

### 1.2 安全原则

**1.2.1 零信任安全模型**
- **永不信任，始终验证：** 对所有访问请求进行验证
- **最小权限原则：** 用户只获得完成工作所需的最小权限
- **持续监控：** 实时监控所有安全事件和异常行为
- **动态访问控制：** 基于上下文的动态权限调整

**1.2.2 纵深防御策略**
- **多层防护：** 在不同层次部署安全控制措施
- **冗余保护：** 关键安全功能具备备份机制
- **故障安全：** 系统故障时默认拒绝访问
- **安全隔离：** 不同安全域之间进行有效隔离

## 2. 身份认证系统

### 2.1 多因素认证 (MFA)

**2.1.1 认证因子类型**
```typescript
enum AuthFactorType {
  KNOWLEDGE = 'knowledge',    // 知识因子：密码、PIN
  POSSESSION = 'possession',  // 持有因子：手机、硬件令牌
  INHERENCE = 'inherence',   // 生物因子：指纹、面部识别
  LOCATION = 'location',     // 位置因子：IP地址、地理位置
  BEHAVIOR = 'behavior'      // 行为因子：输入模式、使用习惯
}

interface AuthFactor {
  type: AuthFactorType;
  value: string;
  verified: boolean;
  createdAt: Date;
  lastUsedAt?: Date;
  metadata?: any;
}

interface MFAConfig {
  required: boolean;
  minimumFactors: number;
  allowedFactors: AuthFactorType[];
  gracePeriod: number; // 宽限期（小时）
  backupCodes: boolean;
}
```

**2.1.2 MFA实现流程**
```typescript
class MFAService {
  // 发起MFA验证
  async initiateMFA(userId: string, primaryFactor: AuthFactor): Promise<MFAChallenge> {
    // 验证主要因子（通常是密码）
    const primaryValid = await this.verifyPrimaryFactor(userId, primaryFactor);
    if (!primaryValid) {
      throw new Error('主要认证因子验证失败');
    }
    
    // 获取用户的MFA配置
    const mfaConfig = await this.getUserMFAConfig(userId);
    if (!mfaConfig.required) {
      return { success: true, requiresSecondFactor: false };
    }
    
    // 生成第二因子挑战
    const challenge = await this.generateSecondFactorChallenge(userId, mfaConfig);
    
    return {
      success: false,
      requiresSecondFactor: true,
      challengeId: challenge.id,
      availableFactors: challenge.availableFactors,
      expiresAt: challenge.expiresAt
    };
  }
  
  // 验证第二因子
  async verifySecondFactor(challengeId: string, factor: AuthFactor): Promise<AuthResult> {
    const challenge = await this.getChallenge(challengeId);
    if (!challenge || challenge.expiresAt < new Date()) {
      throw new Error('验证挑战已过期');
    }
    
    const isValid = await this.validateSecondFactor(challenge.userId, factor);
    if (!isValid) {
      await this.recordFailedAttempt(challenge.userId, factor.type);
      throw new Error('第二因子验证失败');
    }
    
    // 生成访问令牌
    const tokens = await this.generateTokens(challenge.userId);
    await this.recordSuccessfulLogin(challenge.userId);
    
    return {
      success: true,
      user: await this.getUser(challenge.userId),
      tokens
    };
  }
}
```

### 2.2 单点登录 (SSO)

**2.2.1 SAML 2.0集成**
```typescript
interface SAMLConfig {
  entityId: string;
  ssoUrl: string;
  sloUrl: string;
  certificate: string;
  nameIdFormat: string;
  attributeMapping: {
    email: string;
    firstName: string;
    lastName: string;
    roles: string;
  };
}

class SAMLService {
  async handleSAMLResponse(samlResponse: string): Promise<AuthResult> {
    // 验证SAML响应签名
    const isValid = await this.validateSAMLSignature(samlResponse);
    if (!isValid) {
      throw new Error('SAML响应签名验证失败');
    }
    
    // 解析用户属性
    const userAttributes = await this.parseSAMLAttributes(samlResponse);
    
    // 创建或更新用户
    const user = await this.createOrUpdateUser(userAttributes);
    
    // 生成内部访问令牌
    const tokens = await this.generateTokens(user.id);
    
    return { success: true, user, tokens };
  }
}
```

**2.2.2 OAuth 2.0 / OpenID Connect**
```typescript
interface OAuthConfig {
  clientId: string;
  clientSecret: string;
  authorizationUrl: string;
  tokenUrl: string;
  userInfoUrl: string;
  scopes: string[];
  redirectUri: string;
}

class OAuthService {
  // 生成授权URL
  generateAuthUrl(state: string): string {
    const params = new URLSearchParams({
      response_type: 'code',
      client_id: this.config.clientId,
      redirect_uri: this.config.redirectUri,
      scope: this.config.scopes.join(' '),
      state: state
    });
    
    return `${this.config.authorizationUrl}?${params.toString()}`;
  }
  
  // 处理授权回调
  async handleCallback(code: string, state: string): Promise<AuthResult> {
    // 验证state参数
    const isValidState = await this.validateState(state);
    if (!isValidState) {
      throw new Error('无效的state参数');
    }
    
    // 交换访问令牌
    const tokenResponse = await this.exchangeCodeForToken(code);
    
    // 获取用户信息
    const userInfo = await this.getUserInfo(tokenResponse.access_token);
    
    // 创建或更新用户
    const user = await this.createOrUpdateUser(userInfo);
    
    // 生成内部访问令牌
    const tokens = await this.generateTokens(user.id);
    
    return { success: true, user, tokens };
  }
}
```

## 3. 权限控制系统

### 3.1 基于角色的访问控制 (RBAC)

**3.1.1 权限模型设计**
```typescript
interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  conditions?: PermissionCondition[];
  description: string;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description: string;
  permissions: Permission[];
  isSystemRole: boolean;
  parentRoles?: Role[];
}

interface PermissionCondition {
  type: 'owner' | 'department' | 'location' | 'time' | 'custom';
  operator: 'equals' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: any;
  field?: string;
}

// 权限检查示例
interface PermissionContext {
  userId: string;
  resource: string;
  action: string;
  resourceId?: string;
  metadata?: any;
}
```

**3.1.2 动态权限评估**
```typescript
class PermissionService {
  async checkPermission(context: PermissionContext): Promise<boolean> {
    // 获取用户角色和权限
    const userRoles = await this.getUserRoles(context.userId);
    const permissions = await this.getRolePermissions(userRoles);
    
    // 查找匹配的权限
    const matchingPermissions = permissions.filter(p => 
      p.resource === context.resource && p.action === context.action
    );
    
    if (matchingPermissions.length === 0) {
      return false;
    }
    
    // 评估权限条件
    for (const permission of matchingPermissions) {
      const conditionsMet = await this.evaluateConditions(
        permission.conditions || [],
        context
      );
      
      if (conditionsMet) {
        return true;
      }
    }
    
    return false;
  }
  
  private async evaluateConditions(
    conditions: PermissionCondition[],
    context: PermissionContext
  ): Promise<boolean> {
    for (const condition of conditions) {
      const result = await this.evaluateCondition(condition, context);
      if (!result) {
        return false;
      }
    }
    return true;
  }
  
  private async evaluateCondition(
    condition: PermissionCondition,
    context: PermissionContext
  ): Promise<boolean> {
    switch (condition.type) {
      case 'owner':
        return await this.checkOwnership(context.userId, context.resourceId);
      
      case 'department':
        return await this.checkDepartmentAccess(context.userId, condition.value);
      
      case 'time':
        return this.checkTimeConstraint(condition);
      
      case 'location':
        return await this.checkLocationConstraint(context.userId, condition.value);
      
      default:
        return true;
    }
  }
}
```

### 3.2 属性基础访问控制 (ABAC)

**3.2.1 属性定义**
```typescript
interface Attribute {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  value: any;
  source: 'user' | 'resource' | 'environment' | 'action';
}

interface PolicyRule {
  id: string;
  name: string;
  description: string;
  effect: 'allow' | 'deny';
  condition: PolicyCondition;
  priority: number;
}

interface PolicyCondition {
  operator: 'and' | 'or' | 'not';
  operands: (PolicyCondition | AttributeCondition)[];
}

interface AttributeCondition {
  attribute: string;
  operator: 'equals' | 'not_equals' | 'in' | 'not_in' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}
```

**3.2.2 策略评估引擎**
```typescript
class PolicyEngine {
  async evaluateAccess(
    subject: Attribute[],
    resource: Attribute[],
    action: Attribute[],
    environment: Attribute[]
  ): Promise<AccessDecision> {
    const allAttributes = [...subject, ...resource, ...action, ...environment];
    const applicablePolicies = await this.getApplicablePolicies(allAttributes);
    
    // 按优先级排序
    applicablePolicies.sort((a, b) => b.priority - a.priority);
    
    for (const policy of applicablePolicies) {
      const result = await this.evaluatePolicy(policy, allAttributes);
      if (result.applicable) {
        return {
          decision: policy.effect,
          policy: policy.id,
          reason: result.reason
        };
      }
    }
    
    // 默认拒绝
    return {
      decision: 'deny',
      reason: 'No applicable policy found'
    };
  }
  
  private async evaluatePolicy(
    policy: PolicyRule,
    attributes: Attribute[]
  ): Promise<{ applicable: boolean; reason?: string }> {
    return this.evaluateCondition(policy.condition, attributes);
  }
  
  private async evaluateCondition(
    condition: PolicyCondition | AttributeCondition,
    attributes: Attribute[]
  ): Promise<{ applicable: boolean; reason?: string }> {
    if ('operator' in condition && ['and', 'or', 'not'].includes(condition.operator)) {
      // 处理逻辑操作符
      const policyCondition = condition as PolicyCondition;
      return this.evaluateLogicalCondition(policyCondition, attributes);
    } else {
      // 处理属性条件
      const attrCondition = condition as AttributeCondition;
      return this.evaluateAttributeCondition(attrCondition, attributes);
    }
  }
}
```

## 4. 数据安全

### 4.1 数据加密

**4.1.1 传输加密**
```typescript
// TLS配置
const tlsConfig = {
  minVersion: 'TLSv1.2',
  maxVersion: 'TLSv1.3',
  cipherSuites: [
    'TLS_AES_256_GCM_SHA384',
    'TLS_CHACHA20_POLY1305_SHA256',
    'TLS_AES_128_GCM_SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-GCM-SHA256'
  ],
  certificateTransparency: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
};

// API请求加密
class SecureAPIClient {
  async makeRequest(endpoint: string, data: any): Promise<any> {
    // 生成请求签名
    const timestamp = Date.now();
    const nonce = this.generateNonce();
    const signature = await this.signRequest(endpoint, data, timestamp, nonce);
    
    const headers = {
      'X-Timestamp': timestamp.toString(),
      'X-Nonce': nonce,
      'X-Signature': signature,
      'Content-Type': 'application/json'
    };
    
    // 加密请求体
    const encryptedData = await this.encryptData(data);
    
    const response = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify({ data: encryptedData })
    });
    
    // 解密响应
    const responseData = await response.json();
    return this.decryptData(responseData.data);
  }
}
```

**4.1.2 存储加密**
```typescript
// 字段级加密
class FieldEncryption {
  private encryptionKey: string;
  
  async encryptField(value: string, fieldType: string): Promise<string> {
    const algorithm = this.getAlgorithmForField(fieldType);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(algorithm, this.encryptionKey);
    
    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return `${algorithm}:${iv.toString('hex')}:${encrypted}`;
  }
  
  async decryptField(encryptedValue: string): Promise<string> {
    const [algorithm, ivHex, encrypted] = encryptedValue.split(':');
    const iv = Buffer.from(ivHex, 'hex');
    const decipher = crypto.createDecipher(algorithm, this.encryptionKey);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
  
  private getAlgorithmForField(fieldType: string): string {
    const algorithms = {
      'pii': 'aes-256-gcm',      // 个人身份信息
      'financial': 'aes-256-gcm', // 财务数据
      'sensitive': 'aes-256-cbc', // 敏感信息
      'default': 'aes-128-cbc'    // 默认
    };
    
    return algorithms[fieldType] || algorithms.default;
  }
}

// 数据库透明加密
const databaseConfig = {
  encryption: {
    enabled: true,
    algorithm: 'AES-256',
    keyRotation: {
      enabled: true,
      interval: '90d'
    },
    encryptedTables: [
      'users',
      'customers',
      'payments',
      'customer_addresses'
    ],
    encryptedColumns: {
      users: ['email', 'phone', 'ssn'],
      customers: ['email', 'phone', 'address'],
      payments: ['card_number', 'account_number']
    }
  }
};
```

### 4.2 数据脱敏

**4.2.1 动态数据脱敏**
```typescript
interface MaskingRule {
  field: string;
  maskingType: 'partial' | 'full' | 'hash' | 'tokenize';
  pattern?: string;
  preserveLength?: boolean;
  roles?: string[];
}

class DataMaskingService {
  private maskingRules: MaskingRule[] = [
    {
      field: 'email',
      maskingType: 'partial',
      pattern: '***@***.***',
      roles: ['customer_service']
    },
    {
      field: 'phone',
      maskingType: 'partial',
      pattern: '***-***-####',
      roles: ['operator']
    },
    {
      field: 'creditCard',
      maskingType: 'partial',
      pattern: '****-****-****-####',
      roles: ['finance_viewer']
    },
    {
      field: 'ssn',
      maskingType: 'full',
      roles: ['admin']
    }
  ];
  
  async maskData(data: any, userRoles: string[]): Promise<any> {
    const maskedData = { ...data };
    
    for (const rule of this.maskingRules) {
      if (rule.roles && !this.hasRequiredRole(userRoles, rule.roles)) {
        if (maskedData[rule.field]) {
          maskedData[rule.field] = this.applyMasking(
            maskedData[rule.field],
            rule
          );
        }
      }
    }
    
    return maskedData;
  }
  
  private applyMasking(value: string, rule: MaskingRule): string {
    switch (rule.maskingType) {
      case 'full':
        return '*'.repeat(rule.preserveLength ? value.length : 8);
      
      case 'partial':
        return this.applyPartialMasking(value, rule.pattern);
      
      case 'hash':
        return this.hashValue(value);
      
      case 'tokenize':
        return this.tokenizeValue(value);
      
      default:
        return value;
    }
  }
}
```

## 5. 安全监控和审计

### 5.1 安全事件监控

**5.1.1 实时威胁检测**
```typescript
interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  details: any;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
}

enum SecurityEventType {
  FAILED_LOGIN = 'failed_login',
  SUSPICIOUS_LOGIN = 'suspicious_login',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  DATA_EXFILTRATION = 'data_exfiltration',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  BRUTE_FORCE_ATTACK = 'brute_force_attack',
  SQL_INJECTION = 'sql_injection',
  XSS_ATTEMPT = 'xss_attempt'
}

class SecurityMonitor {
  async detectAnomalousLogin(loginAttempt: LoginAttempt): Promise<SecurityEvent[]> {
    const events: SecurityEvent[] = [];
    
    // 检测异常登录位置
    const userLocationHistory = await this.getUserLocationHistory(loginAttempt.userId);
    if (this.isAnomalousLocation(loginAttempt.location, userLocationHistory)) {
      events.push({
        id: generateId(),
        type: SecurityEventType.SUSPICIOUS_LOGIN,
        severity: 'medium',
        userId: loginAttempt.userId,
        ipAddress: loginAttempt.ipAddress,
        userAgent: loginAttempt.userAgent,
        timestamp: new Date(),
        details: {
          reason: 'Unusual login location',
          location: loginAttempt.location,
          previousLocations: userLocationHistory
        },
        status: 'open'
      });
    }
    
    // 检测异常登录时间
    const userLoginPattern = await this.getUserLoginPattern(loginAttempt.userId);
    if (this.isAnomalousTime(loginAttempt.timestamp, userLoginPattern)) {
      events.push({
        id: generateId(),
        type: SecurityEventType.SUSPICIOUS_LOGIN,
        severity: 'low',
        userId: loginAttempt.userId,
        ipAddress: loginAttempt.ipAddress,
        userAgent: loginAttempt.userAgent,
        timestamp: new Date(),
        details: {
          reason: 'Unusual login time',
          loginTime: loginAttempt.timestamp,
          typicalPattern: userLoginPattern
        },
        status: 'open'
      });
    }
    
    return events;
  }
  
  async detectBruteForceAttack(ipAddress: string): Promise<SecurityEvent | null> {
    const recentFailures = await this.getRecentFailedLogins(ipAddress, 15); // 15分钟内
    
    if (recentFailures.length >= 5) {
      return {
        id: generateId(),
        type: SecurityEventType.BRUTE_FORCE_ATTACK,
        severity: 'high',
        ipAddress: ipAddress,
        userAgent: recentFailures[0].userAgent,
        timestamp: new Date(),
        details: {
          failedAttempts: recentFailures.length,
          timeWindow: '15 minutes',
          targetUsers: [...new Set(recentFailures.map(f => f.userId))]
        },
        status: 'open'
      };
    }
    
    return null;
  }
}
```

### 5.2 审计日志系统

**5.2.1 审计日志记录**
```typescript
interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  metadata?: any;
}

class AuditLogger {
  async logAction(auditData: Omit<AuditLog, 'id' | 'timestamp'>): Promise<void> {
    const auditLog: AuditLog = {
      id: generateId(),
      timestamp: new Date(),
      ...auditData
    };
    
    // 异步写入审计日志
    await this.writeAuditLog(auditLog);
    
    // 检查是否需要实时告警
    await this.checkForAlerts(auditLog);
  }
  
  async logDataAccess(
    userId: string,
    resource: string,
    resourceId: string,
    action: 'read' | 'write' | 'delete',
    context: any
  ): Promise<void> {
    await this.logAction({
      userId,
      action: `data_${action}`,
      resource,
      resourceId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      success: true,
      metadata: {
        dataClassification: await this.getDataClassification(resource),
        accessReason: context.reason
      }
    });
  }
  
  async logPrivilegeChange(
    adminUserId: string,
    targetUserId: string,
    oldRoles: string[],
    newRoles: string[],
    context: any
  ): Promise<void> {
    await this.logAction({
      userId: adminUserId,
      action: 'privilege_change',
      resource: 'user',
      resourceId: targetUserId,
      oldValues: { roles: oldRoles },
      newValues: { roles: newRoles },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      success: true,
      metadata: {
        addedRoles: newRoles.filter(r => !oldRoles.includes(r)),
        removedRoles: oldRoles.filter(r => !newRoles.includes(r))
      }
    });
  }
}
```

这个安全性和权限管理方案为跨境电商ERP系统提供了全面的安全防护框架，确保系统和数据的安全性。
