{"name": "cbec-erp", "version": "1.0.0", "description": "跨境电商ERP管理平台 - 支持全球化网店运营的企业级ERP系统", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "docker:build": "docker build -t cbec-erp .", "docker:run": "docker run -p 3000:3000 cbec-erp", "prepare": "husky install"}, "dependencies": {"@next/font": "^14.0.4", "@prisma/client": "^5.7.1", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.14.2", "@tanstack/react-table": "^8.11.2", "@tensorflow/tfjs": "^4.22.0", "@tensorflow/tfjs-node": "^4.22.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^3.0.6", "framer-motion": "^10.16.16", "jose": "^5.1.3", "lucide-react": "^0.303.0", "ml-matrix": "^6.12.1", "next": "^14.0.4", "next-auth": "^4.24.5", "next-intl": "^3.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "stripe": "^14.9.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@hookform/resolvers": "^3.3.2", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-config-next": "^14.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "prisma": "^5.7.1", "tailwindcss": "^3.3.6", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,css}": ["prettier --write"]}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["erp", "ecommerce", "cross-border", "inventory-management", "order-management", "nextjs", "typescript", "prisma", "tailwindcss"], "author": {"name": "CBEC ERP Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cbec-erp/cbec-erp.git"}, "bugs": {"url": "https://github.com/cbec-erp/cbec-erp/issues"}, "homepage": "https://cbec-erp.com"}