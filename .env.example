# 跨境电商ERP系统环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# =============================================================================
# 应用基础配置
# =============================================================================

# 应用环境 (development, staging, production)
NODE_ENV=development

# 应用URL
NEXT_PUBLIC_APP_URL=http://localhost:3000

# API基础URL
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# 应用密钥 (用于JWT签名等)
NEXTAUTH_SECRET=your-super-secret-key-change-this-in-production

# NextAuth URL
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# 数据库配置
# =============================================================================

# PostgreSQL数据库连接字符串
DATABASE_URL="postgresql://username:password@localhost:5432/cbec_erp?schema=public"

# 数据库连接池配置
DATABASE_POOL_SIZE=10
DATABASE_TIMEOUT=30000

# Redis缓存配置
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# =============================================================================
# 认证配置
# =============================================================================

# JWT配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 会话配置
SESSION_SECRET=your-session-secret
SESSION_TIMEOUT=3600

# OAuth配置 - Google
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# OAuth配置 - GitHub
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# =============================================================================
# 支付网关配置
# =============================================================================

# Stripe配置
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret

# PayPal配置
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_ENVIRONMENT=sandbox

# =============================================================================
# 物流服务配置
# =============================================================================

# DHL配置
DHL_API_KEY=your-dhl-api-key
DHL_API_SECRET=your-dhl-api-secret
DHL_ACCOUNT_NUMBER=your-dhl-account-number
DHL_BASE_URL=https://api-test.dhl.com

# FedEx配置
FEDEX_API_KEY=your-fedex-api-key
FEDEX_SECRET_KEY=your-fedex-secret-key
FEDEX_ACCOUNT_NUMBER=your-fedex-account-number
FEDEX_BASE_URL=https://apis-sandbox.fedex.com

# UPS配置
UPS_ACCESS_KEY=your-ups-access-key
UPS_USERNAME=your-ups-username
UPS_PASSWORD=your-ups-password
UPS_BASE_URL=https://onlinetools.ups.com

# =============================================================================
# 电商平台集成配置
# =============================================================================

# Amazon配置
AMAZON_ACCESS_KEY=your-amazon-access-key
AMAZON_SECRET_KEY=your-amazon-secret-key
AMAZON_SELLER_ID=your-amazon-seller-id
AMAZON_MARKETPLACE_ID=your-amazon-marketplace-id
AMAZON_BASE_URL=https://sellingpartnerapi-na.amazon.com

# eBay配置
EBAY_APP_ID=your-ebay-app-id
EBAY_DEV_ID=your-ebay-dev-id
EBAY_CERT_ID=your-ebay-cert-id
EBAY_TOKEN=your-ebay-token
EBAY_BASE_URL=https://api.sandbox.ebay.com

# Shopify配置
SHOPIFY_API_KEY=your-shopify-api-key
SHOPIFY_API_SECRET=your-shopify-api-secret
SHOPIFY_ACCESS_TOKEN=your-shopify-access-token
SHOPIFY_SHOP_DOMAIN=your-shop.myshopify.com

# =============================================================================
# 文件存储配置
# =============================================================================

# AWS S3配置
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-west-2
AWS_S3_BUCKET=cbec-erp-files

# 阿里云OSS配置 (可选)
ALIYUN_ACCESS_KEY_ID=your-aliyun-access-key-id
ALIYUN_ACCESS_KEY_SECRET=your-aliyun-access-key-secret
ALIYUN_OSS_REGION=oss-cn-hangzhou
ALIYUN_OSS_BUCKET=cbec-erp-files

# =============================================================================
# 邮件服务配置
# =============================================================================

# SMTP配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_FROM=<EMAIL>

# SendGrid配置 (可选)
SENDGRID_API_KEY=your-sendgrid-api-key

# =============================================================================
# 短信服务配置
# =============================================================================

# Twilio配置
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# 阿里云短信配置 (可选)
ALIYUN_SMS_ACCESS_KEY_ID=your-aliyun-sms-access-key-id
ALIYUN_SMS_ACCESS_KEY_SECRET=your-aliyun-sms-access-key-secret

# =============================================================================
# 监控和日志配置
# =============================================================================

# Sentry错误监控
SENTRY_DSN=your-sentry-dsn
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project

# 日志级别 (error, warn, info, debug)
LOG_LEVEL=info

# =============================================================================
# 汇率服务配置
# =============================================================================

# 汇率API配置
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key
EXCHANGE_RATE_BASE_URL=https://api.exchangerate-api.com/v4

# =============================================================================
# 缓存配置
# =============================================================================

# 缓存TTL (秒)
CACHE_TTL=3600

# 缓存前缀
CACHE_PREFIX=cbec_erp

# =============================================================================
# 安全配置
# =============================================================================

# CORS允许的域名
CORS_ORIGIN=http://localhost:3000,https://cbec-erp.com

# API限流配置
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100

# 加密密钥
ENCRYPTION_KEY=your-32-character-encryption-key

# =============================================================================
# 开发工具配置
# =============================================================================

# 是否启用调试模式
DEBUG=false

# 是否启用API文档
ENABLE_API_DOCS=true

# 是否启用数据库调试
DATABASE_DEBUG=false

# =============================================================================
# 第三方服务配置
# =============================================================================

# Google Analytics
NEXT_PUBLIC_GA_TRACKING_ID=GA_TRACKING_ID

# Google Maps API
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your-google-maps-api-key

# 客服系统配置
CUSTOMER_SERVICE_WIDGET_ID=your-customer-service-widget-id

# =============================================================================
# 生产环境特定配置
# =============================================================================

# CDN配置
CDN_URL=https://cdn.cbec-erp.com

# 负载均衡器配置
LOAD_BALANCER_URL=https://lb.cbec-erp.com

# 数据库只读副本
DATABASE_READ_URL="************************************************/cbec_erp"
