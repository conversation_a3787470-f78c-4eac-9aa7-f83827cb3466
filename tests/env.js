/**
 * 测试环境变量配置
 * 为Jest测试设置必要的环境变量
 */

// 设置Node.js环境为测试模式
process.env.NODE_ENV = 'test';

// 数据库配置
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/cbec_erp_test';
process.env.DATABASE_POOL_SIZE = '5';
process.env.DATABASE_TIMEOUT = '5000';

// Redis配置
process.env.REDIS_URL = 'redis://localhost:6379/1';

// 认证配置
process.env.NEXTAUTH_SECRET = 'test-secret-key-for-jwt-signing';
process.env.NEXTAUTH_URL = 'http://localhost:3000';
process.env.JWT_SECRET = 'test-jwt-secret-key';
process.env.JWT_EXPIRES_IN = '1h';

// 应用配置
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000';
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api';

// 禁用遥测
process.env.NEXT_TELEMETRY_DISABLED = '1';

// 日志级别
process.env.LOG_LEVEL = 'error';

// 缓存配置
process.env.CACHE_TTL = '300';
process.env.CACHE_PREFIX = 'test_cbec_erp';

// 限流配置
process.env.RATE_LIMIT_WINDOW = '60000';
process.env.RATE_LIMIT_MAX_REQUESTS = '1000';

// 文件上传配置
process.env.MAX_FILE_SIZE = '10485760'; // 10MB

// 邮件配置（测试用）
process.env.SMTP_HOST = 'localhost';
process.env.SMTP_PORT = '1025';
process.env.SMTP_USER = 'test';
process.env.SMTP_PASSWORD = 'test';
process.env.SMTP_FROM = '<EMAIL>';

// 支付配置（测试用）
process.env.STRIPE_PUBLISHABLE_KEY = 'pk_test_test';
process.env.STRIPE_SECRET_KEY = 'sk_test_test';
process.env.PAYPAL_CLIENT_ID = 'test_paypal_client_id';
process.env.PAYPAL_CLIENT_SECRET = 'test_paypal_client_secret';
process.env.PAYPAL_ENVIRONMENT = 'sandbox';

// AWS配置（测试用）
process.env.AWS_ACCESS_KEY_ID = 'test_access_key';
process.env.AWS_SECRET_ACCESS_KEY = 'test_secret_key';
process.env.AWS_REGION = 'us-east-1';
process.env.AWS_S3_BUCKET = 'test-bucket';

// 第三方API配置（测试用）
process.env.AMAZON_ACCESS_KEY = 'test_amazon_key';
process.env.AMAZON_SECRET_KEY = 'test_amazon_secret';
process.env.EBAY_APP_ID = 'test_ebay_app_id';
process.env.SHOPIFY_API_KEY = 'test_shopify_key';

// 物流配置（测试用）
process.env.DHL_API_KEY = 'test_dhl_key';
process.env.FEDEX_API_KEY = 'test_fedex_key';
process.env.UPS_ACCESS_KEY = 'test_ups_key';

// 汇率API配置（测试用）
process.env.EXCHANGE_RATE_API_KEY = 'test_exchange_rate_key';

// 监控配置
process.env.SENTRY_DSN = '';
process.env.DEBUG = 'false';

// 安全配置
process.env.CORS_ORIGIN = 'http://localhost:3000';
process.env.ENCRYPTION_KEY = 'test-32-character-encryption-key';

console.log('测试环境变量已加载');
