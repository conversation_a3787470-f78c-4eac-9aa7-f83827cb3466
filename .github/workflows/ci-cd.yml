# CBEC ERP CI/CD工作流
# 自动化测试、构建和部署流程

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  lint:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 运行ESLint
        run: npm run lint

      - name: 运行Prettier检查
        run: npm run format:check

      - name: TypeScript类型检查
        run: npm run type-check

  # 单元测试
  test:
    name: 单元测试
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: cbec_erp_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 设置测试环境变量
        run: |
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/cbec_erp_test" >> $GITHUB_ENV
          echo "NEXTAUTH_SECRET=test-secret" >> $GITHUB_ENV
          echo "NEXTAUTH_URL=http://localhost:3000" >> $GITHUB_ENV

      - name: 运行数据库迁移
        run: npx prisma migrate deploy

      - name: 生成Prisma客户端
        run: npx prisma generate

      - name: 运行测试
        run: npm run test:ci

      - name: 上传测试覆盖率
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # 构建应用
  build:
    name: 构建应用
    runs-on: ubuntu-latest
    needs: [lint, test]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: npm ci

      - name: 生成Prisma客户端
        run: npx prisma generate

      - name: 构建应用
        run: npm run build
        env:
          NEXT_TELEMETRY_DISABLED: 1

      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: |
            .next/
            public/
            package.json
            package-lock.json
          retention-days: 1

  # 安全扫描
  security:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: [lint]
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 运行Trivy漏洞扫描
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: 上传Trivy扫描结果
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: 依赖安全审计
        run: npm audit --audit-level moderate

  # Docker镜像构建
  docker:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    needs: [build, security]
    if: github.event_name == 'push'
    permissions:
      contents: read
      packages: write
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 登录容器注册表
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 提取元数据
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: 构建并推送Docker镜像
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/develop'
    environment: development
    steps:
      - name: 部署到开发环境
        run: |
          echo "部署到开发环境..."
          # 这里添加实际的部署脚本
          # 例如：kubectl apply -f k8s/dev/ 或者调用部署API

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [docker]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: 部署到生产环境
        run: |
          echo "部署到生产环境..."
          # 这里添加实际的部署脚本
          # 例如：kubectl apply -f k8s/prod/ 或者调用部署API

  # 通知
  notify:
    name: 通知
    runs-on: ubuntu-latest
    needs: [deploy-dev, deploy-prod]
    if: always()
    steps:
      - name: 发送通知
        run: |
          if [ "${{ needs.deploy-dev.result }}" == "success" ] || [ "${{ needs.deploy-prod.result }}" == "success" ]; then
            echo "部署成功通知"
            # 这里可以添加Slack、邮件或其他通知方式
          elif [ "${{ needs.deploy-dev.result }}" == "failure" ] || [ "${{ needs.deploy-prod.result }}" == "failure" ]; then
            echo "部署失败通知"
            # 这里可以添加失败通知
          fi
