{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 100, "tabWidth": 2, "useTabs": false, "quoteProps": "as-needed", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": false, "proseWrap": "preserve", "requirePragma": false, "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.json", "options": {"printWidth": 80}}, {"files": "*.md", "options": {"printWidth": 80, "proseWrap": "always"}}, {"files": "*.yml", "options": {"tabWidth": 2}}]}