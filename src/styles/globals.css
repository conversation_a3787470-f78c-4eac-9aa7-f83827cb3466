/**
 * 全局样式文件
 * 包含Tailwind CSS基础样式、CSS变量定义和自定义样式
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* =============================================================================
   CSS变量定义 - 支持明暗主题切换
   ============================================================================= */

@layer base {
  :root {
    /* 基础颜色变量 */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* 卡片颜色 */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    /* 弹出层颜色 */
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* 主色调 */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;

    /* 次要色调 */
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    /* 静音色调 */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    /* 强调色调 */
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    /* 破坏性色调 */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* 边框颜色 */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;

    /* 圆角半径 */
    --radius: 0.5rem;
  }

  .dark {
    /* 暗色主题变量 */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

/* =============================================================================
   基础样式重置和设置
   ============================================================================= */

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-secondary;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary/20;
  }

  /* 焦点样式 */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }
}

/* =============================================================================
   组件样式
   ============================================================================= */

@layer components {
  /* 页面容器 */
  .page-container {
    @apply container mx-auto px-4 py-6 max-w-7xl;
  }

  /* 页面标题 */
  .page-title {
    @apply text-3xl font-bold tracking-tight text-foreground mb-6;
  }

  .page-subtitle {
    @apply text-lg text-muted-foreground mb-8;
  }

  /* 表单样式 */
  .form-group {
    @apply space-y-2;
  }

  .form-label {
    @apply text-sm font-medium text-foreground;
  }

  .form-error {
    @apply text-sm text-destructive;
  }

  .form-helper {
    @apply text-sm text-muted-foreground;
  }

  /* 数据表格样式 */
  .data-table {
    @apply w-full border-collapse border border-border rounded-lg overflow-hidden;
  }

  .data-table th {
    @apply bg-muted px-4 py-3 text-left text-sm font-medium text-muted-foreground border-b border-border;
  }

  .data-table td {
    @apply px-4 py-3 text-sm text-foreground border-b border-border;
  }

  .data-table tr:hover {
    @apply bg-muted/50;
  }

  /* 状态徽章 */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-badge.success {
    @apply bg-success-100 text-success-800;
  }

  .status-badge.warning {
    @apply bg-warning-100 text-warning-800;
  }

  .status-badge.error {
    @apply bg-error-100 text-error-800;
  }

  .status-badge.info {
    @apply bg-brand-100 text-brand-800;
  }

  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-muted border-t-primary;
  }

  /* 骨架屏 */
  .skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* 工具提示 */
  .tooltip {
    @apply absolute z-50 px-3 py-1.5 text-sm text-white bg-gray-900 rounded-md shadow-lg;
  }

  /* 面包屑导航 */
  .breadcrumb {
    @apply flex items-center space-x-2 text-sm text-muted-foreground;
  }

  .breadcrumb-item {
    @apply hover:text-foreground transition-colors;
  }

  .breadcrumb-separator {
    @apply text-muted-foreground/50;
  }

  /* 侧边栏 */
  .sidebar {
    @apply fixed left-0 top-0 h-full w-64 bg-card border-r border-border z-40 transform transition-transform duration-300 ease-in-out;
  }

  .sidebar.collapsed {
    @apply -translate-x-full lg:translate-x-0 lg:w-16;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-sm text-muted-foreground hover:text-foreground hover:bg-accent transition-colors;
  }

  .sidebar-item.active {
    @apply text-primary bg-accent;
  }

  /* 顶部导航栏 */
  .navbar {
    @apply fixed top-0 left-0 right-0 h-16 bg-card border-b border-border z-30 flex items-center justify-between px-6;
  }

  /* 主内容区域 */
  .main-content {
    @apply flex-1 pt-16 pl-0 lg:pl-64 transition-all duration-300 ease-in-out;
  }

  .main-content.sidebar-collapsed {
    @apply lg:pl-16;
  }

  /* 卡片网格 */
  .card-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6;
  }

  /* 统计卡片 */
  .stat-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm hover:shadow-md transition-shadow;
  }

  .stat-value {
    @apply text-2xl font-bold text-foreground;
  }

  .stat-label {
    @apply text-sm text-muted-foreground;
  }

  .stat-change {
    @apply text-sm font-medium;
  }

  .stat-change.positive {
    @apply text-success-600;
  }

  .stat-change.negative {
    @apply text-error-600;
  }

  /* 空状态 */
  .empty-state {
    @apply flex flex-col items-center justify-center py-12 text-center;
  }

  .empty-state-icon {
    @apply w-16 h-16 text-muted-foreground mb-4;
  }

  .empty-state-title {
    @apply text-lg font-medium text-foreground mb-2;
  }

  .empty-state-description {
    @apply text-sm text-muted-foreground mb-6 max-w-sm;
  }
}

/* =============================================================================
   工具类样式
   ============================================================================= */

@layer utilities {
  /* 文本截断 */
  .text-truncate {
    @apply truncate;
  }

  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 隐藏滚动条 */
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary)) 100%);
  }

  /* 玻璃效果 */
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 阴影效果 */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-large {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  /* 动画延迟 */
  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }
}

/* =============================================================================
   响应式设计
   ============================================================================= */

@media (max-width: 768px) {
  .sidebar {
    @apply -translate-x-full;
  }

  .main-content {
    @apply pl-0;
  }

  .card-grid {
    @apply grid-cols-1;
  }
}

/* =============================================================================
   打印样式
   ============================================================================= */

@media print {
  .no-print {
    @apply hidden;
  }

  .sidebar,
  .navbar {
    @apply hidden;
  }

  .main-content {
    @apply pl-0 pt-0;
  }

  body {
    @apply text-black bg-white;
  }
}
