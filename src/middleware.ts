/**
 * Next.js中间件配置
 * 处理身份认证、权限验证、国际化、安全防护等
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyToken, type UserSession } from '@/lib/auth';
import { rateLimit } from '@/lib/rate-limit';
import { permissionService } from '@/lib/rbac/permission-service';
import { apiPermissionMiddleware } from '@/lib/rbac/api-permission-middleware';

// 需要认证的路径
const PROTECTED_PATHS = [
  '/dashboard',
  '/products',
  '/orders',
  '/customers',
  '/inventory',
  '/settings',
  '/users',
  '/roles',
  '/permissions',
  '/reports',
  '/audit',
  '/api/protected',
  '/api/roles',
  '/api/permissions',
  '/api/users',
];

// 公开访问的路径
const PUBLIC_PATHS = [
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/api/auth',
  '/api/health',
  '/api/public',
];

// API路径
const API_PATHS = ['/api'];

// 静态资源路径
const STATIC_PATHS = ['/_next', '/favicon.ico', '/images', '/icons'];

/**
 * 检查路径是否需要认证
 */
function requiresAuth(pathname: string): boolean {
  return PROTECTED_PATHS.some(path => pathname.startsWith(path));
}

/**
 * 检查路径是否为公开访问
 */
function isPublicPath(pathname: string): boolean {
  return PUBLIC_PATHS.some(path => pathname.startsWith(path));
}

/**
 * 检查路径是否为API路径
 */
function isApiPath(pathname: string): boolean {
  return API_PATHS.some(path => pathname.startsWith(path));
}

/**
 * 检查路径是否为静态资源
 */
function isStaticPath(pathname: string): boolean {
  return STATIC_PATHS.some(path => pathname.startsWith(path));
}

/**
 * 获取用户的访问令牌
 */
function getAccessToken(request: NextRequest): string | null {
  // 从Authorization头获取
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 从Cookie获取
  const token = request.cookies.get('access_token')?.value;
  return token || null;
}

/**
 * 创建认证失败响应
 */
function createUnauthorizedResponse(request: NextRequest): NextResponse {
  if (isApiPath(request.nextUrl.pathname)) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未授权访问',
        },
      },
      { status: 401 }
    );
  }

  // 重定向到登录页面
  const loginUrl = new URL('/login', request.url);
  loginUrl.searchParams.set('redirect', request.nextUrl.pathname);
  return NextResponse.redirect(loginUrl);
}

/**
 * 创建权限不足响应
 */
function createForbiddenResponse(request: NextRequest): NextResponse {
  if (isApiPath(request.nextUrl.pathname)) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'FORBIDDEN',
          message: '权限不足',
        },
      },
      { status: 403 }
    );
  }

  return NextResponse.redirect(new URL('/unauthorized', request.url));
}

/**
 * 处理CORS
 */
function handleCors(request: NextRequest, response: NextResponse): NextResponse {
  const origin = request.headers.get('origin');
  const allowedOrigins = process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000'];

  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }

  response.headers.set(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, DELETE, OPTIONS'
  );
  response.headers.set(
    'Access-Control-Allow-Headers',
    'Content-Type, Authorization, X-Requested-With'
  );
  response.headers.set('Access-Control-Max-Age', '86400');

  return response;
}

/**
 * 处理安全头
 */
function addSecurityHeaders(response: NextResponse): NextResponse {
  // 内容安全策略
  response.headers.set(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:;"
  );

  // 防止点击劫持
  response.headers.set('X-Frame-Options', 'DENY');

  // 防止MIME类型嗅探
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // XSS保护
  response.headers.set('X-XSS-Protection', '1; mode=block');

  // 引用策略
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // 权限策略
  response.headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=()'
  );

  // HSTS (仅在HTTPS环境下)
  if (process.env.NODE_ENV === 'production') {
    response.headers.set(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains; preload'
    );
  }

  return response;
}

/**
 * 处理API限流
 */
async function handleRateLimit(request: NextRequest): Promise<NextResponse | null> {
  if (!isApiPath(request.nextUrl.pathname)) {
    return null;
  }

  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const isAllowed = await rateLimit(ip);

  if (!isAllowed) {
    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: '请求过于频繁，请稍后再试',
        },
      },
      { status: 429 }
    );
  }

  return null;
}

/**
 * 主中间件函数
 */
export async function middleware(request: NextRequest): Promise<NextResponse> {
  const { pathname } = request.nextUrl;

  // 跳过静态资源
  if (isStaticPath(pathname)) {
    return NextResponse.next();
  }

  // 处理OPTIONS请求（CORS预检）
  if (request.method === 'OPTIONS') {
    const response = new NextResponse(null, { status: 200 });
    return handleCors(request, response);
  }

  // 处理API限流
  const rateLimitResponse = await handleRateLimit(request);
  if (rateLimitResponse) {
    return handleCors(request, rateLimitResponse);
  }

  // API权限验证
  if (pathname.startsWith('/api/')) {
    const apiPermissionResponse = await apiPermissionMiddleware(request, pathname);
    if (apiPermissionResponse) {
      return handleCors(request, apiPermissionResponse);
    }
  }

  // 如果是公开路径，直接通过
  if (isPublicPath(pathname)) {
    const response = NextResponse.next();
    return addSecurityHeaders(handleCors(request, response));
  }

  // 检查是否需要认证
  if (requiresAuth(pathname)) {
    const token = getAccessToken(request);

    if (!token) {
      return createUnauthorizedResponse(request);
    }

    // 验证令牌
    const user = await verifyToken(token);
    if (!user) {
      return createUnauthorizedResponse(request);
    }

    // 检查用户状态
    if (!user || !user.id) {
      return createUnauthorizedResponse(request);
    }

    // 添加用户信息到请求头（供API路由使用）
    const requestHeaders = new Headers(request.headers);
    requestHeaders.set('x-user-id', user.id);
    requestHeaders.set('x-user-email', user.email);
    requestHeaders.set('x-user-roles', JSON.stringify(user.roles));
    requestHeaders.set('x-user-permissions', JSON.stringify(user.permissions));

    // 权限检查（使用RBAC权限服务进行细粒度的权限控制）
    const hasAccess = await checkPathPermissionWithRBAC(pathname, user);
    if (!hasAccess) {
      return createForbiddenResponse(request);
    }

    const response = NextResponse.next({
      request: {
        headers: requestHeaders,
      },
    });

    return addSecurityHeaders(handleCors(request, response));
  }

  // 默认处理
  const response = NextResponse.next();
  return addSecurityHeaders(handleCors(request, response));
}

/**
 * 使用RBAC权限服务检查路径权限
 */
async function checkPathPermissionWithRBAC(pathname: string, user: UserSession): Promise<boolean> {
  try {
    // 路径权限映射
    const pathPermissions: Record<string, { resource: string; action: string }> = {
      '/dashboard': { resource: 'dashboard', action: 'read' },
      '/products': { resource: 'products', action: 'read' },
      '/orders': { resource: 'orders', action: 'read' },
      '/customers': { resource: 'customers', action: 'read' },
      '/inventory': { resource: 'inventory', action: 'read' },
      '/users': { resource: 'users', action: 'read' },
      '/roles': { resource: 'roles', action: 'read' },
      '/permissions': { resource: 'permissions', action: 'read' },
      '/settings': { resource: 'settings', action: 'read' },
      '/reports': { resource: 'reports', action: 'read' },
      '/audit': { resource: 'audit', action: 'read' },
      '/api/products': { resource: 'products', action: 'read' },
      '/api/orders': { resource: 'orders', action: 'read' },
      '/api/customers': { resource: 'customers', action: 'read' },
      '/api/inventory': { resource: 'inventory', action: 'read' },
      '/api/users': { resource: 'users', action: 'read' },
      '/api/roles': { resource: 'roles', action: 'read' },
      '/api/permissions': { resource: 'permissions', action: 'read' },
      '/api/settings': { resource: 'settings', action: 'read' },
      '/api/reports': { resource: 'reports', action: 'read' },
      '/api/audit': { resource: 'audit', action: 'read' },
    };

    // 检查具体路径权限
    for (const [path, permission] of Object.entries(pathPermissions)) {
      if (pathname.startsWith(path)) {
        const permissionCheck = await permissionService.checkPermission({
          userId: user.id,
          resource: permission.resource,
          action: permission.action,
        });
        return permissionCheck.allowed;
      }
    }

    // 对于未明确定义权限的路径，检查是否为管理员
    return user.roles.includes('super_admin') || user.roles.includes('admin');
  } catch (error) {
    console.error('权限检查错误:', error);
    // 权限检查失败时，只允许超级管理员访问
    return user.roles.includes('super_admin');
  }
}

// 配置中间件匹配路径
export const config = {
  matcher: [
    /*
     * 匹配所有路径除了:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
