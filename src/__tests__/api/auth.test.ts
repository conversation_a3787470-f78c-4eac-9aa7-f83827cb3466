/**
 * 用户认证API测试
 * 测试用户注册、登录等认证相关功能
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST as registerHandler } from '@/app/api/auth/register/route';
import { POST as loginHandler } from '@/app/api/auth/login/route';

// 模拟数据库
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// 模拟bcrypt
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashedPassword'),
  compare: jest.fn().mockResolvedValue(true),
}));

// 模拟JWT
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('mockToken'),
}));

describe('用户认证API测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/register - 用户注册', () => {
    it('应该成功注册新用户', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟用户不存在
      prisma.user.findUnique.mockResolvedValue(null);
      
      // 模拟创建用户成功
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'USER',
        isActive: true,
        emailVerified: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      prisma.user.create.mockResolvedValue(mockUser);

      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.user.email).toBe('<EMAIL>');
      expect(data.data.user.firstName).toBe('Test');
      expect(data.data.user.lastName).toBe('User');
    });

    it('应该拒绝已存在的邮箱', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟用户已存在
      prisma.user.findUnique.mockResolvedValue({
        id: 'existing-user',
        email: '<EMAIL>',
      });

      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('EMAIL_EXISTS');
    });

    it('应该验证密码确认', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'differentPassword',
        firstName: 'Test',
        lastName: 'User',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });

    it('应该验证邮箱格式', async () => {
      const requestBody = {
        email: 'invalid-email',
        password: 'password123',
        confirmPassword: 'password123',
        firstName: 'Test',
        lastName: 'User',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });

    it('应该验证必填字段', async () => {
      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        // 缺少firstName
      };

      const request = new NextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/auth/login - 用户登录', () => {
    it('应该成功登录有效用户', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟找到用户
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        password: 'hashedPassword',
        firstName: 'Test',
        lastName: 'User',
        role: 'USER',
        isActive: true,
        emailVerified: true,
      };
      prisma.user.findUnique.mockResolvedValue(mockUser);

      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.user.email).toBe('<EMAIL>');
      expect(data.data.token).toBe('mockToken');
    });

    it('应该拒绝不存在的用户', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟用户不存在
      prisma.user.findUnique.mockResolvedValue(null);

      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_CREDENTIALS');
    });

    it('应该拒绝错误密码', async () => {
      const { prisma } = require('@/lib/prisma');
      const bcrypt = require('bcryptjs');
      
      // 模拟找到用户
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        password: 'hashedPassword',
        isActive: true,
        emailVerified: true,
      };
      prisma.user.findUnique.mockResolvedValue(mockUser);
      
      // 模拟密码不匹配
      bcrypt.compare.mockResolvedValue(false);

      const requestBody = {
        email: '<EMAIL>',
        password: 'wrongPassword',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('INVALID_CREDENTIALS');
    });

    it('应该拒绝未激活的用户', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟找到未激活用户
      const mockUser = {
        id: 'user-1',
        email: '<EMAIL>',
        password: 'hashedPassword',
        isActive: false,
        emailVerified: true,
      };
      prisma.user.findUnique.mockResolvedValue(mockUser);

      const requestBody = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const request = new NextRequest('http://localhost:3000/api/auth/login', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('ACCOUNT_DISABLED');
    });
  });
});
