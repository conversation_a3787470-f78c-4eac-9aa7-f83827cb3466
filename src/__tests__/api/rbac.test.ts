/**
 * RBAC权限管理系统测试
 * 测试角色、权限、用户角色分配等功能
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET as getRolesHandler, POST as createRoleHandler } from '@/app/api/roles/route';
import { GET as getPermissionsHandler } from '@/app/api/permissions/route';
import { permissionService } from '@/lib/rbac/permission-service';

// 模拟数据库
jest.mock('@/lib/prisma', () => ({
  prisma: {
    role: {
      count: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      upsert: jest.fn(),
    },
    permission: {
      count: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    rolePermission: {
      createMany: jest.fn(),
      deleteMany: jest.fn(),
      upsert: jest.fn(),
    },
    userRole: {
      findMany: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
      upsert: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
      findMany: jest.fn(),
    },
    $transaction: jest.fn(),
  },
}));

describe('RBAC权限管理系统测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('角色管理API测试', () => {
    describe('GET /api/roles - 获取角色列表', () => {
      it('应该返回分页的角色列表', async () => {
        const { prisma } = require('@/lib/prisma');
        
        // 模拟角色数据
        const mockRoles = [
          {
            id: 'role-1',
            name: 'admin',
            displayName: '管理员',
            description: '系统管理员角色',
            isSystemRole: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            rolePermissions: [],
            userRoles: [],
          },
          {
            id: 'role-2',
            name: 'user',
            displayName: '普通用户',
            description: '普通用户角色',
            isSystemRole: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            rolePermissions: [],
            userRoles: [],
          },
        ];

        prisma.role.count.mockResolvedValue(2);
        prisma.role.findMany.mockResolvedValue(mockRoles);

        const request = new NextRequest('http://localhost:3000/api/roles?page=1&limit=10');
        const response = await getRolesHandler(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.items).toHaveLength(2);
        expect(data.data.pagination.total).toBe(2);
        expect(data.data.items[0].name).toBe('admin');
      });

      it('应该支持搜索功能', async () => {
        const { prisma } = require('@/lib/prisma');
        
        const mockRoles = [
          {
            id: 'role-1',
            name: 'admin',
            displayName: '管理员',
            description: '系统管理员角色',
            isSystemRole: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            rolePermissions: [],
            userRoles: [],
          },
        ];

        prisma.role.count.mockResolvedValue(1);
        prisma.role.findMany.mockResolvedValue(mockRoles);

        const request = new NextRequest('http://localhost:3000/api/roles?search=管理员');
        const response = await getRolesHandler(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.items).toHaveLength(1);
        expect(prisma.role.findMany).toHaveBeenCalledWith(
          expect.objectContaining({
            where: expect.objectContaining({
              OR: expect.arrayContaining([
                { name: { contains: '管理员', mode: 'insensitive' } },
                { displayName: { contains: '管理员', mode: 'insensitive' } },
                { description: { contains: '管理员', mode: 'insensitive' } },
              ]),
            }),
          })
        );
      });
    });

    describe('POST /api/roles - 创建角色', () => {
      it('应该成功创建新角色', async () => {
        const { prisma } = require('@/lib/prisma');
        
        const newRoleData = {
          name: 'test_role',
          displayName: '测试角色',
          description: '这是一个测试角色',
          permissionIds: ['perm-1', 'perm-2'],
        };

        const mockCreatedRole = {
          id: 'role-new',
          name: 'test_role',
          displayName: '测试角色',
          description: '这是一个测试角色',
          isSystemRole: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          rolePermissions: [
            {
              permission: {
                id: 'perm-1',
                name: 'users.read',
                resource: 'users',
                action: 'read',
                description: '查看用户',
              },
            },
            {
              permission: {
                id: 'perm-2',
                name: 'users.create',
                resource: 'users',
                action: 'create',
                description: '创建用户',
              },
            },
          ],
        };

        prisma.role.findUnique.mockResolvedValue(null); // 角色不存在
        prisma.permission.findMany.mockResolvedValue([
          { id: 'perm-1' },
          { id: 'perm-2' },
        ]);
        prisma.$transaction.mockImplementation(async (callback) => {
          return await callback({
            role: {
              create: jest.fn().mockResolvedValue({
                id: 'role-new',
                name: 'test_role',
                displayName: '测试角色',
                description: '这是一个测试角色',
                isSystemRole: false,
              }),
            },
            rolePermission: {
              createMany: jest.fn(),
            },
            role: {
              findUnique: jest.fn().mockResolvedValue(mockCreatedRole),
            },
          });
        });

        const request = new NextRequest('http://localhost:3000/api/roles', {
          method: 'POST',
          body: JSON.stringify(newRoleData),
          headers: { 'Content-Type': 'application/json' },
        });

        const response = await createRoleHandler(request);
        const data = await response.json();

        expect(response.status).toBe(201);
        expect(data.success).toBe(true);
        expect(data.data.name).toBe('test_role');
        expect(data.data.displayName).toBe('测试角色');
      });

      it('应该拒绝创建重复的角色名称', async () => {
        const { prisma } = require('@/lib/prisma');
        
        const newRoleData = {
          name: 'existing_role',
          displayName: '已存在角色',
          description: '这个角色已经存在',
          permissionIds: [],
        };

        prisma.role.findUnique.mockResolvedValue({
          id: 'existing-role-id',
          name: 'existing_role',
        });

        const request = new NextRequest('http://localhost:3000/api/roles', {
          method: 'POST',
          body: JSON.stringify(newRoleData),
          headers: { 'Content-Type': 'application/json' },
        });

        const response = await createRoleHandler(request);
        const data = await response.json();

        expect(response.status).toBe(409);
        expect(data.success).toBe(false);
        expect(data.error.code).toBe('ROLE_EXISTS');
      });
    });
  });

  describe('权限管理API测试', () => {
    describe('GET /api/permissions - 获取权限列表', () => {
      it('应该返回分页的权限列表', async () => {
        const { prisma } = require('@/lib/prisma');
        
        const mockPermissions = [
          {
            id: 'perm-1',
            name: 'users.read',
            resource: 'users',
            action: 'read',
            description: '查看用户',
            createdAt: new Date(),
          },
          {
            id: 'perm-2',
            name: 'users.create',
            resource: 'users',
            action: 'create',
            description: '创建用户',
            createdAt: new Date(),
          },
        ];

        prisma.permission.count.mockResolvedValue(2);
        prisma.permission.findMany.mockResolvedValue(mockPermissions);

        const request = new NextRequest('http://localhost:3000/api/permissions?page=1&limit=10');
        const response = await getPermissionsHandler(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(data.data.items).toHaveLength(2);
        expect(data.data.pagination.total).toBe(2);
      });

      it('应该支持按资源分组', async () => {
        const { prisma } = require('@/lib/prisma');
        
        const mockPermissions = [
          {
            id: 'perm-1',
            name: 'users.read',
            resource: 'users',
            action: 'read',
            description: '查看用户',
            createdAt: new Date(),
          },
          {
            id: 'perm-2',
            name: 'products.read',
            resource: 'products',
            action: 'read',
            description: '查看商品',
            createdAt: new Date(),
          },
        ];

        prisma.permission.findMany.mockResolvedValue(mockPermissions);

        const request = new NextRequest('http://localhost:3000/api/permissions?grouped=true');
        const response = await getPermissionsHandler(request);
        const data = await response.json();

        expect(response.status).toBe(200);
        expect(data.success).toBe(true);
        expect(Array.isArray(data.data)).toBe(true);
        expect(data.data).toHaveLength(2); // 两个资源组
        expect(data.data[0]).toHaveProperty('resource');
        expect(data.data[0]).toHaveProperty('permissions');
      });
    });
  });

  describe('权限验证服务测试', () => {
    it('应该正确检查用户权限', async () => {
      const { prisma } = require('@/lib/prisma');
      
      const mockUser = {
        id: 'user-1',
        userRoles: [
          {
            role: {
              name: 'admin',
              rolePermissions: [
                {
                  permission: {
                    name: 'users.read',
                  },
                },
              ],
            },
          },
        ],
      };

      prisma.user.findUnique.mockResolvedValue(mockUser);

      const result = await permissionService.checkPermission({
        userId: 'user-1',
        resource: 'users',
        action: 'read',
      });

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝没有权限的用户', async () => {
      const { prisma } = require('@/lib/prisma');
      
      const mockUser = {
        id: 'user-1',
        userRoles: [
          {
            role: {
              name: 'viewer',
              rolePermissions: [],
            },
          },
        ],
      };

      prisma.user.findUnique.mockResolvedValue(mockUser);

      const result = await permissionService.checkPermission({
        userId: 'user-1',
        resource: 'users',
        action: 'delete',
      });

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('Missing permission');
    });

    it('应该允许超级管理员访问所有资源', async () => {
      const { prisma } = require('@/lib/prisma');
      
      const mockUser = {
        id: 'user-1',
        userRoles: [
          {
            role: {
              name: 'super_admin',
              rolePermissions: [],
            },
          },
        ],
      };

      prisma.user.findUnique.mockResolvedValue(mockUser);

      const result = await permissionService.checkPermission({
        userId: 'user-1',
        resource: 'any_resource',
        action: 'any_action',
      });

      expect(result.allowed).toBe(true);
      expect(result.reason).toBe('Super admin access');
    });
  });
});
