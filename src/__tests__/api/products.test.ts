/**
 * 商品管理API测试
 * 测试商品CRUD操作
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET as getProductsHandler, POST as createProductHandler } from '@/app/api/products/route';

// 模拟数据库
jest.mock('@/lib/prisma', () => ({
  prisma: {
    product: {
      count: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  },
}));

describe('商品管理API测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/products - 获取商品列表', () => {
    it('应该返回分页的商品列表', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟商品数据
      const mockProducts = [
        {
          id: 'product-1',
          sku: 'SKU001',
          name: '测试商品1',
          description: '这是一个测试商品',
          basePrice: 99.99,
          currency: 'CNY',
          status: 'ACTIVE',
          isDigital: false,
          requiresShipping: true,
          trackInventory: true,
          allowBackorder: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          category: {
            id: 'cat-1',
            name: '电子产品',
            slug: 'electronics',
          },
          images: [],
          variants: [],
          inventoryItems: [],
        },
      ];

      prisma.product.count.mockResolvedValue(1);
      prisma.product.findMany.mockResolvedValue(mockProducts);

      const request = new NextRequest('http://localhost:3000/api/products?page=1&limit=20');

      const response = await getProductsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.data).toHaveLength(1);
      expect(data.data.data[0].name).toBe('测试商品1');
      expect(data.data.pagination.total).toBe(1);
      expect(data.data.pagination.page).toBe(1);
    });

    it('应该支持搜索功能', async () => {
      const { prisma } = require('@/lib/prisma');
      
      prisma.product.count.mockResolvedValue(0);
      prisma.product.findMany.mockResolvedValue([]);

      const request = new NextRequest('http://localhost:3000/api/products?search=测试商品');

      const response = await getProductsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            OR: expect.arrayContaining([
              { sku: { contains: '测试商品', mode: 'insensitive' } },
              { name: { contains: '测试商品', mode: 'insensitive' } },
            ]),
          }),
        })
      );
    });

    it('应该支持状态筛选', async () => {
      const { prisma } = require('@/lib/prisma');
      
      prisma.product.count.mockResolvedValue(0);
      prisma.product.findMany.mockResolvedValue([]);

      const request = new NextRequest('http://localhost:3000/api/products?status=ACTIVE');

      const response = await getProductsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(prisma.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            status: 'ACTIVE',
          }),
        })
      );
    });

    it('应该验证分页参数', async () => {
      const request = new NextRequest('http://localhost:3000/api/products?page=0&limit=1000');

      const response = await getProductsHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('POST /api/products - 创建商品', () => {
    it('应该成功创建新商品', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟SKU不存在
      prisma.product.findUnique.mockResolvedValue(null);
      
      // 模拟创建商品成功
      const mockProduct = {
        id: 'product-1',
        sku: 'SKU001',
        name: '新商品',
        description: '这是一个新商品',
        basePrice: 99.99,
        currency: 'CNY',
        status: 'DRAFT',
        isDigital: false,
        requiresShipping: true,
        trackInventory: true,
        allowBackorder: false,
        createdAt: new Date(),
        updatedAt: new Date(),
        category: null,
        images: [],
        variants: [],
        inventoryItems: [],
      };
      prisma.product.create.mockResolvedValue(mockProduct);

      const requestBody = {
        sku: 'SKU001',
        name: '新商品',
        description: '这是一个新商品',
        basePrice: 99.99,
        currency: 'CNY',
        isDigital: false,
        requiresShipping: true,
        trackInventory: true,
        allowBackorder: false,
      };

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await createProductHandler(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.name).toBe('新商品');
      expect(data.data.sku).toBe('SKU001');
      expect(data.data.status).toBe('DRAFT');
    });

    it('应该拒绝重复的SKU', async () => {
      const { prisma } = require('@/lib/prisma');
      
      // 模拟SKU已存在
      prisma.product.findUnique.mockResolvedValue({
        id: 'existing-product',
        sku: 'SKU001',
      });

      const requestBody = {
        sku: 'SKU001',
        name: '新商品',
        description: '这是一个新商品',
        basePrice: 99.99,
        currency: 'CNY',
        isDigital: false,
        requiresShipping: true,
        trackInventory: true,
        allowBackorder: false,
      };

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await createProductHandler(request);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('SKU_EXISTS');
    });

    it('应该验证必填字段', async () => {
      const requestBody = {
        // 缺少sku
        name: '新商品',
        basePrice: 99.99,
        currency: 'CNY',
      };

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await createProductHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });

    it('应该验证价格为正数', async () => {
      const requestBody = {
        sku: 'SKU001',
        name: '新商品',
        basePrice: -10, // 负价格
        currency: 'CNY',
        isDigital: false,
        requiresShipping: true,
        trackInventory: true,
        allowBackorder: false,
      };

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await createProductHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });

    it('应该验证货币代码', async () => {
      const requestBody = {
        sku: 'SKU001',
        name: '新商品',
        basePrice: 99.99,
        currency: 'INVALID', // 无效货币代码
        isDigital: false,
        requiresShipping: true,
        trackInventory: true,
        allowBackorder: false,
      };

      const request = new NextRequest('http://localhost:3000/api/products', {
        method: 'POST',
        body: JSON.stringify(requestBody),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await createProductHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('VALIDATION_ERROR');
    });
  });
});
