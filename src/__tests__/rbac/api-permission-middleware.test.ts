/**
 * API权限中间件测试
 * 测试API级别的权限验证功能
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest, NextResponse } from 'next/server';
import { apiPermissionMiddleware } from '@/lib/rbac/api-permission-middleware';
import { verifyToken } from '@/lib/auth';
import { permissionService } from '@/lib/rbac/permission-service';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/rbac/permission-service');

describe('API权限中间件', () => {
  const mockUser = {
    id: 'user-123',
    email: '<EMAIL>',
    roles: ['admin'],
  };

  const mockPermissionResult = {
    allowed: true,
    reason: undefined,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (verifyToken as jest.Mock).mockResolvedValue(mockUser);
    (permissionService.checkPermission as jest.Mock).mockResolvedValue(mockPermissionResult);
  });

  describe('商品API权限', () => {
    it('应该允许有权限的用户访问商品列表', async () => {
      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeNull(); // 表示允许继续
      expect(permissionService.checkPermission).toHaveBeenCalledWith({
        userId: 'user-123',
        resource: 'products',
        action: 'read',
        resourceId: undefined,
      });
    });

    it('应该拒绝无权限的用户访问商品创建', async () => {
      (permissionService.checkPermission as jest.Mock).mockResolvedValue({
        allowed: false,
        reason: '权限不足',
      });

      const request = new NextRequest('http://localhost/api/products', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeInstanceOf(NextResponse);
      const response = result as NextResponse;
      expect(response.status).toBe(403);
    });

    it('应该处理商品详情API的资源ID', async () => {
      const request = new NextRequest('http://localhost/api/products/product-123', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products/product-123');

      expect(result).toBeNull();
      expect(permissionService.checkPermission).toHaveBeenCalledWith({
        userId: 'user-123',
        resource: 'products',
        action: 'read',
        resourceId: 'product-123',
      });
    });
  });

  describe('订单API权限', () => {
    it('应该允许有权限的用户访问订单列表', async () => {
      const request = new NextRequest('http://localhost/api/orders', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/orders');

      expect(result).toBeNull();
      expect(permissionService.checkPermission).toHaveBeenCalledWith({
        userId: 'user-123',
        resource: 'orders',
        action: 'read',
        resourceId: undefined,
      });
    });

    it('应该检查订单处理权限', async () => {
      const request = new NextRequest('http://localhost/api/orders/order-123/process', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/orders/order-123/process');

      expect(result).toBeNull();
      expect(permissionService.checkPermission).toHaveBeenCalledWith({
        userId: 'user-123',
        resource: 'orders',
        action: 'process',
        resourceId: 'order-123',
      });
    });

    it('应该检查订单发货权限', async () => {
      const request = new NextRequest('http://localhost/api/orders/order-123/ship', {
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/orders/order-123/ship');

      expect(result).toBeNull();
      expect(permissionService.checkPermission).toHaveBeenCalledWith({
        userId: 'user-123',
        resource: 'orders',
        action: 'ship',
        resourceId: 'order-123',
      });
    });
  });

  describe('用户API权限', () => {
    it('应该允许用户更新自己的信息', async () => {
      const customValidator = jest.fn().mockResolvedValue(true);
      
      const request = new NextRequest('http://localhost/api/users/user-123', {
        method: 'PUT',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/users/user-123');

      expect(result).toBeNull();
    });

    it('应该拒绝用户更新其他人的信息', async () => {
      const request = new NextRequest('http://localhost/api/users/other-user', {
        method: 'PUT',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      // 模拟非管理员用户
      (verifyToken as jest.Mock).mockResolvedValue({
        id: 'user-123',
        email: '<EMAIL>',
        roles: ['user'],
      });

      const result = await apiPermissionMiddleware(request, '/api/users/other-user');

      // 应该被自定义验证器拒绝
      expect(result).toBeInstanceOf(NextResponse);
    });

    it('应该允许管理员更新任何用户信息', async () => {
      const request = new NextRequest('http://localhost/api/users/other-user', {
        method: 'PUT',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      // 模拟管理员用户
      (verifyToken as jest.Mock).mockResolvedValue({
        id: 'admin-123',
        email: '<EMAIL>',
        roles: ['admin'],
      });

      const result = await apiPermissionMiddleware(request, '/api/users/other-user');

      expect(result).toBeNull();
    });
  });

  describe('认证检查', () => {
    it('应该拒绝没有token的请求', async () => {
      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeInstanceOf(NextResponse);
      const response = result as NextResponse;
      expect(response.status).toBe(401);
    });

    it('应该拒绝无效token的请求', async () => {
      (verifyToken as jest.Mock).mockRejectedValue(new Error('Invalid token'));

      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer invalid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeInstanceOf(NextResponse);
      const response = result as NextResponse;
      expect(response.status).toBe(401);
    });

    it('应该从Cookie中读取token', async () => {
      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
        headers: {
          'cookie': 'auth-token=valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeNull();
      expect(verifyToken).toHaveBeenCalledWith('valid-token');
    });
  });

  describe('角色权限检查', () => {
    it('应该允许有指定角色的用户访问', async () => {
      // 假设某个API需要admin角色
      const request = new NextRequest('http://localhost/api/settings', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/settings');

      expect(result).toBeNull();
    });

    it('应该拒绝没有指定角色的用户访问', async () => {
      (verifyToken as jest.Mock).mockResolvedValue({
        id: 'user-123',
        email: '<EMAIL>',
        roles: ['user'], // 没有admin角色
      });

      const request = new NextRequest('http://localhost/api/settings', {
        method: 'PUT',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/settings');

      // 应该被权限检查拒绝
      expect(permissionService.checkPermission).toHaveBeenCalled();
    });
  });

  describe('动态路径匹配', () => {
    it('应该正确匹配动态路径', async () => {
      const testCases = [
        {
          pattern: '/api/products/[id]',
          pathname: '/api/products/123',
          expected: true,
        },
        {
          pattern: '/api/orders/[id]/process',
          pathname: '/api/orders/456/process',
          expected: true,
        },
        {
          pattern: '/api/users/[id]/roles',
          pathname: '/api/users/789/roles',
          expected: true,
        },
        {
          pattern: '/api/products/[id]',
          pathname: '/api/products/123/variants',
          expected: false,
        },
      ];

      // 这里我们需要测试内部的matchDynamicPath函数
      // 由于它是私有函数，我们通过实际的API调用来测试
      for (const testCase of testCases) {
        const request = new NextRequest(`http://localhost${testCase.pathname}`, {
          method: 'GET',
          headers: {
            'authorization': 'Bearer valid-token',
          },
        });

        const result = await apiPermissionMiddleware(request, testCase.pathname);

        if (testCase.expected) {
          expect(result).toBeNull(); // 应该找到匹配的权限配置
        }
      }
    });
  });

  describe('资源ID提取', () => {
    it('应该正确提取资源ID', async () => {
      const testCases = [
        {
          pathname: '/api/products/product-123',
          expectedResourceId: 'product-123',
        },
        {
          pathname: '/api/orders/order-456/process',
          expectedResourceId: 'order-456',
        },
        {
          pathname: '/api/users/user-789/roles',
          expectedResourceId: 'user-789',
        },
      ];

      for (const testCase of testCases) {
        const request = new NextRequest(`http://localhost${testCase.pathname}`, {
          method: 'GET',
          headers: {
            'authorization': 'Bearer valid-token',
          },
        });

        await apiPermissionMiddleware(request, testCase.pathname);

        expect(permissionService.checkPermission).toHaveBeenCalledWith(
          expect.objectContaining({
            resourceId: testCase.expectedResourceId,
          })
        );
      }
    });
  });

  describe('错误处理', () => {
    it('应该处理权限检查错误', async () => {
      (permissionService.checkPermission as jest.Mock).mockRejectedValue(
        new Error('Permission check failed')
      );

      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeInstanceOf(NextResponse);
      const response = result as NextResponse;
      expect(response.status).toBe(500);
    });

    it('应该处理用户验证错误', async () => {
      (verifyToken as jest.Mock).mockRejectedValue(new Error('Token verification failed'));

      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeInstanceOf(NextResponse);
      const response = result as NextResponse;
      expect(response.status).toBe(401);
    });
  });

  describe('请求头处理', () => {
    it('应该在成功验证后添加用户信息到请求头', async () => {
      const request = new NextRequest('http://localhost/api/products', {
        method: 'GET',
        headers: {
          'authorization': 'Bearer valid-token',
        },
      });

      const result = await apiPermissionMiddleware(request, '/api/products');

      expect(result).toBeInstanceOf(NextResponse);
      const response = result as NextResponse;
      
      // 检查是否添加了用户信息到请求头
      const requestHeaders = response.headers;
      // 注意：在实际实现中，我们需要检查NextResponse.next()的返回值
    });
  });

  describe('无权限配置的API', () => {
    it('应该允许访问没有权限配置的API', async () => {
      const request = new NextRequest('http://localhost/api/public/health', {
        method: 'GET',
      });

      const result = await apiPermissionMiddleware(request, '/api/public/health');

      expect(result).toBeNull(); // 没有权限配置，应该允许访问
    });
  });
});
