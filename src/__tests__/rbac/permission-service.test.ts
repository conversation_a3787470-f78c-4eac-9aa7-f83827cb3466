/**
 * 权限服务单元测试
 * 测试权限验证、缓存机制和批量操作功能
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { permissionService } from '@/lib/rbac/permission-service';
import { prisma } from '@/lib/prisma';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
    userRole: {
      findMany: jest.fn(),
    },
    role: {
      findMany: jest.fn(),
    },
    permission: {
      findMany: jest.fn(),
    },
    rolePermission: {
      findMany: jest.fn(),
    },
  },
}));

// Mock Redis
jest.mock('@/lib/redis', () => ({
  redis: {
    get: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
  },
}));

describe('PermissionService', () => {
  const mockUserId = 'user-123';
  const mockUser = {
    id: mockUserId,
    email: '<EMAIL>',
    isActive: true,
  };

  const mockRoles = [
    {
      id: 'role-1',
      name: 'admin',
      displayName: '管理员',
      isActive: true,
    },
    {
      id: 'role-2',
      name: 'user',
      displayName: '普通用户',
      isActive: true,
    },
  ];

  const mockPermissions = [
    {
      id: 'perm-1',
      name: 'products.read',
      resource: 'products',
      action: 'read',
      description: '查看商品',
    },
    {
      id: 'perm-2',
      name: 'products.create',
      resource: 'products',
      action: 'create',
      description: '创建商品',
    },
    {
      id: 'perm-3',
      name: 'orders.read',
      resource: 'orders',
      action: 'read',
      description: '查看订单',
    },
  ];

  const mockUserRoles = [
    {
      id: 'ur-1',
      userId: mockUserId,
      roleId: 'role-1',
      role: mockRoles[0],
    },
  ];

  const mockRolePermissions = [
    {
      id: 'rp-1',
      roleId: 'role-1',
      permissionId: 'perm-1',
      permission: mockPermissions[0],
    },
    {
      id: 'rp-2',
      roleId: 'role-1',
      permissionId: 'perm-2',
      permission: mockPermissions[1],
    },
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    
    // 设置默认的 mock 返回值
    (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);
    (prisma.userRole.findMany as jest.Mock).mockResolvedValue(mockUserRoles);
    (prisma.rolePermission.findMany as jest.Mock).mockResolvedValue(mockRolePermissions);
  });

  afterEach(() => {
    // 清理缓存
    permissionService.clearUserPermissionsCache(mockUserId);
  });

  describe('checkPermission', () => {
    it('应该允许用户访问有权限的资源', async () => {
      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products',
        action: 'read',
      });

      expect(result.allowed).toBe(true);
      expect(result.reason).toBeUndefined();
    });

    it('应该拒绝用户访问无权限的资源', async () => {
      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'orders',
        action: 'read',
      });

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('用户没有执行此操作的权限');
    });

    it('应该拒绝不存在的用户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      const result = await permissionService.checkPermission({
        userId: 'non-existent-user',
        resource: 'products',
        action: 'read',
      });

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('用户不存在');
    });

    it('应该拒绝非活跃用户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        ...mockUser,
        isActive: false,
      });

      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products',
        action: 'read',
      });

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('用户账户已被禁用');
    });

    it('应该支持资源级权限检查', async () => {
      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products',
        action: 'read',
        resourceId: 'product-123',
      });

      expect(result.allowed).toBe(true);
    });

    it('应该处理权限检查错误', async () => {
      (prisma.user.findUnique as jest.Mock).mockRejectedValue(new Error('数据库错误'));

      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products',
        action: 'read',
      });

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('权限检查失败');
    });
  });

  describe('checkMultiplePermissions', () => {
    it('应该批量检查多个权限', async () => {
      const permissions = [
        { resource: 'products', action: 'read' },
        { resource: 'products', action: 'create' },
        { resource: 'orders', action: 'read' },
      ];

      const results = await permissionService.checkMultiplePermissions(
        mockUserId,
        permissions
      );

      expect(results).toHaveProperty('products.read');
      expect(results).toHaveProperty('products.create');
      expect(results).toHaveProperty('orders.read');

      expect(results['products.read'].allowed).toBe(true);
      expect(results['products.create'].allowed).toBe(true);
      expect(results['orders.read'].allowed).toBe(false);
    });

    it('应该处理空权限列表', async () => {
      const results = await permissionService.checkMultiplePermissions(
        mockUserId,
        []
      );

      expect(results).toEqual({});
    });
  });

  describe('getUserPermissions', () => {
    it('应该返回用户的所有权限', async () => {
      const permissions = await permissionService.getUserPermissions(mockUserId);

      expect(permissions).toHaveLength(2);
      expect(permissions).toContainEqual(
        expect.objectContaining({
          name: 'products.read',
          resource: 'products',
          action: 'read',
        })
      );
      expect(permissions).toContainEqual(
        expect.objectContaining({
          name: 'products.create',
          resource: 'products',
          action: 'create',
        })
      );
    });

    it('应该返回空数组当用户没有权限时', async () => {
      (prisma.userRole.findMany as jest.Mock).mockResolvedValue([]);
      (prisma.rolePermission.findMany as jest.Mock).mockResolvedValue([]);

      const permissions = await permissionService.getUserPermissions(mockUserId);

      expect(permissions).toHaveLength(0);
    });
  });

  describe('getUserRoles', () => {
    it('应该返回用户的所有角色', async () => {
      const roles = await permissionService.getUserRoles(mockUserId);

      expect(roles).toHaveLength(1);
      expect(roles[0]).toEqual(
        expect.objectContaining({
          name: 'admin',
          displayName: '管理员',
        })
      );
    });

    it('应该返回空数组当用户没有角色时', async () => {
      (prisma.userRole.findMany as jest.Mock).mockResolvedValue([]);

      const roles = await permissionService.getUserRoles(mockUserId);

      expect(roles).toHaveLength(0);
    });
  });

  describe('getAccessibleResources', () => {
    it('应该返回用户可访问的资源列表', async () => {
      const resources = await permissionService.getAccessibleResources(
        mockUserId,
        'read'
      );

      expect(resources).toContain('products');
      expect(resources).not.toContain('orders');
    });

    it('应该返回空数组当用户没有指定操作权限时', async () => {
      const resources = await permissionService.getAccessibleResources(
        mockUserId,
        'delete'
      );

      expect(resources).toHaveLength(0);
    });
  });

  describe('缓存机制', () => {
    it('应该缓存用户权限', async () => {
      // 第一次调用
      await permissionService.getUserPermissions(mockUserId);
      
      // 第二次调用应该使用缓存
      await permissionService.getUserPermissions(mockUserId);

      // 验证数据库只被调用一次
      expect(prisma.userRole.findMany).toHaveBeenCalledTimes(1);
    });

    it('应该能够清除用户权限缓存', async () => {
      // 第一次调用
      await permissionService.getUserPermissions(mockUserId);
      
      // 清除缓存
      permissionService.clearUserPermissionsCache(mockUserId);
      
      // 第二次调用应该重新查询数据库
      await permissionService.getUserPermissions(mockUserId);

      // 验证数据库被调用两次
      expect(prisma.userRole.findMany).toHaveBeenCalledTimes(2);
    });

    it('应该能够清除所有权限缓存', async () => {
      // 调用多个用户的权限
      await permissionService.getUserPermissions(mockUserId);
      await permissionService.getUserPermissions('user-456');
      
      // 清除所有缓存
      permissionService.clearAllPermissionsCache();
      
      // 再次调用应该重新查询数据库
      await permissionService.getUserPermissions(mockUserId);

      expect(prisma.userRole.findMany).toHaveBeenCalledTimes(3);
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内完成权限检查', async () => {
      const startTime = Date.now();
      
      await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products',
        action: 'read',
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // 权限检查应该在100ms内完成
      expect(duration).toBeLessThan(100);
    });

    it('应该能够处理大量并发权限检查', async () => {
      const promises = Array.from({ length: 100 }, () =>
        permissionService.checkPermission({
          userId: mockUserId,
          resource: 'products',
          action: 'read',
        })
      );

      const results = await Promise.all(promises);
      
      // 所有检查都应该成功
      results.forEach(result => {
        expect(result.allowed).toBe(true);
      });
    });
  });

  describe('边界条件测试', () => {
    it('应该处理空字符串用户ID', async () => {
      const result = await permissionService.checkPermission({
        userId: '',
        resource: 'products',
        action: 'read',
      });

      expect(result.allowed).toBe(false);
    });

    it('应该处理空字符串资源名', async () => {
      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: '',
        action: 'read',
      });

      expect(result.allowed).toBe(false);
    });

    it('应该处理空字符串操作名', async () => {
      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products',
        action: '',
      });

      expect(result.allowed).toBe(false);
    });

    it('应该处理特殊字符', async () => {
      const result = await permissionService.checkPermission({
        userId: mockUserId,
        resource: 'products@#$%',
        action: 'read!@#',
      });

      expect(result.allowed).toBe(false);
    });
  });
});
