/**
 * 行级权限验证测试
 * 测试数据行级别的权限控制功能
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { rowLevelSecurity } from '@/lib/rbac/row-level-security';
import { prisma } from '@/lib/prisma';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
    userWarehouse: {
      findMany: jest.fn(),
    },
  },
}));

describe('RowLevelSecurity', () => {
  const mockUserId = 'user-123';
  const mockAdminUserId = 'admin-456';
  const mockSalesUserId = 'sales-789';

  const mockUser = {
    id: mockUserId,
    email: '<EMAIL>',
    department: 'sales',
    region: 'north',
    userRoles: [
      {
        role: {
          name: 'sales_rep',
        },
      },
    ],
  };

  const mockAdminUser = {
    id: mockAdminUserId,
    email: '<EMAIL>',
    department: 'admin',
    region: 'all',
    userRoles: [
      {
        role: {
          name: 'admin',
        },
      },
    ],
  };

  const mockSalesUser = {
    id: mockSalesUserId,
    email: '<EMAIL>',
    department: 'sales',
    region: 'south',
    userRoles: [
      {
        role: {
          name: 'sales_rep',
        },
      },
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('订单行级权限', () => {
    it('应该允许管理员访问所有订单', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockAdminUser);

      const orderData = {
        id: 'order-123',
        createdBy: mockUserId,
        customerId: 'customer-456',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockAdminUserId,
        'orders',
        orderData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该允许销售人员访问自己创建的订单', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const orderData = {
        id: 'order-123',
        createdBy: mockUserId,
        customerId: 'customer-456',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'orders',
        orderData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝销售人员访问其他人创建的订单', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const orderData = {
        id: 'order-123',
        createdBy: 'other-user-789',
        customerId: 'customer-456',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'orders',
        orderData
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toContain('无权访问');
    });

    it('应该允许客服人员访问所有订单', async () => {
      const customerServiceUser = {
        ...mockUser,
        userRoles: [
          {
            role: {
              name: 'customer_service',
            },
          },
        ],
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(customerServiceUser);

      const orderData = {
        id: 'order-123',
        createdBy: 'other-user-789',
        customerId: 'customer-456',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'orders',
        orderData
      );

      expect(result.allowed).toBe(true);
    });
  });

  describe('客户行级权限', () => {
    it('应该允许销售人员访问自己负责的客户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const customerData = {
        id: 'customer-123',
        assignedSalesRep: mockUserId,
        email: '<EMAIL>',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'customers',
        customerData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝销售人员访问其他人负责的客户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const customerData = {
        id: 'customer-123',
        assignedSalesRep: 'other-sales-456',
        email: '<EMAIL>',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'customers',
        customerData
      );

      expect(result.allowed).toBe(false);
    });

    it('应该允许管理员访问所有客户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockAdminUser);

      const customerData = {
        id: 'customer-123',
        assignedSalesRep: 'other-sales-456',
        email: '<EMAIL>',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockAdminUserId,
        'customers',
        customerData
      );

      expect(result.allowed).toBe(true);
    });
  });

  describe('商品行级权限', () => {
    it('应该允许所有角色查看已发布的商品', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const productData = {
        id: 'product-123',
        status: 'ACTIVE',
        name: '测试商品',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'products',
        productData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝普通角色查看未发布的商品', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const productData = {
        id: 'product-123',
        status: 'DRAFT',
        name: '测试商品',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'products',
        productData
      );

      expect(result.allowed).toBe(false);
    });

    it('应该允许商品管理员查看所有商品', async () => {
      const productManagerUser = {
        ...mockUser,
        userRoles: [
          {
            role: {
              name: 'product_manager',
            },
          },
        ],
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(productManagerUser);

      const productData = {
        id: 'product-123',
        status: 'DRAFT',
        name: '测试商品',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'products',
        productData
      );

      expect(result.allowed).toBe(true);
    });
  });

  describe('库存行级权限', () => {
    it('应该允许仓库人员访问自己负责仓库的库存', async () => {
      const warehouseUser = {
        ...mockUser,
        userRoles: [
          {
            role: {
              name: 'warehouse_staff',
            },
          },
        ],
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(warehouseUser);
      (prisma.userWarehouse.findMany as jest.Mock).mockResolvedValue([
        { warehouseId: 'warehouse-123' },
        { warehouseId: 'warehouse-456' },
      ]);

      const inventoryData = {
        id: 'inventory-123',
        warehouseId: 'warehouse-123',
        productId: 'product-456',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'inventory',
        inventoryData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝仓库人员访问其他仓库的库存', async () => {
      const warehouseUser = {
        ...mockUser,
        userRoles: [
          {
            role: {
              name: 'warehouse_staff',
            },
          },
        ],
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(warehouseUser);
      (prisma.userWarehouse.findMany as jest.Mock).mockResolvedValue([
        { warehouseId: 'warehouse-123' },
      ]);

      const inventoryData = {
        id: 'inventory-123',
        warehouseId: 'warehouse-456',
        productId: 'product-456',
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'inventory',
        inventoryData
      );

      expect(result.allowed).toBe(false);
    });
  });

  describe('用户行级权限', () => {
    it('应该允许用户访问自己的信息', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const userData = {
        id: mockUserId,
        email: '<EMAIL>',
        roles: ['sales_rep'],
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'users',
        userData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝用户访问其他人的信息', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const userData = {
        id: 'other-user-456',
        email: '<EMAIL>',
        roles: ['sales_rep'],
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'users',
        userData
      );

      expect(result.allowed).toBe(false);
    });

    it('应该允许管理员访问非超级管理员用户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockAdminUser);

      const userData = {
        id: 'other-user-456',
        email: '<EMAIL>',
        roles: ['sales_rep'],
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockAdminUserId,
        'users',
        userData
      );

      expect(result.allowed).toBe(true);
    });

    it('应该拒绝管理员访问超级管理员用户', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockAdminUser);

      const userData = {
        id: 'super-admin-789',
        email: '<EMAIL>',
        roles: ['super_admin'],
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockAdminUserId,
        'users',
        userData
      );

      expect(result.allowed).toBe(false);
    });
  });

  describe('批量行级权限检查', () => {
    it('应该正确分离允许和拒绝的资源', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const orderDataList = [
        {
          id: 'order-1',
          createdBy: mockUserId,
          customerId: 'customer-1',
        },
        {
          id: 'order-2',
          createdBy: 'other-user',
          customerId: 'customer-2',
        },
        {
          id: 'order-3',
          createdBy: mockUserId,
          customerId: 'customer-3',
        },
      ];

      const result = await rowLevelSecurity.checkMultipleRowPermissions(
        mockUserId,
        'orders',
        orderDataList
      );

      expect(result.allowed).toHaveLength(2);
      expect(result.denied).toHaveLength(1);
      expect(result.allowed[0].id).toBe('order-1');
      expect(result.allowed[1].id).toBe('order-3');
      expect(result.denied[0].id).toBe('order-2');
    });
  });

  describe('查询过滤器', () => {
    it('应该为销售人员添加订单过滤条件', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockUser);

      const baseQuery = {
        where: {
          status: 'ACTIVE',
        },
        orderBy: {
          createdAt: 'desc',
        },
      };

      const filteredQuery = await rowLevelSecurity.addRowLevelFilter(
        mockUserId,
        'orders',
        baseQuery
      );

      expect(filteredQuery.where).toHaveProperty('createdBy', mockUserId);
      expect(filteredQuery.where).toHaveProperty('status', 'ACTIVE');
    });

    it('应该为管理员保持原始查询', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(mockAdminUser);

      const baseQuery = {
        where: {
          status: 'ACTIVE',
        },
        orderBy: {
          createdAt: 'desc',
        },
      };

      const filteredQuery = await rowLevelSecurity.addRowLevelFilter(
        mockAdminUserId,
        'orders',
        baseQuery
      );

      expect(filteredQuery).toEqual(baseQuery);
    });

    it('应该为仓库人员添加库存仓库过滤条件', async () => {
      const warehouseUser = {
        ...mockUser,
        userRoles: [
          {
            role: {
              name: 'warehouse_staff',
            },
          },
        ],
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(warehouseUser);
      (prisma.userWarehouse.findMany as jest.Mock).mockResolvedValue([
        { warehouseId: 'warehouse-123' },
        { warehouseId: 'warehouse-456' },
      ]);

      const baseQuery = {
        where: {
          productId: 'product-123',
        },
      };

      const filteredQuery = await rowLevelSecurity.addRowLevelFilter(
        mockUserId,
        'inventory',
        baseQuery
      );

      expect(filteredQuery.where).toHaveProperty('warehouseId');
      expect(filteredQuery.where.warehouseId).toEqual({
        in: ['warehouse-123', 'warehouse-456'],
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理用户不存在的情况', async () => {
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      const orderData = {
        id: 'order-123',
        createdBy: mockUserId,
      };

      await expect(
        rowLevelSecurity.checkRowPermission('non-existent-user', 'orders', orderData)
      ).rejects.toThrow('用户 non-existent-user 不存在');
    });

    it('应该处理数据库错误', async () => {
      (prisma.user.findUnique as jest.Mock).mockRejectedValue(new Error('数据库连接失败'));

      const orderData = {
        id: 'order-123',
        createdBy: mockUserId,
      };

      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'orders',
        orderData
      );

      expect(result.allowed).toBe(false);
      expect(result.reason).toBe('行级权限检查失败');
    });

    it('应该处理未配置权限规则的资源', async () => {
      const result = await rowLevelSecurity.checkRowPermission(
        mockUserId,
        'unknown_resource',
        { id: 'test' }
      );

      expect(result.allowed).toBe(true);
    });
  });
});
