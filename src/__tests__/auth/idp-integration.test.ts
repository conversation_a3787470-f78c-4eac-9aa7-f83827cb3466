/**
 * IDP集成测试
 * 测试外部身份提供商集成功能
 */

import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { GoogleWorkspaceHandler } from '@/lib/auth/providers/google-workspace';
import { AzureADOIDCHandler } from '@/lib/auth/providers/azure-ad';
import { GenericOIDCHandler } from '@/lib/auth/providers/generic-oidc';
import { UserSyncService } from '@/lib/auth/user-sync-service';
import { IDPType, IDPProtocol } from '@/lib/auth/idp-config';

// 模拟fetch
global.fetch = jest.fn();

// 模拟crypto.randomUUID
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => 'test-uuid-' + Math.random().toString(36).substr(2, 9),
  },
});

describe('IDP集成测试', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Google Workspace集成', () => {
    const mockConfig = {
      id: 'google-workspace-1',
      name: 'Google Workspace',
      type: IDPType.GOOGLE_WORKSPACE,
      protocol: IDPProtocol.OIDC,
      enabled: true,
      isDefault: false,
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
      tokenUrl: 'https://oauth2.googleapis.com/token',
      userInfoUrl: 'https://openidconnect.googleapis.com/v1/userinfo',
      discoveryUrl: 'https://accounts.google.com/.well-known/openid_configuration',
      scopes: ['openid', 'email', 'profile'],
      responseType: 'code',
      hostedDomain: 'example.com',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('应该生成正确的认证URL', async () => {
      const handler = new GoogleWorkspaceHandler(mockConfig);
      
      const authRequest = {
        idpId: 'google-workspace-1',
        redirectUri: 'http://localhost:3000/auth/callback',
        state: 'test-state',
      };

      const authUrl = await handler.generateAuthUrl(authRequest);
      
      expect(authUrl).toContain('https://accounts.google.com/o/oauth2/v2/auth');
      expect(authUrl).toContain('client_id=test-client-id');
      expect(authUrl).toContain('redirect_uri=http%3A//localhost%3A3000/auth/callback');
      expect(authUrl).toContain('state=test-state');
      expect(authUrl).toContain('hd=example.com');
    });

    it('应该处理认证回调', async () => {
      const handler = new GoogleWorkspaceHandler(mockConfig);
      
      // 模拟令牌交换响应
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          access_token: 'test-access-token',
          token_type: 'Bearer',
          expires_in: 3600,
        }),
      } as Response);

      // 模拟用户信息响应
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          sub: 'google-user-123',
          email: '<EMAIL>',
          email_verified: true,
          name: 'Test User',
          given_name: 'Test',
          family_name: 'User',
          picture: 'https://example.com/avatar.jpg',
          hd: 'example.com',
        }),
      } as Response);

      const callbackParams = {
        code: 'test-auth-code',
        state: 'test-state',
        redirect_uri: 'http://localhost:3000/auth/callback',
      };

      const result = await handler.handleCallback(callbackParams);
      
      expect(result.success).toBe(true);
      expect(result.user).toBeDefined();
      expect(result.user?.email).toBe('<EMAIL>');
      expect(result.user?.idpType).toBe(IDPType.GOOGLE_WORKSPACE);
    });

    it('应该验证托管域名', async () => {
      const handler = new GoogleWorkspaceHandler(mockConfig);
      
      // 模拟令牌交换响应
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          access_token: 'test-access-token',
        }),
      } as Response);

      // 模拟用户信息响应（错误的域名）
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          sub: 'google-user-123',
          email: '<EMAIL>',
          hd: 'wrongdomain.com',
        }),
      } as Response);

      const callbackParams = {
        code: 'test-auth-code',
        state: 'test-state',
        redirect_uri: 'http://localhost:3000/auth/callback',
      };

      const result = await handler.handleCallback(callbackParams);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('不属于指定的域名');
    });
  });

  describe('Azure AD集成', () => {
    const mockConfig = {
      id: 'azure-ad-1',
      name: 'Azure AD',
      type: IDPType.MICROSOFT_AZURE_AD,
      protocol: IDPProtocol.OIDC,
      enabled: true,
      isDefault: false,
      tenantId: 'test-tenant-id',
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      authorizationUrl: 'https://login.microsoftonline.com/test-tenant-id/oauth2/v2.0/authorize',
      tokenUrl: 'https://login.microsoftonline.com/test-tenant-id/oauth2/v2.0/token',
      userInfoUrl: 'https://login.microsoftonline.com/test-tenant-id/openid/userinfo',
      scopes: ['openid', 'profile', 'email'],
      responseType: 'code',
      useGraphAPI: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('应该生成正确的认证URL', async () => {
      const handler = new AzureADOIDCHandler(mockConfig);
      
      const authRequest = {
        idpId: 'azure-ad-1',
        redirectUri: 'http://localhost:3000/auth/callback',
        state: 'test-state',
      };

      const authUrl = await handler.generateAuthUrl(authRequest);
      
      expect(authUrl).toContain('https://login.microsoftonline.com/test-tenant-id/oauth2/v2.0/authorize');
      expect(authUrl).toContain('client_id=test-client-id');
      expect(authUrl).toContain('redirect_uri=http%3A//localhost%3A3000/auth/callback');
      expect(authUrl).toContain('state=test-state');
    });

    it('应该处理Azure AD用户信息', async () => {
      const handler = new AzureADOIDCHandler(mockConfig);
      
      // 模拟用户信息响应
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          sub: 'azure-user-123',
          oid: 'azure-user-123',
          email: '<EMAIL>',
          preferred_username: '<EMAIL>',
          name: 'Azure User',
          given_name: 'Azure',
          family_name: 'User',
          tid: 'test-tenant-id',
          roles: ['User'],
        }),
      } as Response);

      const user = await handler.getUserInfo('test-access-token');
      
      expect(user.id).toBe('azure-user-123');
      expect(user.email).toBe('<EMAIL>');
      expect(user.idpType).toBe(IDPType.MICROSOFT_AZURE_AD);
      expect(user.attributes.tenantId).toBe('test-tenant-id');
    });
  });

  describe('通用OIDC集成', () => {
    const mockConfig = {
      id: 'generic-oidc-1',
      name: '通用OIDC',
      type: IDPType.OIDC,
      protocol: IDPProtocol.OIDC,
      enabled: true,
      isDefault: false,
      clientId: 'test-client-id',
      clientSecret: 'test-client-secret',
      discoveryUrl: 'https://idp.example.com/.well-known/openid_configuration',
      authorizationUrl: '',
      tokenUrl: '',
      userInfoUrl: '',
      scopes: ['openid', 'profile', 'email'],
      responseType: 'code',
      validateIssuer: true,
      validateAudience: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('应该加载OIDC发现文档', async () => {
      const handler = new GenericOIDCHandler(mockConfig);
      
      // 模拟发现文档响应
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          issuer: 'https://idp.example.com',
          authorization_endpoint: 'https://idp.example.com/auth',
          token_endpoint: 'https://idp.example.com/token',
          userinfo_endpoint: 'https://idp.example.com/userinfo',
          jwks_uri: 'https://idp.example.com/jwks',
          scopes_supported: ['openid', 'profile', 'email'],
          response_types_supported: ['code'],
          subject_types_supported: ['public'],
          id_token_signing_alg_values_supported: ['RS256'],
        }),
      } as Response);

      await handler.initialize();
      
      expect(fetch).toHaveBeenCalledWith('https://idp.example.com/.well-known/openid_configuration');
    });

    it('应该映射自定义用户属性', async () => {
      const customConfig = {
        ...mockConfig,
        userInfoMapping: {
          id: 'user_id',
          email: 'email_address',
          firstName: 'first_name',
          lastName: 'last_name',
          groups: 'user_groups',
        },
      };

      const handler = new GenericOIDCHandler(customConfig);
      
      // 模拟用户信息响应
      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          user_id: 'custom-user-123',
          email_address: '<EMAIL>',
          first_name: 'Custom',
          last_name: 'User',
          user_groups: ['group1', 'group2'],
        }),
      } as Response);

      const user = await handler.getUserInfo('test-access-token');
      
      expect(user.id).toBe('custom-user-123');
      expect(user.email).toBe('<EMAIL>');
      expect(user.firstName).toBe('Custom');
      expect(user.lastName).toBe('User');
      expect(user.groups).toEqual(['group1', 'group2']);
    });
  });

  describe('用户同步服务', () => {
    let syncService: UserSyncService;

    beforeEach(() => {
      syncService = new UserSyncService();
    });

    it('应该映射用户属性', () => {
      const attributeMappings = [
        {
          localAttribute: 'firstName',
          externalAttribute: 'given_name',
          required: true,
        },
        {
          localAttribute: 'lastName',
          externalAttribute: 'family_name',
          required: true,
        },
        {
          localAttribute: 'department',
          externalAttribute: 'dept',
          required: false,
          defaultValue: 'Unknown',
        },
      ];

      syncService.setAttributeMappings('test-idp', attributeMappings);

      const externalUser = {
        id: 'ext-user-123',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        attributes: {
          given_name: 'John',
          family_name: 'Doe',
          // dept属性缺失，应该使用默认值
        },
        groups: [],
        roles: [],
        idpId: 'test-idp',
        idpType: IDPType.OIDC,
      };

      // 测试属性映射逻辑
      const mappedAttributes = (syncService as any).mapUserAttributes(externalUser);
      
      expect(mappedAttributes.firstName).toBe('John');
      expect(mappedAttributes.lastName).toBe('Doe');
      expect(mappedAttributes.department).toBe('Unknown');
    });

    it('应该映射用户角色', () => {
      const roleMappings = [
        {
          externalRole: 'admin',
          localRole: 'ADMIN',
          priority: 1,
        },
        {
          externalRole: 'user',
          localRole: 'USER',
          priority: 2,
        },
      ];

      syncService.setRoleMappings('test-idp', roleMappings);

      const externalUser = {
        id: 'ext-user-123',
        email: '<EMAIL>',
        attributes: {},
        groups: [],
        roles: ['admin'],
        idpId: 'test-idp',
        idpType: IDPType.OIDC,
      };

      const mappedRole = (syncService as any).mapUserRole(externalUser);
      
      expect(mappedRole).toBe('ADMIN');
    });

    it('应该处理角色映射优先级', () => {
      const roleMappings = [
        {
          externalRole: 'user',
          localRole: 'USER',
          priority: 2,
        },
        {
          externalRole: 'admin',
          localRole: 'ADMIN',
          priority: 1,
        },
      ];

      syncService.setRoleMappings('test-idp', roleMappings);

      const externalUser = {
        id: 'ext-user-123',
        email: '<EMAIL>',
        attributes: {},
        groups: [],
        roles: ['user', 'admin'], // 用户同时有两个角色
        idpId: 'test-idp',
        idpType: IDPType.OIDC,
      };

      const mappedRole = (syncService as any).mapUserRole(externalUser);
      
      // 应该选择优先级更高的角色（数字更小）
      expect(mappedRole).toBe('ADMIN');
    });
  });
});
