/**
 * 主布局组件
 * 提供应用的整体布局结构，包括侧边栏、头部和主内容区域
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Sidebar } from './sidebar';
import { Header } from './header';
import { cn } from '@/lib/utils';

// 主布局组件属性
interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
}

// 主布局组件
export function MainLayout({ children, className }: MainLayoutProps) {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
      if (window.innerWidth < 1024) {
        setSidebarCollapsed(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 切换侧边栏
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className={cn('min-h-screen bg-background', className)}>
      {/* 侧边栏 */}
      <Sidebar
        collapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        className={cn(
          'fixed left-0 top-0 z-40 h-full',
          sidebarCollapsed && isMobile && '-translate-x-full'
        )}
      />

      {/* 移动端遮罩 */}
      {!sidebarCollapsed && isMobile && (
        <div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          onClick={toggleSidebar}
        />
      )}

      {/* 头部导航 */}
      <Header
        onMenuToggle={toggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
      />

      {/* 主内容区域 */}
      <main
        className={cn(
          'pt-16 transition-all duration-300 ease-in-out',
          sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'
        )}
      >
        <div className="min-h-[calc(100vh-4rem)]">
          {children}
        </div>
      </main>
    </div>
  );
}
