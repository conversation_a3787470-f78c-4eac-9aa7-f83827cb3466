/**
 * 按钮级别权限控制组件
 * 提供细粒度的按钮权限控制和状态管理
 */

'use client';

import React, { ReactNode, useState, useEffect } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { usePermissions, PermissionContext } from '@/hooks/usePermissions';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';
import { Loader2, Lock } from 'lucide-react';

// 按钮权限配置接口
export interface ButtonPermissionConfig {
  permission?: PermissionContext;
  permissions?: PermissionContext[];
  roles?: string[];
  requireAllRoles?: boolean;
  customValidator?: () => Promise<boolean> | boolean;
  fallbackMode?: 'hide' | 'disable' | 'show';
  loadingText?: string;
  disabledText?: string;
  showPermissionIcon?: boolean;
}

// 权限按钮组件属性
export interface PermissionButtonProps extends ButtonProps {
  children: ReactNode;
  permissionConfig: ButtonPermissionConfig;
  onPermissionDenied?: () => void;
  confirmAction?: {
    title: string;
    description: string;
    confirmText?: string;
    cancelText?: string;
  };
}

/**
 * 权限控制按钮组件
 */
export function PermissionButton({
  children,
  permissionConfig,
  onPermissionDenied,
  confirmAction,
  className,
  disabled,
  onClick,
  ...buttonProps
}: PermissionButtonProps) {
  const { isAuthenticated } = useAuth();
  const {
    checkPermission,
    checkMultiplePermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isLoading: permissionsLoading,
  } = usePermissions();

  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const {
    permission,
    permissions,
    roles,
    requireAllRoles = false,
    customValidator,
    fallbackMode = 'disable',
    loadingText = '检查权限中...',
    disabledText = '权限不足',
    showPermissionIcon = true,
  } = permissionConfig;

  // 检查权限
  useEffect(() => {
    if (!isAuthenticated) {
      setHasAccess(false);
      return;
    }

    const checkAccess = async () => {
      setIsChecking(true);

      try {
        let permissionGranted = true;
        let roleGranted = true;
        let customGranted = true;

        // 检查角色权限
        if (roles && roles.length > 0) {
          if (requireAllRoles) {
            roleGranted = hasAllRoles(roles);
          } else {
            roleGranted = hasAnyRole(roles);
          }
        }

        // 检查具体权限
        if (permission) {
          const result = await checkPermission(permission);
          permissionGranted = result.allowed;
        } else if (permissions && permissions.length > 0) {
          const results = await checkMultiplePermissions(permissions);
          permissionGranted = Object.values(results).every(result => result.allowed);
        }

        // 自定义验证
        if (customValidator) {
          customGranted = await customValidator();
        }

        const finalAccess = permissionGranted && roleGranted && customGranted;
        setHasAccess(finalAccess);

        if (!finalAccess && onPermissionDenied) {
          onPermissionDenied();
        }
      } catch (err) {
        console.error('按钮权限检查失败:', err);
        setHasAccess(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [
    isAuthenticated,
    permission,
    permissions,
    roles,
    requireAllRoles,
    customValidator,
    checkPermission,
    checkMultiplePermissions,
    hasAnyRole,
    hasAllRoles,
    onPermissionDenied,
  ]);

  // 处理点击事件
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    if (confirmAction) {
      event.preventDefault();
      setShowConfirm(true);
    } else if (onClick) {
      onClick(event);
    }
  };

  // 确认操作
  const handleConfirm = () => {
    setShowConfirm(false);
    if (onClick) {
      onClick({} as React.MouseEvent<HTMLButtonElement>);
    }
  };

  // 取消操作
  const handleCancel = () => {
    setShowConfirm(false);
  };

  // 显示加载状态
  if (isChecking || permissionsLoading) {
    return (
      <Button
        {...buttonProps}
        disabled={true}
        className={cn('relative', className)}
      >
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        {loadingText}
      </Button>
    );
  }

  // 根据权限检查结果和模式决定如何渲染
  if (hasAccess === false) {
    switch (fallbackMode) {
      case 'hide':
        return null;
      
      case 'disable':
        return (
          <Button
            {...buttonProps}
            disabled={true}
            className={cn('relative opacity-50', className)}
            title={disabledText}
          >
            {showPermissionIcon && <Lock className="h-4 w-4 mr-2" />}
            {children}
          </Button>
        );
      
      case 'show':
        return (
          <Button
            {...buttonProps}
            disabled={disabled}
            className={className}
            onClick={handleClick}
          >
            {children}
          </Button>
        );
      
      default:
        return null;
    }
  }

  // 有权限，显示正常按钮
  return (
    <>
      <Button
        {...buttonProps}
        disabled={disabled}
        className={className}
        onClick={handleClick}
      >
        {children}
      </Button>

      {/* 确认对话框 */}
      {showConfirm && confirmAction && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-2">{confirmAction.title}</h3>
            <p className="text-gray-600 mb-4">{confirmAction.description}</p>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={handleCancel}>
                {confirmAction.cancelText || '取消'}
              </Button>
              <Button onClick={handleConfirm}>
                {confirmAction.confirmText || '确认'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

/**
 * 快捷权限按钮组件
 */
export interface QuickPermissionButtonProps extends Omit<ButtonProps, 'children'> {
  children: ReactNode;
  resource: string;
  action: string;
  resourceId?: string;
  fallbackMode?: 'hide' | 'disable' | 'show';
  confirmAction?: PermissionButtonProps['confirmAction'];
}

export function QuickPermissionButton({
  children,
  resource,
  action,
  resourceId,
  fallbackMode = 'disable',
  confirmAction,
  ...buttonProps
}: QuickPermissionButtonProps) {
  return (
    <PermissionButton
      permissionConfig={{
        permission: { resource, action, resourceId },
        fallbackMode,
      }}
      confirmAction={confirmAction}
      {...buttonProps}
    >
      {children}
    </PermissionButton>
  );
}

/**
 * 角色权限按钮组件
 */
export interface RolePermissionButtonProps extends Omit<ButtonProps, 'children'> {
  children: ReactNode;
  roles: string | string[];
  requireAll?: boolean;
  fallbackMode?: 'hide' | 'disable' | 'show';
  confirmAction?: PermissionButtonProps['confirmAction'];
}

export function RolePermissionButton({
  children,
  roles,
  requireAll = false,
  fallbackMode = 'disable',
  confirmAction,
  ...buttonProps
}: RolePermissionButtonProps) {
  const roleArray = Array.isArray(roles) ? roles : [roles];

  return (
    <PermissionButton
      permissionConfig={{
        roles: roleArray,
        requireAllRoles: requireAll,
        fallbackMode,
      }}
      confirmAction={confirmAction}
      {...buttonProps}
    >
      {children}
    </PermissionButton>
  );
}

/**
 * 危险操作按钮组件（自动添加确认对话框）
 */
export interface DangerousActionButtonProps extends QuickPermissionButtonProps {
  actionName: string;
  warningMessage?: string;
}

export function DangerousActionButton({
  children,
  actionName,
  warningMessage,
  resource,
  action,
  resourceId,
  ...buttonProps
}: DangerousActionButtonProps) {
  const defaultWarningMessage = `此操作不可撤销，确定要${actionName}吗？`;

  return (
    <QuickPermissionButton
      resource={resource}
      action={action}
      resourceId={resourceId}
      variant="destructive"
      confirmAction={{
        title: `确认${actionName}`,
        description: warningMessage || defaultWarningMessage,
        confirmText: actionName,
        cancelText: '取消',
      }}
      {...buttonProps}
    >
      {children}
    </QuickPermissionButton>
  );
}

/**
 * 批量操作按钮组件
 */
export interface BatchActionButtonProps extends Omit<ButtonProps, 'children'> {
  children: ReactNode;
  resource: string;
  action: string;
  selectedItems: any[];
  maxItems?: number;
  minItems?: number;
  confirmAction?: PermissionButtonProps['confirmAction'];
}

export function BatchActionButton({
  children,
  resource,
  action,
  selectedItems,
  maxItems,
  minItems = 1,
  confirmAction,
  disabled,
  ...buttonProps
}: BatchActionButtonProps) {
  const isDisabled = 
    disabled ||
    selectedItems.length < minItems ||
    (maxItems && selectedItems.length > maxItems);

  const customValidator = () => {
    return selectedItems.length >= minItems && (!maxItems || selectedItems.length <= maxItems);
  };

  return (
    <PermissionButton
      permissionConfig={{
        permission: { resource, action },
        customValidator,
        fallbackMode: 'disable',
      }}
      confirmAction={confirmAction}
      disabled={isDisabled}
      {...buttonProps}
    >
      {children}
      {selectedItems.length > 0 && (
        <span className="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded text-xs">
          {selectedItems.length}
        </span>
      )}
    </PermissionButton>
  );
}
