/**
 * 权限保护组件
 * 根据用户权限控制组件的显示和访问
 */

'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { usePermissions, PermissionContext } from '@/hooks/usePermissions';
import { useAuth } from '@/hooks/useAuth';

// 权限保护组件属性接口
export interface PermissionGuardProps {
  children: ReactNode;
  
  // 权限要求（二选一）
  permission?: {
    resource: string;
    action: string;
    resourceId?: string;
  };
  permissions?: PermissionContext[];
  
  // 角色要求
  roles?: string[];
  requireAllRoles?: boolean; // 是否需要所有角色，默认false（任意一个即可）
  
  // 显示选项
  fallback?: ReactNode; // 无权限时显示的内容
  loading?: ReactNode; // 加载时显示的内容
  showError?: boolean; // 是否显示错误信息
  
  // 行为选项
  redirect?: string; // 无权限时重定向的路径
  onAccessDenied?: () => void; // 无权限时的回调函数
  
  // 其他选项
  disabled?: boolean; // 是否禁用权限检查
  mode?: 'hide' | 'disable' | 'show'; // 无权限时的行为模式
}

/**
 * 权限保护组件
 * 用于根据用户权限控制子组件的显示
 */
export function PermissionGuard({
  children,
  permission,
  permissions,
  roles,
  requireAllRoles = false,
  fallback = null,
  loading = null,
  showError = false,
  redirect,
  onAccessDenied,
  disabled = false,
  mode = 'hide',
}: PermissionGuardProps) {
  const { isAuthenticated } = useAuth();
  const {
    checkPermission,
    checkMultiplePermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    isLoading: permissionsLoading,
    error: permissionsError,
  } = usePermissions();

  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 检查权限
  useEffect(() => {
    if (disabled) {
      setHasAccess(true);
      return;
    }

    if (!isAuthenticated) {
      setHasAccess(false);
      return;
    }

    const checkAccess = async () => {
      setIsChecking(true);
      setError(null);

      try {
        let permissionGranted = true;
        let roleGranted = true;

        // 检查角色权限
        if (roles && roles.length > 0) {
          if (requireAllRoles) {
            roleGranted = hasAllRoles(roles);
          } else {
            roleGranted = hasAnyRole(roles);
          }
        }

        // 检查具体权限
        if (permission) {
          const result = await checkPermission(permission);
          permissionGranted = result.allowed;
          if (!permissionGranted && result.reason) {
            setError(result.reason);
          }
        } else if (permissions && permissions.length > 0) {
          const results = await checkMultiplePermissions(permissions);
          permissionGranted = Object.values(results).every(result => result.allowed);
          if (!permissionGranted) {
            const failedReasons = Object.values(results)
              .filter(result => !result.allowed)
              .map(result => result.reason)
              .filter(Boolean);
            if (failedReasons.length > 0) {
              setError(failedReasons.join(', '));
            }
          }
        }

        const finalAccess = permissionGranted && roleGranted;
        setHasAccess(finalAccess);

        // 处理无权限情况
        if (!finalAccess) {
          if (onAccessDenied) {
            onAccessDenied();
          }
          if (redirect) {
            window.location.href = redirect;
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : '权限检查失败';
        setError(errorMessage);
        setHasAccess(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [
    disabled,
    isAuthenticated,
    permission,
    permissions,
    roles,
    requireAllRoles,
    checkPermission,
    checkMultiplePermissions,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    onAccessDenied,
    redirect,
  ]);

  // 显示加载状态
  if (isChecking || permissionsLoading) {
    return <>{loading}</>;
  }

  // 显示错误信息
  if (showError && (error || permissionsError)) {
    return (
      <div className="text-red-600 text-sm p-2 bg-red-50 border border-red-200 rounded">
        权限检查错误: {error || permissionsError}
      </div>
    );
  }

  // 根据权限检查结果和模式决定如何渲染
  if (hasAccess === false) {
    switch (mode) {
      case 'hide':
        return <>{fallback}</>;
      case 'disable':
        return (
          <div className="opacity-50 pointer-events-none" title="您没有权限执行此操作">
            {children}
          </div>
        );
      case 'show':
        return <>{children}</>;
      default:
        return <>{fallback}</>;
    }
  }

  // 有权限或权限检查中，显示子组件
  return <>{children}</>;
}

/**
 * 权限保护的高阶组件
 */
export function withPermissionGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps: Omit<PermissionGuardProps, 'children'>
) {
  const WrappedComponent = (props: P) => {
    return (
      <PermissionGuard {...guardProps}>
        <Component {...props} />
      </PermissionGuard>
    );
  };

  WrappedComponent.displayName = `withPermissionGuard(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

/**
 * 权限检查Hook（用于条件渲染）
 */
export function usePermissionCheck(
  permission?: PermissionGuardProps['permission'],
  permissions?: PermissionGuardProps['permissions'],
  roles?: string[],
  requireAllRoles = false
) {
  const { isAuthenticated } = useAuth();
  const {
    checkPermission,
    checkMultiplePermissions,
    hasAnyRole,
    hasAllRoles,
  } = usePermissions();

  const [hasAccess, setHasAccess] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  useEffect(() => {
    if (!isAuthenticated) {
      setHasAccess(false);
      return;
    }

    const checkAccess = async () => {
      setIsChecking(true);

      try {
        let permissionGranted = true;
        let roleGranted = true;

        // 检查角色权限
        if (roles && roles.length > 0) {
          if (requireAllRoles) {
            roleGranted = hasAllRoles(roles);
          } else {
            roleGranted = hasAnyRole(roles);
          }
        }

        // 检查具体权限
        if (permission) {
          const result = await checkPermission(permission);
          permissionGranted = result.allowed;
        } else if (permissions && permissions.length > 0) {
          const results = await checkMultiplePermissions(permissions);
          permissionGranted = Object.values(results).every(result => result.allowed);
        }

        setHasAccess(permissionGranted && roleGranted);
      } catch (err) {
        setHasAccess(false);
      } finally {
        setIsChecking(false);
      }
    };

    checkAccess();
  }, [
    isAuthenticated,
    permission,
    permissions,
    roles,
    requireAllRoles,
    checkPermission,
    checkMultiplePermissions,
    hasAnyRole,
    hasAllRoles,
  ]);

  return { hasAccess, isChecking };
}

/**
 * 便捷的权限检查组件
 */
export interface CanProps {
  do: string; // action
  on: string; // resource
  with?: string; // resourceId
  role?: string | string[]; // 角色要求
  fallback?: ReactNode;
  children: ReactNode;
}

export function Can({ do: action, on: resource, with: resourceId, role, fallback, children }: CanProps) {
  const permission = { resource, action, resourceId };
  const roles = role ? (Array.isArray(role) ? role : [role]) : undefined;

  return (
    <PermissionGuard permission={permission} roles={roles} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}

/**
 * 便捷的角色检查组件
 */
export interface HasRoleProps {
  role: string | string[];
  requireAll?: boolean;
  fallback?: ReactNode;
  children: ReactNode;
}

export function HasRole({ role, requireAll = false, fallback, children }: HasRoleProps) {
  const roles = Array.isArray(role) ? role : [role];

  return (
    <PermissionGuard roles={roles} requireAllRoles={requireAll} fallback={fallback}>
      {children}
    </PermissionGuard>
  );
}
