/**
 * 数据可视化组件开发
 * 提供高级图表组件、交互式数据展示和自定义报表生成器
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  Scatter<PERSON>hart,
  Scatter,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  <PERSON>map,
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  ResponsiveContainer
} from 'recharts';
import { 
  <PERSON><PERSON>hart3, 
  <PERSON><PERSON><PERSON> as PieChartIcon, 
  TrendingUp, 
  Layers,
  Settings,
  Download,
  Share,
  Eye,
  Edit,
  Save,
  Plus
} from 'lucide-react';

// 图表配置类型
interface ChartConfig {
  id: string;
  type: 'line' | 'area' | 'bar' | 'pie' | 'scatter' | 'radar' | 'treemap';
  title: string;
  dataSource: string;
  xAxis: string;
  yAxis: string[];
  colors: string[];
  filters: Record<string, any>;
  customOptions: Record<string, any>;
}

// 数据源类型
interface DataSource {
  id: string;
  name: string;
  description: string;
  fields: DataField[];
  sampleData: Record<string, any>[];
}

interface DataField {
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean';
  label: string;
}

export default function DataVisualization() {
  const [selectedChart, setSelectedChart] = useState<ChartConfig | null>(null);
  const [chartConfigs, setChartConfigs] = useState<ChartConfig[]>([]);
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    loadDataSources();
    loadSavedCharts();
  }, []);

  /**
   * 加载数据源
   */
  const loadDataSources = () => {
    const mockDataSources: DataSource[] = [
      {
        id: 'sales',
        name: '销售数据',
        description: '包含订单、收入、客户等销售相关数据',
        fields: [
          { name: 'date', type: 'date', label: '日期' },
          { name: 'revenue', type: 'number', label: '收入' },
          { name: 'orders', type: 'number', label: '订单数' },
          { name: 'customers', type: 'number', label: '客户数' },
          { name: 'category', type: 'string', label: '分类' }
        ],
        sampleData: generateSalesData()
      },
      {
        id: 'products',
        name: '商品数据',
        description: '商品销量、库存、价格等数据',
        fields: [
          { name: 'productName', type: 'string', label: '商品名称' },
          { name: 'sales', type: 'number', label: '销量' },
          { name: 'revenue', type: 'number', label: '收入' },
          { name: 'stock', type: 'number', label: '库存' },
          { name: 'category', type: 'string', label: '分类' }
        ],
        sampleData: generateProductData()
      },
      {
        id: 'customers',
        name: '客户数据',
        description: '客户行为、价值、分布等数据',
        fields: [
          { name: 'customerType', type: 'string', label: '客户类型' },
          { name: 'orderCount', type: 'number', label: '订单数量' },
          { name: 'totalValue', type: 'number', label: '总价值' },
          { name: 'region', type: 'string', label: '地区' },
          { name: 'satisfaction', type: 'number', label: '满意度' }
        ],
        sampleData: generateCustomerData()
      }
    ];
    
    setDataSources(mockDataSources);
  };

  /**
   * 加载保存的图表
   */
  const loadSavedCharts = () => {
    const savedCharts: ChartConfig[] = [
      {
        id: 'chart1',
        type: 'line',
        title: '销售趋势分析',
        dataSource: 'sales',
        xAxis: 'date',
        yAxis: ['revenue', 'orders'],
        colors: ['#8884d8', '#82ca9d'],
        filters: {},
        customOptions: {}
      },
      {
        id: 'chart2',
        type: 'pie',
        title: '分类收入分布',
        dataSource: 'sales',
        xAxis: 'category',
        yAxis: ['revenue'],
        colors: ['#8884d8', '#82ca9d', '#ffc658', '#ff7300'],
        filters: {},
        customOptions: {}
      },
      {
        id: 'chart3',
        type: 'bar',
        title: '商品销量排行',
        dataSource: 'products',
        xAxis: 'productName',
        yAxis: ['sales'],
        colors: ['#8884d8'],
        filters: {},
        customOptions: {}
      }
    ];
    
    setChartConfigs(savedCharts);
  };

  /**
   * 生成销售数据
   */
  const generateSalesData = () => {
    const data = [];
    const categories = ['电子产品', '服装', '家居', '图书', '运动'];
    
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      data.push({
        date: date.toISOString().split('T')[0],
        revenue: Math.floor(Math.random() * 100000) + 50000,
        orders: Math.floor(Math.random() * 500) + 100,
        customers: Math.floor(Math.random() * 200) + 50,
        category: categories[Math.floor(Math.random() * categories.length)]
      });
    }
    
    return data.reverse();
  };

  /**
   * 生成商品数据
   */
  const generateProductData = () => {
    const products = ['iPhone 15', 'MacBook Pro', 'AirPods Pro', 'iPad Air', 'Apple Watch'];
    const categories = ['电子产品', '配件'];
    
    return products.map(productName => ({
      productName,
      sales: Math.floor(Math.random() * 1000) + 100,
      revenue: Math.floor(Math.random() * 1000000) + 100000,
      stock: Math.floor(Math.random() * 500) + 50,
      category: categories[Math.floor(Math.random() * categories.length)]
    }));
  };

  /**
   * 生成客户数据
   */
  const generateCustomerData = () => {
    const types = ['新客户', '老客户', 'VIP客户'];
    const regions = ['华北', '华东', '华南', '华中', '西南'];
    
    return types.map(customerType => ({
      customerType,
      orderCount: Math.floor(Math.random() * 50) + 5,
      totalValue: Math.floor(Math.random() * 100000) + 10000,
      region: regions[Math.floor(Math.random() * regions.length)],
      satisfaction: Math.floor(Math.random() * 50) + 50
    }));
  };

  /**
   * 创建新图表
   */
  const createNewChart = () => {
    const newChart: ChartConfig = {
      id: `chart_${Date.now()}`,
      type: 'line',
      title: '新图表',
      dataSource: dataSources[0]?.id || '',
      xAxis: '',
      yAxis: [],
      colors: ['#8884d8'],
      filters: {},
      customOptions: {}
    };
    
    setSelectedChart(newChart);
    setIsEditing(true);
  };

  /**
   * 保存图表配置
   */
  const saveChart = () => {
    if (!selectedChart) return;
    
    const existingIndex = chartConfigs.findIndex(c => c.id === selectedChart.id);
    if (existingIndex >= 0) {
      const updated = [...chartConfigs];
      updated[existingIndex] = selectedChart;
      setChartConfigs(updated);
    } else {
      setChartConfigs([...chartConfigs, selectedChart]);
    }
    
    setIsEditing(false);
  };

  /**
   * 渲染图表
   */
  const renderChart = (config: ChartConfig) => {
    const dataSource = dataSources.find(ds => ds.id === config.dataSource);
    if (!dataSource) return null;

    const data = dataSource.sampleData;
    
    switch (config.type) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={config.xAxis} />
              <YAxis />
              <Tooltip />
              <Legend />
              {config.yAxis.map((field, index) => (
                <Line 
                  key={field}
                  type="monotone" 
                  dataKey={field} 
                  stroke={config.colors[index] || '#8884d8'} 
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        );
        
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={config.xAxis} />
              <YAxis />
              <Tooltip />
              <Legend />
              {config.yAxis.map((field, index) => (
                <Bar 
                  key={field}
                  dataKey={field} 
                  fill={config.colors[index] || '#8884d8'} 
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );
        
      case 'pie':
        const pieData = data.reduce((acc: any[], item) => {
          const existing = acc.find(a => a.name === item[config.xAxis]);
          if (existing) {
            existing.value += item[config.yAxis[0]] || 0;
          } else {
            acc.push({
              name: item[config.xAxis],
              value: item[config.yAxis[0]] || 0
            });
          }
          return acc;
        }, []);
        
        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={config.colors[index % config.colors.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );
        
      case 'scatter':
        return (
          <ResponsiveContainer width="100%" height={300}>
            <ScatterChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={config.xAxis} />
              <YAxis dataKey={config.yAxis[0]} />
              <Tooltip />
              <Scatter dataKey={config.yAxis[0]} fill={config.colors[0] || '#8884d8'} />
            </ScatterChart>
          </ResponsiveContainer>
        );
        
      default:
        return <div className="text-center text-muted-foreground">不支持的图表类型</div>;
    }
  };

  /**
   * 图表编辑器
   */
  const ChartEditor = () => {
    if (!selectedChart) return null;
    
    const dataSource = dataSources.find(ds => ds.id === selectedChart.dataSource);
    
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="title">图表标题</Label>
            <Input
              id="title"
              value={selectedChart.title}
              onChange={(e) => setSelectedChart({
                ...selectedChart,
                title: e.target.value
              })}
            />
          </div>
          
          <div>
            <Label htmlFor="type">图表类型</Label>
            <Select 
              value={selectedChart.type} 
              onValueChange={(value: any) => setSelectedChart({
                ...selectedChart,
                type: value
              })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="line">折线图</SelectItem>
                <SelectItem value="bar">柱状图</SelectItem>
                <SelectItem value="pie">饼图</SelectItem>
                <SelectItem value="scatter">散点图</SelectItem>
                <SelectItem value="radar">雷达图</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="dataSource">数据源</Label>
            <Select 
              value={selectedChart.dataSource} 
              onValueChange={(value) => setSelectedChart({
                ...selectedChart,
                dataSource: value,
                xAxis: '',
                yAxis: []
              })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {dataSources.map(ds => (
                  <SelectItem key={ds.id} value={ds.id}>{ds.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {dataSource && (
            <div>
              <Label htmlFor="xAxis">X轴字段</Label>
              <Select 
                value={selectedChart.xAxis} 
                onValueChange={(value) => setSelectedChart({
                  ...selectedChart,
                  xAxis: value
                })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {dataSource.fields.map(field => (
                    <SelectItem key={field.name} value={field.name}>{field.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
        
        {dataSource && (
          <div>
            <Label>Y轴字段</Label>
            <div className="grid grid-cols-2 gap-2 mt-2">
              {dataSource.fields
                .filter(field => field.type === 'number')
                .map(field => (
                  <div key={field.name} className="flex items-center space-x-2">
                    <Checkbox
                      id={field.name}
                      checked={selectedChart.yAxis.includes(field.name)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedChart({
                            ...selectedChart,
                            yAxis: [...selectedChart.yAxis, field.name]
                          });
                        } else {
                          setSelectedChart({
                            ...selectedChart,
                            yAxis: selectedChart.yAxis.filter(y => y !== field.name)
                          });
                        }
                      }}
                    />
                    <Label htmlFor={field.name}>{field.label}</Label>
                  </div>
                ))}
            </div>
          </div>
        )}
        
        <div className="flex space-x-2">
          <Button onClick={saveChart}>
            <Save className="h-4 w-4 mr-2" />
            保存图表
          </Button>
          <Button variant="outline" onClick={() => setIsEditing(false)}>
            取消
          </Button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <BarChart3 className="h-6 w-6 mr-2" />
            数据可视化
          </h2>
          <p className="text-muted-foreground">创建和管理自定义数据图表</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={() => setPreviewMode(!previewMode)}>
            <Eye className="h-4 w-4 mr-2" />
            {previewMode ? '编辑模式' : '预览模式'}
          </Button>
          <Button onClick={createNewChart}>
            <Plus className="h-4 w-4 mr-2" />
            新建图表
          </Button>
        </div>
      </div>

      {/* 图表编辑器 */}
      {isEditing && selectedChart && (
        <Card>
          <CardHeader>
            <CardTitle>图表编辑器</CardTitle>
            <CardDescription>配置图表参数和样式</CardDescription>
          </CardHeader>
          <CardContent>
            <ChartEditor />
          </CardContent>
        </Card>
      )}

      {/* 图表预览 */}
      {selectedChart && !previewMode && (
        <Card>
          <CardHeader>
            <CardTitle>{selectedChart.title}</CardTitle>
            <CardDescription>图表预览</CardDescription>
          </CardHeader>
          <CardContent>
            {renderChart(selectedChart)}
          </CardContent>
        </Card>
      )}

      {/* 图表列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {chartConfigs.map((config) => (
          <Card key={config.id} className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{config.title}</CardTitle>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline">{config.type}</Badge>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => {
                      setSelectedChart(config);
                      setIsEditing(true);
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              <CardDescription>
                数据源: {dataSources.find(ds => ds.id === config.dataSource)?.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-48">
                {renderChart(config)}
              </div>
              <div className="flex items-center justify-between mt-4">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setSelectedChart(config)}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  查看
                </Button>
                <div className="flex space-x-2">
                  <Button variant="ghost" size="sm">
                    <Download className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm">
                    <Share className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
