/**
 * 预测分析能力组件
 * 提供销售预测、库存需求预测、客户流失预测和市场趋势预测
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Brain, 
  Target, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  BarChart3,
  Users,
  Package
} from 'lucide-react';

// 预测数据类型定义
interface SalesForecast {
  period: string;
  historical: number | null;
  predicted: number;
  confidence: number;
  upperBound: number;
  lowerBound: number;
}

interface InventoryForecast {
  productId: string;
  productName: string;
  currentStock: number;
  predictedDemand: number;
  recommendedStock: number;
  stockoutRisk: number;
  reorderDate: string;
}

interface CustomerChurnPrediction {
  customerId: string;
  customerName: string;
  churnProbability: number;
  riskLevel: 'low' | 'medium' | 'high';
  lastOrderDate: string;
  predictedValue: number;
  retentionActions: string[];
}

interface MarketTrend {
  category: string;
  currentTrend: 'growing' | 'stable' | 'declining';
  predictedTrend: 'growing' | 'stable' | 'declining';
  confidence: number;
  factors: string[];
}

export default function PredictiveAnalytics() {
  const [selectedModel, setSelectedModel] = useState('lstm');
  const [forecastPeriod, setForecastPeriod] = useState('30d');
  const [loading, setLoading] = useState(false);
  
  // 预测数据状态
  const [salesForecast, setSalesForecast] = useState<SalesForecast[]>([]);
  const [inventoryForecast, setInventoryForecast] = useState<InventoryForecast[]>([]);
  const [churnPredictions, setChurnPredictions] = useState<CustomerChurnPrediction[]>([]);
  const [marketTrends, setMarketTrends] = useState<MarketTrend[]>([]);

  useEffect(() => {
    loadPredictiveData();
  }, [selectedModel, forecastPeriod]);

  /**
   * 加载预测数据
   */
  const loadPredictiveData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 生成模拟预测数据
      setSalesForecast(generateSalesForecast());
      setInventoryForecast(generateInventoryForecast());
      setChurnPredictions(generateChurnPredictions());
      setMarketTrends(generateMarketTrends());
      
    } catch (error) {
      console.error('加载预测数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 生成销售预测数据
   */
  const generateSalesForecast = (): SalesForecast[] => {
    const periods = [];
    const today = new Date();
    
    // 历史数据（过去30天）
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const baseValue = 50000 + Math.sin(i / 7) * 10000;
      periods.push({
        period: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        historical: baseValue + (Math.random() - 0.5) * 5000,
        predicted: null,
        confidence: 0,
        upperBound: 0,
        lowerBound: 0
      });
    }
    
    // 预测数据（未来30天）
    for (let i = 1; i <= 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() + i);
      const baseValue = 55000 + Math.sin(i / 7) * 12000;
      const confidence = Math.max(0.6, 0.95 - i * 0.01);
      const variance = baseValue * (1 - confidence) * 0.5;
      
      periods.push({
        period: date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
        historical: null,
        predicted: baseValue,
        confidence: confidence * 100,
        upperBound: baseValue + variance,
        lowerBound: baseValue - variance
      });
    }
    
    return periods;
  };

  /**
   * 生成库存预测数据
   */
  const generateInventoryForecast = (): InventoryForecast[] => {
    const products = ['iPhone 15', 'MacBook Pro', 'AirPods Pro', 'iPad Air', 'Apple Watch'];
    return products.map((productName, index) => {
      const currentStock = Math.floor(Math.random() * 500) + 100;
      const predictedDemand = Math.floor(Math.random() * 300) + 50;
      const stockoutRisk = currentStock < predictedDemand ? 
        Math.min(90, ((predictedDemand - currentStock) / predictedDemand) * 100) : 
        Math.max(5, Math.random() * 20);
      
      return {
        productId: `prod_${index + 1}`,
        productName,
        currentStock,
        predictedDemand,
        recommendedStock: Math.ceil(predictedDemand * 1.2),
        stockoutRisk,
        reorderDate: new Date(Date.now() + Math.random() * 14 * 24 * 60 * 60 * 1000).toLocaleDateString()
      };
    });
  };

  /**
   * 生成客户流失预测数据
   */
  const generateChurnPredictions = (): CustomerChurnPrediction[] => {
    const customers = ['张三', '李四', '王五', '赵六', '钱七'];
    return customers.map((customerName, index) => {
      const churnProbability = Math.random() * 100;
      const riskLevel = churnProbability > 70 ? 'high' : churnProbability > 40 ? 'medium' : 'low';
      
      return {
        customerId: `cust_${index + 1}`,
        customerName,
        churnProbability,
        riskLevel,
        lastOrderDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toLocaleDateString(),
        predictedValue: Math.floor(Math.random() * 50000) + 10000,
        retentionActions: [
          '发送个性化优惠券',
          '客服主动联系',
          '推荐相关产品',
          '邀请参加VIP活动'
        ].slice(0, Math.floor(Math.random() * 3) + 1)
      };
    });
  };

  /**
   * 生成市场趋势数据
   */
  const generateMarketTrends = (): MarketTrend[] => {
    const categories = ['电子产品', '服装配饰', '家居用品', '图书音像', '运动户外'];
    const trends = ['growing', 'stable', 'declining'] as const;
    
    return categories.map(category => {
      const currentTrend = trends[Math.floor(Math.random() * trends.length)];
      const predictedTrend = trends[Math.floor(Math.random() * trends.length)];
      
      return {
        category,
        currentTrend,
        predictedTrend,
        confidence: Math.floor(Math.random() * 30) + 70,
        factors: [
          '季节性因素',
          '市场竞争',
          '消费者偏好变化',
          '经济环境影响',
          '技术发展趋势'
        ].slice(0, Math.floor(Math.random() * 3) + 2)
      };
    });
  };

  /**
   * 获取风险等级颜色
   */
  const getRiskColor = (level: string) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  /**
   * 获取趋势图标
   */
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'growing': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold flex items-center">
            <Brain className="h-6 w-6 mr-2" />
            预测分析
          </h2>
          <p className="text-muted-foreground">基于AI算法的业务预测和趋势分析</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select value={selectedModel} onValueChange={setSelectedModel}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="lstm">LSTM模型</SelectItem>
              <SelectItem value="arima">ARIMA模型</SelectItem>
              <SelectItem value="prophet">Prophet模型</SelectItem>
              <SelectItem value="ensemble">集成模型</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={forecastPeriod} onValueChange={setForecastPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7天预测</SelectItem>
              <SelectItem value="30d">30天预测</SelectItem>
              <SelectItem value="90d">90天预测</SelectItem>
              <SelectItem value="1y">1年预测</SelectItem>
            </SelectContent>
          </Select>
          
          <Button onClick={loadPredictiveData} disabled={loading}>
            {loading ? <Zap className="h-4 w-4 mr-2 animate-pulse" /> : <Target className="h-4 w-4 mr-2" />}
            {loading ? '预测中...' : '重新预测'}
          </Button>
        </div>
      </div>

      {/* 预测分析标签页 */}
      <Tabs defaultValue="sales" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sales">销售预测</TabsTrigger>
          <TabsTrigger value="inventory">库存预测</TabsTrigger>
          <TabsTrigger value="churn">客户流失</TabsTrigger>
          <TabsTrigger value="market">市场趋势</TabsTrigger>
        </TabsList>

        {/* 销售预测 */}
        <TabsContent value="sales" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>销售预测分析</CardTitle>
              <CardDescription>
                基于{selectedModel.toUpperCase()}模型的{forecastPeriod}销售预测
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={salesForecast}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip 
                    formatter={(value, name) => [
                      `¥${Number(value).toLocaleString()}`, 
                      name === 'historical' ? '历史数据' : 
                      name === 'predicted' ? '预测值' :
                      name === 'upperBound' ? '上限' : '下限'
                    ]}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="historical" 
                    stroke="#8884d8" 
                    strokeWidth={2}
                    name="历史数据"
                    connectNulls={false}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="predicted" 
                    stroke="#82ca9d" 
                    strokeWidth={2}
                    strokeDasharray="5 5"
                    name="预测值"
                    connectNulls={false}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="upperBound" 
                    stroke="none" 
                    fill="#82ca9d" 
                    fillOpacity={0.1}
                    connectNulls={false}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="lowerBound" 
                    stroke="none" 
                    fill="#82ca9d" 
                    fillOpacity={0.1}
                    connectNulls={false}
                  />
                  <ReferenceLine x={salesForecast.findIndex(d => d.predicted !== null)} stroke="red" strokeDasharray="2 2" />
                </LineChart>
              </ResponsiveContainer>
              
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    ¥{salesForecast.filter(d => d.predicted).reduce((sum, d) => sum + (d.predicted || 0), 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-muted-foreground">预测总收入</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {(salesForecast.filter(d => d.predicted).reduce((sum, d) => sum + d.confidence, 0) / 
                      salesForecast.filter(d => d.predicted).length).toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">平均置信度</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">+15.2%</div>
                  <div className="text-sm text-muted-foreground">预期增长率</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 库存预测 */}
        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                库存需求预测
              </CardTitle>
              <CardDescription>基于销售趋势的库存补货建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {inventoryForecast.map((item) => (
                  <div key={item.productId} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-medium">{item.productName}</h4>
                        <p className="text-sm text-muted-foreground">
                          当前库存: {item.currentStock} | 预测需求: {item.predictedDemand}
                        </p>
                      </div>
                      <Badge className={getRiskColor(
                        item.stockoutRisk > 60 ? 'high' : 
                        item.stockoutRisk > 30 ? 'medium' : 'low'
                      )}>
                        {item.stockoutRisk > 60 ? '高风险' : 
                         item.stockoutRisk > 30 ? '中风险' : '低风险'}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="text-sm text-muted-foreground">缺货风险</div>
                        <div className="flex items-center space-x-2">
                          <Progress value={item.stockoutRisk} className="flex-1" />
                          <span className="text-sm font-medium">{item.stockoutRisk.toFixed(1)}%</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">建议库存</div>
                        <div className="text-lg font-bold">{item.recommendedStock}</div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">建议补货日期</div>
                        <div className="text-lg font-bold">{item.reorderDate}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 客户流失预测 */}
        <TabsContent value="churn" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                客户流失预测
              </CardTitle>
              <CardDescription>识别高风险客户并提供挽留建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {churnPredictions
                  .sort((a, b) => b.churnProbability - a.churnProbability)
                  .map((customer) => (
                    <div key={customer.customerId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium">{customer.customerName}</h4>
                          <p className="text-sm text-muted-foreground">
                            最后订单: {customer.lastOrderDate} | 预期价值: ¥{customer.predictedValue.toLocaleString()}
                          </p>
                        </div>
                        <Badge className={getRiskColor(customer.riskLevel)}>
                          {customer.riskLevel === 'high' ? '高风险' : 
                           customer.riskLevel === 'medium' ? '中风险' : '低风险'}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <div className="text-sm text-muted-foreground mb-2">流失概率</div>
                          <div className="flex items-center space-x-2">
                            <Progress value={customer.churnProbability} className="flex-1" />
                            <span className="text-sm font-medium">{customer.churnProbability.toFixed(1)}%</span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm text-muted-foreground mb-2">挽留建议</div>
                          <div className="flex flex-wrap gap-1">
                            {customer.retentionActions.map((action, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {action}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 市场趋势预测 */}
        <TabsContent value="market" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>市场趋势预测</CardTitle>
              <CardDescription>各品类市场趋势分析和预测</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {marketTrends.map((trend) => (
                  <div key={trend.category} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{trend.category}</h4>
                      <div className="text-sm text-muted-foreground">
                        置信度: {trend.confidence}%
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">当前趋势</span>
                        <div className="flex items-center space-x-1">
                          {getTrendIcon(trend.currentTrend)}
                          <span className="text-sm font-medium">
                            {trend.currentTrend === 'growing' ? '增长' :
                             trend.currentTrend === 'declining' ? '下降' : '稳定'}
                          </span>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">预测趋势</span>
                        <div className="flex items-center space-x-1">
                          {getTrendIcon(trend.predictedTrend)}
                          <span className="text-sm font-medium">
                            {trend.predictedTrend === 'growing' ? '增长' :
                             trend.predictedTrend === 'declining' ? '下降' : '稳定'}
                          </span>
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm text-muted-foreground mb-1">影响因素</div>
                        <div className="flex flex-wrap gap-1">
                          {trend.factors.map((factor, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {factor}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
