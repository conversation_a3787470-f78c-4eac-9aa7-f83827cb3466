/**
 * 高级分析功能组件
 * 提供趋势分析、同比环比分析、用户行为分析和商品表现分析
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  ScatterChart,
  Scatter,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  ComposedChart
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  <PERSON><PERSON><PERSON> as PieChartIcon, 
  Users, 
  ShoppingBag,
  Calendar,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

// 分析数据类型定义
interface TrendData {
  period: string;
  current: number;
  previous: number;
  growth: number;
}

interface ComparisonData {
  category: string;
  thisYear: number;
  lastYear: number;
  growth: number;
}

interface UserBehaviorData {
  page: string;
  views: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgTime: number;
}

interface ProductPerformanceData {
  productId: string;
  productName: string;
  sales: number;
  revenue: number;
  profit: number;
  margin: number;
  trend: 'up' | 'down' | 'stable';
}

export default function AdvancedAnalytics() {
  const [selectedPeriod, setSelectedPeriod] = useState('30d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [loading, setLoading] = useState(false);
  
  // 模拟数据
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  const [comparisonData, setComparisonData] = useState<ComparisonData[]>([]);
  const [userBehaviorData, setUserBehaviorData] = useState<UserBehaviorData[]>([]);
  const [productPerformanceData, setProductPerformanceData] = useState<ProductPerformanceData[]>([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod, selectedMetric]);

  /**
   * 加载分析数据
   */
  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 生成模拟趋势数据
      const trends = generateTrendData();
      setTrendData(trends);
      
      // 生成模拟对比数据
      const comparisons = generateComparisonData();
      setComparisonData(comparisons);
      
      // 生成模拟用户行为数据
      const userBehavior = generateUserBehaviorData();
      setUserBehaviorData(userBehavior);
      
      // 生成模拟商品表现数据
      const productPerformance = generateProductPerformanceData();
      setProductPerformanceData(productPerformance);
      
    } catch (error) {
      console.error('加载分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 生成趋势数据
   */
  const generateTrendData = (): TrendData[] => {
    const periods = ['1月', '2月', '3月', '4月', '5月', '6月'];
    return periods.map(period => ({
      period,
      current: Math.floor(Math.random() * 100000) + 50000,
      previous: Math.floor(Math.random() * 80000) + 40000,
      growth: Math.floor(Math.random() * 40) - 10
    }));
  };

  /**
   * 生成对比数据
   */
  const generateComparisonData = (): ComparisonData[] => {
    const categories = ['电子产品', '服装', '家居', '图书', '运动'];
    return categories.map(category => ({
      category,
      thisYear: Math.floor(Math.random() * 500000) + 100000,
      lastYear: Math.floor(Math.random() * 400000) + 80000,
      growth: Math.floor(Math.random() * 60) - 20
    }));
  };

  /**
   * 生成用户行为数据
   */
  const generateUserBehaviorData = (): UserBehaviorData[] => {
    const pages = ['首页', '商品列表', '商品详情', '购物车', '结算页'];
    return pages.map(page => ({
      page,
      views: Math.floor(Math.random() * 10000) + 1000,
      uniqueVisitors: Math.floor(Math.random() * 5000) + 500,
      bounceRate: Math.floor(Math.random() * 50) + 20,
      avgTime: Math.floor(Math.random() * 300) + 60
    }));
  };

  /**
   * 生成商品表现数据
   */
  const generateProductPerformanceData = (): ProductPerformanceData[] => {
    const products = ['iPhone 15', 'MacBook Pro', 'AirPods', 'iPad', 'Apple Watch'];
    return products.map((productName, index) => ({
      productId: `prod_${index + 1}`,
      productName,
      sales: Math.floor(Math.random() * 1000) + 100,
      revenue: Math.floor(Math.random() * 1000000) + 100000,
      profit: Math.floor(Math.random() * 300000) + 50000,
      margin: Math.floor(Math.random() * 30) + 10,
      trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as 'up' | 'down' | 'stable'
    }));
  };

  /**
   * 导出数据
   */
  const exportData = (type: string) => {
    console.log(`导出${type}数据`);
    // 实际实现中会调用导出API
  };

  /**
   * 获取趋势图标
   */
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  /**
   * 格式化数值
   */
  const formatNumber = (value: number, type: 'currency' | 'percent' | 'number' = 'number') => {
    switch (type) {
      case 'currency':
        return `¥${value.toLocaleString()}`;
      case 'percent':
        return `${value.toFixed(1)}%`;
      default:
        return value.toLocaleString();
    }
  };

  return (
    <div className="space-y-6">
      {/* 控制面板 */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h2 className="text-2xl font-bold">高级数据分析</h2>
          <p className="text-muted-foreground">深度分析业务数据，洞察商业趋势</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">最近7天</SelectItem>
              <SelectItem value="30d">最近30天</SelectItem>
              <SelectItem value="90d">最近90天</SelectItem>
              <SelectItem value="1y">最近1年</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={selectedMetric} onValueChange={setSelectedMetric}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="revenue">收入</SelectItem>
              <SelectItem value="orders">订单</SelectItem>
              <SelectItem value="customers">客户</SelectItem>
              <SelectItem value="products">商品</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={loadAnalyticsData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>
      </div>

      {/* 分析标签页 */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trends">趋势分析</TabsTrigger>
          <TabsTrigger value="comparison">同比环比</TabsTrigger>
          <TabsTrigger value="behavior">用户行为</TabsTrigger>
          <TabsTrigger value="products">商品表现</TabsTrigger>
        </TabsList>

        {/* 趋势分析 */}
        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>趋势分析</CardTitle>
                  <CardDescription>
                    {selectedMetric === 'revenue' ? '收入' : 
                     selectedMetric === 'orders' ? '订单' :
                     selectedMetric === 'customers' ? '客户' : '商品'}趋势变化分析
                  </CardDescription>
                </div>
                <Button variant="outline" onClick={() => exportData('trend')}>
                  <Download className="h-4 w-4 mr-2" />
                  导出
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <ComposedChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip formatter={(value) => formatNumber(Number(value), 'currency')} />
                  <Legend />
                  <Area 
                    type="monotone" 
                    dataKey="current" 
                    fill="#8884d8" 
                    stroke="#8884d8"
                    name="当前期间"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="previous" 
                    stroke="#82ca9d"
                    name="上期对比"
                  />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 同比环比分析 */}
        <TabsContent value="comparison" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>分类对比分析</CardTitle>
                <CardDescription>各分类同比增长情况</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={comparisonData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="category" />
                    <YAxis />
                    <Tooltip formatter={(value) => formatNumber(Number(value), 'currency')} />
                    <Legend />
                    <Bar dataKey="thisYear" fill="#8884d8" name="今年" />
                    <Bar dataKey="lastYear" fill="#82ca9d" name="去年" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>增长率排行</CardTitle>
                <CardDescription>各分类增长率对比</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {comparisonData
                    .sort((a, b) => b.growth - a.growth)
                    .map((item, index) => (
                      <div key={item.category} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Badge variant="outline">#{index + 1}</Badge>
                          <span className="font-medium">{item.category}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getTrendIcon(item.growth > 0 ? 'up' : item.growth < 0 ? 'down' : 'stable')}
                          <span className={`font-bold ${
                            item.growth > 0 ? 'text-green-600' : 
                            item.growth < 0 ? 'text-red-600' : 'text-gray-600'
                          }`}>
                            {item.growth > 0 ? '+' : ''}{item.growth.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* 用户行为分析 */}
        <TabsContent value="behavior" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                用户行为分析
              </CardTitle>
              <CardDescription>页面访问和用户行为数据</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">页面</th>
                      <th className="text-right p-2">浏览量</th>
                      <th className="text-right p-2">独立访客</th>
                      <th className="text-right p-2">跳出率</th>
                      <th className="text-right p-2">平均停留时间</th>
                    </tr>
                  </thead>
                  <tbody>
                    {userBehaviorData.map((item) => (
                      <tr key={item.page} className="border-b">
                        <td className="p-2 font-medium">{item.page}</td>
                        <td className="p-2 text-right">{formatNumber(item.views)}</td>
                        <td className="p-2 text-right">{formatNumber(item.uniqueVisitors)}</td>
                        <td className="p-2 text-right">
                          <span className={`${
                            item.bounceRate > 60 ? 'text-red-600' : 
                            item.bounceRate > 40 ? 'text-yellow-600' : 'text-green-600'
                          }`}>
                            {formatNumber(item.bounceRate, 'percent')}
                          </span>
                        </td>
                        <td className="p-2 text-right">
                          {Math.floor(item.avgTime / 60)}分{item.avgTime % 60}秒
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 商品表现分析 */}
        <TabsContent value="products" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingBag className="h-5 w-5 mr-2" />
                商品表现分析
              </CardTitle>
              <CardDescription>热销商品和盈利能力分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* 商品销售排行 */}
                <div>
                  <h4 className="font-medium mb-4">销售排行榜</h4>
                  <div className="space-y-3">
                    {productPerformanceData
                      .sort((a, b) => b.sales - a.sales)
                      .map((product, index) => (
                        <div key={product.productId} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <Badge variant="outline">#{index + 1}</Badge>
                            <div>
                              <div className="font-medium">{product.productName}</div>
                              <div className="text-sm text-muted-foreground">
                                销量: {formatNumber(product.sales)}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold">{formatNumber(product.revenue, 'currency')}</div>
                            <div className="flex items-center space-x-1">
                              {getTrendIcon(product.trend)}
                              <span className="text-sm text-muted-foreground">
                                {formatNumber(product.margin, 'percent')}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>

                {/* 利润分析图表 */}
                <div>
                  <h4 className="font-medium mb-4">利润分析</h4>
                  <ResponsiveContainer width="100%" height={300}>
                    <ScatterChart data={productPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="sales" name="销量" />
                      <YAxis dataKey="profit" name="利润" />
                      <Tooltip 
                        formatter={(value, name) => [formatNumber(Number(value), name === '利润' ? 'currency' : 'number'), name]}
                        labelFormatter={(value) => `销量: ${formatNumber(Number(value))}`}
                      />
                      <Scatter dataKey="profit" fill="#8884d8" />
                    </ScatterChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
