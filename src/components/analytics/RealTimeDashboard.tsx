/**
 * 实时数据仪表板组件
 * 提供实时数据监控、关键指标展示和预警功能
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  PieChart, 
  Pie, 
  Cell,
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from 'recharts';
import { 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  ShoppingCart, 
  DollarSign,
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap
} from 'lucide-react';

// 实时数据类型定义
interface RealTimeMetric {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  changePercent: number;
  status: 'normal' | 'warning' | 'critical';
  lastUpdated: Date;
}

interface RealTimeData {
  timestamp: Date;
  sales: number;
  orders: number;
  visitors: number;
  conversion: number;
}

interface AlertItem {
  id: string;
  type: 'info' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
}

export default function RealTimeDashboard() {
  const [isConnected, setIsConnected] = useState(false);
  const [metrics, setMetrics] = useState<RealTimeMetric[]>([]);
  const [realtimeData, setRealtimeData] = useState<RealTimeData[]>([]);
  const [alerts, setAlerts] = useState<AlertItem[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');
  const wsRef = useRef<WebSocket | null>(null);

  // 初始化WebSocket连接
  useEffect(() => {
    connectWebSocket();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  /**
   * 建立WebSocket连接
   */
  const connectWebSocket = () => {
    try {
      const wsUrl = process.env.NODE_ENV === 'production' 
        ? 'wss://api.cbec-erp.com/ws/realtime'
        : 'ws://localhost:3001/ws/realtime';
      
      wsRef.current = new WebSocket(wsUrl);
      
      wsRef.current.onopen = () => {
        console.log('实时数据连接已建立');
        setIsConnected(true);
        
        // 订阅实时数据
        wsRef.current?.send(JSON.stringify({
          type: 'subscribe',
          channels: ['metrics', 'sales', 'alerts']
        }));
      };
      
      wsRef.current.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleRealtimeMessage(data);
      };
      
      wsRef.current.onclose = () => {
        console.log('实时数据连接已断开');
        setIsConnected(false);
        
        // 5秒后重连
        setTimeout(connectWebSocket, 5000);
      };
      
      wsRef.current.onerror = (error) => {
        console.error('WebSocket连接错误:', error);
        setIsConnected(false);
      };
      
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      setIsConnected(false);
    }
  };

  /**
   * 处理实时消息
   */
  const handleRealtimeMessage = (data: any) => {
    switch (data.type) {
      case 'metrics':
        setMetrics(data.payload);
        break;
      case 'sales_data':
        setRealtimeData(prev => {
          const newData = [...prev, data.payload];
          // 保持最近100个数据点
          return newData.slice(-100);
        });
        break;
      case 'alert':
        setAlerts(prev => [data.payload, ...prev.slice(0, 9)]);
        break;
      default:
        console.log('未知消息类型:', data.type);
    }
  };

  /**
   * 获取趋势图标
   */
  const getTrendIcon = (trend: string, changePercent: number) => {
    if (trend === 'up') {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (trend === 'down') {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  /**
   * 格式化数值
   */
  const formatValue = (value: number, unit: string) => {
    if (unit === 'currency') {
      return `¥${value.toLocaleString()}`;
    } else if (unit === 'percent') {
      return `${value.toFixed(1)}%`;
    } else if (unit === 'count') {
      return value.toLocaleString();
    }
    return value.toString();
  };

  /**
   * 确认警报
   */
  const acknowledgeAlert = (alertId: string) => {
    setAlerts(prev => 
      prev.map(alert => 
        alert.id === alertId 
          ? { ...alert, acknowledged: true }
          : alert
      )
    );
  };

  // 图表颜色配置
  const chartColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00'];

  return (
    <div className="space-y-6">
      {/* 连接状态指示器 */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">实时数据仪表板</h2>
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm text-muted-foreground">
            {isConnected ? '实时连接' : '连接断开'}
          </span>
          <Button 
            variant="outline" 
            size="sm"
            onClick={connectWebSocket}
            disabled={isConnected}
          >
            重新连接
          </Button>
        </div>
      </div>

      {/* 关键指标卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric) => (
          <Card key={metric.id} className="relative">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.name}</CardTitle>
              <div className="flex items-center space-x-1">
                {getTrendIcon(metric.trend, metric.changePercent)}
                <div className={`w-2 h-2 rounded-full ${getStatusColor(metric.status)}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatValue(metric.value, metric.unit)}
              </div>
              <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                <span className={metric.changePercent >= 0 ? 'text-green-600' : 'text-red-600'}>
                  {metric.changePercent >= 0 ? '+' : ''}{metric.changePercent.toFixed(1)}%
                </span>
                <span>vs 上期</span>
              </div>
              <div className="text-xs text-muted-foreground mt-1">
                更新时间: {metric.lastUpdated.toLocaleTimeString()}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 实时图表和警报 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 实时销售趋势 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="h-5 w-5 mr-2" />
              实时销售趋势
            </CardTitle>
            <CardDescription>
              过去{selectedTimeRange}的销售数据变化
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <Tabs value={selectedTimeRange} onValueChange={setSelectedTimeRange}>
                <TabsList>
                  <TabsTrigger value="1h">1小时</TabsTrigger>
                  <TabsTrigger value="6h">6小时</TabsTrigger>
                  <TabsTrigger value="24h">24小时</TabsTrigger>
                  <TabsTrigger value="7d">7天</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
            
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={realtimeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={(value) => new Date(value).toLocaleTimeString()}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                  formatter={(value, name) => [
                    typeof value === 'number' ? value.toLocaleString() : value, 
                    name
                  ]}
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="sales" 
                  stackId="1" 
                  stroke="#8884d8" 
                  fill="#8884d8" 
                  name="销售额"
                />
                <Area 
                  type="monotone" 
                  dataKey="orders" 
                  stackId="2" 
                  stroke="#82ca9d" 
                  fill="#82ca9d" 
                  name="订单数"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* 实时警报 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              实时警报
            </CardTitle>
            <CardDescription>
              系统监控和业务警报
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-80 overflow-y-auto">
              {alerts.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                  <p>暂无警报</p>
                </div>
              ) : (
                alerts.map((alert) => (
                  <div 
                    key={alert.id} 
                    className={`p-3 rounded-lg border ${
                      alert.acknowledged ? 'opacity-50' : ''
                    } ${
                      alert.type === 'error' ? 'border-red-200 bg-red-50' :
                      alert.type === 'warning' ? 'border-yellow-200 bg-yellow-50' :
                      'border-blue-200 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={
                            alert.type === 'error' ? 'destructive' :
                            alert.type === 'warning' ? 'secondary' : 'default'
                          }>
                            {alert.type === 'error' ? '错误' :
                             alert.type === 'warning' ? '警告' : '信息'}
                          </Badge>
                          <span className="text-sm font-medium">{alert.title}</span>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {alert.message}
                        </p>
                        <div className="flex items-center space-x-2 mt-2 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>{alert.timestamp.toLocaleString()}</span>
                        </div>
                      </div>
                      {!alert.acknowledged && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => acknowledgeAlert(alert.id)}
                        >
                          确认
                        </Button>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 实时性能指标 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            系统性能监控
          </CardTitle>
          <CardDescription>
            实时系统性能和资源使用情况
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* CPU使用率 */}
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">45%</div>
              <div className="text-sm text-muted-foreground">CPU使用率</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }} />
              </div>
            </div>

            {/* 内存使用率 */}
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">62%</div>
              <div className="text-sm text-muted-foreground">内存使用率</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '62%' }} />
              </div>
            </div>

            {/* 响应时间 */}
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">120ms</div>
              <div className="text-sm text-muted-foreground">平均响应时间</div>
              <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div className="bg-yellow-600 h-2 rounded-full" style={{ width: '30%' }} />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
