/**
 * 语言切换组件
 * 提供用户界面的语言切换功能
 */

'use client';

import React, { useState } from 'react';
import { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { CheckIcon } from '@heroicons/react/20/solid';
import { useTranslation, useLanguageSwitch } from '@/hooks/useTranslation';
import { SupportedLanguage, getActiveLanguages } from '@/lib/i18n/config';

// 语言切换器属性
interface LanguageSwitcherProps {
  variant?: 'dropdown' | 'button' | 'compact';
  showFlag?: boolean;
  showNativeName?: boolean;
  showEnglishName?: boolean;
  className?: string;
  disabled?: boolean;
  placement?: 'bottom-start' | 'bottom-end' | 'top-start' | 'top-end';
}

/**
 * 语言切换器组件
 */
export function LanguageSwitcher({
  variant = 'dropdown',
  showFlag = true,
  showNativeName = true,
  showEnglishName = false,
  className = '',
  disabled = false,
  placement = 'bottom-start',
}: LanguageSwitcherProps) {
  const { tSync } = useTranslation();
  const { currentLanguage, changeLanguage, currentLanguageInfo } = useLanguageSwitch();
  const [isOpen, setIsOpen] = useState(false);
  const [isChanging, setIsChanging] = useState(false);

  const activeLanguages = getActiveLanguages();

  const handleLanguageChange = async (language: SupportedLanguage) => {
    if (language === currentLanguage || isChanging) return;

    try {
      setIsChanging(true);
      await changeLanguage(language);
      setIsOpen(false);
    } catch (error) {
      console.error('语言切换失败:', error);
    } finally {
      setIsChanging(false);
    }
  };

  const renderLanguageOption = (language: any, isCurrent: boolean = false) => (
    <div className="flex items-center space-x-2">
      {showFlag && (
        <span className="text-lg" role="img" aria-label={language.name}>
          {language.flag}
        </span>
      )}
      <div className="flex flex-col">
        {showNativeName && (
          <span className={`text-sm ${isCurrent ? 'font-medium' : ''}`}>
            {language.nativeName}
          </span>
        )}
        {showEnglishName && (
          <span className="text-xs text-gray-500">
            {language.name}
          </span>
        )}
      </div>
    </div>
  );

  if (variant === 'button') {
    return (
      <div className={`relative ${className}`}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled || isChanging}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
        >
          <GlobeAltIcon className="h-4 w-4 mr-2" />
          {renderLanguageOption(currentLanguageInfo, true)}
          <ChevronDownIcon className="ml-2 h-4 w-4" />
        </button>

        {isOpen && (
          <div className={`absolute z-50 mt-1 w-56 bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm ${
            placement.includes('end') ? 'right-0' : 'left-0'
          } ${
            placement.includes('top') ? 'bottom-full mb-1' : 'top-full'
          }`}>
            {activeLanguages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                disabled={isChanging}
                className={`w-full text-left px-4 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none disabled:opacity-50 ${
                  language.code === currentLanguage ? 'bg-indigo-50 text-indigo-900' : 'text-gray-900'
                }`}
              >
                <div className="flex items-center justify-between">
                  {renderLanguageOption(language)}
                  {language.code === currentLanguage && (
                    <CheckIcon className="h-4 w-4 text-indigo-600" />
                  )}
                </div>
              </button>
            ))}
          </div>
        )}

        {/* 点击外部关闭下拉菜单 */}
        {isOpen && (
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={`relative ${className}`}>
        <button
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled || isChanging}
          className="inline-flex items-center p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label={tSync('common.changeLanguage')}
        >
          {showFlag ? (
            <span className="text-lg" role="img" aria-label={currentLanguageInfo?.name}>
              {currentLanguageInfo?.flag}
            </span>
          ) : (
            <GlobeAltIcon className="h-5 w-5" />
          )}
        </button>

        {isOpen && (
          <div className={`absolute z-50 mt-1 w-48 bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm ${
            placement.includes('end') ? 'right-0' : 'left-0'
          } ${
            placement.includes('top') ? 'bottom-full mb-1' : 'top-full'
          }`}>
            {activeLanguages.map((language) => (
              <button
                key={language.code}
                onClick={() => handleLanguageChange(language.code)}
                disabled={isChanging}
                className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none disabled:opacity-50 ${
                  language.code === currentLanguage ? 'bg-indigo-50 text-indigo-900' : 'text-gray-900'
                }`}
              >
                <div className="flex items-center justify-between">
                  {renderLanguageOption(language)}
                  {language.code === currentLanguage && (
                    <CheckIcon className="h-4 w-4 text-indigo-600" />
                  )}
                </div>
              </button>
            ))}
          </div>
        )}

        {isOpen && (
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />
        )}
      </div>
    );
  }

  // 默认下拉菜单样式
  return (
    <div className={`relative ${className}`}>
      <select
        value={currentLanguage}
        onChange={(e) => handleLanguageChange(e.target.value as SupportedLanguage)}
        disabled={disabled || isChanging}
        className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {activeLanguages.map((language) => (
          <option key={language.code} value={language.code}>
            {showFlag && `${language.flag} `}
            {showNativeName && language.nativeName}
            {showEnglishName && ` (${language.name})`}
          </option>
        ))}
      </select>
    </div>
  );
}

/**
 * 语言状态指示器组件
 */
export function LanguageStatusIndicator() {
  const { currentLanguage, currentLanguageInfo } = useLanguageSwitch();
  const { tSync } = useTranslation();

  if (!currentLanguageInfo) return null;

  return (
    <div className="flex items-center space-x-2 text-sm text-gray-600">
      <GlobeAltIcon className="h-4 w-4" />
      <span>{currentLanguageInfo.flag}</span>
      <span>{currentLanguageInfo.nativeName}</span>
      {currentLanguageInfo.completeness < 100 && (
        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
          {Math.round(currentLanguageInfo.completeness)}% {tSync('common.translated')}
        </span>
      )}
    </div>
  );
}

/**
 * 语言完成度进度条组件
 */
export function LanguageCompletenessBar({ 
  language, 
  showPercentage = true,
  className = '' 
}: { 
  language?: SupportedLanguage;
  showPercentage?: boolean;
  className?: string;
}) {
  const { currentLanguageInfo } = useLanguageSwitch();
  const { tSync } = useTranslation();
  
  const languageInfo = language ? getActiveLanguages().find(l => l.code === language) : currentLanguageInfo;
  
  if (!languageInfo) return null;

  const completeness = languageInfo.completeness;
  const isComplete = completeness >= 100;
  const isNearComplete = completeness >= 90;

  return (
    <div className={`space-y-1 ${className}`}>
      <div className="flex justify-between text-sm">
        <span className="text-gray-600">
          {tSync('common.translationProgress')}
        </span>
        {showPercentage && (
          <span className={`font-medium ${
            isComplete ? 'text-green-600' : 
            isNearComplete ? 'text-yellow-600' : 
            'text-red-600'
          }`}>
            {Math.round(completeness)}%
          </span>
        )}
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isComplete ? 'bg-green-500' : 
            isNearComplete ? 'bg-yellow-500' : 
            'bg-red-500'
          }`}
          style={{ width: `${Math.min(completeness, 100)}%` }}
        />
      </div>
    </div>
  );
}

/**
 * 语言网格选择器组件
 */
export function LanguageGridSelector({ 
  onLanguageSelect,
  selectedLanguage,
  className = '' 
}: {
  onLanguageSelect?: (language: SupportedLanguage) => void;
  selectedLanguage?: SupportedLanguage;
  className?: string;
}) {
  const { currentLanguage, changeLanguage } = useLanguageSwitch();
  const [isChanging, setIsChanging] = useState(false);
  
  const activeLanguages = getActiveLanguages();
  const selected = selectedLanguage || currentLanguage;

  const handleSelect = async (language: SupportedLanguage) => {
    if (onLanguageSelect) {
      onLanguageSelect(language);
    } else {
      if (language === currentLanguage || isChanging) return;
      
      try {
        setIsChanging(true);
        await changeLanguage(language);
      } catch (error) {
        console.error('语言切换失败:', error);
      } finally {
        setIsChanging(false);
      }
    }
  };

  return (
    <div className={`grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 ${className}`}>
      {activeLanguages.map((language) => (
        <button
          key={language.code}
          onClick={() => handleSelect(language.code)}
          disabled={isChanging}
          className={`relative p-3 rounded-lg border-2 transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed ${
            language.code === selected
              ? 'border-indigo-500 bg-indigo-50 text-indigo-900'
              : 'border-gray-200 bg-white text-gray-900 hover:border-gray-300'
          }`}
        >
          <div className="flex flex-col items-center space-y-2">
            <span className="text-2xl" role="img" aria-label={language.name}>
              {language.flag}
            </span>
            <div className="text-center">
              <div className="text-sm font-medium">
                {language.nativeName}
              </div>
              <div className="text-xs text-gray-500">
                {language.name}
              </div>
            </div>
            {language.completeness < 100 && (
              <div className="text-xs text-yellow-600">
                {Math.round(language.completeness)}%
              </div>
            )}
          </div>
          
          {language.code === selected && (
            <div className="absolute top-2 right-2">
              <CheckIcon className="h-4 w-4 text-indigo-600" />
            </div>
          )}
        </button>
      ))}
    </div>
  );
}
