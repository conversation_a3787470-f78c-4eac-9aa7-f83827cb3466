/**
 * 跨境电商ERP系统类型定义
 * 定义系统中使用的所有TypeScript类型和接口
 */

// =============================================================================
// 基础类型定义
// =============================================================================

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
    field?: string;
  };
  pagination?: PaginationInfo;
  metadata?: {
    timestamp: string;
    requestId: string;
    version: string;
    processingTime?: number;
  };
}

// 分页信息类型
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 分页查询参数
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

// 过滤参数基础类型
export interface FilterParams {
  search?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
  [key: string]: any;
}

// =============================================================================
// 用户管理相关类型
// =============================================================================

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  DELETED = 'DELETED',
}

// 用户基础信息
export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  avatarUrl?: string;
  languageCode: string;
  timezone: string;
  status: UserStatus;
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  roles?: Role[];
}

// 角色信息
export interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  permissions?: Permission[];
}

// 权限信息
export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

// 用户创建请求
export interface CreateUserRequest {
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  password: string;
  phone?: string;
  languageCode?: string;
  timezone?: string;
  roles?: string[];
}

// 用户更新请求
export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  languageCode?: string;
  timezone?: string;
  status?: UserStatus;
  roles?: string[];
}

// =============================================================================
// 商品管理相关类型
// =============================================================================

// 商品状态枚举
export enum ProductStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DISCONTINUED = 'DISCONTINUED',
}

// 商品基础信息
export interface Product {
  id: string;
  sku: string;
  name: string;
  description?: string;
  shortDescription?: string;
  categoryId?: string;
  brand?: string;
  model?: string;
  weight?: number;
  dimensions?: ProductDimensions;
  basePrice: number;
  costPrice?: number;
  currency: string;
  taxClass?: string;
  status: ProductStatus;
  isDigital: boolean;
  requiresShipping: boolean;
  trackInventory: boolean;
  allowBackorder: boolean;
  metaData?: Record<string, any>;
  seoTitle?: string;
  seoDescription?: string;
  createdAt: Date;
  updatedAt: Date;
  category?: Category;
  variants?: ProductVariant[];
  images?: ProductImage[];
  translations?: ProductTranslation[];
}

// 商品尺寸信息
export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: 'cm' | 'inch';
}

// 商品分类
export interface Category {
  id: string;
  parentId?: string;
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  sortOrder: number;
  isActive: boolean;
  seoTitle?: string;
  seoDescription?: string;
  children?: Category[];
  translations?: CategoryTranslation[];
}

// 分类多语言
export interface CategoryTranslation {
  id: string;
  categoryId: string;
  languageCode: string;
  name: string;
  description?: string;
  seoTitle?: string;
  seoDescription?: string;
}

// 商品变体
export interface ProductVariant {
  id: string;
  productId: string;
  sku: string;
  name?: string;
  price?: number;
  costPrice?: number;
  weight?: number;
  dimensions?: ProductDimensions;
  attributes?: Record<string, any>;
  imageUrl?: string;
  isActive: boolean;
}

// 商品图片
export interface ProductImage {
  id: string;
  productId: string;
  variantId?: string;
  url: string;
  altText?: string;
  sortOrder: number;
  isPrimary: boolean;
}

// 商品多语言
export interface ProductTranslation {
  id: string;
  productId: string;
  languageCode: string;
  name: string;
  description?: string;
  shortDescription?: string;
  seoTitle?: string;
  seoDescription?: string;
}

// 商品创建请求
export interface CreateProductRequest {
  sku: string;
  name: string;
  description?: string;
  shortDescription?: string;
  categoryId?: string;
  brand?: string;
  model?: string;
  weight?: number;
  dimensions?: ProductDimensions;
  basePrice: number;
  costPrice?: number;
  currency?: string;
  taxClass?: string;
  isDigital?: boolean;
  requiresShipping?: boolean;
  trackInventory?: boolean;
  allowBackorder?: boolean;
  metaData?: Record<string, any>;
  seoTitle?: string;
  seoDescription?: string;
  variants?: Omit<ProductVariant, 'id' | 'productId'>[];
  images?: Omit<ProductImage, 'id' | 'productId'>[];
  translations?: Omit<ProductTranslation, 'id' | 'productId'>[];
}

// =============================================================================
// 订单管理相关类型
// =============================================================================

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PROCESSING = 'PROCESSING',
  SHIPPED = 'SHIPPED',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

// 地址类型枚举
export enum AddressType {
  BILLING = 'BILLING',
  SHIPPING = 'SHIPPING',
}

// 地址信息
export interface Address {
  firstName?: string;
  lastName?: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  stateProvince?: string;
  postalCode?: string;
  countryCode: string;
  phone?: string;
}

// 客户信息
export interface Customer {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  languageCode: string;
  currencyCode: string;
  timezone: string;
  customerGroup: string;
  totalOrders: number;
  totalSpent: number;
  averageOrderValue: number;
  lastOrderAt?: Date;
  notes?: string;
  tags: string[];
  marketingConsent: boolean;
  addresses?: CustomerAddress[];
}

// 客户地址
export interface CustomerAddress extends Address {
  id: string;
  customerId: string;
  type: AddressType;
  isDefault: boolean;
}

// 订单信息
export interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  status: OrderStatus;
  currency: string;
  exchangeRate: number;
  subtotal: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  totalAmount: number;
  billingAddress: Address;
  shippingAddress: Address;
  notes?: string;
  internalNotes?: string;
  source?: string;
  sourceOrderId?: string;
  orderDate: Date;
  confirmedAt?: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
  cancelledAt?: Date;
  customer?: Customer;
  items?: OrderItem[];
  payments?: Payment[];
  shipments?: Shipment[];
}

// 订单商品
export interface OrderItem {
  id: string;
  orderId: string;
  productId?: string;
  variantId?: string;
  sku: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  costPrice?: number;
  weight?: number;
  productData?: Record<string, any>;
}

// =============================================================================
// 支付管理相关类型
// =============================================================================

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REFUNDED = 'REFUNDED',
}

// 支付信息
export interface Payment {
  id: string;
  orderId: string;
  paymentMethod: string;
  paymentProvider: string;
  providerPaymentId?: string;
  status: PaymentStatus;
  amount: number;
  currency: string;
  exchangeRate: number;
  feeAmount: number;
  providerResponse?: Record<string, any>;
  failureReason?: string;
  processedAt?: Date;
  refunds?: Refund[];
}

// 退款信息
export interface Refund {
  id: string;
  paymentId: string;
  orderId: string;
  amount: number;
  currency: string;
  reason?: string;
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  providerRefundId?: string;
  providerResponse?: Record<string, any>;
  processedAt?: Date;
}

// =============================================================================
// 库存管理相关类型
// =============================================================================

// 库存变动类型枚举
export enum InventoryMovementType {
  IN = 'IN',
  OUT = 'OUT',
  ADJUSTMENT = 'ADJUSTMENT',
  TRANSFER = 'TRANSFER',
}

// 仓库信息
export interface Warehouse {
  id: string;
  code: string;
  name: string;
  address: Address;
  contactInfo?: Record<string, any>;
  isActive: boolean;
  isDefault: boolean;
}

// 库存信息
export interface Inventory {
  id: string;
  warehouseId: string;
  productId?: string;
  variantId?: string;
  sku: string;
  quantityOnHand: number;
  quantityReserved: number;
  quantityAvailable: number;
  reorderPoint: number;
  reorderQuantity: number;
  costPerUnit?: number;
  lastCountedAt?: Date;
  warehouse?: Warehouse;
  product?: Product;
  variant?: ProductVariant;
}

// 库存变动记录
export interface InventoryMovement {
  id: string;
  inventoryId: string;
  warehouseId: string;
  movementType: InventoryMovementType;
  quantity: number;
  referenceType?: string;
  referenceId?: string;
  reason?: string;
  costPerUnit?: number;
  notes?: string;
  createdAt: Date;
  createdBy?: string;
}

// =============================================================================
// 物流管理相关类型
// =============================================================================

// 发货状态枚举
export enum ShipmentStatus {
  PENDING = 'PENDING',
  PICKED_UP = 'PICKED_UP',
  IN_TRANSIT = 'IN_TRANSIT',
  OUT_FOR_DELIVERY = 'OUT_FOR_DELIVERY',
  DELIVERED = 'DELIVERED',
  EXCEPTION = 'EXCEPTION',
  RETURNED = 'RETURNED',
}

// 物流承运商
export interface ShippingCarrier {
  id: string;
  name: string;
  code: string;
  apiEndpoint?: string;
  apiKey?: string;
  isActive: boolean;
}

// 发货信息
export interface Shipment {
  id: string;
  orderId: string;
  carrierId: string;
  trackingNumber?: string;
  serviceType?: string;
  status: ShipmentStatus;
  shippingCost?: number;
  weight?: number;
  dimensions?: ProductDimensions;
  shippingAddress: Address;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  shippedAt?: Date;
  carrier?: ShippingCarrier;
  trackingEvents?: TrackingEvent[];
}

// 物流跟踪事件
export interface TrackingEvent {
  id: string;
  shipmentId: string;
  status: string;
  description: string;
  location?: string;
  timestamp: Date;
}
