/**
 * AI智能功能仪表板页面
 * 展示所有AI服务的状态和快速访问入口
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  Package, 
  MessageSquare, 
  Activity,
  CheckCircle,
  XCircle,
  Clock,
  Zap
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ServiceStatus {
  isInitialized: boolean;
  isReady: boolean;
  timestamp: string;
}

interface AIServiceCard {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: ServiceStatus | null;
  loading: boolean;
  href: string;
}

export default function AIPage() {
  const { toast } = useToast();
  const [services, setServices] = useState<AIServiceCard[]>([
    {
      id: 'recommendations',
      title: '智能商品推荐',
      description: '基于用户行为和商品特征的个性化推荐系统',
      icon: <Brain className="h-6 w-6" />,
      status: null,
      loading: true,
      href: '/ai/recommendations'
    },
    {
      id: 'pricing',
      title: '智能定价策略',
      description: '动态定价算法和竞争对手价格监控',
      icon: <TrendingUp className="h-6 w-6" />,
      status: null,
      loading: true,
      href: '/ai/pricing'
    },
    {
      id: 'inventory',
      title: '智能库存管理',
      description: '需求预测和库存优化建议',
      icon: <Package className="h-6 w-6" />,
      status: null,
      loading: true,
      href: '/ai/inventory'
    },
    {
      id: 'customer-service',
      title: '智能客户服务',
      description: '聊天机器人和情感分析',
      icon: <MessageSquare className="h-6 w-6" />,
      status: null,
      loading: true,
      href: '/ai/customer-service'
    }
  ]);

  // 检查所有AI服务状态
  useEffect(() => {
    checkAllServicesStatus();
    
    // 每30秒检查一次状态
    const interval = setInterval(checkAllServicesStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  /**
   * 检查所有AI服务状态
   */
  const checkAllServicesStatus = async () => {
    const updatedServices = await Promise.all(
      services.map(async (service) => {
        try {
          const response = await fetch(`/api/ai/${service.id}/status`);
          const data = await response.json();
          
          return {
            ...service,
            status: data.success ? data.data : null,
            loading: false
          };
        } catch (error) {
          console.error(`检查 ${service.id} 服务状态失败:`, error);
          return {
            ...service,
            status: null,
            loading: false
          };
        }
      })
    );
    
    setServices(updatedServices);
  };

  /**
   * 重启AI服务
   */
  const restartService = async (serviceId: string) => {
    try {
      // 先清理资源
      await fetch(`/api/ai/${serviceId}`, { method: 'DELETE' });
      
      // 等待一秒后重新检查状态
      setTimeout(() => {
        checkAllServicesStatus();
      }, 1000);
      
      toast({
        title: '服务重启',
        description: `${serviceId} 服务正在重启...`,
      });
    } catch (error) {
      console.error('重启服务失败:', error);
      toast({
        title: '重启失败',
        description: '服务重启失败，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  /**
   * 获取服务状态徽章
   */
  const getStatusBadge = (status: ServiceStatus | null, loading: boolean) => {
    if (loading) {
      return <Badge variant="secondary"><Clock className="h-3 w-3 mr-1" />检查中</Badge>;
    }
    
    if (!status) {
      return <Badge variant="destructive"><XCircle className="h-3 w-3 mr-1" />离线</Badge>;
    }
    
    if (status.isReady) {
      return <Badge variant="default"><CheckCircle className="h-3 w-3 mr-1" />就绪</Badge>;
    }
    
    if (status.isInitialized) {
      return <Badge variant="secondary"><Activity className="h-3 w-3 mr-1" />初始化中</Badge>;
    }
    
    return <Badge variant="outline"><XCircle className="h-3 w-3 mr-1" />未初始化</Badge>;
  };

  /**
   * 计算整体健康度
   */
  const getOverallHealth = () => {
    const readyServices = services.filter(s => s.status?.isReady).length;
    const totalServices = services.length;
    return Math.round((readyServices / totalServices) * 100);
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">AI智能功能中心</h1>
          <p className="text-muted-foreground">
            管理和监控所有AI智能服务的运行状态
          </p>
        </div>
        <Button onClick={checkAllServicesStatus} variant="outline">
          <Activity className="h-4 w-4 mr-2" />
          刷新状态
        </Button>
      </div>

      {/* 整体状态概览 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            系统健康度
          </CardTitle>
          <CardDescription>
            AI服务整体运行状况
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="text-3xl font-bold">{getOverallHealth()}%</div>
            <div className="flex-1">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${getOverallHealth()}%` }}
                />
              </div>
            </div>
            <div className="text-sm text-muted-foreground">
              {services.filter(s => s.status?.isReady).length} / {services.length} 服务就绪
            </div>
          </div>
        </CardContent>
      </Card>

      {/* AI服务卡片 */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">服务概览</TabsTrigger>
          <TabsTrigger value="details">详细信息</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {services.map((service) => (
              <Card key={service.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {service.icon}
                      <CardTitle className="text-lg">{service.title}</CardTitle>
                    </div>
                    {getStatusBadge(service.status, service.loading)}
                  </div>
                  <CardDescription className="text-sm">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      className="flex-1"
                      disabled={!service.status?.isReady}
                      onClick={() => window.location.href = service.href}
                    >
                      使用服务
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => restartService(service.id)}
                    >
                      重启
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
        
        <TabsContent value="details" className="space-y-4">
          <div className="grid gap-4">
            {services.map((service) => (
              <Card key={service.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {service.icon}
                      <div>
                        <CardTitle>{service.title}</CardTitle>
                        <CardDescription>{service.description}</CardDescription>
                      </div>
                    </div>
                    {getStatusBadge(service.status, service.loading)}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-muted-foreground">初始化状态</div>
                      <div className="mt-1">
                        {service.status?.isInitialized ? '已初始化' : '未初始化'}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-muted-foreground">服务状态</div>
                      <div className="mt-1">
                        {service.status?.isReady ? '就绪' : '未就绪'}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-muted-foreground">最后检查</div>
                      <div className="mt-1">
                        {service.status?.timestamp 
                          ? new Date(service.status.timestamp).toLocaleTimeString()
                          : '未知'
                        }
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-muted-foreground">操作</div>
                      <div className="mt-1 space-x-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => restartService(service.id)}
                        >
                          重启
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
