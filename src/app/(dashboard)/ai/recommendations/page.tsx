/**
 * 智能商品推荐页面
 * 提供基于AI的个性化商品推荐功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Brain, 
  Star, 
  TrendingUp, 
  Users, 
  ShoppingCart,
  Loader2,
  RefreshCw
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface RecommendationItem {
  productId: string;
  score: number;
  reason: string;
  confidence: number;
}

interface RecommendationResponse {
  recommendations: RecommendationItem[];
  algorithm: 'collaborative' | 'content' | 'hybrid';
  timestamp: Date;
  metadata: {
    totalProducts: number;
    processingTime: number;
    cacheHit: boolean;
  };
}

export default function RecommendationsPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [recommendations, setRecommendations] = useState<RecommendationResponse | null>(null);
  
  // 推荐参数
  const [userId, setUserId] = useState('');
  const [productId, setProductId] = useState('');
  const [category, setCategory] = useState('');
  const [priceMin, setPriceMin] = useState('');
  const [priceMax, setPriceMax] = useState('');
  const [limit, setLimit] = useState('10');

  /**
   * 获取推荐
   */
  const getRecommendations = async () => {
    if (!userId.trim()) {
      toast({
        title: '参数错误',
        description: '请输入用户ID',
        variant: 'destructive',
      });
      return;
    }

    setLoading(true);
    
    try {
      const requestBody = {
        userId: userId.trim(),
        productId: productId.trim() || undefined,
        category: category || undefined,
        priceRange: (priceMin && priceMax) ? {
          min: parseFloat(priceMin),
          max: parseFloat(priceMax)
        } : undefined,
        limit: parseInt(limit) || 10
      };

      const response = await fetch('/api/ai/recommendations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (data.success) {
        setRecommendations(data.data);
        toast({
          title: '推荐获取成功',
          description: `为用户 ${userId} 生成了 ${data.data.recommendations.length} 个推荐`,
        });
      } else {
        throw new Error(data.error || '推荐获取失败');
      }
    } catch (error) {
      console.error('推荐获取失败:', error);
      toast({
        title: '推荐获取失败',
        description: error.message || '请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取算法类型图标
   */
  const getAlgorithmIcon = (algorithm: string) => {
    switch (algorithm) {
      case 'collaborative':
        return <Users className="h-4 w-4" />;
      case 'content':
        return <Star className="h-4 w-4" />;
      case 'hybrid':
        return <Brain className="h-4 w-4" />;
      default:
        return <Brain className="h-4 w-4" />;
    }
  };

  /**
   * 获取算法类型名称
   */
  const getAlgorithmName = (algorithm: string) => {
    switch (algorithm) {
      case 'collaborative':
        return '协同过滤';
      case 'content':
        return '内容推荐';
      case 'hybrid':
        return '混合推荐';
      default:
        return '未知算法';
    }
  };

  /**
   * 获取置信度颜色
   */
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-500';
    if (confidence >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">智能商品推荐</h1>
          <p className="text-muted-foreground">
            基于AI算法的个性化商品推荐系统
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 推荐参数设置 */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Brain className="h-5 w-5 mr-2" />
              推荐参数
            </CardTitle>
            <CardDescription>
              设置推荐算法的输入参数
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="userId">用户ID *</Label>
              <Input
                id="userId"
                placeholder="输入用户ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="productId">参考商品ID</Label>
              <Input
                id="productId"
                placeholder="输入商品ID（可选）"
                value={productId}
                onChange={(e) => setProductId(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">商品分类</Label>
              <Select value={category} onValueChange={setCategory}>
                <SelectTrigger>
                  <SelectValue placeholder="选择分类（可选）" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部分类</SelectItem>
                  <SelectItem value="electronics">电子产品</SelectItem>
                  <SelectItem value="clothing">服装</SelectItem>
                  <SelectItem value="books">图书</SelectItem>
                  <SelectItem value="home">家居用品</SelectItem>
                  <SelectItem value="sports">运动户外</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid grid-cols-2 gap-2">
              <div className="space-y-2">
                <Label htmlFor="priceMin">最低价格</Label>
                <Input
                  id="priceMin"
                  type="number"
                  placeholder="0"
                  value={priceMin}
                  onChange={(e) => setPriceMin(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="priceMax">最高价格</Label>
                <Input
                  id="priceMax"
                  type="number"
                  placeholder="1000"
                  value={priceMax}
                  onChange={(e) => setPriceMax(e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="limit">推荐数量</Label>
              <Select value={limit} onValueChange={setLimit}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5个</SelectItem>
                  <SelectItem value="10">10个</SelectItem>
                  <SelectItem value="20">20个</SelectItem>
                  <SelectItem value="50">50个</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button 
              onClick={getRecommendations} 
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  生成推荐中...
                </>
              ) : (
                <>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  获取推荐
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* 推荐结果 */}
        <div className="lg:col-span-2 space-y-4">
          {recommendations && (
            <>
              {/* 推荐概览 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span className="flex items-center">
                      {getAlgorithmIcon(recommendations.algorithm)}
                      <span className="ml-2">推荐结果概览</span>
                    </span>
                    <Badge variant="outline">
                      {getAlgorithmName(recommendations.algorithm)}
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <div className="font-medium text-muted-foreground">推荐数量</div>
                      <div className="text-2xl font-bold">{recommendations.metadata.totalProducts}</div>
                    </div>
                    <div>
                      <div className="font-medium text-muted-foreground">处理时间</div>
                      <div className="text-2xl font-bold">{recommendations.metadata.processingTime}ms</div>
                    </div>
                    <div>
                      <div className="font-medium text-muted-foreground">缓存命中</div>
                      <div className="text-2xl font-bold">
                        {recommendations.metadata.cacheHit ? '是' : '否'}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-muted-foreground">生成时间</div>
                      <div className="text-sm">
                        {new Date(recommendations.timestamp).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 推荐商品列表 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    推荐商品列表
                    <Button variant="outline" size="sm" onClick={getRecommendations}>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      刷新
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    基于AI算法为用户 {userId} 生成的个性化推荐
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {recommendations.recommendations.map((item, index) => (
                      <div key={item.productId} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Badge variant="secondary">#{index + 1}</Badge>
                            <span className="font-medium">商品 {item.productId}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="text-sm text-muted-foreground">
                              推荐分数: {(item.score * 100).toFixed(1)}%
                            </div>
                            <div className="flex items-center space-x-1">
                              <div className="text-xs text-muted-foreground">置信度:</div>
                              <div className="flex items-center space-x-1">
                                <div 
                                  className={`w-2 h-2 rounded-full ${getConfidenceColor(item.confidence)}`}
                                />
                                <span className="text-xs">
                                  {(item.confidence * 100).toFixed(0)}%
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="text-sm text-muted-foreground mb-3">
                          {item.reason}
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${item.score * 100}%` }}
                            />
                          </div>
                          <Button size="sm" variant="outline" className="ml-4">
                            <ShoppingCart className="h-3 w-3 mr-1" />
                            查看详情
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}

          {/* 空状态 */}
          {!recommendations && !loading && (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Brain className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">暂无推荐结果</h3>
                <p className="text-muted-foreground text-center mb-4">
                  请在左侧设置推荐参数，然后点击"获取推荐"按钮
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
