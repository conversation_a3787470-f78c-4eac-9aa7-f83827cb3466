/**
 * 高级数据分析主页面
 * 集成实时数据仪表板、高级分析、预测分析和数据可视化功能
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  TrendingUp, 
  Brain, 
  Activity,
  PieChart,
  LineChart,
  Zap,
  Target
} from 'lucide-react';

// 导入分析组件
import RealTimeDashboard from '@/components/analytics/RealTimeDashboard';
import AdvancedAnalytics from '@/components/analytics/AdvancedAnalytics';
import PredictiveAnalytics from '@/components/analytics/PredictiveAnalytics';
import DataVisualization from '@/components/analytics/DataVisualization';

export default function AnalyticsPage() {
  const [activeTab, setActiveTab] = useState('realtime');

  // 功能模块配置
  const analyticsModules = [
    {
      id: 'realtime',
      title: '实时数据仪表板',
      description: '实时监控业务关键指标和系统性能',
      icon: <Activity className="h-6 w-6" />,
      features: ['实时数据监控', '关键指标展示', '系统性能监控', '实时警报'],
      status: 'active'
    },
    {
      id: 'advanced',
      title: '高级分析功能',
      description: '深度分析业务数据，洞察商业趋势',
      icon: <BarChart3 className="h-6 w-6" />,
      features: ['趋势分析', '同比环比分析', '用户行为分析', '商品表现分析'],
      status: 'active'
    },
    {
      id: 'predictive',
      title: '预测分析能力',
      description: '基于AI算法的业务预测和趋势分析',
      icon: <Brain className="h-6 w-6" />,
      features: ['销售预测', '库存需求预测', '客户流失预测', '市场趋势预测'],
      status: 'active'
    },
    {
      id: 'visualization',
      title: '数据可视化',
      description: '创建和管理自定义数据图表',
      icon: <PieChart className="h-6 w-6" />,
      features: ['高级图表组件', '交互式数据展示', '自定义报表生成', '数据导出'],
      status: 'active'
    }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center">
            <TrendingUp className="h-8 w-8 mr-3" />
            高级数据分析
          </h1>
          <p className="text-muted-foreground mt-2">
            全方位数据分析平台，提供实时监控、深度分析、预测建模和可视化展示
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          <Zap className="h-4 w-4 mr-1" />
          企业版
        </Badge>
      </div>

      {/* 功能模块概览 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {analyticsModules.map((module) => (
          <Card 
            key={module.id} 
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              activeTab === module.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => setActiveTab(module.id)}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  {module.icon}
                  <CardTitle className="text-lg">{module.title}</CardTitle>
                </div>
                <Badge 
                  variant={module.status === 'active' ? 'default' : 'secondary'}
                  className="text-xs"
                >
                  {module.status === 'active' ? '已启用' : '未启用'}
                </Badge>
              </div>
              <CardDescription className="text-sm">
                {module.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1">
                {module.features.map((feature, index) => (
                  <div key={index} className="flex items-center text-xs text-muted-foreground">
                    <div className="w-1 h-1 bg-primary rounded-full mr-2" />
                    {feature}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 分析功能标签页 */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="realtime" className="flex items-center space-x-2">
            <Activity className="h-4 w-4" />
            <span>实时监控</span>
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>高级分析</span>
          </TabsTrigger>
          <TabsTrigger value="predictive" className="flex items-center space-x-2">
            <Brain className="h-4 w-4" />
            <span>预测分析</span>
          </TabsTrigger>
          <TabsTrigger value="visualization" className="flex items-center space-x-2">
            <PieChart className="h-4 w-4" />
            <span>数据可视化</span>
          </TabsTrigger>
        </TabsList>

        {/* 实时数据仪表板 */}
        <TabsContent value="realtime" className="space-y-4">
          <RealTimeDashboard />
        </TabsContent>

        {/* 高级分析功能 */}
        <TabsContent value="advanced" className="space-y-4">
          <AdvancedAnalytics />
        </TabsContent>

        {/* 预测分析能力 */}
        <TabsContent value="predictive" className="space-y-4">
          <PredictiveAnalytics />
        </TabsContent>

        {/* 数据可视化 */}
        <TabsContent value="visualization" className="space-y-4">
          <DataVisualization />
        </TabsContent>
      </Tabs>

      {/* 快速操作面板 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            快速操作
          </CardTitle>
          <CardDescription>
            常用分析功能的快速访问入口
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <LineChart className="h-6 w-6" />
              <span className="text-sm">销售趋势</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <BarChart3 className="h-6 w-6" />
              <span className="text-sm">商品分析</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <Brain className="h-6 w-6" />
              <span className="text-sm">AI预测</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <Activity className="h-6 w-6" />
              <span className="text-sm">实时监控</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 分析洞察摘要 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">今日洞察</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">销售增长</span>
                <Badge variant="default" className="text-green-600 bg-green-50">
                  +12.5%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">转化率</span>
                <Badge variant="secondary" className="text-blue-600 bg-blue-50">
                  3.2%
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">客户满意度</span>
                <Badge variant="default" className="text-green-600 bg-green-50">
                  4.8/5.0
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">预测提醒</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm">
                <div className="font-medium">库存预警</div>
                <div className="text-muted-foreground">3个商品需要补货</div>
              </div>
              <div className="text-sm">
                <div className="font-medium">客户流失风险</div>
                <div className="text-muted-foreground">5个高价值客户需关注</div>
              </div>
              <div className="text-sm">
                <div className="font-medium">销售机会</div>
                <div className="text-muted-foreground">电子产品类目增长潜力大</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">系统状态</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">数据同步</span>
                <Badge variant="default" className="text-green-600 bg-green-50">
                  正常
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">AI模型</span>
                <Badge variant="default" className="text-green-600 bg-green-50">
                  运行中
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">实时连接</span>
                <Badge variant="default" className="text-green-600 bg-green-50">
                  已连接
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
