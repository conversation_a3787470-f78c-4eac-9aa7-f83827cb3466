/**
 * 系统配置管理页面
 * 提供系统配置的查看、编辑、导入导出等功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { 
  CogIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  BellIcon,
  TruckIcon,
  DocumentArrowDownIcon,
  DocumentArrowUpIcon,
  ArrowPathIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';

// 配置类型枚举
enum ConfigType {
  SYSTEM = 'SYSTEM',
  INTEGRATION = 'INTEGRATION',
  SECURITY = 'SECURITY',
  BUSINESS = 'BUSINESS',
  UI = 'UI',
  NOTIFICATION = 'NOTIFICATION',
  PAYMENT = 'PAYMENT',
  SHIPPING = 'SHIPPING',
  TAX = 'TAX',
  CURRENCY = 'CURRENCY',
}

// 配置数据类型枚举
enum ConfigDataType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  ARRAY = 'ARRAY',
  DATE = 'DATE',
  PASSWORD = 'PASSWORD',
  URL = 'URL',
  EMAIL = 'EMAIL',
  FILE = 'FILE',
}

// 配置项接口
interface ConfigItem {
  id: string;
  key: string;
  name: string;
  description?: string;
  type: ConfigType;
  dataType: ConfigDataType;
  value: any;
  defaultValue?: any;
  isRequired: boolean;
  isEncrypted: boolean;
  isReadonly: boolean;
  validation?: any;
  group?: string;
  order: number;
}

// 配置类型图标映射
const configTypeIcons = {
  [ConfigType.SYSTEM]: CogIcon,
  [ConfigType.INTEGRATION]: GlobeAltIcon,
  [ConfigType.SECURITY]: ShieldCheckIcon,
  [ConfigType.BUSINESS]: CogIcon,
  [ConfigType.UI]: CogIcon,
  [ConfigType.NOTIFICATION]: BellIcon,
  [ConfigType.PAYMENT]: CurrencyDollarIcon,
  [ConfigType.SHIPPING]: TruckIcon,
  [ConfigType.TAX]: CurrencyDollarIcon,
  [ConfigType.CURRENCY]: CurrencyDollarIcon,
};

// 配置类型标签映射
const configTypeLabels = {
  [ConfigType.SYSTEM]: '系统配置',
  [ConfigType.INTEGRATION]: '集成配置',
  [ConfigType.SECURITY]: '安全配置',
  [ConfigType.BUSINESS]: '业务配置',
  [ConfigType.UI]: '界面配置',
  [ConfigType.NOTIFICATION]: '通知配置',
  [ConfigType.PAYMENT]: '支付配置',
  [ConfigType.SHIPPING]: '物流配置',
  [ConfigType.TAX]: '税务配置',
  [ConfigType.CURRENCY]: '货币配置',
};

export default function ConfigManagementPage() {
  const { toast } = useToast();
  const [configs, setConfigs] = useState<Record<ConfigType, ConfigItem[]>>({} as any);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<ConfigType>(ConfigType.SYSTEM);
  const [editingConfig, setEditingConfig] = useState<ConfigItem | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // 加载配置数据
  useEffect(() => {
    loadConfigs();
  }, []);

  const loadConfigs = async () => {
    try {
      setLoading(true);
      
      // 加载所有配置类型的数据
      const configPromises = Object.values(ConfigType).map(async (type) => {
        const response = await fetch(`/api/config/group?type=${type}`);
        if (response.ok) {
          const data = await response.json();
          return { type, configs: data.configs || [] };
        }
        return { type, configs: [] };
      });

      const results = await Promise.all(configPromises);
      
      const configsByType = results.reduce((acc, { type, configs }) => {
        acc[type] = configs;
        return acc;
      }, {} as Record<ConfigType, ConfigItem[]>);

      setConfigs(configsByType);
    } catch (error) {
      console.error('加载配置失败:', error);
      toast({
        title: '加载失败',
        description: '无法加载系统配置',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // 保存配置
  const saveConfig = async (key: string, value: any) => {
    try {
      const response = await fetch('/api/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ key, value }),
      });

      if (response.ok) {
        toast({
          title: '保存成功',
          description: '配置已更新',
        });
        await loadConfigs();
      } else {
        const error = await response.json();
        throw new Error(error.message || '保存失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      toast({
        title: '保存失败',
        description: error instanceof Error ? error.message : '保存配置失败',
        variant: 'destructive',
      });
    }
  };

  // 重置配置
  const resetConfig = async (key: string) => {
    try {
      const response = await fetch(`/api/config/${key}/reset`, {
        method: 'POST',
      });

      if (response.ok) {
        toast({
          title: '重置成功',
          description: '配置已重置为默认值',
        });
        await loadConfigs();
      } else {
        const error = await response.json();
        throw new Error(error.message || '重置失败');
      }
    } catch (error) {
      console.error('重置配置失败:', error);
      toast({
        title: '重置失败',
        description: error instanceof Error ? error.message : '重置配置失败',
        variant: 'destructive',
      });
    }
  };

  // 导出配置
  const exportConfigs = async () => {
    try {
      const response = await fetch(`/api/config/export?type=${activeTab}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `config_${activeTab.toLowerCase()}_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast({
          title: '导出成功',
          description: '配置文件已下载',
        });
      }
    } catch (error) {
      console.error('导出配置失败:', error);
      toast({
        title: '导出失败',
        description: '导出配置文件失败',
        variant: 'destructive',
      });
    }
  };

  // 渲染配置值编辑器
  const renderConfigEditor = (config: ConfigItem) => {
    const handleValueChange = (newValue: any) => {
      if (editingConfig && editingConfig.key === config.key) {
        setEditingConfig({ ...editingConfig, value: newValue });
      }
    };

    const currentValue = editingConfig?.key === config.key ? editingConfig.value : config.value;

    switch (config.dataType) {
      case ConfigDataType.BOOLEAN:
        return (
          <Switch
            checked={currentValue}
            onCheckedChange={handleValueChange}
            disabled={config.isReadonly}
          />
        );

      case ConfigDataType.NUMBER:
        return (
          <Input
            type="number"
            value={currentValue || ''}
            onChange={(e) => handleValueChange(parseFloat(e.target.value) || 0)}
            disabled={config.isReadonly}
            min={config.validation?.min}
            max={config.validation?.max}
          />
        );

      case ConfigDataType.PASSWORD:
        return (
          <Input
            type="password"
            value={currentValue || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            disabled={config.isReadonly}
            placeholder={config.isEncrypted ? '***已加密***' : ''}
          />
        );

      case ConfigDataType.JSON:
        return (
          <Textarea
            value={typeof currentValue === 'object' ? JSON.stringify(currentValue, null, 2) : currentValue || ''}
            onChange={(e) => {
              try {
                const parsed = JSON.parse(e.target.value);
                handleValueChange(parsed);
              } catch {
                handleValueChange(e.target.value);
              }
            }}
            disabled={config.isReadonly}
            rows={6}
            className="font-mono text-sm"
          />
        );

      case ConfigDataType.ARRAY:
        return (
          <Textarea
            value={Array.isArray(currentValue) ? currentValue.join('\n') : currentValue || ''}
            onChange={(e) => handleValueChange(e.target.value.split('\n').filter(line => line.trim()))}
            disabled={config.isReadonly}
            rows={4}
            placeholder="每行一个值"
          />
        );

      default:
        if (config.validation?.options) {
          return (
            <Select
              value={currentValue || ''}
              onValueChange={handleValueChange}
              disabled={config.isReadonly}
            >
              <SelectTrigger>
                <SelectValue placeholder="请选择..." />
              </SelectTrigger>
              <SelectContent>
                {config.validation.options.map((option: string) => (
                  <SelectItem key={option} value={option}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );
        }

        return (
          <Input
            type={config.dataType === ConfigDataType.EMAIL ? 'email' : 
                  config.dataType === ConfigDataType.URL ? 'url' : 
                  config.dataType === ConfigDataType.DATE ? 'datetime-local' : 'text'}
            value={currentValue || ''}
            onChange={(e) => handleValueChange(e.target.value)}
            disabled={config.isReadonly}
            placeholder={config.description}
          />
        );
    }
  };

  // 过滤配置
  const filteredConfigs = configs[activeTab]?.filter(config =>
    config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    config.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
    config.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  // 按组分组配置
  const groupedConfigs = filteredConfigs.reduce((groups, config) => {
    const group = config.group || '其他';
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(config);
    return groups;
  }, {} as Record<string, ConfigItem[]>);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <ArrowPathIcon className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">系统配置管理</h1>
          <p className="text-gray-600">管理系统的各项配置参数</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            onClick={exportConfigs}
            className="flex items-center space-x-2"
          >
            <DocumentArrowDownIcon className="h-4 w-4" />
            <span>导出配置</span>
          </Button>
          
          <Button
            variant="outline"
            onClick={() => {/* 实现导入功能 */}}
            className="flex items-center space-x-2"
          >
            <DocumentArrowUpIcon className="h-4 w-4" />
            <span>导入配置</span>
          </Button>
        </div>
      </div>

      {/* 搜索框 */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <Input
            placeholder="搜索配置项..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-md"
          />
        </div>
      </div>

      {/* 配置标签页 */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as ConfigType)}>
        <TabsList className="grid w-full grid-cols-5 lg:grid-cols-10">
          {Object.values(ConfigType).map((type) => {
            const Icon = configTypeIcons[type];
            return (
              <TabsTrigger
                key={type}
                value={type}
                className="flex items-center space-x-1 text-xs"
              >
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{configTypeLabels[type]}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {Object.values(ConfigType).map((type) => (
          <TabsContent key={type} value={type} className="space-y-6">
            {Object.entries(groupedConfigs).map(([groupName, groupConfigs]) => (
              <Card key={groupName}>
                <CardHeader>
                  <CardTitle className="text-lg">{groupName}</CardTitle>
                  <CardDescription>
                    {groupConfigs.length} 个配置项
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {groupConfigs.map((config) => (
                    <div key={config.key} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1 space-y-2">
                        <div className="flex items-center space-x-2">
                          <Label className="font-medium">{config.name}</Label>
                          {config.isRequired && (
                            <Badge variant="destructive" className="text-xs">必需</Badge>
                          )}
                          {config.isEncrypted && (
                            <Badge variant="secondary" className="text-xs">加密</Badge>
                          )}
                          {config.isReadonly && (
                            <Badge variant="outline" className="text-xs">只读</Badge>
                          )}
                        </div>
                        
                        {config.description && (
                          <p className="text-sm text-gray-600">{config.description}</p>
                        )}
                        
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <span>键: {config.key}</span>
                          <Separator orientation="vertical" className="h-4" />
                          <span>类型: {config.dataType}</span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <div className="min-w-[200px]">
                          {renderConfigEditor(config)}
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setEditingConfig(config);
                              setIsEditDialogOpen(true);
                            }}
                            disabled={config.isReadonly}
                          >
                            <PencilIcon className="h-4 w-4" />
                          </Button>
                          
                          {config.defaultValue !== undefined && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => resetConfig(config.key)}
                              disabled={config.isReadonly}
                              title="重置为默认值"
                            >
                              <ArrowPathIcon className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        ))}
      </Tabs>

      {/* 编辑配置对话框 */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>编辑配置</DialogTitle>
            <DialogDescription>
              修改配置项的值，请确保输入的值符合要求。
            </DialogDescription>
          </DialogHeader>

          {editingConfig && (
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">配置名称</Label>
                <p className="text-sm text-gray-600">{editingConfig.name}</p>
              </div>

              <div>
                <Label className="text-sm font-medium">配置键</Label>
                <p className="text-sm text-gray-600 font-mono">{editingConfig.key}</p>
              </div>

              {editingConfig.description && (
                <div>
                  <Label className="text-sm font-medium">描述</Label>
                  <p className="text-sm text-gray-600">{editingConfig.description}</p>
                </div>
              )}

              <div>
                <Label className="text-sm font-medium">当前值</Label>
                <div className="mt-2">
                  {renderConfigEditor(editingConfig)}
                </div>
              </div>

              {editingConfig.validation && (
                <div>
                  <Label className="text-sm font-medium">验证规则</Label>
                  <div className="text-sm text-gray-600 space-y-1">
                    {editingConfig.validation.min !== undefined && (
                      <p>最小值: {editingConfig.validation.min}</p>
                    )}
                    {editingConfig.validation.max !== undefined && (
                      <p>最大值: {editingConfig.validation.max}</p>
                    )}
                    {editingConfig.validation.pattern && (
                      <p>格式: {editingConfig.validation.pattern}</p>
                    )}
                    {editingConfig.validation.options && (
                      <p>可选值: {editingConfig.validation.options.join(', ')}</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsEditDialogOpen(false)}
            >
              取消
            </Button>
            <Button
              onClick={() => {
                if (editingConfig) {
                  saveConfig(editingConfig.key, editingConfig.value);
                  setIsEditDialogOpen(false);
                  setEditingConfig(null);
                }
              }}
              disabled={!editingConfig || editingConfig.isReadonly}
            >
              保存
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
