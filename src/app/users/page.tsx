/**
 * 用户权限管理页面
 * 提供用户管理、角色分配、权限管理等功能，集成RBAC权限控制
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { PermissionGuard, Can } from '@/components/auth/PermissionGuard';
import { usePermissions } from '@/hooks/usePermissions';
import {
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  MoreHorizontal,
  Users,
  Shield,
  Key,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  RefreshCw,
  UserPlus,
  Settings,
} from 'lucide-react';

// 用户数据接口
interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  roles: UserRole[];
}

interface UserRole {
  id: string;
  userId: string;
  roleId: string;
  assignedAt: string;
  assignedBy: string;
  expiresAt?: string;
  role: Role;
}

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isActive: boolean;
  permissions: RolePermission[];
}

interface RolePermission {
  id: string;
  roleId: string;
  permissionId: string;
  permission: Permission;
}

interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

// 分页信息接口
interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 用户权限管理页面组件
 */
export default function UsersPage() {
  const { hasPermission } = usePermissions();
  
  // 状态管理
  const [users, setUsers] = useState<User[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 构建查询参数
      const queryParams = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(searchQuery && { search: searchQuery }),
        ...(roleFilter && { role: roleFilter }),
        ...(statusFilter && { status: statusFilter }),
      });

      // 模拟API调用 - 实际项目中应该调用真实的API
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟数据
      const mockUsers: User[] = [
        {
          id: 'user-1',
          email: '<EMAIL>',
          firstName: '系统',
          lastName: '管理员',
          phone: '+86 138 0013 8000',
          isActive: true,
          emailVerified: true,
          lastLoginAt: '2024-01-15T10:30:00Z',
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z',
          roles: [
            {
              id: 'ur-1',
              userId: 'user-1',
              roleId: 'role-1',
              assignedAt: '2024-01-01T00:00:00Z',
              assignedBy: 'system',
              role: {
                id: 'role-1',
                name: 'super_admin',
                displayName: '超级管理员',
                description: '拥有系统所有权限',
                isActive: true,
                permissions: [],
              },
            },
          ],
        },
        {
          id: 'user-2',
          email: '<EMAIL>',
          firstName: '张',
          lastName: '经理',
          phone: '+86 138 0013 8001',
          isActive: true,
          emailVerified: true,
          lastLoginAt: '2024-01-14T16:20:00Z',
          createdAt: '2024-01-02T00:00:00Z',
          updatedAt: '2024-01-14T16:20:00Z',
          roles: [
            {
              id: 'ur-2',
              userId: 'user-2',
              roleId: 'role-2',
              assignedAt: '2024-01-02T00:00:00Z',
              assignedBy: 'user-1',
              role: {
                id: 'role-2',
                name: 'admin',
                displayName: '系统管理员',
                description: '拥有大部分管理权限',
                isActive: true,
                permissions: [],
              },
            },
          ],
        },
        {
          id: 'user-3',
          email: '<EMAIL>',
          firstName: '李',
          lastName: '销售',
          phone: '+86 138 0013 8002',
          isActive: true,
          emailVerified: true,
          lastLoginAt: '2024-01-13T09:15:00Z',
          createdAt: '2024-01-03T00:00:00Z',
          updatedAt: '2024-01-13T09:15:00Z',
          roles: [
            {
              id: 'ur-3',
              userId: 'user-3',
              roleId: 'role-3',
              assignedAt: '2024-01-03T00:00:00Z',
              assignedBy: 'user-1',
              role: {
                id: 'role-3',
                name: 'sales_rep',
                displayName: '销售代表',
                description: '负责客户和订单管理',
                isActive: true,
                permissions: [],
              },
            },
          ],
        },
        {
          id: 'user-4',
          email: '<EMAIL>',
          firstName: '王',
          lastName: '仓管',
          phone: '+86 138 0013 8003',
          isActive: true,
          emailVerified: false,
          lastLoginAt: '2024-01-12T14:30:00Z',
          createdAt: '2024-01-04T00:00:00Z',
          updatedAt: '2024-01-12T14:30:00Z',
          roles: [
            {
              id: 'ur-4',
              userId: 'user-4',
              roleId: 'role-4',
              assignedAt: '2024-01-04T00:00:00Z',
              assignedBy: 'user-1',
              role: {
                id: 'role-4',
                name: 'warehouse_staff',
                displayName: '仓库人员',
                description: '负责库存和发货管理',
                isActive: true,
                permissions: [],
              },
            },
          ],
        },
        {
          id: 'user-5',
          email: '<EMAIL>',
          firstName: '赵',
          lastName: '观察员',
          phone: '+86 138 0013 8004',
          isActive: false,
          emailVerified: true,
          lastLoginAt: '2024-01-10T11:45:00Z',
          createdAt: '2024-01-05T00:00:00Z',
          updatedAt: '2024-01-10T11:45:00Z',
          roles: [
            {
              id: 'ur-5',
              userId: 'user-5',
              roleId: 'role-5',
              assignedAt: '2024-01-05T00:00:00Z',
              assignedBy: 'user-1',
              role: {
                id: 'role-5',
                name: 'viewer',
                displayName: '只读用户',
                description: '只能查看数据',
                isActive: true,
                permissions: [],
              },
            },
          ],
        },
      ];

      setUsers(mockUsers);
      setPagination(prev => ({
        ...prev,
        total: mockUsers.length,
        totalPages: Math.ceil(mockUsers.length / prev.limit),
        hasNext: prev.page < Math.ceil(mockUsers.length / prev.limit),
        hasPrev: prev.page > 1,
      }));
    } catch (err) {
      setError('获取用户列表失败');
      console.error('Users fetch error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      // 模拟API调用
      const mockRoles: Role[] = [
        {
          id: 'role-1',
          name: 'super_admin',
          displayName: '超级管理员',
          description: '拥有系统所有权限',
          isActive: true,
          permissions: [],
        },
        {
          id: 'role-2',
          name: 'admin',
          displayName: '系统管理员',
          description: '拥有大部分管理权限',
          isActive: true,
          permissions: [],
        },
        {
          id: 'role-3',
          name: 'sales_rep',
          displayName: '销售代表',
          description: '负责客户和订单管理',
          isActive: true,
          permissions: [],
        },
        {
          id: 'role-4',
          name: 'warehouse_staff',
          displayName: '仓库人员',
          description: '负责库存和发货管理',
          isActive: true,
          permissions: [],
        },
        {
          id: 'role-5',
          name: 'viewer',
          displayName: '只读用户',
          description: '只能查看数据',
          isActive: true,
          permissions: [],
        },
      ];

      setRoles(mockRoles);
    } catch (err) {
      console.error('Roles fetch error:', err);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, [pagination.page, pagination.limit, searchQuery, roleFilter, statusFilter]);

  // 获取用户状态显示信息
  const getUserStatusInfo = (user: User) => {
    if (!user.isActive) {
      return { text: '已禁用', color: 'bg-red-100 text-red-800', icon: AlertTriangle };
    }
    if (!user.emailVerified) {
      return { text: '未验证', color: 'bg-yellow-100 text-yellow-800', icon: Clock };
    }
    return { text: '正常', color: 'bg-green-100 text-green-800', icon: CheckCircle };
  };

  // 格式化用户名称
  const formatUserName = (user: User) => {
    if (user.firstName || user.lastName) {
      return `${user.firstName || ''} ${user.lastName || ''}`.trim();
    }
    return user.email.split('@')[0];
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // 处理筛选
  const handleFilter = (key: string, value: string) => {
    if (key === 'role') {
      setRoleFilter(value);
    } else if (key === 'status') {
      setStatusFilter(value);
    }
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载用户数据中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
          <p className="mt-4 text-red-600">{error}</p>
          <Button 
            onClick={() => fetchUsers()} 
            className="mt-4"
            variant="outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  return (
    <PermissionGuard permission={{ resource: 'users', action: 'read' }}>
      <div className="space-y-6 p-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">用户权限管理</h1>
            <p className="text-gray-600 mt-1">管理系统用户、角色分配和权限控制</p>
          </div>
          <div className="flex space-x-2">
            <Can do="manage_roles" on="users">
              <Button variant="outline">
                <Shield className="h-4 w-4 mr-2" />
                角色管理
              </Button>
            </Can>
            <Can do="export" on="users">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出用户
              </Button>
            </Can>
            <Can do="create" on="users">
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                添加用户
              </Button>
            </Can>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="lg:col-span-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="搜索用户邮箱、姓名..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div>
                <Select value={roleFilter} onValueChange={(value) => handleFilter('role', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">所有角色</SelectItem>
                    {roles.map((role) => (
                      <SelectItem key={role.id} value={role.name}>
                        {role.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={statusFilter} onValueChange={(value) => handleFilter('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">所有状态</SelectItem>
                    <SelectItem value="active">正常</SelectItem>
                    <SelectItem value="inactive">已禁用</SelectItem>
                    <SelectItem value="unverified">未验证</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 用户列表 */}
        <Card>
          <CardHeader>
            <CardTitle>用户列表</CardTitle>
            <CardDescription>
              共 {pagination.total} 个用户，当前显示第 {pagination.page} 页
            </CardDescription>
          </CardHeader>
          <CardContent>
            {users.length === 0 ? (
              <div className="text-center py-12">
                <Users className="h-12 w-12 text-gray-400 mx-auto" />
                <h3 className="mt-4 text-lg font-medium text-gray-900">暂无用户</h3>
                <p className="mt-2 text-gray-600">还没有添加任何用户，点击上方按钮开始添加用户。</p>
                <Can do="create" on="users">
                  <Button className="mt-4">
                    <UserPlus className="h-4 w-4 mr-2" />
                    添加第一个用户
                  </Button>
                </Can>
              </div>
            ) : (
              <div className="space-y-4">
                {users.map((user) => {
                  const statusInfo = getUserStatusInfo(user);
                  const StatusIcon = statusInfo.icon;

                  return (
                    <div
                      key={user.id}
                      className="flex items-center space-x-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                    >
                      {/* 用户头像 */}
                      <div className="w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                        <Users className="w-6 h-6 text-muted-foreground" />
                      </div>

                      {/* 用户信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-medium text-foreground truncate">
                            {formatUserName(user)}
                          </h3>
                          <div className="flex items-center space-x-2">
                            <Badge className={statusInfo.color}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {statusInfo.text}
                            </Badge>
                            {user.roles.map((userRole) => (
                              <Badge key={userRole.id} variant="outline">
                                {userRole.role.displayName}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>邮箱: {user.email}</span>
                          {user.phone && <span>电话: {user.phone}</span>}
                          {user.lastLoginAt && (
                            <span>最后登录: {formatDate(user.lastLoginAt)}</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <span>创建时间: {formatDate(user.createdAt)}</span>
                          <span>角色数量: {user.roles.length}</span>
                        </div>
                      </div>

                      {/* 操作按钮 */}
                      <div className="flex items-center space-x-2">
                        <Can do="read" on="users">
                          <Button variant="outline" size="sm">
                            <Eye className="w-4 h-4 mr-1" />
                            查看
                          </Button>
                        </Can>
                        <Can do="update" on="users">
                          <Button variant="outline" size="sm">
                            <Edit className="w-4 h-4 mr-1" />
                            编辑
                          </Button>
                        </Can>
                        <Can do="manage_roles" on="users">
                          <Button variant="outline" size="sm">
                            <Shield className="w-4 h-4 mr-1" />
                            角色
                          </Button>
                        </Can>
                        <Can do="delete" on="users">
                          <Button variant="outline" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </Can>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* 分页 */}
            {pagination.totalPages > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-muted-foreground">
                  显示第 {(pagination.page - 1) * pagination.limit + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，共 {pagination.total} 条
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                    disabled={!pagination.hasPrev}
                  >
                    上一页
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    第 {pagination.page} 页，共 {pagination.totalPages} 页
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPagination(prev => ({ ...prev, page: Math.min(pagination.totalPages, prev.page + 1) }))}
                    disabled={!pagination.hasNext}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </PermissionGuard>
  );
}
