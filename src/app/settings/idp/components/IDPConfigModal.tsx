/**
 * IDP配置模态框组件
 * 用于创建和编辑IDP配置
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { IDPType, IDPProtocol } from '@/lib/auth/idp-config';

// IDP配置表单数据
interface IDPConfigForm {
  name: string;
  type: IDPType;
  protocol: IDPProtocol;
  description: string;
  enabled: boolean;
  isDefault: boolean;
  config: Record<string, any>;
}

// 模态框属性
interface IDPConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (config: IDPConfigForm) => Promise<void>;
  editingConfig?: any;
}

// IDP类型选项
const IDP_TYPE_OPTIONS = [
  { value: IDPType.GOOGLE_WORKSPACE, label: 'Google Workspace', protocols: [IDPProtocol.OIDC] },
  { value: IDPType.MICROSOFT_AZURE_AD, label: 'Microsoft Azure AD', protocols: [IDPProtocol.OIDC, IDPProtocol.SAML2] },
  { value: IDPType.OKTA, label: 'Okta', protocols: [IDPProtocol.OIDC, IDPProtocol.SAML2] },
  { value: IDPType.AUTH0, label: 'Auth0', protocols: [IDPProtocol.OIDC] },
  { value: IDPType.LDAP, label: 'LDAP', protocols: [IDPProtocol.LDAP] },
  { value: IDPType.SAML, label: '通用SAML', protocols: [IDPProtocol.SAML2] },
  { value: IDPType.OIDC, label: '通用OIDC', protocols: [IDPProtocol.OIDC] },
];

export default function IDPConfigModal({ isOpen, onClose, onSave, editingConfig }: IDPConfigModalProps) {
  const [formData, setFormData] = useState<IDPConfigForm>({
    name: '',
    type: IDPType.GOOGLE_WORKSPACE,
    protocol: IDPProtocol.OIDC,
    description: '',
    enabled: true,
    isDefault: false,
    config: {},
  });
  const [loading, setSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  // 初始化表单数据
  useEffect(() => {
    if (editingConfig) {
      setFormData({
        name: editingConfig.name,
        type: editingConfig.type,
        protocol: editingConfig.protocol,
        description: editingConfig.description || '',
        enabled: editingConfig.enabled,
        isDefault: editingConfig.isDefault,
        config: editingConfig.config,
      });
    } else {
      setFormData({
        name: '',
        type: IDPType.GOOGLE_WORKSPACE,
        protocol: IDPProtocol.OIDC,
        description: '',
        enabled: true,
        isDefault: false,
        config: {},
      });
    }
    setCurrentStep(1);
  }, [editingConfig, isOpen]);

  // 获取当前类型支持的协议
  const getSupportedProtocols = (type: IDPType) => {
    const typeOption = IDP_TYPE_OPTIONS.find(option => option.value === type);
    return typeOption?.protocols || [IDPProtocol.OIDC];
  };

  // 处理类型变更
  const handleTypeChange = (type: IDPType) => {
    const supportedProtocols = getSupportedProtocols(type);
    setFormData(prev => ({
      ...prev,
      type,
      protocol: supportedProtocols[0],
      config: {}, // 重置配置
    }));
  };

  // 处理保存
  const handleSave = async () => {
    try {
      setSaving(true);
      await onSave(formData);
      onClose();
    } catch (error) {
      console.error('保存IDP配置失败:', error);
    } finally {
      setSaving(false);
    }
  };

  // 渲染基本信息步骤
  const renderBasicInfoStep = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          配置名称 *
        </label>
        <Input
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="输入IDP配置名称"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          IDP类型 *
        </label>
        <select
          value={formData.type}
          onChange={(e) => handleTypeChange(e.target.value as IDPType)}
          className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
          required
        >
          {IDP_TYPE_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          协议类型 *
        </label>
        <select
          value={formData.protocol}
          onChange={(e) => setFormData(prev => ({ ...prev, protocol: e.target.value as IDPProtocol }))}
          className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
          required
        >
          {getSupportedProtocols(formData.type).map(protocol => (
            <option key={protocol} value={protocol}>
              {protocol.toUpperCase()}
            </option>
          ))}
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          描述
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="输入配置描述"
          rows={3}
          className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground"
        />
      </div>

      <div className="flex items-center space-x-6">
        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={formData.enabled}
            onChange={(e) => setFormData(prev => ({ ...prev, enabled: e.target.checked }))}
            className="w-4 h-4 text-brand-600 border-gray-300 rounded focus:ring-brand-500"
          />
          <span className="text-sm font-medium text-foreground">启用此配置</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={formData.isDefault}
            onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
            className="w-4 h-4 text-brand-600 border-gray-300 rounded focus:ring-brand-500"
          />
          <span className="text-sm font-medium text-foreground">设为默认IDP</span>
        </label>
      </div>
    </div>
  );

  // 渲染协议配置步骤
  const renderProtocolConfigStep = () => {
    switch (formData.protocol) {
      case IDPProtocol.OIDC:
      case IDPProtocol.OAUTH2:
        return renderOAuthConfig();
      case IDPProtocol.SAML2:
        return renderSAMLConfig();
      case IDPProtocol.LDAP:
        return renderLDAPConfig();
      default:
        return <div>不支持的协议类型</div>;
    }
  };

  // 渲染OAuth/OIDC配置
  const renderOAuthConfig = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            客户端ID *
          </label>
          <Input
            value={formData.config.clientId || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, clientId: e.target.value }
            }))}
            placeholder="输入客户端ID"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            客户端密钥 *
          </label>
          <Input
            type="password"
            value={formData.config.clientSecret || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, clientSecret: e.target.value }
            }))}
            placeholder="输入客户端密钥"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          发现端点URL
        </label>
        <Input
          value={formData.config.discoveryUrl || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, discoveryUrl: e.target.value }
          }))}
          placeholder="https://example.com/.well-known/openid_configuration"
        />
        <p className="text-xs text-muted-foreground mt-1">
          如果提供发现端点，将自动获取其他端点URL
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            授权端点URL
          </label>
          <Input
            value={formData.config.authorizationUrl || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, authorizationUrl: e.target.value }
            }))}
            placeholder="https://example.com/oauth2/authorize"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            令牌端点URL
          </label>
          <Input
            value={formData.config.tokenUrl || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, tokenUrl: e.target.value }
            }))}
            placeholder="https://example.com/oauth2/token"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          用户信息端点URL
        </label>
        <Input
          value={formData.config.userInfoUrl || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, userInfoUrl: e.target.value }
          }))}
          placeholder="https://example.com/oauth2/userinfo"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          作用域 (用空格分隔)
        </label>
        <Input
          value={formData.config.scopes?.join(' ') || 'openid profile email'}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, scopes: e.target.value.split(' ').filter(s => s) }
          }))}
          placeholder="openid profile email"
        />
      </div>
    </div>
  );

  // 渲染SAML配置
  const renderSAMLConfig = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            实体ID *
          </label>
          <Input
            value={formData.config.entityId || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, entityId: e.target.value }
            }))}
            placeholder="https://idp.example.com/metadata"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            SSO服务URL *
          </label>
          <Input
            value={formData.config.ssoServiceUrl || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, ssoServiceUrl: e.target.value }
            }))}
            placeholder="https://idp.example.com/sso"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          X.509证书 *
        </label>
        <textarea
          value={formData.config.certificate || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, certificate: e.target.value }
          }))}
          placeholder="-----BEGIN CERTIFICATE-----&#10;...&#10;-----END CERTIFICATE-----"
          rows={6}
          className="w-full px-3 py-2 border border-border rounded-md bg-background text-foreground font-mono text-sm"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          元数据URL
        </label>
        <Input
          value={formData.config.metadataUrl || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, metadataUrl: e.target.value }
          }))}
          placeholder="https://idp.example.com/metadata"
        />
        <p className="text-xs text-muted-foreground mt-1">
          如果提供元数据URL，将自动获取配置信息
        </p>
      </div>
    </div>
  );

  // 渲染LDAP配置
  const renderLDAPConfig = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            服务器URL *
          </label>
          <Input
            value={formData.config.serverUrl || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, serverUrl: e.target.value }
            }))}
            placeholder="ldap://ldap.example.com:389"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            绑定DN *
          </label>
          <Input
            value={formData.config.bindDn || ''}
            onChange={(e) => setFormData(prev => ({
              ...prev,
              config: { ...prev.config, bindDn: e.target.value }
            }))}
            placeholder="cn=admin,dc=example,dc=com"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          绑定密码 *
        </label>
        <Input
          type="password"
          value={formData.config.bindPassword || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, bindPassword: e.target.value }
          }))}
          placeholder="输入绑定密码"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          用户搜索基础DN *
        </label>
        <Input
          value={formData.config.userBaseDn || ''}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, userBaseDn: e.target.value }
          }))}
          placeholder="ou=users,dc=example,dc=com"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          用户搜索过滤器
        </label>
        <Input
          value={formData.config.userSearchFilter || '(uid={username})'}
          onChange={(e) => setFormData(prev => ({
            ...prev,
            config: { ...prev.config, userSearchFilter: e.target.value }
          }))}
          placeholder="(uid={username})"
        />
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* 模态框头部 */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              {editingConfig ? '编辑IDP配置' : '添加IDP配置'}
            </h2>
            <p className="text-sm text-muted-foreground mt-1">
              配置外部身份提供商集成
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-muted-foreground hover:text-foreground"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 步骤指示器 */}
        <div className="px-6 py-4 border-b border-border">
          <div className="flex items-center space-x-4">
            <div className={cn(
              'flex items-center space-x-2',
              currentStep === 1 ? 'text-brand-600' : 'text-muted-foreground'
            )}>
              <div className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                currentStep === 1 ? 'bg-brand-600 text-white' : 'bg-muted text-muted-foreground'
              )}>
                1
              </div>
              <span className="font-medium">基本信息</span>
            </div>
            <div className="flex-1 h-px bg-border"></div>
            <div className={cn(
              'flex items-center space-x-2',
              currentStep === 2 ? 'text-brand-600' : 'text-muted-foreground'
            )}>
              <div className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                currentStep === 2 ? 'bg-brand-600 text-white' : 'bg-muted text-muted-foreground'
              )}>
                2
              </div>
              <span className="font-medium">协议配置</span>
            </div>
          </div>
        </div>

        {/* 模态框内容 */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {currentStep === 1 ? renderBasicInfoStep() : renderProtocolConfigStep()}
        </div>

        {/* 模态框底部 */}
        <div className="flex items-center justify-between p-6 border-t border-border">
          <div>
            {currentStep === 2 && (
              <Button
                variant="outline"
                onClick={() => setCurrentStep(1)}
              >
                上一步
              </Button>
            )}
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline" onClick={onClose}>
              取消
            </Button>
            {currentStep === 1 ? (
              <Button
                onClick={() => setCurrentStep(2)}
                disabled={!formData.name || !formData.type}
              >
                下一步
              </Button>
            ) : (
              <Button
                onClick={handleSave}
                loading={loading}
                disabled={loading}
              >
                {loading ? '保存中...' : '保存配置'}
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
