/**
 * IDP配置管理页面
 * 管理外部身份提供商的配置
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { IDPType, IDPProtocol } from '@/lib/auth/idp-config';
import IDPConfigModal from './components/IDPConfigModal';

// IDP配置数据类型
interface IDPConfigData {
  id: string;
  name: string;
  type: IDPType;
  protocol: IDPProtocol;
  enabled: boolean;
  isDefault: boolean;
  description?: string;
  iconUrl?: string;
  config: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  lastTestResult?: {
    success: boolean;
    message: string;
    timestamp: string;
  };
}

// IDP类型配置
const IDP_TYPE_CONFIG = {
  [IDPType.GOOGLE_WORKSPACE]: {
    name: 'Google Workspace',
    icon: '🔍',
    color: 'bg-blue-100 text-blue-800',
    description: 'Google Workspace企业身份认证',
  },
  [IDPType.MICROSOFT_AZURE_AD]: {
    name: 'Microsoft Azure AD',
    icon: '🏢',
    color: 'bg-indigo-100 text-indigo-800',
    description: 'Microsoft Azure Active Directory',
  },
  [IDPType.OKTA]: {
    name: 'Okta',
    icon: '🔐',
    color: 'bg-purple-100 text-purple-800',
    description: 'Okta身份管理平台',
  },
  [IDPType.AUTH0]: {
    name: 'Auth0',
    icon: '🛡️',
    color: 'bg-orange-100 text-orange-800',
    description: 'Auth0身份认证服务',
  },
  [IDPType.LDAP]: {
    name: 'LDAP',
    icon: '📁',
    color: 'bg-green-100 text-green-800',
    description: '企业LDAP目录服务',
  },
  [IDPType.SAML]: {
    name: 'SAML',
    icon: '🔗',
    color: 'bg-yellow-100 text-yellow-800',
    description: '通用SAML 2.0身份提供商',
  },
  [IDPType.OIDC]: {
    name: 'OIDC',
    icon: '🌐',
    color: 'bg-teal-100 text-teal-800',
    description: '通用OpenID Connect提供商',
  },
};

// 协议类型配置
const PROTOCOL_CONFIG = {
  [IDPProtocol.OAUTH2]: { name: 'OAuth 2.0', color: 'bg-blue-50 text-blue-700' },
  [IDPProtocol.OIDC]: { name: 'OpenID Connect', color: 'bg-green-50 text-green-700' },
  [IDPProtocol.SAML2]: { name: 'SAML 2.0', color: 'bg-purple-50 text-purple-700' },
  [IDPProtocol.LDAP]: { name: 'LDAP', color: 'bg-orange-50 text-orange-700' },
};

export default function IDPManagementPage() {
  const [idpConfigs, setIdpConfigs] = useState<IDPConfigData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<IDPType | ''>('');
  const [enabledFilter, setEnabledFilter] = useState<string>('');
  const [selectedConfigs, setSelectedConfigs] = useState<Set<string>>(new Set());
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 加载IDP配置列表
  useEffect(() => {
    loadIDPConfigs();
  }, [typeFilter, enabledFilter]);

  const loadIDPConfigs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (typeFilter) params.set('type', typeFilter);
      if (enabledFilter) params.set('enabled', enabledFilter);

      const response = await fetch(`/api/auth/idp?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setIdpConfigs(data.data);
      } else {
        console.error('加载IDP配置失败:', data.error);
      }
    } catch (error) {
      console.error('加载IDP配置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 过滤配置列表
  const filteredConfigs = idpConfigs.filter(config => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        config.name.toLowerCase().includes(query) ||
        config.description?.toLowerCase().includes(query) ||
        config.type.toLowerCase().includes(query)
      );
    }
    return true;
  });

  // 测试IDP连接
  const testConnection = async (configId: string) => {
    try {
      const response = await fetch(`/api/auth/idp/${configId}/test`, {
        method: 'POST',
      });
      const data = await response.json();

      if (data.success) {
        // 更新测试结果
        setIdpConfigs(prev => prev.map(config => 
          config.id === configId 
            ? { ...config, lastTestResult: data.data }
            : config
        ));
      }
    } catch (error) {
      console.error('连接测试失败:', error);
    }
  };

  // 切换配置启用状态
  const toggleEnabled = async (configId: string, enabled: boolean) => {
    try {
      const response = await fetch(`/api/auth/idp/${configId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled }),
      });

      if (response.ok) {
        setIdpConfigs(prev => prev.map(config => 
          config.id === configId 
            ? { ...config, enabled }
            : config
        ));
      }
    } catch (error) {
      console.error('更新配置失败:', error);
    }
  };

  // 设置默认IDP
  const setAsDefault = async (configId: string) => {
    try {
      const response = await fetch(`/api/auth/idp/${configId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isDefault: true }),
      });

      if (response.ok) {
        setIdpConfigs(prev => prev.map(config => ({
          ...config,
          isDefault: config.id === configId,
        })));
      }
    } catch (error) {
      console.error('设置默认IDP失败:', error);
    }
  };

  // 批量操作
  const handleBatchAction = async (action: string) => {
    if (selectedConfigs.size === 0) return;

    try {
      const response = await fetch('/api/auth/idp', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          ids: Array.from(selectedConfigs),
        }),
      });

      if (response.ok) {
        await loadIDPConfigs();
        setSelectedConfigs(new Set());
      }
    } catch (error) {
      console.error('批量操作失败:', error);
    }
  };

  // 获取状态指示器
  const getStatusIndicator = (config: IDPConfigData) => {
    if (!config.enabled) {
      return <span className="w-2 h-2 bg-gray-400 rounded-full"></span>;
    }
    
    if (config.lastTestResult) {
      return (
        <span 
          className={cn(
            'w-2 h-2 rounded-full',
            config.lastTestResult.success ? 'bg-green-500' : 'bg-red-500'
          )}
          title={config.lastTestResult.message}
        ></span>
      );
    }
    
    return <span className="w-2 h-2 bg-yellow-500 rounded-full" title="未测试"></span>;
  };

  return (
    <div className="page-container">
      {/* 页面标题 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="page-title">身份提供商管理</h1>
          <p className="page-subtitle">
            配置和管理外部身份提供商集成
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => loadIDPConfigs()}>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            刷新
          </Button>
          <Button onClick={() => setShowCreateModal(true)}>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            添加IDP
          </Button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总配置数</p>
                <p className="text-2xl font-bold text-foreground">{idpConfigs.length}</p>
              </div>
              <div className="p-3 bg-blue-100 text-blue-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">已启用</p>
                <p className="text-2xl font-bold text-foreground">
                  {idpConfigs.filter(c => c.enabled).length}
                </p>
              </div>
              <div className="p-3 bg-green-100 text-green-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">测试通过</p>
                <p className="text-2xl font-bold text-foreground">
                  {idpConfigs.filter(c => c.lastTestResult?.success).length}
                </p>
              </div>
              <div className="p-3 bg-purple-100 text-purple-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">默认IDP</p>
                <p className="text-lg font-bold text-foreground">
                  {idpConfigs.find(c => c.isDefault)?.name || '未设置'}
                </p>
              </div>
              <div className="p-3 bg-orange-100 text-orange-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                type="search"
                placeholder="搜索IDP配置..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />
            </div>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value as IDPType | '')}
              className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="">所有类型</option>
              {Object.entries(IDP_TYPE_CONFIG).map(([type, config]) => (
                <option key={type} value={type}>{config.name}</option>
              ))}
            </select>
            <select
              value={enabledFilter}
              onChange={(e) => setEnabledFilter(e.target.value)}
              className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="">所有状态</option>
              <option value="true">已启用</option>
              <option value="false">已禁用</option>
            </select>
            {selectedConfigs.size > 0 && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchAction('enable')}
                >
                  启用
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchAction('disable')}
                >
                  禁用
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBatchAction('delete')}
                >
                  删除
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* IDP配置列表 */}
      <Card>
        <CardHeader>
          <CardTitle>IDP配置列表</CardTitle>
          <CardDescription>
            管理外部身份提供商的配置和连接
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="loading-spinner w-8 h-8"></div>
              <span className="ml-2 text-muted-foreground">加载中...</span>
            </div>
          ) : filteredConfigs.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="empty-state-title">暂无IDP配置</h3>
              <p className="empty-state-description">
                还没有配置任何身份提供商，开始添加您的第一个IDP配置吧。
              </p>
              <Button onClick={() => setShowCreateModal(true)}>添加IDP配置</Button>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredConfigs.map((config) => (
                <div
                  key={config.id}
                  className="flex items-center space-x-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  {/* 选择框 */}
                  <input
                    type="checkbox"
                    checked={selectedConfigs.has(config.id)}
                    onChange={(e) => {
                      const newSelected = new Set(selectedConfigs);
                      if (e.target.checked) {
                        newSelected.add(config.id);
                      } else {
                        newSelected.delete(config.id);
                      }
                      setSelectedConfigs(newSelected);
                    }}
                    className="w-4 h-4 text-brand-600 border-gray-300 rounded focus:ring-brand-500"
                  />

                  {/* 状态指示器 */}
                  <div className="flex items-center space-x-2">
                    {getStatusIndicator(config)}
                  </div>

                  {/* IDP信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{IDP_TYPE_CONFIG[config.type]?.icon}</span>
                        <div>
                          <h3 className="font-medium text-foreground flex items-center space-x-2">
                            <span>{config.name}</span>
                            {config.isDefault && (
                              <span className="px-2 py-1 text-xs bg-brand-100 text-brand-800 rounded-full">
                                默认
                              </span>
                            )}
                          </h3>
                          <p className="text-sm text-muted-foreground">
                            {config.description || IDP_TYPE_CONFIG[config.type]?.description}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={cn(
                          'px-2 py-1 text-xs font-medium rounded-full',
                          IDP_TYPE_CONFIG[config.type]?.color
                        )}>
                          {IDP_TYPE_CONFIG[config.type]?.name}
                        </span>
                        <span className={cn(
                          'px-2 py-1 text-xs font-medium rounded-full',
                          PROTOCOL_CONFIG[config.protocol]?.color
                        )}>
                          {PROTOCOL_CONFIG[config.protocol]?.name}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>状态: {config.enabled ? '已启用' : '已禁用'}</span>
                      <span>创建时间: {new Date(config.createdAt).toLocaleDateString()}</span>
                      {config.lastTestResult && (
                        <span>
                          最后测试: {config.lastTestResult.success ? '✅ 成功' : '❌ 失败'}
                          ({new Date(config.lastTestResult.timestamp).toLocaleDateString()})
                        </span>
                      )}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => testConnection(config.id)}
                    >
                      测试
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleEnabled(config.id, !config.enabled)}
                    >
                      {config.enabled ? '禁用' : '启用'}
                    </Button>
                    {!config.isDefault && config.enabled && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setAsDefault(config.id)}
                      >
                        设为默认
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      编辑
                    </Button>
                    <Button variant="outline" size="sm">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
