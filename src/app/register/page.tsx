/**
 * 用户注册页面组件
 * 提供新用户注册功能
 */

'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// 注册表单数据接口
interface RegisterFormData {
  email: string;
  username: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone: string;
  agreeToTerms: boolean;
}

// 注册页面组件
export default function RegisterPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<RegisterFormData>({
    email: '',
    username: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    agreeToTerms: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // 处理表单输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    // 清除错误信息
    if (error) setError(null);
  };

  // 验证表单
  const validateForm = (): string | null => {
    if (!formData.email) return '请输入邮箱地址';
    if (!formData.password) return '请输入密码';
    if (formData.password.length < 8) return '密码至少需要8位';
    if (formData.password !== formData.confirmPassword) return '两次输入的密码不一致';
    if (!formData.firstName) return '请输入姓名';
    if (!formData.agreeToTerms) return '请同意服务条款和隐私政策';
    
    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) return '邮箱格式不正确';
    
    // 验证密码强度
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/;
    if (!passwordRegex.test(formData.password)) return '密码必须包含大小写字母和数字';
    
    return null;
  };

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证表单
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          username: formData.username || undefined,
          password: formData.password,
          confirmPassword: formData.confirmPassword,
          firstName: formData.firstName,
          lastName: formData.lastName || undefined,
          phone: formData.phone || undefined,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSuccess('注册成功！请查看邮箱完成验证，然后登录。');
        // 3秒后跳转到登录页面
        setTimeout(() => {
          router.push('/login');
        }, 3000);
      } else {
        setError(result.error?.message || '注册失败');
      }
    } catch (err) {
      setError('网络错误，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-brand-50 to-brand-100 dark:from-gray-900 dark:to-gray-800 px-4">
      <div className="w-full max-w-md">
        {/* 品牌标识 */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-500 text-white rounded-full mb-4">
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            CBEC ERP
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            跨境电商ERP管理平台
          </p>
        </div>

        {/* 注册表单 */}
        <Card className="shadow-large">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold text-center">
              创建账户
            </CardTitle>
            <CardDescription className="text-center">
              填写以下信息来创建您的账户
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* 成功信息显示 */}
              {success && (
                <div className="p-3 text-sm text-success-700 bg-success-50 border border-success-200 rounded-md">
                  {success}
                </div>
              )}

              {/* 错误信息显示 */}
              {error && (
                <div className="p-3 text-sm text-error-700 bg-error-50 border border-error-200 rounded-md">
                  {error}
                </div>
              )}

              {/* 姓名输入 */}
              <div className="grid grid-cols-2 gap-4">
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  label="姓名"
                  placeholder="请输入姓名"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  required
                  disabled={isLoading}
                />
                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  label="姓氏"
                  placeholder="请输入姓氏（可选）"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  disabled={isLoading}
                />
              </div>

              {/* 邮箱输入 */}
              <Input
                id="email"
                name="email"
                type="email"
                label="邮箱地址"
                placeholder="请输入您的邮箱"
                value={formData.email}
                onChange={handleInputChange}
                required
                disabled={isLoading}
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                    />
                  </svg>
                }
              />

              {/* 用户名输入 */}
              <Input
                id="username"
                name="username"
                type="text"
                label="用户名"
                placeholder="请输入用户名（可选）"
                value={formData.username}
                onChange={handleInputChange}
                disabled={isLoading}
                helperText="用户名只能包含字母、数字和下划线"
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                    />
                  </svg>
                }
              />

              {/* 手机号输入 */}
              <Input
                id="phone"
                name="phone"
                type="tel"
                label="手机号"
                placeholder="请输入手机号（可选）"
                value={formData.phone}
                onChange={handleInputChange}
                disabled={isLoading}
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                    />
                  </svg>
                }
              />

              {/* 密码输入 */}
              <Input
                id="password"
                name="password"
                type="password"
                label="密码"
                placeholder="请输入密码"
                value={formData.password}
                onChange={handleInputChange}
                required
                disabled={isLoading}
                helperText="密码至少8位，包含大小写字母和数字"
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                }
              />

              {/* 确认密码输入 */}
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                label="确认密码"
                placeholder="请再次输入密码"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                required
                disabled={isLoading}
                leftIcon={
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                }
              />

              {/* 服务条款同意 */}
              <div className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  id="agreeToTerms"
                  name="agreeToTerms"
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange}
                  disabled={isLoading}
                  className="mt-1 rounded border-gray-300 text-brand-600 focus:ring-brand-500"
                />
                <label htmlFor="agreeToTerms" className="text-sm text-gray-700 dark:text-gray-300">
                  我已阅读并同意{' '}
                  <Link href="/terms" className="text-brand-600 hover:text-brand-500">
                    服务条款
                  </Link>{' '}
                  和{' '}
                  <Link href="/privacy" className="text-brand-600 hover:text-brand-500">
                    隐私政策
                  </Link>
                </label>
              </div>

              {/* 注册按钮 */}
              <Button
                type="submit"
                className="w-full"
                size="lg"
                loading={isLoading}
                disabled={isLoading || success !== null}
              >
                {isLoading ? '注册中...' : '创建账户'}
              </Button>
            </form>

            {/* 登录链接 */}
            <div className="text-center mt-6">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                已有账户？{' '}
                <Link
                  href="/login"
                  className="text-brand-600 hover:text-brand-500 dark:text-brand-400 font-medium"
                >
                  立即登录
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 版权信息 */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            © 2024 CBEC ERP. 保留所有权利。
          </p>
        </div>
      </div>
    </div>
  );
}
