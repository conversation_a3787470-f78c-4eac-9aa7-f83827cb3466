/**
 * AI智能商品推荐API路由
 * 提供基于用户行为和商品特征的智能推荐服务
 */

import { NextRequest, NextResponse } from 'next/server';
import { RecommendationService } from '@/lib/ai/recommendation/RecommendationService';
import { RecommendationRequest } from '@/lib/ai/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// 创建推荐服务实例
let recommendationService: RecommendationService | null = null;

/**
 * 初始化推荐服务
 */
async function initializeService(): Promise<RecommendationService> {
  if (!recommendationService) {
    recommendationService = new RecommendationService();
    await recommendationService.initialize();
  }
  return recommendationService;
}

/**
 * POST /api/ai/recommendations
 * 获取商品推荐
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求数据
    const body = await request.json();
    const recommendationRequest: RecommendationRequest = {
      userId: body.userId || session.user.id,
      productId: body.productId,
      category: body.category,
      priceRange: body.priceRange,
      limit: body.limit || 10,
      excludeIds: body.excludeIds || []
    };

    // 验证请求参数
    if (!recommendationRequest.userId) {
      return NextResponse.json(
        { error: '用户ID不能为空' },
        { status: 400 }
      );
    }

    // 初始化推荐服务
    const service = await initializeService();

    // 获取推荐结果
    const recommendations = await service.getRecommendations(recommendationRequest);

    // 记录API调用日志
    console.log(`用户 ${recommendationRequest.userId} 获取推荐成功，返回 ${recommendations.recommendations.length} 个推荐商品`);

    return NextResponse.json({
      success: true,
      data: recommendations,
      message: '推荐获取成功'
    });

  } catch (error) {
    console.error('推荐API错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '推荐服务暂时不可用，请稍后重试',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/recommendations/status
 * 获取推荐服务状态
 */
export async function GET() {
  try {
    const service = recommendationService;
    
    return NextResponse.json({
      success: true,
      data: {
        isInitialized: service !== null,
        isReady: service?.isReady() || false,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('推荐服务状态检查错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '无法获取服务状态'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/ai/recommendations
 * 清理推荐服务资源
 */
export async function DELETE() {
  try {
    // 验证管理员权限
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      );
    }

    if (recommendationService) {
      recommendationService.dispose();
      recommendationService = null;
    }

    return NextResponse.json({
      success: true,
      message: '推荐服务资源已清理'
    });

  } catch (error) {
    console.error('推荐服务清理错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '资源清理失败'
      },
      { status: 500 }
    );
  }
}
