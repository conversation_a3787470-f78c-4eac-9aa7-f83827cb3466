/**
 * AI智能定价策略API路由
 * 提供基于机器学习的动态定价建议和利润优化
 */

import { NextRequest, NextResponse } from 'next/server';
import { PricingService } from '@/lib/ai/pricing/PricingService';
import { PricingRequest } from '@/lib/ai/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// 创建定价服务实例
let pricingService: PricingService | null = null;

/**
 * 初始化定价服务
 */
async function initializeService(): Promise<PricingService> {
  if (!pricingService) {
    pricingService = new PricingService();
    await pricingService.initialize();
  }
  return pricingService;
}

/**
 * POST /api/ai/pricing
 * 获取智能定价建议
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份和权限
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查定价权限
    if (!session.user.permissions?.includes('pricing:read')) {
      return NextResponse.json(
        { error: '没有定价查看权限' },
        { status: 403 }
      );
    }

    // 解析请求数据
    const body = await request.json();
    const pricingRequest: PricingRequest = {
      productId: body.productId,
      currentPrice: body.currentPrice,
      cost: body.cost,
      competitorPrices: body.competitorPrices,
      salesHistory: body.salesHistory?.map((item: any) => ({
        date: new Date(item.date),
        price: item.price,
        quantity: item.quantity,
        revenue: item.revenue
      })),
      marketConditions: body.marketConditions
    };

    // 验证必需参数
    if (!pricingRequest.productId || !pricingRequest.currentPrice || !pricingRequest.cost) {
      return NextResponse.json(
        { error: '商品ID、当前价格和成本不能为空' },
        { status: 400 }
      );
    }

    if (pricingRequest.currentPrice <= 0 || pricingRequest.cost <= 0) {
      return NextResponse.json(
        { error: '价格和成本必须大于0' },
        { status: 400 }
      );
    }

    if (pricingRequest.currentPrice < pricingRequest.cost) {
      return NextResponse.json(
        { error: '当前价格不能低于成本' },
        { status: 400 }
      );
    }

    // 初始化定价服务
    const service = await initializeService();

    // 获取定价建议
    const pricingRecommendation = await service.getPricingRecommendation(pricingRequest);

    // 记录API调用日志
    console.log(`商品 ${pricingRequest.productId} 定价分析完成，建议价格: ${pricingRecommendation.recommendedPrice}`);

    return NextResponse.json({
      success: true,
      data: pricingRecommendation,
      message: '定价分析完成'
    });

  } catch (error) {
    console.error('定价API错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '定价服务暂时不可用，请稍后重试',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/pricing/status
 * 获取定价服务状态
 */
export async function GET() {
  try {
    const service = pricingService;
    
    return NextResponse.json({
      success: true,
      data: {
        isInitialized: service !== null,
        isReady: service?.isReady() || false,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('定价服务状态检查错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '无法获取服务状态'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/ai/pricing
 * 清理定价服务资源
 */
export async function DELETE() {
  try {
    // 验证管理员权限
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      );
    }

    if (pricingService) {
      pricingService.dispose();
      pricingService = null;
    }

    return NextResponse.json({
      success: true,
      message: '定价服务资源已清理'
    });

  } catch (error) {
    console.error('定价服务清理错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '资源清理失败'
      },
      { status: 500 }
    );
  }
}
