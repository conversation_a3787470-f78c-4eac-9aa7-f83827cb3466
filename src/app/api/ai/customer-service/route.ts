/**
 * AI智能客户服务API路由
 * 提供聊天机器人、情感分析和自动回复服务
 */

import { NextRequest, NextResponse } from 'next/server';
import { CustomerService } from '@/lib/ai/customer/CustomerService';
import { CustomerServiceRequest } from '@/lib/ai/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// 创建客户服务实例
let customerService: CustomerService | null = null;

/**
 * 初始化客户服务
 */
async function initializeService(): Promise<CustomerService> {
  if (!customerService) {
    customerService = new CustomerService();
    await customerService.initialize();
  }
  return customerService;
}

/**
 * POST /api/ai/customer-service
 * 处理客户服务请求
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求数据
    const body = await request.json();
    const serviceRequest: CustomerServiceRequest = {
      message: body.message,
      customerId: body.customerId || session.user.id,
      language: body.language || 'zh-CN',
      context: body.context,
      intent: body.intent
    };

    // 验证必需参数
    if (!serviceRequest.message || serviceRequest.message.trim().length === 0) {
      return NextResponse.json(
        { error: '消息内容不能为空' },
        { status: 400 }
      );
    }

    if (serviceRequest.message.length > 1000) {
      return NextResponse.json(
        { error: '消息内容过长，请控制在1000字符以内' },
        { status: 400 }
      );
    }

    // 初始化客户服务
    const service = await initializeService();

    // 处理客户服务请求
    const serviceResponse = await service.handleCustomerRequest(serviceRequest);

    // 记录API调用日志
    console.log(`客户 ${serviceRequest.customerId} 服务请求处理完成，意图: ${serviceResponse.intent}`);

    return NextResponse.json({
      success: true,
      data: serviceResponse,
      message: '客户服务请求处理完成'
    });

  } catch (error) {
    console.error('客户服务API错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '客户服务暂时不可用，请稍后重试',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/customer-service/status
 * 获取客户服务状态
 */
export async function GET() {
  try {
    const service = customerService;
    
    return NextResponse.json({
      success: true,
      data: {
        isInitialized: service !== null,
        isReady: service?.isReady() || false,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('客户服务状态检查错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '无法获取服务状态'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/ai/customer-service
 * 清理客户服务资源
 */
export async function DELETE() {
  try {
    // 验证管理员权限
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      );
    }

    if (customerService) {
      customerService.dispose();
      customerService = null;
    }

    return NextResponse.json({
      success: true,
      message: '客户服务资源已清理'
    });

  } catch (error) {
    console.error('客户服务清理错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '资源清理失败'
      },
      { status: 500 }
    );
  }
}
