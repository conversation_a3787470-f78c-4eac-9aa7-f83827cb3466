/**
 * AI智能库存管理API路由
 * 提供需求预测、库存优化和补货建议服务
 */

import { NextRequest, NextResponse } from 'next/server';
import { InventoryService } from '@/lib/ai/inventory/InventoryService';
import { InventoryPredictionRequest } from '@/lib/ai/types';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// 创建库存服务实例
let inventoryService: InventoryService | null = null;

/**
 * 初始化库存服务
 */
async function initializeService(): Promise<InventoryService> {
  if (!inventoryService) {
    inventoryService = new InventoryService();
    await inventoryService.initialize();
  }
  return inventoryService;
}

/**
 * POST /api/ai/inventory
 * 获取库存预测和优化建议
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份和权限
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    // 检查库存管理权限
    if (!session.user.permissions?.includes('inventory:read')) {
      return NextResponse.json(
        { error: '没有库存查看权限' },
        { status: 403 }
      );
    }

    // 解析请求数据
    const body = await request.json();
    const inventoryRequest: InventoryPredictionRequest = {
      productId: body.productId,
      currentStock: body.currentStock,
      salesHistory: body.salesHistory?.map((item: any) => ({
        date: new Date(item.date),
        price: item.price,
        quantity: item.quantity,
        revenue: item.revenue
      })) || [],
      leadTime: body.leadTime || 7,
      seasonalFactors: body.seasonalFactors,
      promotions: body.promotions?.map((promo: any) => ({
        startDate: new Date(promo.startDate),
        endDate: new Date(promo.endDate),
        expectedImpact: promo.expectedImpact
      }))
    };

    // 验证必需参数
    if (!inventoryRequest.productId || inventoryRequest.currentStock === undefined) {
      return NextResponse.json(
        { error: '商品ID和当前库存不能为空' },
        { status: 400 }
      );
    }

    if (inventoryRequest.currentStock < 0) {
      return NextResponse.json(
        { error: '当前库存不能为负数' },
        { status: 400 }
      );
    }

    if (inventoryRequest.leadTime <= 0) {
      return NextResponse.json(
        { error: '交货时间必须大于0' },
        { status: 400 }
      );
    }

    // 初始化库存服务
    const service = await initializeService();

    // 获取库存预测
    const inventoryPrediction = await service.getInventoryPrediction(inventoryRequest);

    // 记录API调用日志
    console.log(`商品 ${inventoryRequest.productId} 库存分析完成，再订货点: ${inventoryPrediction.reorderPoint}`);

    return NextResponse.json({
      success: true,
      data: inventoryPrediction,
      message: '库存分析完成'
    });

  } catch (error) {
    console.error('库存API错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '库存服务暂时不可用，请稍后重试',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/ai/inventory/status
 * 获取库存服务状态
 */
export async function GET() {
  try {
    const service = inventoryService;
    
    return NextResponse.json({
      success: true,
      data: {
        isInitialized: service !== null,
        isReady: service?.isReady() || false,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('库存服务状态检查错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '无法获取服务状态'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/ai/inventory
 * 清理库存服务资源
 */
export async function DELETE() {
  try {
    // 验证管理员权限
    const session = await getServerSession(authOptions);
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: '需要管理员权限' },
        { status: 403 }
      );
    }

    if (inventoryService) {
      inventoryService.dispose();
      inventoryService = null;
    }

    return NextResponse.json({
      success: true,
      message: '库存服务资源已清理'
    });

  } catch (error) {
    console.error('库存服务清理错误:', error);
    
    return NextResponse.json(
      { 
        success: false,
        error: '资源清理失败'
      },
      { status: 500 }
    );
  }
}
