/**
 * 支付管理API路由
 * 处理支付的查询和创建操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createPaymentSchema, paginationSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse } from '@/types';

// 支付信息类型
interface PaymentInfo {
  id: string;
  orderId: string;
  paymentMethod: string;
  paymentProvider: string;
  amount: number;
  currency: string;
  exchangeRate: number;
  status: string;
  providerPaymentId?: string;
  providerResponse?: any;
  processedAt?: string;
  createdAt: string;
  updatedAt: string;
  order: {
    id: string;
    orderNumber: string;
    totalAmount: number;
    customer?: {
      id: string;
      email: string;
      firstName?: string;
      lastName?: string;
    };
  };
  refunds: Array<{
    id: string;
    amount: number;
    reason?: string;
    status: string;
    createdAt: string;
  }>;
}

/**
 * GET /api/payments
 * 获取支付列表（分页）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginatedResponse<PaymentInfo>>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sort: searchParams.get('sort') || 'createdAt',
      order: searchParams.get('order') || 'desc',
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
      paymentMethod: searchParams.get('paymentMethod') || '',
      dateFrom: searchParams.get('dateFrom') || '',
      dateTo: searchParams.get('dateTo') || '',
    };

    // 验证分页参数
    const paginationResult = paginationSchema.safeParse(queryParams);
    if (!paginationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '分页参数验证失败',
            details: paginationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, sort, order } = paginationResult.data;
    const { search, status, paymentMethod, dateFrom, dateTo } = queryParams;

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { providerPaymentId: { contains: search, mode: 'insensitive' } },
        { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
        { order: { customer: { email: { contains: search, mode: 'insensitive' } } } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (paymentMethod) {
      where.paymentMethod = paymentMethod;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询支付总数
    const total = await prisma.payment.count({ where });

    // 查询支付列表
    const payments = await prisma.payment.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sort]: order },
      include: {
        order: {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true,
            customer: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        refunds: {
          select: {
            id: true,
            amount: true,
            reason: true,
            status: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    // 格式化支付数据
    const formattedPayments: PaymentInfo[] = payments.map(payment => ({
      id: payment.id,
      orderId: payment.orderId,
      paymentMethod: payment.paymentMethod,
      paymentProvider: payment.paymentProvider,
      amount: payment.amount,
      currency: payment.currency,
      exchangeRate: payment.exchangeRate,
      status: payment.status,
      providerPaymentId: payment.providerPaymentId || undefined,
      providerResponse: payment.providerResponse,
      processedAt: payment.processedAt?.toISOString(),
      createdAt: payment.createdAt.toISOString(),
      updatedAt: payment.updatedAt.toISOString(),
      order: payment.order,
      refunds: payment.refunds.map(refund => ({
        id: refund.id,
        amount: refund.amount,
        reason: refund.reason || undefined,
        status: refund.status,
        createdAt: refund.createdAt.toISOString(),
      })),
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const responseData: PaginatedResponse<PaymentInfo> = {
      data: formattedPayments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取支付列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/payments
 * 创建新支付记录
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<PaymentInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createPaymentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const paymentData = validationResult.data;

    // 检查订单是否存在
    const order = await prisma.order.findUnique({
      where: { id: paymentData.orderId },
      include: {
        customer: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: '订单不存在',
          },
        },
        { status: 404 }
      );
    }

    // 创建支付记录
    const newPayment = await prisma.payment.create({
      data: {
        orderId: paymentData.orderId,
        paymentMethod: paymentData.paymentMethod,
        paymentProvider: paymentData.paymentProvider,
        amount: paymentData.amount,
        currency: paymentData.currency,
        exchangeRate: paymentData.exchangeRate,
        status: 'PENDING',
        providerPaymentId: paymentData.providerPaymentId,
        providerResponse: paymentData.providerResponse,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
      include: {
        order: {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true,
            customer: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        refunds: {
          select: {
            id: true,
            amount: true,
            reason: true,
            status: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    // 格式化响应数据
    const responseData: PaymentInfo = {
      id: newPayment.id,
      orderId: newPayment.orderId,
      paymentMethod: newPayment.paymentMethod,
      paymentProvider: newPayment.paymentProvider,
      amount: newPayment.amount,
      currency: newPayment.currency,
      exchangeRate: newPayment.exchangeRate,
      status: newPayment.status,
      providerPaymentId: newPayment.providerPaymentId || undefined,
      providerResponse: newPayment.providerResponse,
      processedAt: newPayment.processedAt?.toISOString(),
      createdAt: newPayment.createdAt.toISOString(),
      updatedAt: newPayment.updatedAt.toISOString(),
      order: newPayment.order,
      refunds: newPayment.refunds.map(refund => ({
        id: refund.id,
        amount: refund.amount,
        reason: refund.reason || undefined,
        status: refund.status,
        createdAt: refund.createdAt.toISOString(),
      })),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建支付API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
