/**
 * Stripe支付意图API
 * 创建和管理Stripe支付意图
 */

import { NextRequest, NextResponse } from 'next/server';
import { stripeService } from '@/lib/integrations/stripe-service';
import { verifyToken } from '@/lib/auth';
import { z } from 'zod';

// 创建支付意图请求验证模式
const createPaymentIntentSchema = z.object({
  amount: z.number().positive('金额必须大于0'),
  currency: z.string().min(3).max(3, '货币代码必须是3位'),
  orderId: z.string().min(1, '订单ID不能为空'),
  paymentMethodTypes: z.array(z.string()).optional(),
  captureMethod: z.enum(['automatic', 'manual']).optional(),
  returnUrl: z.string().url().optional(),
  description: z.string().optional(),
});

// 确认支付意图请求验证模式
const confirmPaymentIntentSchema = z.object({
  paymentIntentId: z.string().min(1, '支付意图ID不能为空'),
  paymentMethodId: z.string().optional(),
});

/**
 * 创建支付意图
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const user = await verifyToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    
    // 验证请求数据
    const validationResult = createPaymentIntentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求参数验证失败',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // 创建支付意图
    const result = await stripeService.createPaymentIntent({
      amount: data.amount,
      currency: data.currency,
      orderId: data.orderId,
      paymentMethodTypes: data.paymentMethodTypes as any,
      captureMethod: data.captureMethod,
      returnUrl: data.returnUrl,
      description: data.description,
      metadata: {
        userId: user.id,
        userEmail: user.email,
      },
    });

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
    });
  } catch (error) {
    console.error('创建Stripe支付意图失败:', error);
    return NextResponse.json(
      { success: false, error: '内部服务器错误' },
      { status: 500 }
    );
  }
}

/**
 * 确认支付意图
 */
export async function PUT(request: NextRequest) {
  try {
    // 验证用户身份
    const user = await verifyToken(request);
    if (!user) {
      return NextResponse.json(
        { success: false, error: '未授权访问' },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();
    
    // 验证请求数据
    const validationResult = confirmPaymentIntentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: '请求参数验证失败',
          details: validationResult.error.errors,
        },
        { status: 400 }
      );
    }

    const data = validationResult.data;

    // 确认支付意图
    const result = await stripeService.confirmPaymentIntent(
      data.paymentIntentId,
      data.paymentMethodId
    );

    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result.data,
    });
  } catch (error) {
    console.error('确认Stripe支付意图失败:', error);
    return NextResponse.json(
      { success: false, error: '内部服务器错误' },
      { status: 500 }
    );
  }
}
