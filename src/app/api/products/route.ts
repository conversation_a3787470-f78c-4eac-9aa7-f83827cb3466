/**
 * 商品管理API路由
 * 处理商品的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createProductSchema, paginationSchema, searchFilterSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse } from '@/types';

// 商品信息类型
interface ProductInfo {
  id: string;
  sku: string;
  name: string;
  description?: string;
  shortDescription?: string;
  brand?: string;
  model?: string;
  weight?: number;
  dimensions?: any;
  basePrice: number;
  costPrice?: number;
  currency: string;
  isDigital: boolean;
  requiresShipping: boolean;
  trackInventory: boolean;
  allowBackorder: boolean;
  status: string;
  createdAt: string;
  updatedAt: string;
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  images: Array<{
    id: string;
    url: string;
    altText?: string;
    sortOrder: number;
  }>;
  variants: Array<{
    id: string;
    sku: string;
    name: string;
    price: number;
    attributes: any;
  }>;
  inventory: {
    totalStock: number;
    availableStock: number;
    reservedStock: number;
  };
}

/**
 * GET /api/products
 * 获取商品列表（分页）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginatedResponse<ProductInfo>>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sort: searchParams.get('sort') || 'createdAt',
      order: searchParams.get('order') || 'desc',
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
      category: searchParams.get('category') || '',
      brand: searchParams.get('brand') || '',
    };

    // 验证分页参数
    const paginationResult = paginationSchema.safeParse(queryParams);
    if (!paginationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '分页参数验证失败',
            details: paginationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, sort, order } = paginationResult.data;
    const { search, status, category, brand } = queryParams;

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { sku: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { brand: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (category) {
      where.categoryId = category;
    }

    if (brand) {
      where.brand = { contains: brand, mode: 'insensitive' };
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询商品总数
    const total = await prisma.product.count({ where });

    // 查询商品列表
    const products = await prisma.product.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sort]: order },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        images: {
          select: {
            id: true,
            url: true,
            altText: true,
            sortOrder: true,
          },
          orderBy: { sortOrder: 'asc' },
        },
        variants: {
          select: {
            id: true,
            sku: true,
            name: true,
            price: true,
            attributes: true,
          },
        },
        inventoryItems: {
          select: {
            quantity: true,
            reservedQuantity: true,
          },
        },
      },
    });

    // 格式化商品数据
    const formattedProducts: ProductInfo[] = products.map(product => {
      const totalStock = product.inventoryItems.reduce((sum, item) => sum + item.quantity, 0);
      const reservedStock = product.inventoryItems.reduce((sum, item) => sum + item.reservedQuantity, 0);
      const availableStock = totalStock - reservedStock;

      return {
        id: product.id,
        sku: product.sku,
        name: product.name,
        description: product.description || undefined,
        shortDescription: product.shortDescription || undefined,
        brand: product.brand || undefined,
        model: product.model || undefined,
        weight: product.weight || undefined,
        dimensions: product.dimensions,
        basePrice: product.basePrice,
        costPrice: product.costPrice || undefined,
        currency: product.currency,
        isDigital: product.isDigital,
        requiresShipping: product.requiresShipping,
        trackInventory: product.trackInventory,
        allowBackorder: product.allowBackorder,
        status: product.status,
        createdAt: product.createdAt.toISOString(),
        updatedAt: product.updatedAt.toISOString(),
        category: product.category || undefined,
        images: product.images,
        variants: product.variants,
        inventory: {
          totalStock,
          availableStock,
          reservedStock,
        },
      };
    });

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const responseData: PaginatedResponse<ProductInfo> = {
      data: formattedProducts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取商品列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/products
 * 创建新商品
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<ProductInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createProductSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const productData = validationResult.data;

    // 检查SKU是否已存在
    const existingProduct = await prisma.product.findUnique({
      where: { sku: productData.sku },
    });

    if (existingProduct) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SKU_EXISTS',
            message: '该SKU已存在',
          },
        },
        { status: 409 }
      );
    }

    // 创建商品
    const newProduct = await prisma.product.create({
      data: {
        ...productData,
        status: 'DRAFT', // 默认为草稿状态
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        images: {
          select: {
            id: true,
            url: true,
            altText: true,
            sortOrder: true,
          },
          orderBy: { sortOrder: 'asc' },
        },
        variants: {
          select: {
            id: true,
            sku: true,
            name: true,
            price: true,
            attributes: true,
          },
        },
        inventoryItems: {
          select: {
            quantity: true,
            reservedQuantity: true,
          },
        },
      },
    });

    // 格式化响应数据
    const totalStock = newProduct.inventoryItems.reduce((sum, item) => sum + item.quantity, 0);
    const reservedStock = newProduct.inventoryItems.reduce((sum, item) => sum + item.reservedQuantity, 0);
    const availableStock = totalStock - reservedStock;

    const responseData: ProductInfo = {
      id: newProduct.id,
      sku: newProduct.sku,
      name: newProduct.name,
      description: newProduct.description || undefined,
      shortDescription: newProduct.shortDescription || undefined,
      brand: newProduct.brand || undefined,
      model: newProduct.model || undefined,
      weight: newProduct.weight || undefined,
      dimensions: newProduct.dimensions,
      basePrice: newProduct.basePrice,
      costPrice: newProduct.costPrice || undefined,
      currency: newProduct.currency,
      isDigital: newProduct.isDigital,
      requiresShipping: newProduct.requiresShipping,
      trackInventory: newProduct.trackInventory,
      allowBackorder: newProduct.allowBackorder,
      status: newProduct.status,
      createdAt: newProduct.createdAt.toISOString(),
      updatedAt: newProduct.updatedAt.toISOString(),
      category: newProduct.category || undefined,
      images: newProduct.images,
      variants: newProduct.variants,
      inventory: {
        totalStock,
        availableStock,
        reservedStock,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建商品API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
