/**
 * 商品分类管理API路由
 * 处理商品分类的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createCategorySchema } from '@/lib/validations';
import { ApiResponse } from '@/types';

// 分类信息类型
interface CategoryInfo {
  id: string;
  parentId?: string;
  name: string;
  slug: string;
  description?: string;
  imageUrl?: string;
  sortOrder: number;
  isActive: boolean;
  seoTitle?: string;
  seoDescription?: string;
  createdAt: string;
  updatedAt: string;
  children?: CategoryInfo[];
  parent?: {
    id: string;
    name: string;
    slug: string;
  };
  productCount: number;
}

/**
 * GET /api/categories
 * 获取分类列表（树形结构）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<CategoryInfo[]>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const flat = searchParams.get('flat') === 'true';

    // 构建查询条件
    const where: any = {};
    if (!includeInactive) {
      where.isActive = true;
    }

    // 查询所有分类
    const categories = await prisma.category.findMany({
      where,
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' },
      ],
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    // 格式化分类数据
    const formattedCategories: CategoryInfo[] = categories.map(category => ({
      id: category.id,
      parentId: category.parentId || undefined,
      name: category.name,
      slug: category.slug,
      description: category.description || undefined,
      imageUrl: category.imageUrl || undefined,
      sortOrder: category.sortOrder,
      isActive: category.isActive,
      seoTitle: category.seoTitle || undefined,
      seoDescription: category.seoDescription || undefined,
      createdAt: category.createdAt.toISOString(),
      updatedAt: category.updatedAt.toISOString(),
      parent: category.parent || undefined,
      productCount: category._count.products,
    }));

    // 如果需要平铺结构，直接返回
    if (flat) {
      return NextResponse.json(
        {
          success: true,
          data: formattedCategories,
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: crypto.randomUUID(),
            version: 'v1',
          },
        },
        { status: 200 }
      );
    }

    // 构建树形结构
    const categoryMap = new Map<string, CategoryInfo>();
    const rootCategories: CategoryInfo[] = [];

    // 第一遍：创建映射
    formattedCategories.forEach(category => {
      categoryMap.set(category.id, { ...category, children: [] });
    });

    // 第二遍：构建树形结构
    formattedCategories.forEach(category => {
      const categoryNode = categoryMap.get(category.id)!;
      
      if (category.parentId) {
        const parent = categoryMap.get(category.parentId);
        if (parent) {
          parent.children!.push(categoryNode);
        }
      } else {
        rootCategories.push(categoryNode);
      }
    });

    return NextResponse.json(
      {
        success: true,
        data: rootCategories,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取分类列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/categories
 * 创建新分类
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<CategoryInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createCategorySchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const categoryData = validationResult.data;

    // 检查slug是否已存在
    const existingCategory = await prisma.category.findUnique({
      where: { slug: categoryData.slug },
    });

    if (existingCategory) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SLUG_EXISTS',
            message: '该URL别名已存在',
          },
        },
        { status: 409 }
      );
    }

    // 如果有父分类，检查父分类是否存在
    if (categoryData.parentId) {
      const parentCategory = await prisma.category.findUnique({
        where: { id: categoryData.parentId },
      });

      if (!parentCategory) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'PARENT_NOT_FOUND',
              message: '父分类不存在',
            },
          },
          { status: 404 }
        );
      }
    }

    // 创建分类
    const newCategory = await prisma.category.create({
      data: {
        ...categoryData,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
      include: {
        parent: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        _count: {
          select: {
            products: true,
          },
        },
      },
    });

    // 格式化响应数据
    const responseData: CategoryInfo = {
      id: newCategory.id,
      parentId: newCategory.parentId || undefined,
      name: newCategory.name,
      slug: newCategory.slug,
      description: newCategory.description || undefined,
      imageUrl: newCategory.imageUrl || undefined,
      sortOrder: newCategory.sortOrder,
      isActive: newCategory.isActive,
      seoTitle: newCategory.seoTitle || undefined,
      seoDescription: newCategory.seoDescription || undefined,
      createdAt: newCategory.createdAt.toISOString(),
      updatedAt: newCategory.updatedAt.toISOString(),
      parent: newCategory.parent || undefined,
      productCount: newCategory._count.products,
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建分类API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
