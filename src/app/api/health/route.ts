/**
 * 系统健康检查API路由
 * 提供系统状态监控和健康检查功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { checkDatabaseConnection, getDatabaseStats } from '@/lib/prisma';
import { ApiResponse } from '@/types';

// 健康检查响应类型
interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  uptime: number;
  services: {
    database: {
      status: 'up' | 'down';
      responseTime?: number;
      details?: any;
    };
    redis: {
      status: 'up' | 'down';
      responseTime?: number;
    };
    external: {
      [key: string]: {
        status: 'up' | 'down';
        responseTime?: number;
      };
    };
  };
  system: {
    memory: {
      used: number;
      total: number;
      percentage: number;
    };
    cpu: {
      usage: number;
    };
    disk: {
      used: number;
      total: number;
      percentage: number;
    };
  };
}

/**
 * 检查数据库健康状态
 */
async function checkDatabaseHealth(): Promise<{
  status: 'up' | 'down';
  responseTime?: number;
  details?: any;
}> {
  const startTime = Date.now();
  
  try {
    const isConnected = await checkDatabaseConnection();
    const responseTime = Date.now() - startTime;
    
    if (isConnected) {
      const stats = await getDatabaseStats();
      return {
        status: 'up',
        responseTime,
        details: {
          connected: true,
          stats: stats ? 'available' : 'unavailable',
        },
      };
    } else {
      return {
        status: 'down',
        responseTime,
        details: {
          connected: false,
          error: 'Database connection failed',
        },
      };
    }
  } catch (error) {
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
      details: {
        connected: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
    };
  }
}

/**
 * 检查Redis健康状态
 */
async function checkRedisHealth(): Promise<{
  status: 'up' | 'down';
  responseTime?: number;
}> {
  const startTime = Date.now();
  
  try {
    // 这里应该实现Redis连接检查
    // 暂时返回模拟状态
    const responseTime = Date.now() - startTime;
    return {
      status: 'up',
      responseTime,
    };
  } catch (error) {
    return {
      status: 'down',
      responseTime: Date.now() - startTime,
    };
  }
}

/**
 * 检查外部服务健康状态
 */
async function checkExternalServices(): Promise<{
  [key: string]: {
    status: 'up' | 'down';
    responseTime?: number;
  };
}> {
  const services = {
    stripe: 'https://status.stripe.com',
    paypal: 'https://status.paypal.com',
    // 可以添加更多外部服务
  };

  const results: { [key: string]: { status: 'up' | 'down'; responseTime?: number } } = {};

  for (const [name, url] of Object.entries(services)) {
    const startTime = Date.now();
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        timeout: 5000,
      });
      
      results[name] = {
        status: response.ok ? 'up' : 'down',
        responseTime: Date.now() - startTime,
      };
    } catch (error) {
      results[name] = {
        status: 'down',
        responseTime: Date.now() - startTime,
      };
    }
  }

  return results;
}

/**
 * 获取系统资源使用情况
 */
function getSystemInfo(): {
  memory: { used: number; total: number; percentage: number };
  cpu: { usage: number };
  disk: { used: number; total: number; percentage: number };
} {
  // 在Node.js环境中获取系统信息
  const memoryUsage = process.memoryUsage();
  
  return {
    memory: {
      used: memoryUsage.heapUsed,
      total: memoryUsage.heapTotal,
      percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100,
    },
    cpu: {
      usage: 0, // 需要使用专门的库来获取CPU使用率
    },
    disk: {
      used: 0,  // 需要使用专门的库来获取磁盘使用情况
      total: 0,
      percentage: 0,
    },
  };
}

/**
 * GET /api/health
 * 系统健康检查
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<HealthCheckResponse>>> {
  try {
    const startTime = Date.now();

    // 并行检查各个服务
    const [databaseHealth, redisHealth, externalServices] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealth(),
      checkExternalServices(),
    ]);

    // 获取系统信息
    const systemInfo = getSystemInfo();

    // 计算总体健康状态
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded' = 'healthy';
    
    if (databaseHealth.status === 'down') {
      overallStatus = 'unhealthy';
    } else if (redisHealth.status === 'down' || 
               Object.values(externalServices).some(service => service.status === 'down')) {
      overallStatus = 'degraded';
    }

    // 构建响应数据
    const healthData: HealthCheckResponse = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      uptime: process.uptime(),
      services: {
        database: databaseHealth,
        redis: redisHealth,
        external: externalServices,
      },
      system: systemInfo,
    };

    // 根据健康状态设置HTTP状态码
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    return NextResponse.json(
      {
        success: true,
        data: healthData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
          processingTime: Date.now() - startTime,
        },
      },
      { status: httpStatus }
    );
  } catch (error) {
    console.error('健康检查API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'HEALTH_CHECK_FAILED',
          message: '健康检查失败',
          details: error instanceof Error ? error.message : 'Unknown error',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * HEAD /api/health
 * 简单的健康检查（仅返回状态码）
 */
export async function HEAD(): Promise<NextResponse> {
  try {
    const isDbHealthy = await checkDatabaseConnection();
    return new NextResponse(null, {
      status: isDbHealthy ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}

/**
 * OPTIONS /api/health
 * 处理CORS预检请求
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400',
    },
  });
}
