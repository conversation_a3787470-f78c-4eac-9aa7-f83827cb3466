/**
 * Stripe Webhook处理API
 * 处理来自Stripe的Webhook事件
 */

import { NextRequest, NextResponse } from 'next/server';
import { stripeService } from '@/lib/integrations/stripe-service';

export async function POST(request: NextRequest) {
  try {
    // 获取原始请求体
    const body = await request.text();
    
    // 获取Stripe签名
    const signature = request.headers.get('stripe-signature');
    
    if (!signature) {
      console.error('缺少Stripe签名');
      return NextResponse.json(
        { error: '缺少Stripe签名' },
        { status: 400 }
      );
    }

    // 处理Webhook事件
    const result = await stripeService.handleWebhook(body, signature);

    if (!result.success) {
      console.error('处理Stripe Webhook失败:', result.error);
      return NextResponse.json(
        { error: result.error },
        { status: 400 }
      );
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Stripe Webhook处理异常:', error);
    return NextResponse.json(
      { error: '内部服务器错误' },
      { status: 500 }
    );
  }
}

// 禁用Next.js的请求体解析，因为我们需要原始请求体来验证Stripe签名
export const runtime = 'nodejs';
export const preferredRegion = 'auto';
