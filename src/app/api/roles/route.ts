/**
 * 角色管理API路由
 * 提供角色的CRUD操作功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { ApiResponse, PaginationParams, PaginationResult } from '@/types';

// 角色信息类型定义
export interface RoleInfo {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  createdAt: string;
  updatedAt: string;
  permissions?: PermissionInfo[];
  userCount?: number;
}

// 权限信息类型定义
export interface PermissionInfo {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
}

// 创建角色请求验证模式
const createRoleSchema = z.object({
  name: z.string().min(1, '角色名称不能为空').max(50, '角色名称不能超过50个字符'),
  displayName: z.string().min(1, '显示名称不能为空').max(100, '显示名称不能超过100个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  permissionIds: z.array(z.string()).optional().default([]),
});

// 更新角色请求验证模式
const updateRoleSchema = z.object({
  displayName: z.string().min(1, '显示名称不能为空').max(100, '显示名称不能超过100个字符').optional(),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  permissionIds: z.array(z.string()).optional(),
});

// 查询参数验证模式
const querySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).optional().default(1),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional().default(20),
  search: z.string().optional(),
  includePermissions: z.string().transform(val => val === 'true').optional().default(false),
  includeUserCount: z.string().transform(val => val === 'true').optional().default(false),
});

/**
 * GET /api/roles
 * 获取角色列表
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginationResult<RoleInfo>>>> {
  try {
    // 解析查询参数
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validationResult = querySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '查询参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, search, includePermissions, includeUserCount } = validationResult.data;

    // 构建查询条件
    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { displayName: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }

    // 计算总数
    const total = await prisma.role.count({ where });

    // 查询角色列表
    const roles = await prisma.role.findMany({
      where,
      include: {
        rolePermissions: includePermissions ? {
          include: {
            permission: true,
          },
        } : false,
        userRoles: includeUserCount ? {
          select: { id: true },
        } : false,
      },
      orderBy: [
        { isSystemRole: 'desc' }, // 系统角色优先
        { createdAt: 'desc' },
      ],
      skip: (page - 1) * limit,
      take: limit,
    });

    // 转换数据格式
    const roleList: RoleInfo[] = roles.map(role => ({
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description || undefined,
      isSystemRole: role.isSystemRole,
      createdAt: role.createdAt.toISOString(),
      updatedAt: role.updatedAt.toISOString(),
      permissions: includePermissions && role.rolePermissions ? 
        role.rolePermissions.map(rp => ({
          id: rp.permission.id,
          name: rp.permission.name,
          resource: rp.permission.resource,
          action: rp.permission.action,
          description: rp.permission.description || undefined,
        })) : undefined,
      userCount: includeUserCount && role.userRoles ? role.userRoles.length : undefined,
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json(
      {
        success: true,
        data: {
          items: roleList,
          pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取角色列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/roles
 * 创建新角色
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<RoleInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createRoleSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { name, displayName, description, permissionIds } = validationResult.data;

    // 检查角色名称是否已存在
    const existingRole = await prisma.role.findUnique({
      where: { name },
    });

    if (existingRole) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ROLE_EXISTS',
            message: '角色名称已存在',
          },
        },
        { status: 409 }
      );
    }

    // 验证权限ID是否有效
    if (permissionIds.length > 0) {
      const validPermissions = await prisma.permission.findMany({
        where: { id: { in: permissionIds } },
        select: { id: true },
      });

      const validPermissionIds = validPermissions.map(p => p.id);
      const invalidPermissionIds = permissionIds.filter(id => !validPermissionIds.includes(id));

      if (invalidPermissionIds.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INVALID_PERMISSIONS',
              message: '包含无效的权限ID',
              details: { invalidPermissionIds },
            },
          },
          { status: 400 }
        );
      }
    }

    // 使用事务创建角色和权限关联
    const result = await prisma.$transaction(async (tx) => {
      // 创建角色
      const newRole = await tx.role.create({
        data: {
          name,
          displayName,
          description,
          isSystemRole: false, // 用户创建的角色都不是系统角色
        },
      });

      // 创建角色权限关联
      if (permissionIds.length > 0) {
        await tx.rolePermission.createMany({
          data: permissionIds.map(permissionId => ({
            roleId: newRole.id,
            permissionId,
            grantedBy: 'admin', // TODO: 从认证信息获取当前用户ID
          })),
        });
      }

      // 查询完整的角色信息（包含权限）
      return await tx.role.findUnique({
        where: { id: newRole.id },
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
        },
      });
    });

    if (!result) {
      throw new Error('创建角色失败');
    }

    // 转换数据格式
    const responseData: RoleInfo = {
      id: result.id,
      name: result.name,
      displayName: result.displayName,
      description: result.description || undefined,
      isSystemRole: result.isSystemRole,
      createdAt: result.createdAt.toISOString(),
      updatedAt: result.updatedAt.toISOString(),
      permissions: result.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description || undefined,
      })),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
