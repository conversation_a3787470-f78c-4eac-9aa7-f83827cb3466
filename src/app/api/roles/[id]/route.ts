/**
 * 单个角色管理API路由
 * 提供角色的详细操作功能（获取、更新、删除）
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { ApiResponse } from '@/types';
import { RoleInfo, PermissionInfo } from '../route';

// 更新角色请求验证模式
const updateRoleSchema = z.object({
  displayName: z.string().min(1, '显示名称不能为空').max(100, '显示名称不能超过100个字符').optional(),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
  permissionIds: z.array(z.string()).optional(),
});

/**
 * GET /api/roles/[id]
 * 获取单个角色详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<RoleInfo>>> {
  try {
    const { id } = params;

    // 查询角色详情
    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        rolePermissions: {
          include: {
            permission: true,
          },
        },
        userRoles: {
          select: { id: true },
        },
      },
    });

    if (!role) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ROLE_NOT_FOUND',
            message: '角色不存在',
          },
        },
        { status: 404 }
      );
    }

    // 转换数据格式
    const responseData: RoleInfo = {
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description || undefined,
      isSystemRole: role.isSystemRole,
      createdAt: role.createdAt.toISOString(),
      updatedAt: role.updatedAt.toISOString(),
      permissions: role.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description || undefined,
      })),
      userCount: role.userRoles.length,
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取角色详情API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/roles/[id]
 * 更新角色信息
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<RoleInfo>>> {
  try {
    const { id } = params;

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = updateRoleSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { displayName, description, permissionIds } = validationResult.data;

    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id },
    });

    if (!existingRole) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ROLE_NOT_FOUND',
            message: '角色不存在',
          },
        },
        { status: 404 }
      );
    }

    // 检查是否为系统角色（系统角色不允许修改权限）
    if (existingRole.isSystemRole && permissionIds !== undefined) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYSTEM_ROLE_PROTECTED',
            message: '系统角色的权限不允许修改',
          },
        },
        { status: 403 }
      );
    }

    // 验证权限ID是否有效
    if (permissionIds && permissionIds.length > 0) {
      const validPermissions = await prisma.permission.findMany({
        where: { id: { in: permissionIds } },
        select: { id: true },
      });

      const validPermissionIds = validPermissions.map(p => p.id);
      const invalidPermissionIds = permissionIds.filter(id => !validPermissionIds.includes(id));

      if (invalidPermissionIds.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INVALID_PERMISSIONS',
              message: '包含无效的权限ID',
              details: { invalidPermissionIds },
            },
          },
          { status: 400 }
        );
      }
    }

    // 使用事务更新角色信息
    const result = await prisma.$transaction(async (tx) => {
      // 更新角色基本信息
      const updateData: any = {};
      if (displayName !== undefined) updateData.displayName = displayName;
      if (description !== undefined) updateData.description = description;

      const updatedRole = await tx.role.update({
        where: { id },
        data: updateData,
      });

      // 更新权限关联（如果提供了权限ID列表）
      if (permissionIds !== undefined) {
        // 删除现有的权限关联
        await tx.rolePermission.deleteMany({
          where: { roleId: id },
        });

        // 创建新的权限关联
        if (permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId: id,
              permissionId,
              grantedBy: 'admin', // TODO: 从认证信息获取当前用户ID
            })),
          });
        }
      }

      // 查询完整的角色信息（包含权限）
      return await tx.role.findUnique({
        where: { id },
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
          userRoles: {
            select: { id: true },
          },
        },
      });
    });

    if (!result) {
      throw new Error('更新角色失败');
    }

    // 转换数据格式
    const responseData: RoleInfo = {
      id: result.id,
      name: result.name,
      displayName: result.displayName,
      description: result.description || undefined,
      isSystemRole: result.isSystemRole,
      createdAt: result.createdAt.toISOString(),
      updatedAt: result.updatedAt.toISOString(),
      permissions: result.rolePermissions.map(rp => ({
        id: rp.permission.id,
        name: rp.permission.name,
        resource: rp.permission.resource,
        action: rp.permission.action,
        description: rp.permission.description || undefined,
      })),
      userCount: result.userRoles.length,
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('更新角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/roles/[id]
 * 删除角色
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<{ id: string }>>> {
  try {
    const { id } = params;

    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id },
      include: {
        userRoles: {
          select: { id: true },
        },
      },
    });

    if (!existingRole) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ROLE_NOT_FOUND',
            message: '角色不存在',
          },
        },
        { status: 404 }
      );
    }

    // 检查是否为系统角色（系统角色不允许删除）
    if (existingRole.isSystemRole) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'SYSTEM_ROLE_PROTECTED',
            message: '系统角色不允许删除',
          },
        },
        { status: 403 }
      );
    }

    // 检查是否有用户正在使用该角色
    if (existingRole.userRoles.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ROLE_IN_USE',
            message: '该角色正在被用户使用，无法删除',
            details: { userCount: existingRole.userRoles.length },
          },
        },
        { status: 409 }
      );
    }

    // 删除角色（级联删除权限关联）
    await prisma.role.delete({
      where: { id },
    });

    return NextResponse.json(
      {
        success: true,
        data: { id },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('删除角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
