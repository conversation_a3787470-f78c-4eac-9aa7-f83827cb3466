/**
 * 系统配置管理API路由
 * 提供配置的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { configService } from '@/lib/config/config-service';
import { auth } from '@/lib/auth';

/**
 * 获取配置值
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const key = searchParams.get('key');

    if (!key) {
      return NextResponse.json({ error: '缺少配置键' }, { status: 400 });
    }

    const config = await configService.getConfig(key);
    
    if (!config) {
      return NextResponse.json({ error: '配置不存在' }, { status: 404 });
    }

    return NextResponse.json({ config });
  } catch (error) {
    console.error('获取配置失败:', error);
    return NextResponse.json(
      { error: '获取配置失败' },
      { status: 500 }
    );
  }
}

/**
 * 更新配置值
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 检查权限
    if (!session.user.permissions?.includes('config:write')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const { key, value } = body;

    if (!key) {
      return NextResponse.json({ error: '缺少配置键' }, { status: 400 });
    }

    const result = await configService.setConfig(key, value, session.user.id);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('更新配置失败:', error);
    return NextResponse.json(
      { error: '更新配置失败' },
      { status: 500 }
    );
  }
}

/**
 * 创建配置项
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 检查权限
    if (!session.user.permissions?.includes('config:create')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const result = await configService.createConfig(body, session.user.id);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({ config: result.config });
  } catch (error) {
    console.error('创建配置失败:', error);
    return NextResponse.json(
      { error: '创建配置失败' },
      { status: 500 }
    );
  }
}

/**
 * 批量更新配置
 */
export async function PATCH(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 检查权限
    if (!session.user.permissions?.includes('config:write')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const body = await request.json();
    const result = await configService.batchUpdateConfigs({
      ...body,
      userId: session.user.id,
    });

    if (!result.success) {
      return NextResponse.json({ 
        error: result.error,
        errors: result.errors 
      }, { status: 400 });
    }

    return NextResponse.json({ success: true, errors: result.errors });
  } catch (error) {
    console.error('批量更新配置失败:', error);
    return NextResponse.json(
      { error: '批量更新配置失败' },
      { status: 500 }
    );
  }
}
