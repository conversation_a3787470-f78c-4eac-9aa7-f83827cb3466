/**
 * 配置导出API路由
 * 提供配置数据的导出功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { configService, ConfigType } from '@/lib/config/config-service';
import { auth } from '@/lib/auth';

/**
 * 导出配置
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 检查权限
    if (!session.user.permissions?.includes('config:export')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as ConfigType;
    const includeEncrypted = searchParams.get('includeEncrypted') === 'true';

    const result = await configService.exportConfigs(
      type || undefined,
      includeEncrypted
    );

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    // 返回JSON文件
    const filename = type 
      ? `config_${type.toLowerCase()}_${new Date().toISOString().split('T')[0]}.json`
      : `config_all_${new Date().toISOString().split('T')[0]}.json`;

    return new NextResponse(JSON.stringify(result.data, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });
  } catch (error) {
    console.error('导出配置失败:', error);
    return NextResponse.json(
      { error: '导出配置失败' },
      { status: 500 }
    );
  }
}
