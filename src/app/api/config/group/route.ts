/**
 * 配置组管理API路由
 * 提供按类型和组获取配置的功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { configService, ConfigType } from '@/lib/config/config-service';
import { auth } from '@/lib/auth';

/**
 * 获取配置组
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: '未授权' }, { status: 401 });
    }

    // 检查权限
    if (!session.user.permissions?.includes('config:read')) {
      return NextResponse.json({ error: '权限不足' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as ConfigType;
    const group = searchParams.get('group');

    if (!type || !Object.values(ConfigType).includes(type)) {
      return NextResponse.json({ error: '无效的配置类型' }, { status: 400 });
    }

    const result = await configService.getConfigGroup(type, group || undefined);

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    return NextResponse.json({ configs: result.configs });
  } catch (error) {
    console.error('获取配置组失败:', error);
    return NextResponse.json(
      { error: '获取配置组失败' },
      { status: 500 }
    );
  }
}
