/**
 * 用户管理API路由
 * 处理用户的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hashPassword } from '@/lib/auth';
import { paginationSchema, updateUserSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse } from '@/types';

// 用户信息类型
interface UserInfo {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  status: string;
  languageCode: string;
  timezone: string;
  emailVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  roles: Array<{
    id: string;
    name: string;
    displayName: string;
  }>;
}

/**
 * GET /api/users
 * 获取用户列表（分页）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginatedResponse<UserInfo>>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sort: searchParams.get('sort') || 'createdAt',
      order: searchParams.get('order') || 'desc',
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
    };

    // 验证分页参数
    const paginationResult = paginationSchema.safeParse(queryParams);
    if (!paginationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '分页参数验证失败',
            details: paginationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, sort, order } = paginationResult.data;
    const { search, status } = queryParams;

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        { username: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询用户总数
    const total = await prisma.user.count({ where });

    // 查询用户列表
    const users = await prisma.user.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sort]: order },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        phone: true,
        status: true,
        languageCode: true,
        timezone: true,
        emailVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        userRoles: {
          select: {
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
          },
        },
      },
    });

    // 格式化用户数据
    const formattedUsers: UserInfo[] = users.map(user => ({
      id: user.id,
      email: user.email,
      username: user.username || undefined,
      firstName: user.firstName || undefined,
      lastName: user.lastName || undefined,
      phone: user.phone || undefined,
      status: user.status,
      languageCode: user.languageCode,
      timezone: user.timezone,
      emailVerified: user.emailVerified,
      lastLoginAt: user.lastLoginAt?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      roles: user.userRoles.map(ur => ur.role),
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const responseData: PaginatedResponse<UserInfo> = {
      data: formattedUsers,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取用户列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/users
 * 创建新用户（管理员功能）
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<UserInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = updateUserSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { firstName, lastName, phone, languageCode, timezone, status, roles } = validationResult.data;

    // 检查邮箱是否已存在
    if (body.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: body.email },
      });

      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'EMAIL_EXISTS',
              message: '该邮箱已被注册',
            },
          },
          { status: 409 }
        );
      }
    }

    // 生成临时密码
    const tempPassword = Math.random().toString(36).slice(-8);
    const hashedPassword = await hashPassword(tempPassword);

    // 创建用户
    const newUser = await prisma.user.create({
      data: {
        email: body.email,
        passwordHash: hashedPassword,
        firstName,
        lastName,
        phone,
        languageCode: languageCode || 'zh-CN',
        timezone: timezone || 'Asia/Shanghai',
        status: status || 'ACTIVE',
        emailVerified: false,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        phone: true,
        status: true,
        languageCode: true,
        timezone: true,
        emailVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 分配角色
    if (roles && roles.length > 0) {
      const roleRecords = await prisma.role.findMany({
        where: { name: { in: roles } },
      });

      const userRoleData = roleRecords.map(role => ({
        userId: newUser.id,
        roleId: role.id,
        assignedBy: 'admin', // TODO: 从认证信息获取当前用户ID
      }));

      await prisma.userRole.createMany({
        data: userRoleData,
      });
    }

    // 获取用户角色信息
    const userWithRoles = await prisma.user.findUnique({
      where: { id: newUser.id },
      select: {
        userRoles: {
          select: {
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
          },
        },
      },
    });

    const responseData: UserInfo = {
      ...newUser,
      username: newUser.username || undefined,
      firstName: newUser.firstName || undefined,
      lastName: newUser.lastName || undefined,
      phone: newUser.phone || undefined,
      lastLoginAt: newUser.lastLoginAt?.toISOString(),
      createdAt: newUser.createdAt.toISOString(),
      updatedAt: newUser.updatedAt.toISOString(),
      roles: userWithRoles?.userRoles.map(ur => ur.role) || [],
    };

    // TODO: 发送账户创建通知邮件，包含临时密码

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建用户API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
