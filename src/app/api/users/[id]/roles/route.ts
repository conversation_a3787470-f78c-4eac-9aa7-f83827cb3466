/**
 * 用户角色分配API路由
 * 提供用户角色的分配和管理功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { ApiResponse } from '@/types';

// 用户角色信息类型定义
export interface UserRoleInfo {
  id: string;
  userId: string;
  roleId: string;
  assignedAt: string;
  assignedBy?: string;
  expiresAt?: string;
  role: {
    id: string;
    name: string;
    displayName: string;
    description?: string;
    isSystemRole: boolean;
  };
}

// 角色分配请求验证模式
const assignRolesSchema = z.object({
  roleIds: z.array(z.string()).min(1, '至少需要分配一个角色'),
  expiresAt: z.string().datetime().optional(), // ISO 8601 格式的过期时间
  assignedBy: z.string().optional(), // 分配者ID，通常从认证信息获取
});

// 批量角色分配请求验证模式
const batchAssignRolesSchema = z.object({
  userIds: z.array(z.string()).min(1, '至少需要选择一个用户'),
  roleIds: z.array(z.string()).min(1, '至少需要分配一个角色'),
  expiresAt: z.string().datetime().optional(),
  assignedBy: z.string().optional(),
});

/**
 * GET /api/users/[id]/roles
 * 获取用户的角色列表
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<UserRoleInfo[]>>> {
  try {
    const { id: userId } = params;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        },
        { status: 404 }
      );
    }

    // 查询用户的角色列表
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: {
        role: true,
      },
      orderBy: [
        { role: { isSystemRole: 'desc' } }, // 系统角色优先
        { assignedAt: 'desc' },
      ],
    });

    // 转换数据格式
    const roleList: UserRoleInfo[] = userRoles.map(userRole => ({
      id: userRole.id,
      userId: userRole.userId,
      roleId: userRole.roleId,
      assignedAt: userRole.assignedAt.toISOString(),
      assignedBy: userRole.assignedBy || undefined,
      expiresAt: userRole.expiresAt?.toISOString(),
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        displayName: userRole.role.displayName,
        description: userRole.role.description || undefined,
        isSystemRole: userRole.role.isSystemRole,
      },
    }));

    return NextResponse.json(
      {
        success: true,
        data: roleList,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取用户角色列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/users/[id]/roles
 * 为用户分配角色
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<UserRoleInfo[]>>> {
  try {
    const { id: userId } = params;

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = assignRolesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { roleIds, expiresAt, assignedBy } = validationResult.data;

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        },
        { status: 404 }
      );
    }

    // 验证角色ID是否有效
    const validRoles = await prisma.role.findMany({
      where: { id: { in: roleIds } },
      select: { id: true },
    });

    const validRoleIds = validRoles.map(r => r.id);
    const invalidRoleIds = roleIds.filter(id => !validRoleIds.includes(id));

    if (invalidRoleIds.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_ROLES',
            message: '包含无效的角色ID',
            details: { invalidRoleIds },
          },
        },
        { status: 400 }
      );
    }

    // 检查是否已经分配了这些角色
    const existingUserRoles = await prisma.userRole.findMany({
      where: {
        userId,
        roleId: { in: roleIds },
      },
      select: { roleId: true },
    });

    const existingRoleIds = existingUserRoles.map(ur => ur.roleId);
    const newRoleIds = roleIds.filter(id => !existingRoleIds.includes(id));

    if (newRoleIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ROLES_ALREADY_ASSIGNED',
            message: '所有角色都已分配给该用户',
          },
        },
        { status: 409 }
      );
    }

    // 使用事务分配角色
    const result = await prisma.$transaction(async (tx) => {
      // 创建用户角色关联
      await tx.userRole.createMany({
        data: newRoleIds.map(roleId => ({
          userId,
          roleId,
          assignedBy: assignedBy || 'system',
          expiresAt: expiresAt ? new Date(expiresAt) : null,
        })),
      });

      // 查询更新后的用户角色列表
      return await tx.userRole.findMany({
        where: { userId },
        include: {
          role: true,
        },
        orderBy: [
          { role: { isSystemRole: 'desc' } },
          { assignedAt: 'desc' },
        ],
      });
    });

    // 转换数据格式
    const roleList: UserRoleInfo[] = result.map(userRole => ({
      id: userRole.id,
      userId: userRole.userId,
      roleId: userRole.roleId,
      assignedAt: userRole.assignedAt.toISOString(),
      assignedBy: userRole.assignedBy || undefined,
      expiresAt: userRole.expiresAt?.toISOString(),
      role: {
        id: userRole.role.id,
        name: userRole.role.name,
        displayName: userRole.role.displayName,
        description: userRole.role.description || undefined,
        isSystemRole: userRole.role.isSystemRole,
      },
    }));

    return NextResponse.json(
      {
        success: true,
        data: roleList,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
          message: `成功为用户分配了 ${newRoleIds.length} 个角色`,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('分配用户角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/users/[id]/roles
 * 移除用户的角色
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<{ removedCount: number }>>> {
  try {
    const { id: userId } = params;

    // 解析查询参数
    const url = new URL(request.url);
    const roleIds = url.searchParams.get('roleIds')?.split(',') || [];

    if (roleIds.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'MISSING_ROLE_IDS',
            message: '请提供要移除的角色ID列表',
          },
        },
        { status: 400 }
      );
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        },
        { status: 404 }
      );
    }

    // 移除用户角色关联
    const deleteResult = await prisma.userRole.deleteMany({
      where: {
        userId,
        roleId: { in: roleIds },
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: { removedCount: deleteResult.count },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
          message: `成功移除了 ${deleteResult.count} 个角色`,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('移除用户角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
