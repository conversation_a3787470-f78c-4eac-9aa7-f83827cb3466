/**
 * 单个用户管理API路由
 * 处理特定用户的获取、更新和删除操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { updateUserSchema } from '@/lib/validations';
import { ApiResponse } from '@/types';

// 用户详细信息类型
interface UserDetail {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  status: string;
  languageCode: string;
  timezone: string;
  emailVerified: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy?: string;
  updatedBy?: string;
  roles: Array<{
    id: string;
    name: string;
    displayName: string;
    assignedAt: string;
    assignedBy?: string;
  }>;
}

/**
 * GET /api/users/[id]
 * 获取单个用户详细信息
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<UserDetail>>> {
  try {
    const { id } = params;

    // 查询用户信息
    const user = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        phone: true,
        status: true,
        languageCode: true,
        timezone: true,
        emailVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        createdBy: true,
        updatedBy: true,
        userRoles: {
          select: {
            assignedAt: true,
            assignedBy: true,
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        },
        { status: 404 }
      );
    }

    // 格式化用户数据
    const responseData: UserDetail = {
      id: user.id,
      email: user.email,
      username: user.username || undefined,
      firstName: user.firstName || undefined,
      lastName: user.lastName || undefined,
      phone: user.phone || undefined,
      status: user.status,
      languageCode: user.languageCode,
      timezone: user.timezone,
      emailVerified: user.emailVerified,
      lastLoginAt: user.lastLoginAt?.toISOString(),
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      createdBy: user.createdBy || undefined,
      updatedBy: user.updatedBy || undefined,
      roles: user.userRoles.map(ur => ({
        id: ur.role.id,
        name: ur.role.name,
        displayName: ur.role.displayName,
        assignedAt: ur.assignedAt.toISOString(),
        assignedBy: ur.assignedBy || undefined,
      })),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取用户详情API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/users/[id]
 * 更新用户信息
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<UserDetail>>> {
  try {
    const { id } = params;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        },
        { status: 404 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = updateUserSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { firstName, lastName, phone, languageCode, timezone, status, roles } = validationResult.data;

    // 更新用户基本信息
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        firstName,
        lastName,
        phone,
        languageCode,
        timezone,
        status,
        updatedBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        phone: true,
        status: true,
        languageCode: true,
        timezone: true,
        emailVerified: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
        createdBy: true,
        updatedBy: true,
      },
    });

    // 更新用户角色
    if (roles !== undefined) {
      // 删除现有角色
      await prisma.userRole.deleteMany({
        where: { userId: id },
      });

      // 添加新角色
      if (roles.length > 0) {
        const roleRecords = await prisma.role.findMany({
          where: { name: { in: roles } },
        });

        const userRoleData = roleRecords.map(role => ({
          userId: id,
          roleId: role.id,
          assignedBy: 'admin', // TODO: 从认证信息获取当前用户ID
        }));

        await prisma.userRole.createMany({
          data: userRoleData,
        });
      }
    }

    // 获取更新后的用户角色信息
    const userWithRoles = await prisma.user.findUnique({
      where: { id },
      select: {
        userRoles: {
          select: {
            assignedAt: true,
            assignedBy: true,
            role: {
              select: {
                id: true,
                name: true,
                displayName: true,
              },
            },
          },
        },
      },
    });

    // 格式化响应数据
    const responseData: UserDetail = {
      id: updatedUser.id,
      email: updatedUser.email,
      username: updatedUser.username || undefined,
      firstName: updatedUser.firstName || undefined,
      lastName: updatedUser.lastName || undefined,
      phone: updatedUser.phone || undefined,
      status: updatedUser.status,
      languageCode: updatedUser.languageCode,
      timezone: updatedUser.timezone,
      emailVerified: updatedUser.emailVerified,
      lastLoginAt: updatedUser.lastLoginAt?.toISOString(),
      createdAt: updatedUser.createdAt.toISOString(),
      updatedAt: updatedUser.updatedAt.toISOString(),
      createdBy: updatedUser.createdBy || undefined,
      updatedBy: updatedUser.updatedBy || undefined,
      roles: userWithRoles?.userRoles.map(ur => ({
        id: ur.role.id,
        name: ur.role.name,
        displayName: ur.role.displayName,
        assignedAt: ur.assignedAt.toISOString(),
        assignedBy: ur.assignedBy || undefined,
      })) || [],
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('更新用户API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/users/[id]
 * 删除用户（软删除）
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<null>>> {
  try {
    const { id } = params;

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'USER_NOT_FOUND',
            message: '用户不存在',
          },
        },
        { status: 404 }
      );
    }

    // 软删除用户（设置状态为INACTIVE）
    await prisma.user.update({
      where: { id },
      data: {
        status: 'INACTIVE',
        updatedBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: null,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('删除用户API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
