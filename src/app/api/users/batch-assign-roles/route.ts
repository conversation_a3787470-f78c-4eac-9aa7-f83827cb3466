/**
 * 批量用户角色分配API路由
 * 提供批量角色分配和管理功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { ApiResponse } from '@/types';

// 批量操作结果类型定义
export interface BatchAssignResult {
  successCount: number;
  failureCount: number;
  totalCount: number;
  details: {
    userId: string;
    userEmail: string;
    success: boolean;
    assignedRoles: number;
    error?: string;
  }[];
}

// 批量角色分配请求验证模式
const batchAssignRolesSchema = z.object({
  userIds: z.array(z.string()).min(1, '至少需要选择一个用户'),
  roleIds: z.array(z.string()).min(1, '至少需要分配一个角色'),
  expiresAt: z.string().datetime().optional(), // ISO 8601 格式的过期时间
  assignedBy: z.string().optional(), // 分配者ID，通常从认证信息获取
  overwriteExisting: z.boolean().optional().default(false), // 是否覆盖现有角色
});

// 批量移除角色请求验证模式
const batchRemoveRolesSchema = z.object({
  userIds: z.array(z.string()).min(1, '至少需要选择一个用户'),
  roleIds: z.array(z.string()).min(1, '至少需要移除一个角色'),
});

/**
 * POST /api/users/batch-assign-roles
 * 批量为用户分配角色
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<BatchAssignResult>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = batchAssignRolesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { userIds, roleIds, expiresAt, assignedBy, overwriteExisting } = validationResult.data;

    // 验证用户ID是否有效
    const validUsers = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, email: true },
    });

    const validUserIds = validUsers.map(u => u.id);
    const invalidUserIds = userIds.filter(id => !validUserIds.includes(id));

    if (invalidUserIds.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_USERS',
            message: '包含无效的用户ID',
            details: { invalidUserIds },
          },
        },
        { status: 400 }
      );
    }

    // 验证角色ID是否有效
    const validRoles = await prisma.role.findMany({
      where: { id: { in: roleIds } },
      select: { id: true, name: true },
    });

    const validRoleIds = validRoles.map(r => r.id);
    const invalidRoleIds = roleIds.filter(id => !validRoleIds.includes(id));

    if (invalidRoleIds.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_ROLES',
            message: '包含无效的角色ID',
            details: { invalidRoleIds },
          },
        },
        { status: 400 }
      );
    }

    // 批量处理用户角色分配
    const results: BatchAssignResult['details'] = [];
    let successCount = 0;
    let failureCount = 0;

    for (const user of validUsers) {
      try {
        await prisma.$transaction(async (tx) => {
          // 如果需要覆盖现有角色，先删除用户的所有角色
          if (overwriteExisting) {
            await tx.userRole.deleteMany({
              where: { userId: user.id },
            });
          }

          // 查询用户已有的角色
          const existingUserRoles = await tx.userRole.findMany({
            where: {
              userId: user.id,
              roleId: { in: roleIds },
            },
            select: { roleId: true },
          });

          const existingRoleIds = existingUserRoles.map(ur => ur.roleId);
          const newRoleIds = roleIds.filter(id => !existingRoleIds.includes(id));

          // 创建新的角色分配
          if (newRoleIds.length > 0) {
            await tx.userRole.createMany({
              data: newRoleIds.map(roleId => ({
                userId: user.id,
                roleId,
                assignedBy: assignedBy || 'system',
                expiresAt: expiresAt ? new Date(expiresAt) : null,
              })),
            });
          }

          results.push({
            userId: user.id,
            userEmail: user.email,
            success: true,
            assignedRoles: overwriteExisting ? roleIds.length : newRoleIds.length,
          });

          successCount++;
        });
      } catch (error) {
        console.error(`为用户 ${user.id} 分配角色失败:`, error);
        
        results.push({
          userId: user.id,
          userEmail: user.email,
          success: false,
          assignedRoles: 0,
          error: error instanceof Error ? error.message : '未知错误',
        });

        failureCount++;
      }
    }

    const batchResult: BatchAssignResult = {
      successCount,
      failureCount,
      totalCount: validUsers.length,
      details: results,
    };

    return NextResponse.json(
      {
        success: true,
        data: batchResult,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
          message: `批量角色分配完成：成功 ${successCount} 个，失败 ${failureCount} 个`,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('批量分配用户角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/users/batch-assign-roles
 * 批量移除用户角色
 */
export async function DELETE(request: NextRequest): Promise<NextResponse<ApiResponse<BatchAssignResult>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = batchRemoveRolesSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { userIds, roleIds } = validationResult.data;

    // 验证用户ID是否有效
    const validUsers = await prisma.user.findMany({
      where: { id: { in: userIds } },
      select: { id: true, email: true },
    });

    const validUserIds = validUsers.map(u => u.id);
    const invalidUserIds = userIds.filter(id => !validUserIds.includes(id));

    if (invalidUserIds.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'INVALID_USERS',
            message: '包含无效的用户ID',
            details: { invalidUserIds },
          },
        },
        { status: 400 }
      );
    }

    // 批量处理用户角色移除
    const results: BatchAssignResult['details'] = [];
    let successCount = 0;
    let failureCount = 0;

    for (const user of validUsers) {
      try {
        const deleteResult = await prisma.userRole.deleteMany({
          where: {
            userId: user.id,
            roleId: { in: roleIds },
          },
        });

        results.push({
          userId: user.id,
          userEmail: user.email,
          success: true,
          assignedRoles: -deleteResult.count, // 负数表示移除的角色数量
        });

        successCount++;
      } catch (error) {
        console.error(`为用户 ${user.id} 移除角色失败:`, error);
        
        results.push({
          userId: user.id,
          userEmail: user.email,
          success: false,
          assignedRoles: 0,
          error: error instanceof Error ? error.message : '未知错误',
        });

        failureCount++;
      }
    }

    const batchResult: BatchAssignResult = {
      successCount,
      failureCount,
      totalCount: validUsers.length,
      details: results,
    };

    return NextResponse.json(
      {
        success: true,
        data: batchResult,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
          message: `批量角色移除完成：成功 ${successCount} 个，失败 ${failureCount} 个`,
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('批量移除用户角色API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
