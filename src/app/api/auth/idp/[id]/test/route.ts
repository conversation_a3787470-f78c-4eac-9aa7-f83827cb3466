/**
 * IDP连接测试API路由
 * 测试IDP配置的连接性和有效性
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ApiResponse } from '@/types';
import { IDPType, IDPProtocol, IDPConnectionTest } from '@/lib/auth/idp-config';
import { IDPHandlerFactory } from '@/lib/auth/idp-handler';

/**
 * POST /api/auth/idp/[id]/test
 * 测试IDP连接
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<IDPConnectionTest>>> {
  try {
    const { id } = params;

    // 查询IDP配置
    const idpConfig = await prisma.idpConfig.findUnique({
      where: { id },
    });

    if (!idpConfig) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'IDP配置不存在',
          },
        },
        { status: 404 }
      );
    }

    // 构建IDP配置对象
    const config = {
      id: idpConfig.id,
      name: idpConfig.name,
      type: idpConfig.type as IDPType,
      protocol: idpConfig.protocol as IDPProtocol,
      enabled: idpConfig.enabled,
      isDefault: idpConfig.isDefault,
      description: idpConfig.description || undefined,
      iconUrl: idpConfig.iconUrl || undefined,
      createdAt: idpConfig.createdAt,
      updatedAt: idpConfig.updatedAt,
      ...idpConfig.config,
    };

    // 执行连接测试
    let testResult: IDPConnectionTest;

    try {
      switch (config.protocol) {
        case IDPProtocol.OAUTH2:
        case IDPProtocol.OIDC:
          testResult = await testOAuthConnection(config);
          break;
        case IDPProtocol.SAML2:
          testResult = await testSAMLConnection(config);
          break;
        case IDPProtocol.LDAP:
          testResult = await testLDAPConnection(config);
          break;
        default:
          testResult = {
            success: false,
            message: `不支持的协议类型: ${config.protocol}`,
            timestamp: new Date(),
          };
      }
    } catch (error) {
      testResult = {
        success: false,
        message: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: new Date(),
      };
    }

    // 保存测试结果
    await prisma.idpConfig.update({
      where: { id },
      data: {
        lastTestResult: {
          success: testResult.success,
          message: testResult.message,
          timestamp: testResult.timestamp.toISOString(),
          details: testResult.details,
        },
      },
    });

    return NextResponse.json(
      {
        success: true,
        data: testResult,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('IDP连接测试API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * 测试OAuth/OIDC连接
 */
async function testOAuthConnection(config: any): Promise<IDPConnectionTest> {
  const tests: Array<{ name: string; test: () => Promise<void> }> = [];

  // 测试发现端点（如果有）
  if (config.discoveryUrl) {
    tests.push({
      name: '发现端点',
      test: async () => {
        const response = await fetch(config.discoveryUrl, {
          method: 'GET',
          headers: { 'Accept': 'application/json' },
        });
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const data = await response.json();
        if (!data.authorization_endpoint || !data.token_endpoint) {
          throw new Error('发现端点返回的数据不完整');
        }
      },
    });
  }

  // 测试授权端点
  if (config.authorizationUrl) {
    tests.push({
      name: '授权端点',
      test: async () => {
        const response = await fetch(config.authorizationUrl, { method: 'HEAD' });
        if (!response.ok && response.status !== 405) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      },
    });
  }

  // 测试令牌端点
  if (config.tokenUrl) {
    tests.push({
      name: '令牌端点',
      test: async () => {
        const response = await fetch(config.tokenUrl, { method: 'HEAD' });
        if (!response.ok && response.status !== 405) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      },
    });
  }

  // 测试用户信息端点
  if (config.userInfoUrl) {
    tests.push({
      name: '用户信息端点',
      test: async () => {
        const response = await fetch(config.userInfoUrl, { method: 'HEAD' });
        if (!response.ok && response.status !== 401 && response.status !== 405) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      },
    });
  }

  // 执行所有测试
  const results: Array<{ name: string; success: boolean; error?: string }> = [];
  
  for (const test of tests) {
    try {
      await test.test();
      results.push({ name: test.name, success: true });
    } catch (error) {
      results.push({
        name: test.name,
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  const failedTests = results.filter(r => !r.success);
  const success = failedTests.length === 0;

  return {
    success,
    message: success 
      ? `OAuth/OIDC连接测试成功 (${results.length}/${results.length} 项通过)`
      : `OAuth/OIDC连接测试失败 (${results.length - failedTests.length}/${results.length} 项通过)`,
    details: {
      testResults: results,
      endpoints: {
        authorization: config.authorizationUrl,
        token: config.tokenUrl,
        userInfo: config.userInfoUrl,
        discovery: config.discoveryUrl,
      },
    },
    timestamp: new Date(),
  };
}

/**
 * 测试SAML连接
 */
async function testSAMLConnection(config: any): Promise<IDPConnectionTest> {
  const tests: Array<{ name: string; test: () => Promise<void> }> = [];

  // 测试SSO服务端点
  if (config.ssoServiceUrl) {
    tests.push({
      name: 'SSO服务端点',
      test: async () => {
        const response = await fetch(config.ssoServiceUrl, { method: 'HEAD' });
        if (!response.ok && response.status !== 405) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      },
    });
  }

  // 测试SLO服务端点
  if (config.sloServiceUrl) {
    tests.push({
      name: 'SLO服务端点',
      test: async () => {
        const response = await fetch(config.sloServiceUrl, { method: 'HEAD' });
        if (!response.ok && response.status !== 405) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      },
    });
  }

  // 验证证书格式
  if (config.certificate) {
    tests.push({
      name: 'X.509证书',
      test: async () => {
        const cert = config.certificate.trim();
        if (!cert.includes('BEGIN CERTIFICATE') || !cert.includes('END CERTIFICATE')) {
          throw new Error('证书格式不正确');
        }
      },
    });
  }

  // 执行所有测试
  const results: Array<{ name: string; success: boolean; error?: string }> = [];
  
  for (const test of tests) {
    try {
      await test.test();
      results.push({ name: test.name, success: true });
    } catch (error) {
      results.push({
        name: test.name,
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  const failedTests = results.filter(r => !r.success);
  const success = failedTests.length === 0;

  return {
    success,
    message: success 
      ? `SAML连接测试成功 (${results.length}/${results.length} 项通过)`
      : `SAML连接测试失败 (${results.length - failedTests.length}/${results.length} 项通过)`,
    details: {
      testResults: results,
      endpoints: {
        sso: config.ssoServiceUrl,
        slo: config.sloServiceUrl,
      },
      entityId: config.entityId,
    },
    timestamp: new Date(),
  };
}

/**
 * 测试LDAP连接
 */
async function testLDAPConnection(config: any): Promise<IDPConnectionTest> {
  // LDAP连接测试需要在服务端进行，这里只做基础验证
  const validations: Array<{ name: string; valid: boolean; error?: string }> = [];

  // 验证服务器URL
  try {
    new URL(config.serverUrl);
    validations.push({ name: '服务器URL格式', valid: true });
  } catch {
    validations.push({ name: '服务器URL格式', valid: false, error: 'URL格式不正确' });
  }

  // 验证绑定DN
  if (config.bindDn) {
    validations.push({ name: '绑定DN', valid: true });
  } else {
    validations.push({ name: '绑定DN', valid: false, error: '绑定DN不能为空' });
  }

  // 验证用户搜索基础DN
  if (config.userBaseDn) {
    validations.push({ name: '用户搜索基础DN', valid: true });
  } else {
    validations.push({ name: '用户搜索基础DN', valid: false, error: '用户搜索基础DN不能为空' });
  }

  const failedValidations = validations.filter(v => !v.valid);
  const success = failedValidations.length === 0;

  return {
    success,
    message: success 
      ? `LDAP配置验证成功 (${validations.length}/${validations.length} 项通过)`
      : `LDAP配置验证失败 (${validations.length - failedValidations.length}/${validations.length} 项通过)`,
    details: {
      validations,
      serverUrl: config.serverUrl,
      userBaseDn: config.userBaseDn,
      note: 'LDAP连接测试需要在服务端进行实际连接验证',
    },
    timestamp: new Date(),
  };
}
