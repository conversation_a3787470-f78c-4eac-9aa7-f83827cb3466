/**
 * IDP配置详细管理API路由
 * 处理单个IDP配置的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ApiResponse } from '@/types';
import { IDPType, IDPProtocol, idpConfigManager } from '@/lib/auth/idp-config';
import { IDPHandlerFactory } from '@/lib/auth/idp-handler';

// IDP配置数据类型
interface IDPConfigData {
  id: string;
  name: string;
  type: IDPType;
  protocol: IDPProtocol;
  enabled: boolean;
  isDefault: boolean;
  description?: string;
  iconUrl?: string;
  config: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  lastTestResult?: {
    success: boolean;
    message: string;
    timestamp: string;
  };
}

/**
 * GET /api/auth/idp/[id]
 * 获取单个IDP配置详情
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<IDPConfigData>>> {
  try {
    const { id } = params;

    // 查询IDP配置
    const idpConfig = await prisma.idpConfig.findUnique({
      where: { id },
    });

    if (!idpConfig) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'IDP配置不存在',
          },
        },
        { status: 404 }
      );
    }

    // 格式化响应数据
    const responseData: IDPConfigData = {
      id: idpConfig.id,
      name: idpConfig.name,
      type: idpConfig.type as IDPType,
      protocol: idpConfig.protocol as IDPProtocol,
      enabled: idpConfig.enabled,
      isDefault: idpConfig.isDefault,
      description: idpConfig.description || undefined,
      iconUrl: idpConfig.iconUrl || undefined,
      config: idpConfig.config as Record<string, any>,
      createdAt: idpConfig.createdAt.toISOString(),
      updatedAt: idpConfig.updatedAt.toISOString(),
      lastTestResult: idpConfig.lastTestResult ? {
        success: (idpConfig.lastTestResult as any).success,
        message: (idpConfig.lastTestResult as any).message,
        timestamp: (idpConfig.lastTestResult as any).timestamp,
      } : undefined,
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取IDP配置详情API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/auth/idp/[id]
 * 更新IDP配置
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<IDPConfigData>>> {
  try {
    const { id } = params;
    const body = await request.json();

    // 检查配置是否存在
    const existingConfig = await prisma.idpConfig.findUnique({
      where: { id },
    });

    if (!existingConfig) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'IDP配置不存在',
          },
        },
        { status: 404 }
      );
    }

    // 如果更新名称，检查是否与其他配置冲突
    if (body.name && body.name !== existingConfig.name) {
      const nameConflict = await prisma.idpConfig.findFirst({
        where: {
          name: body.name,
          id: { not: id },
        },
      });

      if (nameConflict) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'NAME_EXISTS',
              message: '该配置名称已存在',
            },
          },
          { status: 409 }
        );
      }
    }

    // 如果设置为默认IDP，需要取消其他IDP的默认状态
    if (body.isDefault && !existingConfig.isDefault) {
      await prisma.idpConfig.updateMany({
        where: { 
          isDefault: true,
          id: { not: id },
        },
        data: { isDefault: false },
      });
    }

    // 更新配置
    const updatedConfig = await prisma.idpConfig.update({
      where: { id },
      data: {
        name: body.name || existingConfig.name,
        type: body.type || existingConfig.type,
        protocol: body.protocol || existingConfig.protocol,
        enabled: body.enabled !== undefined ? body.enabled : existingConfig.enabled,
        isDefault: body.isDefault !== undefined ? body.isDefault : existingConfig.isDefault,
        description: body.description !== undefined ? body.description : existingConfig.description,
        iconUrl: body.iconUrl !== undefined ? body.iconUrl : existingConfig.iconUrl,
        config: body.config || existingConfig.config,
        updatedBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
    });

    // 格式化响应数据
    const responseData: IDPConfigData = {
      id: updatedConfig.id,
      name: updatedConfig.name,
      type: updatedConfig.type as IDPType,
      protocol: updatedConfig.protocol as IDPProtocol,
      enabled: updatedConfig.enabled,
      isDefault: updatedConfig.isDefault,
      description: updatedConfig.description || undefined,
      iconUrl: updatedConfig.iconUrl || undefined,
      config: updatedConfig.config as Record<string, any>,
      createdAt: updatedConfig.createdAt.toISOString(),
      updatedAt: updatedConfig.updatedAt.toISOString(),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('更新IDP配置API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/auth/idp/[id]
 * 删除IDP配置
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
): Promise<NextResponse<ApiResponse<{ deleted: boolean }>>> {
  try {
    const { id } = params;

    // 检查配置是否存在
    const existingConfig = await prisma.idpConfig.findUnique({
      where: { id },
    });

    if (!existingConfig) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'IDP配置不存在',
          },
        },
        { status: 404 }
      );
    }

    // 检查是否有用户正在使用此IDP
    const usersUsingIDP = await prisma.user.count({
      where: { idpId: id },
    });

    if (usersUsingIDP > 0) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'IDP_IN_USE',
            message: `无法删除，还有 ${usersUsingIDP} 个用户正在使用此IDP`,
          },
        },
        { status: 409 }
      );
    }

    // 删除配置
    await prisma.idpConfig.delete({
      where: { id },
    });

    return NextResponse.json(
      {
        success: true,
        data: { deleted: true },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('删除IDP配置API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
