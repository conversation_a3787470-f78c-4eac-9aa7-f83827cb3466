/**
 * IDP配置管理API路由
 * 处理外部身份提供商的配置管理
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ApiResponse, PaginatedResponse } from '@/types';
import { IDPConfig, IDPType, IDPProtocol, idpConfigManager } from '@/lib/auth/idp-config';

// IDP配置数据类型
interface IDPConfigData {
  id: string;
  name: string;
  type: IDPType;
  protocol: IDPProtocol;
  enabled: boolean;
  isDefault: boolean;
  description?: string;
  iconUrl?: string;
  config: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  lastTestResult?: {
    success: boolean;
    message: string;
    timestamp: string;
  };
}

/**
 * GET /api/auth/idp
 * 获取IDP配置列表
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<IDPConfigData[]>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as IDPType | null;
    const enabled = searchParams.get('enabled');

    // 构建查询条件
    const where: any = {};
    if (type) {
      where.type = type;
    }
    if (enabled !== null) {
      where.enabled = enabled === 'true';
    }

    // 查询IDP配置
    const idpConfigs = await prisma.idpConfig.findMany({
      where,
      orderBy: [
        { isDefault: 'desc' },
        { enabled: 'desc' },
        { name: 'asc' },
      ],
    });

    // 格式化响应数据
    const formattedConfigs: IDPConfigData[] = idpConfigs.map(config => ({
      id: config.id,
      name: config.name,
      type: config.type as IDPType,
      protocol: config.protocol as IDPProtocol,
      enabled: config.enabled,
      isDefault: config.isDefault,
      description: config.description || undefined,
      iconUrl: config.iconUrl || undefined,
      config: config.config as Record<string, any>,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString(),
      lastTestResult: config.lastTestResult ? {
        success: (config.lastTestResult as any).success,
        message: (config.lastTestResult as any).message,
        timestamp: (config.lastTestResult as any).timestamp,
      } : undefined,
    }));

    return NextResponse.json(
      {
        success: true,
        data: formattedConfigs,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取IDP配置列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/auth/idp
 * 创建新的IDP配置
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<IDPConfigData>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 基础验证
    if (!body.name || !body.type || !body.protocol || !body.config) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '缺少必需的字段',
          },
        },
        { status: 400 }
      );
    }

    // 检查名称是否已存在
    const existingConfig = await prisma.idpConfig.findFirst({
      where: { name: body.name },
    });

    if (existingConfig) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'NAME_EXISTS',
            message: '该配置名称已存在',
          },
        },
        { status: 409 }
      );
    }

    // 如果设置为默认IDP，需要取消其他IDP的默认状态
    if (body.isDefault) {
      await prisma.idpConfig.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });
    }

    // 创建IDP配置
    const newConfig = await prisma.idpConfig.create({
      data: {
        id: crypto.randomUUID(),
        name: body.name,
        type: body.type,
        protocol: body.protocol,
        enabled: body.enabled ?? true,
        isDefault: body.isDefault ?? false,
        description: body.description,
        iconUrl: body.iconUrl,
        config: body.config,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
    });

    // 格式化响应数据
    const responseData: IDPConfigData = {
      id: newConfig.id,
      name: newConfig.name,
      type: newConfig.type as IDPType,
      protocol: newConfig.protocol as IDPProtocol,
      enabled: newConfig.enabled,
      isDefault: newConfig.isDefault,
      description: newConfig.description || undefined,
      iconUrl: newConfig.iconUrl || undefined,
      config: newConfig.config as Record<string, any>,
      createdAt: newConfig.createdAt.toISOString(),
      updatedAt: newConfig.updatedAt.toISOString(),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建IDP配置API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/auth/idp
 * 批量更新IDP配置状态
 */
export async function PUT(request: NextRequest): Promise<NextResponse<ApiResponse<{ updated: number }>>> {
  try {
    const body = await request.json();
    const { action, ids } = body;

    if (!action || !ids || !Array.isArray(ids)) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '缺少必需的参数',
          },
        },
        { status: 400 }
      );
    }

    let updateData: any = {};
    
    switch (action) {
      case 'enable':
        updateData = { enabled: true };
        break;
      case 'disable':
        updateData = { enabled: false };
        break;
      case 'delete':
        // 删除配置
        const deleteResult = await prisma.idpConfig.deleteMany({
          where: { id: { in: ids } },
        });
        
        return NextResponse.json(
          {
            success: true,
            data: { updated: deleteResult.count },
            metadata: {
              timestamp: new Date().toISOString(),
              requestId: crypto.randomUUID(),
              version: 'v1',
            },
          },
          { status: 200 }
        );
      default:
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INVALID_ACTION',
              message: '无效的操作类型',
            },
          },
          { status: 400 }
        );
    }

    // 更新配置
    const updateResult = await prisma.idpConfig.updateMany({
      where: { id: { in: ids } },
      data: updateData,
    });

    return NextResponse.json(
      {
        success: true,
        data: { updated: updateResult.count },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('批量更新IDP配置API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
