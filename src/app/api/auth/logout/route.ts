/**
 * 用户登出API路由
 * 处理用户登出和令牌清理
 */

import { NextRequest, NextResponse } from 'next/server';
import { logout } from '@/lib/auth';
import { ApiResponse } from '@/types';

/**
 * POST /api/auth/logout
 * 用户登出
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<null>>> {
  try {
    // 获取访问令牌
    const authHeader = request.headers.get('authorization');
    let token: string | undefined;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else {
      // 从Cookie获取令牌
      token = request.cookies.get('access_token')?.value;
    }

    if (token) {
      // 执行登出操作
      await logout(token);
    }

    // 创建响应
    const response = NextResponse.json(
      {
        success: true,
        data: null,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );

    // 清除Cookie
    response.cookies.delete('access_token');
    response.cookies.delete('refresh_token');

    return response;
  } catch (error) {
    console.error('登出API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/logout
 * 处理CORS预检请求
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
