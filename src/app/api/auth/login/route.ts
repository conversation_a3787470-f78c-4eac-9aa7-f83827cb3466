/**
 * 用户登录API路由
 * 处理用户身份认证和令牌生成
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { login } from '@/lib/auth';
import { loginRateLimit } from '@/lib/rate-limit';
import { ApiResponse } from '@/types';

// 登录请求验证模式
const loginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(1, '密码不能为空'),
  rememberMe: z.boolean().optional().default(false),
});

// 登录响应类型
interface LoginResponse {
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    roles: string[];
  };
  tokens: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
}

/**
 * POST /api/auth/login
 * 用户登录
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<LoginResponse>>> {
  try {
    // 获取客户端IP地址用于限流
    const clientIp = request.ip || 
      request.headers.get('x-forwarded-for') || 
      request.headers.get('x-real-ip') || 
      'unknown';

    // 检查登录限流
    const isAllowed = await loginRateLimit(clientIp);
    if (!isAllowed) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: '登录尝试过于频繁，请15分钟后再试',
          },
        },
        { status: 429 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { email, password, rememberMe } = validationResult.data;

    // 执行登录
    const authResult = await login({
      email,
      password,
      rememberMe,
    });

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'LOGIN_FAILED',
            message: authResult.error || '登录失败',
          },
        },
        { status: 401 }
      );
    }

    // 构建响应数据
    const responseData: LoginResponse = {
      user: {
        id: authResult.user!.id,
        email: authResult.user!.email,
        firstName: authResult.user!.firstName,
        lastName: authResult.user!.lastName,
        roles: authResult.user!.roles,
      },
      tokens: authResult.tokens!,
    };

    // 创建响应
    const response = NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );

    // 设置Cookie（可选，用于浏览器客户端）
    if (rememberMe) {
      response.cookies.set('access_token', authResult.tokens!.accessToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: authResult.tokens!.expiresIn,
        path: '/',
      });

      response.cookies.set('refresh_token', authResult.tokens!.refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 30 * 24 * 60 * 60, // 30天
        path: '/',
      });
    }

    return response;
  } catch (error) {
    console.error('登录API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/login
 * 处理CORS预检请求
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
