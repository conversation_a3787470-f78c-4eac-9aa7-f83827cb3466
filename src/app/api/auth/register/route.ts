/**
 * 用户注册API路由
 * 处理新用户注册和账户创建
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { hashPassword } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { registerSchema } from '@/lib/validations';
import { ApiResponse } from '@/types';

// 注册响应类型
interface RegisterResponse {
  user: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
    username?: string;
  };
  message: string;
}

/**
 * POST /api/auth/register
 * 用户注册
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<RegisterResponse>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = registerSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { email, username, password, firstName, lastName, phone, languageCode, timezone } = validationResult.data;

    // 检查邮箱是否已存在
    const existingUserByEmail = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUserByEmail) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: '该邮箱已被注册',
          },
        },
        { status: 409 }
      );
    }

    // 检查用户名是否已存在（如果提供了用户名）
    if (username) {
      const existingUserByUsername = await prisma.user.findUnique({
        where: { username },
      });

      if (existingUserByUsername) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'USERNAME_EXISTS',
              message: '该用户名已被使用',
            },
          },
          { status: 409 }
        );
      }
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 创建新用户
    const newUser = await prisma.user.create({
      data: {
        email,
        username,
        passwordHash: hashedPassword,
        firstName,
        lastName,
        phone,
        languageCode: languageCode || 'zh-CN',
        timezone: timezone || 'Asia/Shanghai',
        status: 'ACTIVE',
        emailVerified: false, // 需要邮箱验证
        createdBy: 'self',
      },
      select: {
        id: true,
        email: true,
        username: true,
        firstName: true,
        lastName: true,
        createdAt: true,
      },
    });

    // 为新用户分配默认角色（普通用户）
    const defaultRole = await prisma.role.findUnique({
      where: { name: 'employee' },
    });

    if (defaultRole) {
      await prisma.userRole.create({
        data: {
          userId: newUser.id,
          roleId: defaultRole.id,
          assignedBy: 'system',
        },
      });
    }

    // TODO: 发送邮箱验证邮件
    // await sendVerificationEmail(newUser.email, verificationToken);

    // 构建响应数据
    const responseData: RegisterResponse = {
      user: {
        id: newUser.id,
        email: newUser.email,
        firstName: newUser.firstName || undefined,
        lastName: newUser.lastName || undefined,
        username: newUser.username || undefined,
      },
      message: '注册成功，请查看邮箱完成验证',
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('注册API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/register
 * 处理CORS预检请求
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}
