/**
 * 权限检查API路由
 * 提供用户权限验证功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ApiResponse } from '@/types';
import { permissionService, PermissionCheckResult } from '@/lib/rbac/permission-service';

// 权限检查请求验证模式
const checkPermissionSchema = z.object({
  resource: z.string().min(1, '资源名称不能为空'),
  action: z.string().min(1, '操作名称不能为空'),
  resourceId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// 批量权限检查请求验证模式
const batchCheckPermissionSchema = z.object({
  permissions: z.array(z.object({
    resource: z.string().min(1, '资源名称不能为空'),
    action: z.string().min(1, '操作名称不能为空'),
    resourceId: z.string().optional(),
  })).min(1, '至少需要检查一个权限'),
});

/**
 * 从请求头获取用户ID
 */
function getUserIdFromHeaders(request: NextRequest): string | null {
  return request.headers.get('x-user-id');
}

/**
 * POST /api/auth/check-permission
 * 检查单个权限
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<PermissionCheckResult>>> {
  try {
    // 获取用户ID
    const userId = getUserIdFromHeaders(request);
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未提供用户认证信息',
          },
        },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = checkPermissionSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { resource, action, resourceId, metadata } = validationResult.data;

    // 检查权限
    const permissionResult = await permissionService.checkPermission({
      userId,
      resource,
      action,
      resourceId,
      metadata,
    });

    return NextResponse.json(
      {
        success: true,
        data: permissionResult,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('权限检查API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/auth/check-permission
 * 批量检查权限
 */
export async function PUT(request: NextRequest): Promise<NextResponse<ApiResponse<Record<string, PermissionCheckResult>>>> {
  try {
    // 获取用户ID
    const userId = getUserIdFromHeaders(request);
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未提供用户认证信息',
          },
        },
        { status: 401 }
      );
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = batchCheckPermissionSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { permissions } = validationResult.data;

    // 批量检查权限
    const permissionResults = await permissionService.checkMultiplePermissions(userId, permissions);

    return NextResponse.json(
      {
        success: true,
        data: permissionResults,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('批量权限检查API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/auth/check-permission
 * 获取用户可访问的资源列表
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<string[]>>> {
  try {
    // 获取用户ID
    const userId = getUserIdFromHeaders(request);
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未提供用户认证信息',
          },
        },
        { status: 401 }
      );
    }

    // 解析查询参数
    const url = new URL(request.url);
    const resource = url.searchParams.get('resource');
    const action = url.searchParams.get('action');

    if (!resource || !action) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'MISSING_PARAMETERS',
            message: '缺少必需的查询参数: resource 和 action',
          },
        },
        { status: 400 }
      );
    }

    // 获取用户可访问的资源列表
    const accessibleResources = await permissionService.getUserAccessibleResources(
      userId,
      resource,
      action
    );

    return NextResponse.json(
      {
        success: true,
        data: accessibleResources,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取可访问资源API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/auth/check-permission
 * 清除用户权限缓存
 */
export async function DELETE(request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string }>>> {
  try {
    // 获取用户ID
    const userId = getUserIdFromHeaders(request);
    if (!userId) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'UNAUTHORIZED',
            message: '未提供用户认证信息',
          },
        },
        { status: 401 }
      );
    }

    // 清除用户权限缓存
    permissionService.clearUserPermissionCache(userId);

    return NextResponse.json(
      {
        success: true,
        data: { message: '用户权限缓存已清除' },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('清除权限缓存API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
