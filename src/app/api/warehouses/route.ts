/**
 * 仓库管理API路由
 * 处理仓库的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createWarehouseSchema } from '@/lib/validations';
import { ApiResponse } from '@/types';

// 仓库信息类型
interface WarehouseInfo {
  id: string;
  code: string;
  name: string;
  address: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateProvince?: string;
    countryCode: string;
    postalCode?: string;
  };
  contactInfo?: any;
  isActive: boolean;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  inventoryCount: number;
  totalValue: number;
}

/**
 * GET /api/warehouses
 * 获取仓库列表
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<WarehouseInfo[]>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';

    // 构建查询条件
    const where: any = {};
    if (!includeInactive) {
      where.isActive = true;
    }

    // 查询仓库列表
    const warehouses = await prisma.warehouse.findMany({
      where,
      orderBy: [
        { isDefault: 'desc' },
        { name: 'asc' },
      ],
      include: {
        _count: {
          select: {
            inventoryItems: true,
          },
        },
        inventoryItems: {
          select: {
            quantity: true,
            costPerUnit: true,
          },
        },
      },
    });

    // 格式化仓库数据
    const formattedWarehouses: WarehouseInfo[] = warehouses.map(warehouse => {
      // 计算库存总价值
      const totalValue = warehouse.inventoryItems.reduce((sum, item) => {
        return sum + (item.quantity * (item.costPerUnit || 0));
      }, 0);

      return {
        id: warehouse.id,
        code: warehouse.code,
        name: warehouse.name,
        address: warehouse.address as any,
        contactInfo: warehouse.contactInfo,
        isActive: warehouse.isActive,
        isDefault: warehouse.isDefault,
        createdAt: warehouse.createdAt.toISOString(),
        updatedAt: warehouse.updatedAt.toISOString(),
        inventoryCount: warehouse._count.inventoryItems,
        totalValue,
      };
    });

    return NextResponse.json(
      {
        success: true,
        data: formattedWarehouses,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取仓库列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/warehouses
 * 创建新仓库
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<WarehouseInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createWarehouseSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const warehouseData = validationResult.data;

    // 检查仓库代码是否已存在
    const existingWarehouse = await prisma.warehouse.findUnique({
      where: { code: warehouseData.code },
    });

    if (existingWarehouse) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'CODE_EXISTS',
            message: '该仓库代码已存在',
          },
        },
        { status: 409 }
      );
    }

    // 如果设置为默认仓库，需要取消其他仓库的默认状态
    if (warehouseData.isDefault) {
      await prisma.warehouse.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });
    }

    // 创建仓库
    const newWarehouse = await prisma.warehouse.create({
      data: {
        ...warehouseData,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
      include: {
        _count: {
          select: {
            inventoryItems: true,
          },
        },
        inventoryItems: {
          select: {
            quantity: true,
            costPerUnit: true,
          },
        },
      },
    });

    // 格式化响应数据
    const totalValue = newWarehouse.inventoryItems.reduce((sum, item) => {
      return sum + (item.quantity * (item.costPerUnit || 0));
    }, 0);

    const responseData: WarehouseInfo = {
      id: newWarehouse.id,
      code: newWarehouse.code,
      name: newWarehouse.name,
      address: newWarehouse.address as any,
      contactInfo: newWarehouse.contactInfo,
      isActive: newWarehouse.isActive,
      isDefault: newWarehouse.isDefault,
      createdAt: newWarehouse.createdAt.toISOString(),
      updatedAt: newWarehouse.updatedAt.toISOString(),
      inventoryCount: newWarehouse._count.inventoryItems,
      totalValue,
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建仓库API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
