/**
 * 权限管理API路由
 * 提供权限的查询和管理功能
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/prisma';
import { ApiResponse, PaginationResult } from '@/types';

// 权限信息类型定义
export interface PermissionInfo {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  createdAt: string;
}

// 权限分组信息
export interface PermissionGroup {
  resource: string;
  resourceDisplayName: string;
  permissions: PermissionInfo[];
}

// 创建权限请求验证模式
const createPermissionSchema = z.object({
  name: z.string().min(1, '权限名称不能为空').max(100, '权限名称不能超过100个字符'),
  resource: z.string().min(1, '资源名称不能为空').max(100, '资源名称不能超过100个字符'),
  action: z.string().min(1, '操作名称不能为空').max(50, '操作名称不能超过50个字符'),
  description: z.string().max(500, '描述不能超过500个字符').optional(),
});

// 查询参数验证模式
const querySchema = z.object({
  page: z.string().transform(Number).pipe(z.number().min(1)).optional().default(1),
  limit: z.string().transform(Number).pipe(z.number().min(1).max(100)).optional().default(50),
  search: z.string().optional(),
  resource: z.string().optional(),
  grouped: z.string().transform(val => val === 'true').optional().default(false),
});

// 资源显示名称映射
const RESOURCE_DISPLAY_NAMES: Record<string, string> = {
  dashboard: '仪表板',
  users: '用户管理',
  roles: '角色管理',
  permissions: '权限管理',
  products: '商品管理',
  categories: '分类管理',
  inventory: '库存管理',
  orders: '订单管理',
  customers: '客户管理',
  payments: '支付管理',
  shipping: '物流管理',
  reports: '报表分析',
  settings: '系统设置',
  audit: '审计日志',
};

/**
 * GET /api/permissions
 * 获取权限列表
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginationResult<PermissionInfo> | PermissionGroup[]>>> {
  try {
    // 解析查询参数
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    const validationResult = querySchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '查询参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, search, resource, grouped } = validationResult.data;

    // 构建查询条件
    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { resource: { contains: search, mode: 'insensitive' } },
        { action: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
      ];
    }
    if (resource) {
      where.resource = resource;
    }

    if (grouped) {
      // 返回按资源分组的权限列表
      const permissions = await prisma.permission.findMany({
        where,
        orderBy: [
          { resource: 'asc' },
          { action: 'asc' },
        ],
      });

      // 按资源分组
      const groupedPermissions: Record<string, PermissionInfo[]> = {};
      permissions.forEach(permission => {
        if (!groupedPermissions[permission.resource]) {
          groupedPermissions[permission.resource] = [];
        }
        groupedPermissions[permission.resource].push({
          id: permission.id,
          name: permission.name,
          resource: permission.resource,
          action: permission.action,
          description: permission.description || undefined,
          createdAt: permission.createdAt.toISOString(),
        });
      });

      // 转换为分组格式
      const result: PermissionGroup[] = Object.entries(groupedPermissions).map(([resource, permissions]) => ({
        resource,
        resourceDisplayName: RESOURCE_DISPLAY_NAMES[resource] || resource,
        permissions,
      }));

      return NextResponse.json(
        {
          success: true,
          data: result,
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: crypto.randomUUID(),
            version: 'v1',
          },
        },
        { status: 200 }
      );
    } else {
      // 返回分页的权限列表
      const total = await prisma.permission.count({ where });

      const permissions = await prisma.permission.findMany({
        where,
        orderBy: [
          { resource: 'asc' },
          { action: 'asc' },
        ],
        skip: (page - 1) * limit,
        take: limit,
      });

      // 转换数据格式
      const permissionList: PermissionInfo[] = permissions.map(permission => ({
        id: permission.id,
        name: permission.name,
        resource: permission.resource,
        action: permission.action,
        description: permission.description || undefined,
        createdAt: permission.createdAt.toISOString(),
      }));

      // 计算分页信息
      const totalPages = Math.ceil(total / limit);

      return NextResponse.json(
        {
          success: true,
          data: {
            items: permissionList,
            pagination: {
              page,
              limit,
              total,
              totalPages,
              hasNext: page < totalPages,
              hasPrev: page > 1,
            },
          },
          metadata: {
            timestamp: new Date().toISOString(),
            requestId: crypto.randomUUID(),
            version: 'v1',
          },
        },
        { status: 200 }
      );
    }
  } catch (error) {
    console.error('获取权限列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/permissions
 * 创建新权限
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<PermissionInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createPermissionSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { name, resource, action, description } = validationResult.data;

    // 检查权限名称是否已存在
    const existingPermission = await prisma.permission.findUnique({
      where: { name },
    });

    if (existingPermission) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'PERMISSION_EXISTS',
            message: '权限名称已存在',
          },
        },
        { status: 409 }
      );
    }

    // 创建权限
    const newPermission = await prisma.permission.create({
      data: {
        name,
        resource,
        action,
        description,
      },
    });

    // 转换数据格式
    const responseData: PermissionInfo = {
      id: newPermission.id,
      name: newPermission.name,
      resource: newPermission.resource,
      action: newPermission.action,
      description: newPermission.description || undefined,
      createdAt: newPermission.createdAt.toISOString(),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建权限API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
