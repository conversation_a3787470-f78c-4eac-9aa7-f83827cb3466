/**
 * 库存管理API路由
 * 处理库存的查询和调整操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { inventoryAdjustmentSchema, paginationSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse } from '@/types';

// 库存信息类型
interface InventoryInfo {
  id: string;
  warehouseId: string;
  productId?: string;
  variantId?: string;
  sku: string;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  costPerUnit?: number;
  lastUpdated: string;
  warehouse: {
    id: string;
    code: string;
    name: string;
  };
  product?: {
    id: string;
    name: string;
    sku: string;
    basePrice: number;
  };
  variant?: {
    id: string;
    name: string;
    sku: string;
    price: number;
  };
}

// 库存调整记录类型
interface InventoryAdjustment {
  id: string;
  warehouseId: string;
  productId?: string;
  variantId?: string;
  sku: string;
  quantityBefore: number;
  quantityChange: number;
  quantityAfter: number;
  reason: string;
  notes?: string;
  costPerUnit?: number;
  adjustedBy?: string;
  adjustedAt: string;
}

/**
 * GET /api/inventory
 * 获取库存列表（分页）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginatedResponse<InventoryInfo>>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sort: searchParams.get('sort') || 'lastUpdated',
      order: searchParams.get('order') || 'desc',
      search: searchParams.get('search') || '',
      warehouseId: searchParams.get('warehouseId') || '',
      lowStock: searchParams.get('lowStock') === 'true',
      outOfStock: searchParams.get('outOfStock') === 'true',
    };

    // 验证分页参数
    const paginationResult = paginationSchema.safeParse(queryParams);
    if (!paginationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '分页参数验证失败',
            details: paginationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, sort, order } = paginationResult.data;
    const { search, warehouseId, lowStock, outOfStock } = queryParams;

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { sku: { contains: search, mode: 'insensitive' } },
        { product: { name: { contains: search, mode: 'insensitive' } } },
        { variant: { name: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (warehouseId) {
      where.warehouseId = warehouseId;
    }

    if (lowStock) {
      where.quantity = { lt: 10 }; // 库存少于10的商品
    }

    if (outOfStock) {
      where.quantity = { lte: 0 }; // 缺货商品
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询库存总数
    const total = await prisma.inventoryItem.count({ where });

    // 查询库存列表
    const inventoryItems = await prisma.inventoryItem.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sort]: order },
      include: {
        warehouse: {
          select: {
            id: true,
            code: true,
            name: true,
          },
        },
        product: {
          select: {
            id: true,
            name: true,
            sku: true,
            basePrice: true,
          },
        },
        variant: {
          select: {
            id: true,
            name: true,
            sku: true,
            price: true,
          },
        },
      },
    });

    // 格式化库存数据
    const formattedInventory: InventoryInfo[] = inventoryItems.map(item => ({
      id: item.id,
      warehouseId: item.warehouseId,
      productId: item.productId || undefined,
      variantId: item.variantId || undefined,
      sku: item.sku,
      quantity: item.quantity,
      reservedQuantity: item.reservedQuantity,
      availableQuantity: item.quantity - item.reservedQuantity,
      costPerUnit: item.costPerUnit || undefined,
      lastUpdated: item.updatedAt.toISOString(),
      warehouse: item.warehouse,
      product: item.product || undefined,
      variant: item.variant || undefined,
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const responseData: PaginatedResponse<InventoryInfo> = {
      data: formattedInventory,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取库存列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/inventory
 * 创建库存调整记录
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<InventoryAdjustment>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = inventoryAdjustmentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const adjustmentData = validationResult.data;

    // 查找现有库存记录
    const existingInventory = await prisma.inventoryItem.findFirst({
      where: {
        warehouseId: adjustmentData.warehouseId,
        sku: adjustmentData.sku,
      },
    });

    let quantityBefore = 0;
    let inventoryItem;

    if (existingInventory) {
      // 更新现有库存
      quantityBefore = existingInventory.quantity;
      const newQuantity = quantityBefore + adjustmentData.quantityChange;

      if (newQuantity < 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INSUFFICIENT_STOCK',
              message: '库存不足，无法进行此调整',
            },
          },
          { status: 400 }
        );
      }

      inventoryItem = await prisma.inventoryItem.update({
        where: { id: existingInventory.id },
        data: {
          quantity: newQuantity,
          costPerUnit: adjustmentData.costPerUnit || existingInventory.costPerUnit,
          updatedBy: 'admin', // TODO: 从认证信息获取当前用户ID
        },
      });
    } else {
      // 创建新库存记录
      if (adjustmentData.quantityChange < 0) {
        return NextResponse.json(
          {
            success: false,
            error: {
              code: 'INVALID_ADJUSTMENT',
              message: '无法对不存在的库存进行负调整',
            },
          },
          { status: 400 }
        );
      }

      inventoryItem = await prisma.inventoryItem.create({
        data: {
          warehouseId: adjustmentData.warehouseId,
          productId: adjustmentData.productId,
          variantId: adjustmentData.variantId,
          sku: adjustmentData.sku,
          quantity: adjustmentData.quantityChange,
          reservedQuantity: 0,
          costPerUnit: adjustmentData.costPerUnit,
          createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
        },
      });
    }

    // 创建库存变动记录
    const adjustmentRecord = await prisma.inventoryMovement.create({
      data: {
        warehouseId: adjustmentData.warehouseId,
        productId: adjustmentData.productId,
        variantId: adjustmentData.variantId,
        sku: adjustmentData.sku,
        type: 'ADJUSTMENT',
        quantityBefore,
        quantityChange: adjustmentData.quantityChange,
        quantityAfter: quantityBefore + adjustmentData.quantityChange,
        reason: adjustmentData.reason,
        notes: adjustmentData.notes,
        costPerUnit: adjustmentData.costPerUnit,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
      },
    });

    // 格式化响应数据
    const responseData: InventoryAdjustment = {
      id: adjustmentRecord.id,
      warehouseId: adjustmentRecord.warehouseId,
      productId: adjustmentRecord.productId || undefined,
      variantId: adjustmentRecord.variantId || undefined,
      sku: adjustmentRecord.sku,
      quantityBefore: adjustmentRecord.quantityBefore,
      quantityChange: adjustmentRecord.quantityChange,
      quantityAfter: adjustmentRecord.quantityAfter,
      reason: adjustmentRecord.reason,
      notes: adjustmentRecord.notes || undefined,
      costPerUnit: adjustmentRecord.costPerUnit || undefined,
      adjustedBy: adjustmentRecord.createdBy || undefined,
      adjustedAt: adjustmentRecord.createdAt.toISOString(),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建库存调整API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
