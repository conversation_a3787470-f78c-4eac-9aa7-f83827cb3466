/**
 * 订单管理API路由
 * 处理订单的CRUD操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createOrderSchema, paginationSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse } from '@/types';
import { orderService, CreateOrderRequest } from '@/lib/services/order-service';
import { rowLevelSecurity } from '@/lib/rbac/row-level-security';

// 订单信息类型
interface OrderInfo {
  id: string;
  orderNumber: string;
  customerId?: string;
  status: string;
  currency: string;
  exchangeRate: number;
  subtotal: number;
  taxAmount: number;
  shippingAmount: number;
  discountAmount: number;
  totalAmount: number;
  billingAddress: any;
  shippingAddress: any;
  notes?: string;
  source?: string;
  sourceOrderId?: string;
  createdAt: string;
  updatedAt: string;
  customer?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  items: Array<{
    id: string;
    sku: string;
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
  statusHistory: Array<{
    id: string;
    status: string;
    notes?: string;
    changedAt: string;
    changedBy?: string;
  }>;
}

/**
 * GET /api/orders
 * 获取订单列表（分页）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginatedResponse<OrderInfo>>>> {
  try {
    // 获取用户信息
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未提供用户信息'
        }
      }, { status: 401 });
    }

    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sort: searchParams.get('sort') || 'createdAt',
      order: searchParams.get('order') || 'desc',
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
      dateFrom: searchParams.get('dateFrom') || '',
      dateTo: searchParams.get('dateTo') || '',
    };

    // 验证分页参数
    const paginationResult = paginationSchema.safeParse(queryParams);
    if (!paginationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '分页参数验证失败',
            details: paginationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, sort, order } = paginationResult.data;
    const { search, status, dateFrom, dateTo } = queryParams;

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { orderNumber: { contains: search, mode: 'insensitive' } },
        { customer: { email: { contains: search, mode: 'insensitive' } } },
        { customer: { firstName: { contains: search, mode: 'insensitive' } } },
        { customer: { lastName: { contains: search, mode: 'insensitive' } } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 构建基础查询
    let baseQuery = {
      where,
      skip,
      take: limit,
      orderBy: { [sort]: order },
    };

    // 应用行级权限过滤
    baseQuery = await rowLevelSecurity.addRowLevelFilter(userId, 'orders', baseQuery);

    // 查询订单总数
    const total = await prisma.order.count({ where: baseQuery.where });

    // 查询订单列表
    const orders = await prisma.order.findMany({
      ...baseQuery,
      include: {
        customer: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        items: {
          select: {
            id: true,
            sku: true,
            name: true,
            quantity: true,
            unitPrice: true,
            totalPrice: true,
          },
        },
        statusHistory: {
          select: {
            id: true,
            status: true,
            notes: true,
            createdAt: true,
            createdBy: true,
          },
          orderBy: { createdAt: 'desc' },
          take: 5,
        },
      },
    });

    // 格式化订单数据
    const formattedOrders: OrderInfo[] = orders.map(order => ({
      id: order.id,
      orderNumber: order.orderNumber,
      customerId: order.customerId || undefined,
      status: order.status,
      currency: order.currency,
      exchangeRate: order.exchangeRate,
      subtotal: order.subtotal,
      taxAmount: order.taxAmount,
      shippingAmount: order.shippingAmount,
      discountAmount: order.discountAmount,
      totalAmount: order.totalAmount,
      billingAddress: order.billingAddress,
      shippingAddress: order.shippingAddress,
      notes: order.notes || undefined,
      source: order.source || undefined,
      sourceOrderId: order.sourceOrderId || undefined,
      createdAt: order.createdAt.toISOString(),
      updatedAt: order.updatedAt.toISOString(),
      customer: order.customer || undefined,
      items: order.items,
      statusHistory: order.statusHistory.map(history => ({
        id: history.id,
        status: history.status,
        notes: history.notes || undefined,
        changedAt: history.createdAt.toISOString(),
        changedBy: history.createdBy || undefined,
      })),
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const responseData: PaginatedResponse<OrderInfo> = {
      data: formattedOrders,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取订单列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/orders
 * 创建新订单
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<OrderInfo>>> {
  try {
    // 获取用户信息
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: '未提供用户信息'
        }
      }, { status: 401 });
    }

    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createOrderSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const orderData = validationResult.data;

    // 生成订单号
    const orderNumber = `ORD${Date.now()}${Math.random().toString(36).substr(2, 4).toUpperCase()}`;

    // 创建订单
    const newOrder = await prisma.order.create({
      data: {
        orderNumber,
        customerId: orderData.customerId,
        status: 'PENDING',
        currency: orderData.currency,
        exchangeRate: orderData.exchangeRate,
        subtotal: orderData.subtotal,
        taxAmount: orderData.taxAmount,
        shippingAmount: orderData.shippingAmount,
        discountAmount: orderData.discountAmount,
        totalAmount: orderData.totalAmount,
        billingAddress: orderData.billingAddress,
        shippingAddress: orderData.shippingAddress,
        notes: orderData.notes,
        internalNotes: orderData.internalNotes,
        source: orderData.source,
        sourceOrderId: orderData.sourceOrderId,
        createdBy: userId,
        items: {
          create: orderData.items.map(item => ({
            productId: item.productId,
            variantId: item.variantId,
            sku: item.sku,
            name: item.name,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            costPrice: item.costPrice,
            weight: item.weight,
            productData: item.productData,
          })),
        },
        statusHistory: {
          create: {
            status: 'PENDING',
            notes: '订单已创建',
            createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
          },
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        items: {
          select: {
            id: true,
            sku: true,
            name: true,
            quantity: true,
            unitPrice: true,
            totalPrice: true,
          },
        },
        statusHistory: {
          select: {
            id: true,
            status: true,
            notes: true,
            createdAt: true,
            createdBy: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    // 格式化响应数据
    const responseData: OrderInfo = {
      id: newOrder.id,
      orderNumber: newOrder.orderNumber,
      customerId: newOrder.customerId || undefined,
      status: newOrder.status,
      currency: newOrder.currency,
      exchangeRate: newOrder.exchangeRate,
      subtotal: newOrder.subtotal,
      taxAmount: newOrder.taxAmount,
      shippingAmount: newOrder.shippingAmount,
      discountAmount: newOrder.discountAmount,
      totalAmount: newOrder.totalAmount,
      billingAddress: newOrder.billingAddress,
      shippingAddress: newOrder.shippingAddress,
      notes: newOrder.notes || undefined,
      source: newOrder.source || undefined,
      sourceOrderId: newOrder.sourceOrderId || undefined,
      createdAt: newOrder.createdAt.toISOString(),
      updatedAt: newOrder.updatedAt.toISOString(),
      customer: newOrder.customer || undefined,
      items: newOrder.items,
      statusHistory: newOrder.statusHistory.map(history => ({
        id: history.id,
        status: history.status,
        notes: history.notes || undefined,
        changedAt: history.createdAt.toISOString(),
        changedBy: history.createdBy || undefined,
      })),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建订单API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
