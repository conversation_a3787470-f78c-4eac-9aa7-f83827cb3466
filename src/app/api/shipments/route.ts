/**
 * 物流管理API路由
 * 处理发货和物流跟踪操作
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { createShipmentSchema, paginationSchema } from '@/lib/validations';
import { ApiResponse, PaginatedResponse } from '@/types';

// 发货信息类型
interface ShipmentInfo {
  id: string;
  orderId: string;
  carrierId: string;
  trackingNumber?: string;
  serviceType?: string;
  status: string;
  shippingCost?: number;
  weight?: number;
  dimensions?: any;
  shippingAddress: any;
  estimatedDelivery?: string;
  actualDelivery?: string;
  createdAt: string;
  updatedAt: string;
  order: {
    id: string;
    orderNumber: string;
    totalAmount: number;
    customer?: {
      id: string;
      email: string;
      firstName?: string;
      lastName?: string;
    };
  };
  carrier: {
    id: string;
    name: string;
    code: string;
    trackingUrl?: string;
  };
  trackingEvents: Array<{
    id: string;
    status: string;
    description: string;
    location?: string;
    timestamp: string;
  }>;
}

/**
 * GET /api/shipments
 * 获取发货列表（分页）
 */
export async function GET(request: NextRequest): Promise<NextResponse<ApiResponse<PaginatedResponse<ShipmentInfo>>>> {
  try {
    // 获取查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = {
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '20'),
      sort: searchParams.get('sort') || 'createdAt',
      order: searchParams.get('order') || 'desc',
      search: searchParams.get('search') || '',
      status: searchParams.get('status') || '',
      carrierId: searchParams.get('carrierId') || '',
      dateFrom: searchParams.get('dateFrom') || '',
      dateTo: searchParams.get('dateTo') || '',
    };

    // 验证分页参数
    const paginationResult = paginationSchema.safeParse(queryParams);
    if (!paginationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '分页参数验证失败',
            details: paginationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const { page, limit, sort, order } = paginationResult.data;
    const { search, status, carrierId, dateFrom, dateTo } = queryParams;

    // 构建查询条件
    const where: any = {};

    if (search) {
      where.OR = [
        { trackingNumber: { contains: search, mode: 'insensitive' } },
        { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
        { order: { customer: { email: { contains: search, mode: 'insensitive' } } } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (carrierId) {
      where.carrierId = carrierId;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // 计算偏移量
    const skip = (page - 1) * limit;

    // 查询发货总数
    const total = await prisma.shipment.count({ where });

    // 查询发货列表
    const shipments = await prisma.shipment.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sort]: order },
      include: {
        order: {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true,
            customer: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        carrier: {
          select: {
            id: true,
            name: true,
            code: true,
            trackingUrl: true,
          },
        },
        trackingEvents: {
          select: {
            id: true,
            status: true,
            description: true,
            location: true,
            timestamp: true,
          },
          orderBy: { timestamp: 'desc' },
          take: 5,
        },
      },
    });

    // 格式化发货数据
    const formattedShipments: ShipmentInfo[] = shipments.map(shipment => ({
      id: shipment.id,
      orderId: shipment.orderId,
      carrierId: shipment.carrierId,
      trackingNumber: shipment.trackingNumber || undefined,
      serviceType: shipment.serviceType || undefined,
      status: shipment.status,
      shippingCost: shipment.shippingCost || undefined,
      weight: shipment.weight || undefined,
      dimensions: shipment.dimensions,
      shippingAddress: shipment.shippingAddress,
      estimatedDelivery: shipment.estimatedDelivery?.toISOString(),
      actualDelivery: shipment.actualDelivery?.toISOString(),
      createdAt: shipment.createdAt.toISOString(),
      updatedAt: shipment.updatedAt.toISOString(),
      order: shipment.order,
      carrier: shipment.carrier,
      trackingEvents: shipment.trackingEvents.map(event => ({
        id: event.id,
        status: event.status,
        description: event.description,
        location: event.location || undefined,
        timestamp: event.timestamp.toISOString(),
      })),
    }));

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const responseData: PaginatedResponse<ShipmentInfo> = {
      data: formattedShipments,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev,
      },
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('获取发货列表API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/shipments
 * 创建新发货记录
 */
export async function POST(request: NextRequest): Promise<NextResponse<ApiResponse<ShipmentInfo>>> {
  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = createShipmentSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: '请求参数验证失败',
            details: validationResult.error.flatten().fieldErrors,
          },
        },
        { status: 400 }
      );
    }

    const shipmentData = validationResult.data;

    // 检查订单是否存在
    const order = await prisma.order.findUnique({
      where: { id: shipmentData.orderId },
      include: {
        customer: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
      },
    });

    if (!order) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'ORDER_NOT_FOUND',
            message: '订单不存在',
          },
        },
        { status: 404 }
      );
    }

    // 检查承运商是否存在
    const carrier = await prisma.carrier.findUnique({
      where: { id: shipmentData.carrierId },
    });

    if (!carrier) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: 'CARRIER_NOT_FOUND',
            message: '承运商不存在',
          },
        },
        { status: 404 }
      );
    }

    // 创建发货记录
    const newShipment = await prisma.shipment.create({
      data: {
        orderId: shipmentData.orderId,
        carrierId: shipmentData.carrierId,
        trackingNumber: shipmentData.trackingNumber,
        serviceType: shipmentData.serviceType,
        status: 'PENDING',
        shippingCost: shipmentData.shippingCost,
        weight: shipmentData.weight,
        dimensions: shipmentData.dimensions,
        shippingAddress: shipmentData.shippingAddress,
        estimatedDelivery: shipmentData.estimatedDelivery ? new Date(shipmentData.estimatedDelivery) : undefined,
        createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
        trackingEvents: {
          create: {
            status: 'PENDING',
            description: '发货记录已创建',
            timestamp: new Date(),
            createdBy: 'admin', // TODO: 从认证信息获取当前用户ID
          },
        },
      },
      include: {
        order: {
          select: {
            id: true,
            orderNumber: true,
            totalAmount: true,
            customer: {
              select: {
                id: true,
                email: true,
                firstName: true,
                lastName: true,
              },
            },
          },
        },
        carrier: {
          select: {
            id: true,
            name: true,
            code: true,
            trackingUrl: true,
          },
        },
        trackingEvents: {
          select: {
            id: true,
            status: true,
            description: true,
            location: true,
            timestamp: true,
          },
          orderBy: { timestamp: 'desc' },
        },
      },
    });

    // 更新订单状态为已发货
    await prisma.order.update({
      where: { id: shipmentData.orderId },
      data: { status: 'SHIPPED' },
    });

    // 格式化响应数据
    const responseData: ShipmentInfo = {
      id: newShipment.id,
      orderId: newShipment.orderId,
      carrierId: newShipment.carrierId,
      trackingNumber: newShipment.trackingNumber || undefined,
      serviceType: newShipment.serviceType || undefined,
      status: newShipment.status,
      shippingCost: newShipment.shippingCost || undefined,
      weight: newShipment.weight || undefined,
      dimensions: newShipment.dimensions,
      shippingAddress: newShipment.shippingAddress,
      estimatedDelivery: newShipment.estimatedDelivery?.toISOString(),
      actualDelivery: newShipment.actualDelivery?.toISOString(),
      createdAt: newShipment.createdAt.toISOString(),
      updatedAt: newShipment.updatedAt.toISOString(),
      order: newShipment.order,
      carrier: newShipment.carrier,
      trackingEvents: newShipment.trackingEvents.map(event => ({
        id: event.id,
        status: event.status,
        description: event.description,
        location: event.location || undefined,
        timestamp: event.timestamp.toISOString(),
      })),
    };

    return NextResponse.json(
      {
        success: true,
        data: responseData,
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('创建发货API错误:', error);

    return NextResponse.json(
      {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '服务器内部错误',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: crypto.randomUUID(),
          version: 'v1',
        },
      },
      { status: 500 }
    );
  }
}
