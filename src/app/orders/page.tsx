/**
 * 订单管理页面
 * 显示订单列表和管理功能，集成RBAC权限控制
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PermissionGuard, Can } from '@/components/auth/PermissionGuard';
import { usePermissions } from '@/hooks/usePermissions';
import { cn } from '@/lib/utils';
import {
  Search,
  Plus,
  Edit,
  Eye,
  MoreHorizontal,
  ShoppingCart,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  RefreshCw,
  Package,
  Truck,
  DollarSign,
} from 'lucide-react';

// 订单数据类型
interface Order {
  id: string;
  orderNumber: string;
  customerId?: string;
  status: string;
  currency: string;
  totalAmount: number;
  createdAt: string;
  customer?: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  items: Array<{
    id: string;
    sku: string;
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }>;
}

// 订单管理页面组件
export default function OrdersPage() {
  const { hasPermission } = usePermissions();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);

  // 获取订单列表
  const fetchOrders = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter && { status: statusFilter }),
        ...(dateFrom && { dateFrom }),
        ...(dateTo && { dateTo }),
      });

      const response = await fetch(`/api/orders?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setOrders(result.data.data || []);
        setTotalPages(result.data.pagination?.totalPages || 1);
        setTotal(result.data.pagination?.total || 0);
      } else {
        throw new Error(result.error?.message || '获取订单列表失败');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取订单列表失败';
      setError(errorMessage);
      console.error('获取订单列表错误:', error);
      setOrders([]);
      setTotal(0);
      setTotalPages(1);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchOrders();
  }, [currentPage, searchQuery, statusFilter, dateFrom, dateTo]);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchOrders();
  };

  // 获取订单状态显示样式
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: '待处理', className: 'bg-warning-100 text-warning-800' },
      CONFIRMED: { label: '已确认', className: 'bg-brand-100 text-brand-800' },
      PROCESSING: { label: '处理中', className: 'bg-blue-100 text-blue-800' },
      SHIPPED: { label: '已发货', className: 'bg-purple-100 text-purple-800' },
      DELIVERED: { label: '已送达', className: 'bg-success-100 text-success-800' },
      CANCELLED: { label: '已取消', className: 'bg-error-100 text-error-800' },
      REFUNDED: { label: '已退款', className: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      className: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={cn('px-2 py-1 text-xs font-medium rounded-full', config.className)}>
        {config.label}
      </span>
    );
  };

  // 格式化价格
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency === 'USD' ? 'USD' : 'CNY',
    }).format(price);
  };

  // 格式化客户名称
  const formatCustomerName = (customer?: Order['customer']) => {
    if (!customer) return '游客';
    if (customer.firstName || customer.lastName) {
      return `${customer.firstName || ''} ${customer.lastName || ''}`.trim();
    }
    return customer.email;
  };

  // 计算统计数据
  const stats = {
    totalOrders: total,
    pendingOrders: orders.filter(order => order.status === 'PENDING').length,
    processingOrders: orders.filter(order => order.status === 'PROCESSING').length,
    shippedOrders: orders.filter(order => order.status === 'SHIPPED').length,
  };

  // 错误状态显示
  if (error) {
    return (
      <PermissionGuard permission={{ resource: 'orders', action: 'read' }}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
            <p className="mt-4 text-red-600">{error}</p>
            <Button
              onClick={() => fetchOrders()}
              className="mt-4"
              variant="outline"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              重新加载
            </Button>
          </div>
        </div>
      </PermissionGuard>
    );
  }

  return (
    <PermissionGuard permission={{ resource: 'orders', action: 'read' }}>
      <div className="space-y-6 p-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">订单管理</h1>
            <p className="text-gray-600 mt-1">管理您的订单处理流程和客户订单</p>
          </div>
          <div className="flex space-x-2">
            <Can do="export" on="orders">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出订单
              </Button>
            </Can>
            <Can do="create" on="orders">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                创建订单
              </Button>
            </Can>
          </div>
        </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总订单数</p>
                <p className="text-2xl font-bold text-foreground">{stats.totalOrders}</p>
              </div>
              <div className="p-3 bg-brand-100 text-brand-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">待处理</p>
                <p className="text-2xl font-bold text-foreground">{stats.pendingOrders}</p>
              </div>
              <div className="p-3 bg-warning-100 text-warning-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">处理中</p>
                <p className="text-2xl font-bold text-foreground">{stats.processingOrders}</p>
              </div>
              <div className="p-3 bg-blue-100 text-blue-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">已发货</p>
                <p className="text-2xl font-bold text-foreground">{stats.shippedOrders}</p>
              </div>
              <div className="p-3 bg-success-100 text-success-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Input
                  type="search"
                  placeholder="搜索订单号、客户邮箱、姓名..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  }
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
              >
                <option value="">所有状态</option>
                <option value="PENDING">待处理</option>
                <option value="CONFIRMED">已确认</option>
                <option value="PROCESSING">处理中</option>
                <option value="SHIPPED">已发货</option>
                <option value="DELIVERED">已送达</option>
                <option value="CANCELLED">已取消</option>
                <option value="REFUNDED">已退款</option>
              </select>
              <Button type="submit">搜索</Button>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-foreground">开始日期:</label>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
                />
              </div>
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-foreground">结束日期:</label>
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
                />
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* 订单列表 */}
      <Card>
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
          <CardDescription>
            共 {total} 个订单，当前显示第 {currentPage} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="loading-spinner w-8 h-8"></div>
              <span className="ml-2 text-muted-foreground">加载中...</span>
            </div>
          ) : orders.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <h3 className="empty-state-title">暂无订单</h3>
              <p className="empty-state-description">
                还没有任何订单，等待客户下单或手动创建订单。
              </p>
              <Button>创建第一个订单</Button>
            </div>
          ) : (
            <div className="space-y-4">
              {orders.map((order) => (
                <div
                  key={order.id}
                  className="flex items-center space-x-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  {/* 订单信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-foreground">
                        订单 #{order.orderNumber}
                      </h3>
                      {getStatusBadge(order.status)}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>客户: {formatCustomerName(order.customer)}</span>
                      <span>商品数量: {order.items.reduce((sum, item) => sum + item.quantity, 0)}</span>
                      <span>总金额: {formatPrice(order.totalAmount, order.currency)}</span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <span>下单时间: {new Date(order.createdAt).toLocaleString()}</span>
                      {order.customer?.email && (
                        <span>邮箱: {order.customer.email}</span>
                      )}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    <Can do="read" on="orders">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        查看
                      </Button>
                    </Can>
                    <Can do="update" on="orders">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-1" />
                        编辑
                      </Button>
                    </Can>
                    <Can do="process" on="orders">
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </Can>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                显示第 {(currentPage - 1) * 20 + 1} - {Math.min(currentPage * 20, total)} 条，共 {total} 条
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
