/**
 * 数据分析页面组件
 * 显示业务数据分析和报表
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// 分析数据类型
interface AnalyticsData {
  overview: {
    totalRevenue: number;
    totalOrders: number;
    totalCustomers: number;
    averageOrderValue: number;
    revenueGrowth: number;
    orderGrowth: number;
    customerGrowth: number;
  };
  salesTrend: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
  topProducts: Array<{
    id: string;
    name: string;
    sales: number;
    revenue: number;
    growth: number;
  }>;
  customerSegments: Array<{
    segment: string;
    count: number;
    revenue: number;
    percentage: number;
  }>;
  geographicData: Array<{
    country: string;
    countryCode: string;
    orders: number;
    revenue: number;
    customers: number;
  }>;
}

// 数据分析页面组件
export default function AnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('30d');

  // 模拟分析数据
  useEffect(() => {
    const mockData: AnalyticsData = {
      overview: {
        totalRevenue: 125680.50,
        totalOrders: 1247,
        totalCustomers: 892,
        averageOrderValue: 100.78,
        revenueGrowth: 12.5,
        orderGrowth: 8.3,
        customerGrowth: 15.2,
      },
      salesTrend: [
        { date: '2024-01-01', revenue: 4200, orders: 42 },
        { date: '2024-01-02', revenue: 3800, orders: 38 },
        { date: '2024-01-03', revenue: 5100, orders: 51 },
        { date: '2024-01-04', revenue: 4600, orders: 46 },
        { date: '2024-01-05', revenue: 5400, orders: 54 },
        { date: '2024-01-06', revenue: 4900, orders: 49 },
        { date: '2024-01-07', revenue: 5800, orders: 58 },
      ],
      topProducts: [
        { id: '1', name: '无线蓝牙耳机', sales: 156, revenue: 15600, growth: 23.5 },
        { id: '2', name: '智能手表', sales: 89, revenue: 26700, growth: 18.2 },
        { id: '3', name: '便携充电宝', sales: 234, revenue: 11700, growth: 15.8 },
        { id: '4', name: '手机保护壳', sales: 445, revenue: 8900, growth: 12.1 },
        { id: '5', name: '数据线', sales: 678, revenue: 6780, growth: 9.4 },
      ],
      customerSegments: [
        { segment: 'VIP客户', count: 89, revenue: 45230, percentage: 36.0 },
        { segment: '普通客户', count: 567, revenue: 56780, percentage: 45.2 },
        { segment: '新客户', count: 236, revenue: 23670, percentage: 18.8 },
      ],
      geographicData: [
        { country: '美国', countryCode: 'US', orders: 456, revenue: 45600, customers: 234 },
        { country: '中国', countryCode: 'CN', orders: 389, revenue: 38900, customers: 198 },
        { country: '德国', countryCode: 'DE', orders: 234, revenue: 23400, customers: 156 },
        { country: '英国', countryCode: 'GB', orders: 168, revenue: 16800, customers: 89 },
      ],
    };

    // 模拟API调用延迟
    setTimeout(() => {
      setAnalyticsData(mockData);
      setLoading(false);
    }, 1000);
  }, [timeRange]);

  // 格式化价格
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(price);
  };

  // 格式化百分比
  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  // 获取增长趋势图标和颜色
  const getGrowthIndicator = (growth: number) => {
    const isPositive = growth > 0;
    return (
      <div className={cn('flex items-center', isPositive ? 'text-success-600' : 'text-error-600')}>
        <svg
          className={cn('w-4 h-4 mr-1', isPositive ? 'rotate-0' : 'rotate-180')}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
        </svg>
        <span className="text-sm font-medium">{formatPercentage(growth)}</span>
      </div>
    );
  };

  if (loading || !analyticsData) {
    return (
      <div className="page-container">
        <div className="flex items-center justify-center py-12">
          <div className="loading-spinner w-8 h-8"></div>
          <span className="ml-2 text-muted-foreground">加载分析数据中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container">
      {/* 页面标题 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="page-title">数据分析</h1>
          <p className="page-subtitle">
            查看业务数据分析和关键指标
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
          >
            <option value="7d">最近7天</option>
            <option value="30d">最近30天</option>
            <option value="90d">最近90天</option>
            <option value="1y">最近1年</option>
          </select>
          <Button variant="outline">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出报表
          </Button>
        </div>
      </div>

      {/* 核心指标概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总收入</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatPrice(analyticsData.overview.totalRevenue)}
                </p>
                {getGrowthIndicator(analyticsData.overview.revenueGrowth)}
              </div>
              <div className="p-3 bg-green-100 text-green-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总订单数</p>
                <p className="text-2xl font-bold text-foreground">
                  {analyticsData.overview.totalOrders.toLocaleString()}
                </p>
                {getGrowthIndicator(analyticsData.overview.orderGrowth)}
              </div>
              <div className="p-3 bg-blue-100 text-blue-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总客户数</p>
                <p className="text-2xl font-bold text-foreground">
                  {analyticsData.overview.totalCustomers.toLocaleString()}
                </p>
                {getGrowthIndicator(analyticsData.overview.customerGrowth)}
              </div>
              <div className="p-3 bg-purple-100 text-purple-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">平均订单价值</p>
                <p className="text-2xl font-bold text-foreground">
                  {formatPrice(analyticsData.overview.averageOrderValue)}
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  每笔订单平均金额
                </p>
              </div>
              <div className="p-3 bg-orange-100 text-orange-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        {/* 热销商品 */}
        <Card>
          <CardHeader>
            <CardTitle>热销商品</CardTitle>
            <CardDescription>销量最高的商品排行</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.topProducts.map((product, index) => (
                <div key={product.id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-brand-100 text-brand-600 rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-foreground">{product.name}</p>
                      <p className="text-sm text-muted-foreground">销量: {product.sales}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-foreground">{formatPrice(product.revenue)}</p>
                    <div className="text-sm text-success-600">+{product.growth}%</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 客户分群 */}
        <Card>
          <CardHeader>
            <CardTitle>客户分群</CardTitle>
            <CardDescription>不同客户群体的贡献分析</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analyticsData.customerSegments.map((segment) => (
                <div key={segment.segment} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-foreground">{segment.segment}</span>
                    <span className="text-sm text-muted-foreground">
                      {segment.count} 人 · {formatPrice(segment.revenue)}
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-brand-600 h-2 rounded-full"
                      style={{ width: `${segment.percentage}%` }}
                    ></div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    占总收入的 {segment.percentage}%
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 地理分布 */}
      <Card>
        <CardHeader>
          <CardTitle>地理分布</CardTitle>
          <CardDescription>不同国家和地区的销售情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {analyticsData.geographicData.map((country) => (
              <div key={country.countryCode} className="p-4 border border-border rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-medium text-foreground">{country.country}</h3>
                  <span className="text-2xl">{country.countryCode === 'US' ? '🇺🇸' : country.countryCode === 'CN' ? '🇨🇳' : country.countryCode === 'DE' ? '🇩🇪' : '🇬🇧'}</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">订单数:</span>
                    <span className="font-medium">{country.orders}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">收入:</span>
                    <span className="font-medium">{formatPrice(country.revenue)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">客户数:</span>
                    <span className="font-medium">{country.customers}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
