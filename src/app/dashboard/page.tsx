/**
 * Dashboard主页
 * 显示系统概览、关键指标和快捷操作，集成RBAC权限控制
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PermissionGuard, Can } from '@/components/auth/PermissionGuard';
import { usePermissions } from '@/hooks/usePermissions';
import {
  ShoppingCart,
  Package,
  Users,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Eye,
  Edit,
  MoreHorizontal,
} from 'lucide-react';

// 仪表板数据接口
interface DashboardStats {
  totalOrders: number;
  totalRevenue: number;
  totalProducts: number;
  totalCustomers: number;
  pendingOrders: number;
  lowStockProducts: number;
  revenueGrowth: number;
  orderGrowth: number;
}

interface RecentOrder {
  id: string;
  customerName: string;
  amount: number;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  createdAt: string;
}

/**
 * Dashboard主页组件
 */
export default function DashboardPage() {
  const { hasPermission } = usePermissions();

  // 状态管理
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 获取仪表板数据
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // 模拟API调用 - 实际项目中应该调用真实的API
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 模拟数据
        setStats({
          totalOrders: 1234,
          totalRevenue: 98765.43,
          totalProducts: 567,
          totalCustomers: 890,
          pendingOrders: 23,
          lowStockProducts: 12,
          revenueGrowth: 12.5,
          orderGrowth: 8.3,
        });

        setRecentOrders([
          {
            id: 'ORD-001',
            customerName: '张三',
            amount: 299.99,
            status: 'pending',
            createdAt: '2024-01-15T10:30:00Z',
          },
          {
            id: 'ORD-002',
            customerName: '李四',
            amount: 599.99,
            status: 'processing',
            createdAt: '2024-01-15T09:15:00Z',
          },
          {
            id: 'ORD-003',
            customerName: '王五',
            amount: 199.99,
            status: 'shipped',
            createdAt: '2024-01-15T08:45:00Z',
          },
        ]);
      } catch (err) {
        setError('获取仪表板数据失败');
        console.error('Dashboard data fetch error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // 旧的模拟数据（保留作为备用）
  const oldStats = [
    {
      title: '今日订单',
      value: '156',
      change: '+12.5%',
      changeType: 'positive' as const,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      ),
    },
    {
      title: '今日销售额',
      value: '¥24,580',
      change: '+8.2%',
      changeType: 'positive' as const,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
    },
    {
      title: '待处理订单',
      value: '23',
      change: '-5.1%',
      changeType: 'negative' as const,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      title: '库存预警',
      value: '8',
      change: '+2',
      changeType: 'warning' as const,
      icon: (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      ),
    },
  ];

  // 获取订单状态显示文本和颜色
  const getOrderStatusInfo = (status: RecentOrder['status']) => {
    const statusMap = {
      pending: { text: '待处理', color: 'bg-yellow-100 text-yellow-800' },
      processing: { text: '处理中', color: 'bg-blue-100 text-blue-800' },
      shipped: { text: '已发货', color: 'bg-purple-100 text-purple-800' },
      delivered: { text: '已完成', color: 'bg-green-100 text-green-800' },
      cancelled: { text: '已取消', color: 'bg-red-100 text-red-800' },
    };
    return statusMap[status];
  };

  // 格式化货币
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(amount);
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载仪表板数据中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
          <p className="mt-4 text-red-600">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="mt-4"
            variant="outline"
          >
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case '已完成':
        return 'text-success-600 bg-success-100';
      case '已发货':
        return 'text-brand-600 bg-brand-100';
      case '处理中':
        return 'text-warning-600 bg-warning-100';
      case '待付款':
        return 'text-error-600 bg-error-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getChangeColor = (type: string) => {
    switch (type) {
      case 'positive':
        return 'text-success-600';
      case 'negative':
        return 'text-error-600';
      case 'warning':
        return 'text-warning-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <PermissionGuard permission={{ resource: 'dashboard', action: 'read' }}>
      <div className="space-y-6 p-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
            <p className="text-gray-600 mt-1">欢迎回来，这里是您的业务概览</p>
          </div>
          <div className="flex space-x-2">
            <Can do="create" on="orders">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                新建订单
              </Button>
            </Can>
            <Can do="create" on="products">
              <Button variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                添加商品
              </Button>
            </Can>
          </div>
        </div>

        {/* 关键指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总订单数</CardTitle>
              <ShoppingCart className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalOrders.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-green-500">+{stats?.orderGrowth}%</span>
                <span className="ml-1">较上月</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总收入</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats?.totalRevenue || 0)}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <ArrowUpRight className="h-3 w-3 text-green-500 mr-1" />
                <span className="text-green-500">+{stats?.revenueGrowth}%</span>
                <span className="ml-1">较上月</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">商品总数</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalProducts.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <AlertTriangle className="h-3 w-3 text-orange-500 mr-1" />
                <span className="text-orange-500">{stats?.lowStockProducts}</span>
                <span className="ml-1">库存不足</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">客户总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats?.totalCustomers.toLocaleString()}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                <Clock className="h-3 w-3 text-blue-500 mr-1" />
                <span className="text-blue-500">{stats?.pendingOrders}</span>
                <span className="ml-1">待处理订单</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 最近订单和快捷操作 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 最近订单 */}
          <Can do="read" on="orders">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>最近订单</CardTitle>
                  <CardDescription>
                    最新的订单信息和状态
                  </CardDescription>
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  查看全部
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentOrders.map((order) => {
                    const statusInfo = getOrderStatusInfo(order.status);
                    return (
                      <div key={order.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium text-gray-900">{order.id}</p>
                            <Badge className={statusInfo.color}>
                              {statusInfo.text}
                            </Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm text-gray-600">
                            <span>{order.customerName}</span>
                            <span>{formatDate(order.createdAt)}</span>
                          </div>
                        </div>
                        <div className="ml-4 text-right">
                          <p className="font-semibold text-gray-900">{formatCurrency(order.amount)}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </Can>

          {/* 快速操作 */}
          <Card>
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
              <CardDescription>
                常用功能的快速入口
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <Can do="create" on="orders">
                  <Button className="h-20 flex-col space-y-2" variant="outline">
                    <ShoppingCart className="w-6 h-6" />
                    <span>创建订单</span>
                  </Button>
                </Can>

                <Can do="create" on="products">
                  <Button className="h-20 flex-col space-y-2" variant="outline">
                    <Package className="w-6 h-6" />
                    <span>添加商品</span>
                  </Button>
                </Can>

                <Can do="read" on="customers">
                  <Button className="h-20 flex-col space-y-2" variant="outline">
                    <Users className="w-6 h-6" />
                    <span>客户管理</span>
                  </Button>
                </Can>

                <Can do="read" on="inventory">
                  <Button className="h-20 flex-col space-y-2" variant="outline">
                    <Package className="w-6 h-6" />
                    <span>库存管理</span>
                  </Button>
                </Can>

                <Can do="read" on="reports">
                  <Button className="h-20 flex-col space-y-2" variant="outline">
                    <TrendingUp className="w-6 h-6" />
                    <span>财务报表</span>
                  </Button>
                </Can>

                <Can do="read" on="settings">
                  <Button className="h-20 flex-col space-y-2" variant="outline">
                    <Edit className="w-6 h-6" />
                    <span>系统设置</span>
                  </Button>
                </Can>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 系统状态 */}
        <Can do="read" on="settings">
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>系统状态</CardTitle>
              <CardDescription>
                系统运行状态和性能指标
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 text-green-600 rounded-full mb-3">
                    <CheckCircle className="w-6 h-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900">系统运行正常</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    所有服务运行稳定
                  </p>
                </div>

                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 text-blue-600 rounded-full mb-3">
                    <TrendingUp className="w-6 h-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900">响应时间</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    平均 120ms
                  </p>
                </div>

                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-yellow-100 text-yellow-600 rounded-full mb-3">
                    <Clock className="w-6 h-6" />
                  </div>
                  <h3 className="font-semibold text-gray-900">数据同步</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    最后同步: 5分钟前
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </Can>
      </div>
    </PermissionGuard>
  );
}
