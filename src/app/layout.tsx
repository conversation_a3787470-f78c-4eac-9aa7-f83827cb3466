/**
 * 根布局组件
 * 定义应用的全局布局结构和样式
 */

import type { Metadata } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { cn } from '@/lib/utils';
import '@/styles/globals.css';

// 字体配置
const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
});

// 网站元数据
export const metadata: Metadata = {
  title: {
    default: 'CBEC ERP - 跨境电商ERP管理平台',
    template: '%s | CBEC ERP',
  },
  description: '专业的跨境电商ERP管理平台，支持全球化网店运营、库存管理、订单处理、客户管理等功能',
  keywords: [
    '跨境电商',
    'ERP系统',
    '库存管理',
    '订单管理',
    '客户管理',
    '电商平台',
    '企业管理',
    '供应链管理',
  ],
  authors: [
    {
      name: 'CBEC ERP Team',
      url: 'https://cbec-erp.com',
    },
  ],
  creator: 'CBEC ERP Team',
  publisher: 'CBEC ERP',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
    languages: {
      'zh-CN': '/zh-CN',
      'en-US': '/en-US',
      'ja-JP': '/ja-JP',
      'de-DE': '/de-DE',
      'fr-FR': '/fr-FR',
      'es-ES': '/es-ES',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: '/',
    title: 'CBEC ERP - 跨境电商ERP管理平台',
    description: '专业的跨境电商ERP管理平台，支持全球化网店运营',
    siteName: 'CBEC ERP',
    images: [
      {
        url: '/images/og-image.png',
        width: 1200,
        height: 630,
        alt: 'CBEC ERP - 跨境电商ERP管理平台',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'CBEC ERP - 跨境电商ERP管理平台',
    description: '专业的跨境电商ERP管理平台，支持全球化网店运营',
    images: ['/images/twitter-image.png'],
    creator: '@cbec_erp',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_VERIFICATION,
  },
  category: 'technology',
  classification: 'Business Software',
  referrer: 'origin-when-cross-origin',
  colorScheme: 'light dark',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 5,
    userScalable: true,
  },
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#0ea5e9',
      },
    ],
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'CBEC ERP',
  },
  applicationName: 'CBEC ERP',
  generator: 'Next.js',
  abstract: '跨境电商ERP管理平台，提供全面的电商运营解决方案',
  archives: [],
  assets: [],
  bookmarks: [],
  category: 'Business',
};

// 根布局组件属性接口
interface RootLayoutProps {
  children: React.ReactNode;
}

/**
 * 根布局组件
 */
export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html 
      lang="zh-CN" 
      className={cn(
        'h-full scroll-smooth antialiased',
        inter.variable,
        jetbrainsMono.variable
      )}
      suppressHydrationWarning
    >
      <head>
        {/* 预加载关键资源 */}
        <link
          rel="preload"
          href="/fonts/inter-var.woff2"
          as="font"
          type="font/woff2"
          crossOrigin="anonymous"
        />
        
        {/* DNS预解析 */}
        <link rel="dns-prefetch" href="//fonts.googleapis.com" />
        <link rel="dns-prefetch" href="//fonts.gstatic.com" />
        <link rel="dns-prefetch" href="//api.cbec-erp.com" />
        
        {/* 预连接重要域名 */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        
        {/* 安全策略 */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
        
        {/* PWA配置 */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="CBEC ERP" />
        <meta name="application-name" content="CBEC ERP" />
        <meta name="msapplication-TileColor" content="#0ea5e9" />
        <meta name="msapplication-config" content="/browserconfig.xml" />
        <meta name="theme-color" content="#ffffff" />
        
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'SoftwareApplication',
              name: 'CBEC ERP',
              description: '专业的跨境电商ERP管理平台',
              url: 'https://cbec-erp.com',
              applicationCategory: 'BusinessApplication',
              operatingSystem: 'Web',
              offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD',
              },
              author: {
                '@type': 'Organization',
                name: 'CBEC ERP Team',
                url: 'https://cbec-erp.com',
              },
            }),
          }}
        />
      </head>
      
      <body className={cn(
        'min-h-screen bg-background font-sans text-foreground',
        'selection:bg-primary/20 selection:text-primary-foreground'
      )}>
        {/* 跳过导航链接（无障碍访问） */}
        <a
          href="#main-content"
          className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded-md focus:shadow-lg"
        >
          跳转到主要内容
        </a>
        
        {/* 主要内容区域 */}
        <div id="main-content" className="relative flex min-h-screen flex-col">
          {children}
        </div>
        
        {/* 全局脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 主题切换脚本
              (function() {
                function getThemePreference() {
                  if (typeof localStorage !== 'undefined' && localStorage.getItem('theme')) {
                    return localStorage.getItem('theme');
                  }
                  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
                }
                
                const isDark = getThemePreference() === 'dark';
                document.documentElement.classList[isDark ? 'add' : 'remove']('dark');
                
                if (typeof localStorage !== 'undefined') {
                  const observer = new MutationObserver(() => {
                    const isDark = document.documentElement.classList.contains('dark');
                    localStorage.setItem('theme', isDark ? 'dark' : 'light');
                  });
                  observer.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
                }
              })();
              
              // 性能监控
              if (typeof window !== 'undefined' && 'performance' in window) {
                window.addEventListener('load', function() {
                  setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    if (perfData && perfData.loadEventEnd > 0) {
                      const loadTime = perfData.loadEventEnd - perfData.fetchStart;
                      console.log('页面加载时间:', loadTime + 'ms');
                    }
                  }, 0);
                });
              }
            `,
          }}
        />
        
        {/* Google Analytics */}
        {process.env.NEXT_PUBLIC_GA_TRACKING_ID && (
          <>
            <script
              async
              src={`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_TRACKING_ID}`}
            />
            <script
              dangerouslySetInnerHTML={{
                __html: `
                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', '${process.env.NEXT_PUBLIC_GA_TRACKING_ID}', {
                    page_title: document.title,
                    page_location: window.location.href,
                  });
                `,
              }}
            />
          </>
        )}
      </body>
    </html>
  );
}
