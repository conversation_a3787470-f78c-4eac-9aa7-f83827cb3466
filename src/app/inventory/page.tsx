/**
 * 库存管理页面组件
 * 显示库存概览和管理功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PermissionGuard, Can } from '@/components/auth/PermissionGuard';
import { usePermissions } from '@/hooks/usePermissions';
import { cn } from '@/lib/utils';
import {
  Search,
  Plus,
  Edit,
  Eye,
  MoreHorizontal,
  Package,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  RefreshCw,
  Warehouse,
  TrendingUp,
  TrendingDown,
} from 'lucide-react';

// 库存数据类型
interface InventoryItem {
  id: string;
  warehouseId: string;
  sku: string;
  quantity: number;
  reservedQuantity: number;
  availableQuantity: number;
  costPerUnit?: number;
  lastUpdated: string;
  warehouse: {
    id: string;
    code: string;
    name: string;
  };
  product?: {
    id: string;
    name: string;
    sku: string;
    basePrice: number;
  };
}

// 仓库数据类型
interface Warehouse {
  id: string;
  code: string;
  name: string;
  isActive: boolean;
  isDefault: boolean;
  inventoryCount: number;
  totalValue: number;
}

// 库存管理页面组件
export default function InventoryPage() {
  const { hasPermission } = usePermissions();

  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedWarehouse, setSelectedWarehouse] = useState('');
  const [filterType, setFilterType] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await fetch('/api/warehouses');
      const result = await response.json();

      if (result.success) {
        setWarehouses(result.data);
      } else {
        console.error('获取仓库列表失败:', result.error);
      }
    } catch (error) {
      console.error('获取仓库列表错误:', error);
    }
  };

  // 获取库存列表
  const fetchInventory = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchQuery && { search: searchQuery }),
        ...(selectedWarehouse && { warehouseId: selectedWarehouse }),
        ...(filterType === 'lowStock' && { lowStock: 'true' }),
        ...(filterType === 'outOfStock' && { outOfStock: 'true' }),
      });

      const response = await fetch(`/api/inventory?${params}`);
      const result = await response.json();

      if (result.success) {
        setInventoryItems(result.data.data);
        setTotalPages(result.data.pagination.totalPages);
        setTotal(result.data.pagination.total);
      } else {
        console.error('获取库存列表失败:', result.error);
      }
    } catch (error) {
      console.error('获取库存列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchWarehouses();
  }, []);

  useEffect(() => {
    fetchInventory();
  }, [currentPage, searchQuery, selectedWarehouse, filterType]);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchInventory();
  };

  // 获取库存状态样式
  const getStockStatus = (available: number, total: number) => {
    if (available <= 0) {
      return { label: '缺货', className: 'bg-error-100 text-error-800' };
    } else if (available < 10) {
      return { label: '库存不足', className: 'bg-warning-100 text-warning-800' };
    } else {
      return { label: '正常', className: 'bg-success-100 text-success-800' };
    }
  };

  // 格式化价格
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(price);
  };

  // 计算统计数据
  const stats = {
    totalItems: inventoryItems.length,
    lowStockItems: inventoryItems.filter(item => item.availableQuantity < 10 && item.availableQuantity > 0).length,
    outOfStockItems: inventoryItems.filter(item => item.availableQuantity <= 0).length,
    totalValue: inventoryItems.reduce((sum, item) => sum + (item.quantity * (item.costPerUnit || 0)), 0),
  };

  return (
    <PermissionGuard permission={{ resource: 'inventory', action: 'read' }}>
      <div className="space-y-6 p-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">库存管理</h1>
            <p className="text-gray-600 mt-1">管理您的库存数量、成本和仓库分布</p>
          </div>
          <div className="flex space-x-2">
            <Can do="import" on="inventory">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导入库存
              </Button>
            </Can>
            <Can do="export" on="inventory">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出数据
              </Button>
            </Can>
            <Can do="adjust" on="inventory">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                库存调整
              </Button>
            </Can>
          </div>
        </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总库存项目</p>
                <p className="text-2xl font-bold text-foreground">{total}</p>
              </div>
              <div className="p-3 bg-brand-100 text-brand-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">库存不足</p>
                <p className="text-2xl font-bold text-foreground">{stats.lowStockItems}</p>
              </div>
              <div className="p-3 bg-warning-100 text-warning-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">缺货商品</p>
                <p className="text-2xl font-bold text-foreground">{stats.outOfStockItems}</p>
              </div>
              <div className="p-3 bg-error-100 text-error-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">库存总价值</p>
                <p className="text-2xl font-bold text-foreground">{formatPrice(stats.totalValue)}</p>
              </div>
              <div className="p-3 bg-success-100 text-success-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                type="search"
                placeholder="搜索SKU、商品名称..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />
            </div>
            <select
              value={selectedWarehouse}
              onChange={(e) => setSelectedWarehouse(e.target.value)}
              className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="">所有仓库</option>
              {warehouses.map((warehouse) => (
                <option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name} ({warehouse.code})
                </option>
              ))}
            </select>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="">所有状态</option>
              <option value="lowStock">库存不足</option>
              <option value="outOfStock">缺货</option>
            </select>
            <Button type="submit">搜索</Button>
          </form>
        </CardContent>
      </Card>

      {/* 库存列表 */}
      <Card>
        <CardHeader>
          <CardTitle>库存列表</CardTitle>
          <CardDescription>
            共 {total} 个库存项目，当前显示第 {currentPage} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="loading-spinner w-8 h-8"></div>
              <span className="ml-2 text-muted-foreground">加载中...</span>
            </div>
          ) : inventoryItems.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2z" />
                </svg>
              </div>
              <h3 className="empty-state-title">暂无库存数据</h3>
              <p className="empty-state-description">
                还没有任何库存记录，请先添加商品和库存。
              </p>
              <Button>添加库存</Button>
            </div>
          ) : (
            <div className="space-y-4">
              {inventoryItems.map((item) => {
                const stockStatus = getStockStatus(item.availableQuantity, item.quantity);
                
                return (
                  <div
                    key={item.id}
                    className="flex items-center space-x-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    {/* 库存信息 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-foreground">
                          {item.product?.name || item.sku}
                        </h3>
                        <span className={cn('px-2 py-1 text-xs font-medium rounded-full', stockStatus.className)}>
                          {stockStatus.label}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        <span>SKU: {item.sku}</span>
                        <span>仓库: {item.warehouse.name}</span>
                        <span>总库存: {item.quantity}</span>
                        <span>可用: {item.availableQuantity}</span>
                        <span>预留: {item.reservedQuantity}</span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                        {item.costPerUnit && (
                          <span>成本: {formatPrice(item.costPerUnit)}</span>
                        )}
                        <span>更新时间: {new Date(item.lastUpdated).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center space-x-2">
                      <Can do="adjust" on="inventory">
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4 mr-1" />
                          调整
                        </Button>
                      </Can>
                      <Can do="transfer" on="inventory">
                        <Button variant="outline" size="sm">
                          <Package className="w-4 h-4 mr-1" />
                          转移
                        </Button>
                      </Can>
                      <Can do="manage" on="inventory">
                        <Button variant="outline" size="sm">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </Can>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                显示第 {(currentPage - 1) * 20 + 1} - {Math.min(currentPage * 20, total)} 条，共 {total} 条
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </PermissionGuard>
  );
}
