/**
 * 客户管理页面组件
 * 显示客户列表和管理功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PermissionGuard, Can } from '@/components/auth/PermissionGuard';
import { usePermissions } from '@/hooks/usePermissions';
import { cn } from '@/lib/utils';
import {
  Search,
  Plus,
  Edit,
  Eye,
  MoreHorizontal,
  Users,
  AlertTriangle,
  CheckCircle,
  Clock,
  Download,
  RefreshCw,
  Mail,
  Phone,
  MapPin,
} from 'lucide-react';

// 客户数据类型
interface Customer {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  languageCode: string;
  currencyCode: string;
  customerGroup: string;
  notes?: string;
  tags: string[];
  marketingConsent: boolean;
  createdAt: string;
  updatedAt: string;
  totalOrders: number;
  totalSpent: number;
  lastOrderDate?: string;
  addresses: Array<{
    id: string;
    type: string;
    addressLine1: string;
    city: string;
    countryCode: string;
    isDefault: boolean;
  }>;
}

// 客户管理页面组件
export default function CustomersPage() {
  const { hasPermission } = usePermissions();

  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [groupFilter, setGroupFilter] = useState('');
  const [countryFilter, setCountryFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);

  // 模拟客户数据
  useEffect(() => {
    const mockCustomers: Customer[] = [
      {
        id: '1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        phone: '******-0123',
        languageCode: 'en-US',
        currencyCode: 'USD',
        customerGroup: 'VIP',
        marketingConsent: true,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-20T14:45:00Z',
        totalOrders: 15,
        totalSpent: 2450.00,
        lastOrderDate: '2024-01-18T09:15:00Z',
        tags: ['VIP', '回头客'],
        addresses: [
          {
            id: 'addr1',
            type: 'SHIPPING',
            addressLine1: '123 Main St',
            city: 'New York',
            countryCode: 'US',
            isDefault: true,
          }
        ],
      },
      {
        id: '2',
        email: '<EMAIL>',
        firstName: '张',
        lastName: '伟',
        phone: '+86-138-0013-8000',
        languageCode: 'zh-CN',
        currencyCode: 'CNY',
        customerGroup: '普通客户',
        marketingConsent: false,
        createdAt: '2024-01-10T08:20:00Z',
        updatedAt: '2024-01-15T16:30:00Z',
        totalOrders: 3,
        totalSpent: 580.00,
        lastOrderDate: '2024-01-12T11:45:00Z',
        tags: ['新客户'],
        addresses: [
          {
            id: 'addr2',
            type: 'SHIPPING',
            addressLine1: '北京市朝阳区建国路1号',
            city: '北京',
            countryCode: 'CN',
            isDefault: true,
          }
        ],
      },
      {
        id: '3',
        email: '<EMAIL>',
        firstName: 'Maria',
        lastName: 'Garcia',
        phone: '+34-600-123-456',
        languageCode: 'es-ES',
        currencyCode: 'EUR',
        customerGroup: '普通客户',
        marketingConsent: true,
        createdAt: '2024-01-05T12:15:00Z',
        updatedAt: '2024-01-08T10:20:00Z',
        totalOrders: 7,
        totalSpent: 1200.00,
        lastOrderDate: '2024-01-07T15:30:00Z',
        tags: ['欧洲客户'],
        addresses: [
          {
            id: 'addr3',
            type: 'SHIPPING',
            addressLine1: 'Calle Mayor 123',
            city: 'Madrid',
            countryCode: 'ES',
            isDefault: true,
          }
        ],
      },
    ];

    // 模拟API调用延迟
    setTimeout(() => {
      setCustomers(mockCustomers);
      setTotal(mockCustomers.length);
      setTotalPages(1);
      setLoading(false);
    }, 1000);
  }, []);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: 实现真实的搜索功能
    console.log('搜索客户:', searchQuery);
  };

  // 获取客户组显示样式
  const getGroupBadge = (group: string) => {
    const groupConfig = {
      'VIP': { label: 'VIP', className: 'bg-purple-100 text-purple-800' },
      '普通客户': { label: '普通', className: 'bg-blue-100 text-blue-800' },
      '新客户': { label: '新客户', className: 'bg-green-100 text-green-800' },
      '流失客户': { label: '流失', className: 'bg-gray-100 text-gray-800' },
    };

    const config = groupConfig[group as keyof typeof groupConfig] || {
      label: group,
      className: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={cn('px-2 py-1 text-xs font-medium rounded-full', config.className)}>
        {config.label}
      </span>
    );
  };

  // 格式化价格
  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency === 'USD' ? 'USD' : currency === 'EUR' ? 'EUR' : 'CNY',
    }).format(price);
  };

  // 格式化客户名称
  const formatCustomerName = (customer: Customer) => {
    if (customer.firstName || customer.lastName) {
      return `${customer.firstName || ''} ${customer.lastName || ''}`.trim();
    }
    return customer.email;
  };

  // 获取国家名称
  const getCountryName = (countryCode: string) => {
    const countries = {
      'US': '美国',
      'CN': '中国',
      'ES': '西班牙',
      'GB': '英国',
      'DE': '德国',
      'FR': '法国',
      'JP': '日本',
      'KR': '韩国',
    };
    return countries[countryCode as keyof typeof countries] || countryCode;
  };

  // 计算统计数据
  const stats = {
    totalCustomers: total,
    vipCustomers: customers.filter(c => c.customerGroup === 'VIP').length,
    newCustomers: customers.filter(c => {
      const createdDate = new Date(c.createdAt);
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return createdDate > thirtyDaysAgo;
    }).length,
    totalRevenue: customers.reduce((sum, c) => sum + c.totalSpent, 0),
  };

  return (
    <PermissionGuard permission={{ resource: 'customers', action: 'read' }}>
      <div className="space-y-6 p-6">
        {/* 页面标题和操作 */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">客户管理</h1>
            <p className="text-gray-600 mt-1">管理您的客户信息、订单历史和客户关系</p>
          </div>
          <div className="flex space-x-2">
            <Can do="import" on="customers">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导入客户
              </Button>
            </Can>
            <Can do="export" on="customers">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                导出数据
              </Button>
            </Can>
            <Can do="create" on="customers">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加客户
              </Button>
            </Can>
          </div>
        </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总客户数</p>
                <p className="text-2xl font-bold text-foreground">{stats.totalCustomers}</p>
              </div>
              <div className="p-3 bg-brand-100 text-brand-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">VIP客户</p>
                <p className="text-2xl font-bold text-foreground">{stats.vipCustomers}</p>
              </div>
              <div className="p-3 bg-purple-100 text-purple-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">新客户(30天)</p>
                <p className="text-2xl font-bold text-foreground">{stats.newCustomers}</p>
              </div>
              <div className="p-3 bg-success-100 text-success-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总收入</p>
                <p className="text-2xl font-bold text-foreground">{formatPrice(stats.totalRevenue, 'CNY')}</p>
              </div>
              <div className="p-3 bg-green-100 text-green-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="flex items-center space-x-4">
            <div className="flex-1">
              <Input
                type="search"
                placeholder="搜索客户邮箱、姓名、电话..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />
            </div>
            <select
              value={groupFilter}
              onChange={(e) => setGroupFilter(e.target.value)}
              className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="">所有客户组</option>
              <option value="VIP">VIP客户</option>
              <option value="普通客户">普通客户</option>
              <option value="新客户">新客户</option>
              <option value="流失客户">流失客户</option>
            </select>
            <select
              value={countryFilter}
              onChange={(e) => setCountryFilter(e.target.value)}
              className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
            >
              <option value="">所有国家</option>
              <option value="US">美国</option>
              <option value="CN">中国</option>
              <option value="ES">西班牙</option>
              <option value="GB">英国</option>
              <option value="DE">德国</option>
            </select>
            <Button type="submit">搜索</Button>
          </form>
        </CardContent>
      </Card>

      {/* 客户列表 */}
      <Card>
        <CardHeader>
          <CardTitle>客户列表</CardTitle>
          <CardDescription>
            共 {total} 个客户，当前显示第 {currentPage} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="loading-spinner w-8 h-8"></div>
              <span className="ml-2 text-muted-foreground">加载中...</span>
            </div>
          ) : customers.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="empty-state-title">暂无客户</h3>
              <p className="empty-state-description">
                还没有任何客户，开始添加您的第一个客户吧。
              </p>
              <Button>添加客户</Button>
            </div>
          ) : (
            <div className="space-y-4">
              {customers.map((customer) => (
                <div
                  key={customer.id}
                  className="flex items-center space-x-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  {/* 客户头像 */}
                  <div className="w-12 h-12 bg-brand-100 text-brand-600 rounded-full flex items-center justify-center">
                    <span className="font-medium text-lg">
                      {formatCustomerName(customer).charAt(0).toUpperCase()}
                    </span>
                  </div>

                  {/* 客户信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-foreground">
                        {formatCustomerName(customer)}
                      </h3>
                      <div className="flex items-center space-x-2">
                        {getGroupBadge(customer.customerGroup)}
                        {customer.tags.map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>邮箱: {customer.email}</span>
                      {customer.phone && <span>电话: {customer.phone}</span>}
                      <span>订单: {customer.totalOrders}笔</span>
                      <span>消费: {formatPrice(customer.totalSpent, customer.currencyCode)}</span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <span>注册时间: {new Date(customer.createdAt).toLocaleDateString()}</span>
                      {customer.lastOrderDate && (
                        <span>最后下单: {new Date(customer.lastOrderDate).toLocaleDateString()}</span>
                      )}
                      {customer.addresses.length > 0 && (
                        <span>地址: {getCountryName(customer.addresses[0].countryCode)}</span>
                      )}
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    <Can do="read" on="customers">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4 mr-1" />
                        查看
                      </Button>
                    </Can>
                    <Can do="update" on="customers">
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-1" />
                        编辑
                      </Button>
                    </Can>
                    <Can do="manage" on="customers">
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </Can>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      </div>
    </PermissionGuard>
  );
}
