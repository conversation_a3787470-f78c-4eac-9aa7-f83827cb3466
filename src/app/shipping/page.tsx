/**
 * 物流管理页面组件
 * 显示发货和物流跟踪功能
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

// 发货数据类型
interface Shipment {
  id: string;
  orderId: string;
  carrierId: string;
  trackingNumber?: string;
  serviceType?: string;
  status: string;
  shippingCost?: number;
  estimatedDelivery?: string;
  actualDelivery?: string;
  createdAt: string;
  order: {
    id: string;
    orderNumber: string;
    totalAmount: number;
    customer?: {
      id: string;
      email: string;
      firstName?: string;
      lastName?: string;
    };
  };
  carrier: {
    id: string;
    name: string;
    code: string;
    trackingUrl?: string;
  };
  trackingEvents: Array<{
    id: string;
    status: string;
    description: string;
    location?: string;
    timestamp: string;
  }>;
}

// 物流管理页面组件
export default function ShippingPage() {
  const [shipments, setShipments] = useState<Shipment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [carrierFilter, setCarrierFilter] = useState('');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);

  // 获取发货列表
  const fetchShipments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        ...(searchQuery && { search: searchQuery }),
        ...(statusFilter && { status: statusFilter }),
        ...(carrierFilter && { carrierId: carrierFilter }),
        ...(dateFrom && { dateFrom }),
        ...(dateTo && { dateTo }),
      });

      const response = await fetch(`/api/shipments?${params}`);
      const result = await response.json();

      if (result.success) {
        setShipments(result.data.data);
        setTotalPages(result.data.pagination.totalPages);
        setTotal(result.data.pagination.total);
      } else {
        console.error('获取发货列表失败:', result.error);
      }
    } catch (error) {
      console.error('获取发货列表错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 页面加载时获取数据
  useEffect(() => {
    fetchShipments();
  }, [currentPage, searchQuery, statusFilter, carrierFilter, dateFrom, dateTo]);

  // 处理搜索
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchShipments();
  };

  // 获取发货状态显示样式
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { label: '待发货', className: 'bg-warning-100 text-warning-800' },
      PICKED_UP: { label: '已取件', className: 'bg-blue-100 text-blue-800' },
      IN_TRANSIT: { label: '运输中', className: 'bg-purple-100 text-purple-800' },
      OUT_FOR_DELIVERY: { label: '派送中', className: 'bg-orange-100 text-orange-800' },
      DELIVERED: { label: '已送达', className: 'bg-success-100 text-success-800' },
      FAILED: { label: '派送失败', className: 'bg-error-100 text-error-800' },
      RETURNED: { label: '已退回', className: 'bg-gray-100 text-gray-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      className: 'bg-gray-100 text-gray-800',
    };

    return (
      <span className={cn('px-2 py-1 text-xs font-medium rounded-full', config.className)}>
        {config.label}
      </span>
    );
  };

  // 格式化价格
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
    }).format(price);
  };

  // 格式化客户名称
  const formatCustomerName = (customer?: Shipment['order']['customer']) => {
    if (!customer) return '游客';
    if (customer.firstName || customer.lastName) {
      return `${customer.firstName || ''} ${customer.lastName || ''}`.trim();
    }
    return customer.email;
  };

  // 计算统计数据
  const stats = {
    totalShipments: total,
    pendingShipments: shipments.filter(s => s.status === 'PENDING').length,
    inTransitShipments: shipments.filter(s => s.status === 'IN_TRANSIT').length,
    deliveredShipments: shipments.filter(s => s.status === 'DELIVERED').length,
  };

  return (
    <div className="page-container">
      {/* 页面标题 */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="page-title">物流管理</h1>
          <p className="page-subtitle">
            管理发货、物流跟踪和配送状态
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            批量发货
          </Button>
          <Button>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            创建发货
          </Button>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">总发货数</p>
                <p className="text-2xl font-bold text-foreground">{stats.totalShipments}</p>
              </div>
              <div className="p-3 bg-brand-100 text-brand-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">待发货</p>
                <p className="text-2xl font-bold text-foreground">{stats.pendingShipments}</p>
              </div>
              <div className="p-3 bg-warning-100 text-warning-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">运输中</p>
                <p className="text-2xl font-bold text-foreground">{stats.inTransitShipments}</p>
              </div>
              <div className="p-3 bg-purple-100 text-purple-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">已送达</p>
                <p className="text-2xl font-bold text-foreground">{stats.deliveredShipments}</p>
              </div>
              <div className="p-3 bg-success-100 text-success-600 rounded-full">
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="p-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <Input
                  type="search"
                  placeholder="搜索快递单号、订单号、客户邮箱..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  leftIcon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  }
                />
              </div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
              >
                <option value="">所有状态</option>
                <option value="PENDING">待发货</option>
                <option value="PICKED_UP">已取件</option>
                <option value="IN_TRANSIT">运输中</option>
                <option value="OUT_FOR_DELIVERY">派送中</option>
                <option value="DELIVERED">已送达</option>
                <option value="FAILED">派送失败</option>
                <option value="RETURNED">已退回</option>
              </select>
              <select
                value={carrierFilter}
                onChange={(e) => setCarrierFilter(e.target.value)}
                className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
              >
                <option value="">所有承运商</option>
                {/* TODO: 动态加载承运商选项 */}
                <option value="sf">顺丰速运</option>
                <option value="ems">中国邮政</option>
                <option value="yt">圆通速递</option>
                <option value="sto">申通快递</option>
                <option value="zto">中通快递</option>
              </select>
              <Button type="submit">搜索</Button>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-foreground">开始日期:</label>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
                />
              </div>
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-foreground">结束日期:</label>
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="px-3 py-2 border border-border rounded-md bg-background text-foreground"
                />
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* 发货列表 */}
      <Card>
        <CardHeader>
          <CardTitle>发货记录</CardTitle>
          <CardDescription>
            共 {total} 条发货记录，当前显示第 {currentPage} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="loading-spinner w-8 h-8"></div>
              <span className="ml-2 text-muted-foreground">加载中...</span>
            </div>
          ) : shipments.length === 0 ? (
            <div className="empty-state">
              <div className="empty-state-icon">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                </svg>
              </div>
              <h3 className="empty-state-title">暂无发货记录</h3>
              <p className="empty-state-description">
                还没有任何发货记录，开始处理订单发货吧。
              </p>
              <Button>创建发货</Button>
            </div>
          ) : (
            <div className="space-y-4">
              {shipments.map((shipment) => (
                <div
                  key={shipment.id}
                  className="flex items-center space-x-4 p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  {/* 发货信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium text-foreground">
                        订单 #{shipment.order.orderNumber}
                      </h3>
                      {getStatusBadge(shipment.status)}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>客户: {formatCustomerName(shipment.order.customer)}</span>
                      <span>承运商: {shipment.carrier.name}</span>
                      {shipment.trackingNumber && (
                        <span>快递单号: {shipment.trackingNumber}</span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                      <span>发货时间: {new Date(shipment.createdAt).toLocaleString()}</span>
                      {shipment.estimatedDelivery && (
                        <span>预计送达: {new Date(shipment.estimatedDelivery).toLocaleDateString()}</span>
                      )}
                      {shipment.shippingCost && (
                        <span>运费: {formatPrice(shipment.shippingCost)}</span>
                      )}
                    </div>
                    {shipment.trackingEvents.length > 0 && (
                      <div className="mt-2 text-sm text-muted-foreground">
                        最新状态: {shipment.trackingEvents[0].description}
                        {shipment.trackingEvents[0].location && (
                          <span> - {shipment.trackingEvents[0].location}</span>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      跟踪
                    </Button>
                    {shipment.trackingNumber && shipment.carrier.trackingUrl && (
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={shipment.carrier.trackingUrl.replace('{trackingNumber}', shipment.trackingNumber)}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          官网查询
                        </a>
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                      </svg>
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-muted-foreground">
                显示第 {(currentPage - 1) * 20 + 1} - {Math.min(currentPage * 20, total)} 条，共 {total} 条
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  上一页
                </Button>
                <span className="text-sm text-muted-foreground">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
