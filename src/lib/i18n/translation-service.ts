/**
 * 翻译管理服务
 * 提供翻译文本获取、语言包管理、翻译缓存、动态加载等功能
 */

import { 
  SupportedLanguage, 
  TranslationNamespace, 
  TranslationKey, 
  TranslationParams, 
  TranslationOptions,
  TranslationLoadingState,
  TranslationStats,
  DEFAULT_LANGUAGE,
  FALLBACK_LANGUAGE,
} from './config';
import { prisma } from '@/lib/prisma';

// 翻译数据接口
export interface TranslationData {
  [key: string]: string | TranslationData;
}

// 翻译条目接口
export interface TranslationEntry {
  id: string;
  namespace: TranslationNamespace;
  key: string;
  language: SupportedLanguage;
  value: string;
  description?: string;
  context?: string;
  pluralForm?: string;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 翻译缓存接口
interface TranslationCache {
  [language: string]: {
    [namespace: string]: TranslationData;
  };
}

/**
 * 翻译服务类
 */
export class TranslationService {
  private cache: TranslationCache = {};
  private loadingStates: Map<string, TranslationLoadingState> = new Map();
  private missingKeys: Set<string> = new Set();

  /**
   * 获取翻译文本
   */
  async translate(
    key: TranslationKey,
    language: SupportedLanguage = DEFAULT_LANGUAGE,
    options: TranslationOptions = {}
  ): Promise<string> {
    const {
      namespace = TranslationNamespace.COMMON,
      fallback,
      params = {},
      count,
      context,
    } = options;

    try {
      // 确保翻译数据已加载
      await this.ensureTranslationsLoaded(language, namespace);

      // 获取翻译值
      let translation = this.getTranslationFromCache(language, namespace, key);

      // 如果没有找到翻译，尝试使用回退语言
      if (!translation && language !== FALLBACK_LANGUAGE) {
        await this.ensureTranslationsLoaded(FALLBACK_LANGUAGE, namespace);
        translation = this.getTranslationFromCache(FALLBACK_LANGUAGE, namespace, key);
      }

      // 如果仍然没有找到，使用提供的回退文本或键名
      if (!translation) {
        translation = fallback || key;
        this.recordMissingKey(language, namespace, key);
      }

      // 处理复数形式
      if (count !== undefined) {
        translation = this.handlePlural(translation, count, language);
      }

      // 处理参数替换
      if (Object.keys(params).length > 0) {
        translation = this.interpolateParams(translation, params);
      }

      return translation;
    } catch (error) {
      console.error('翻译获取失败:', error);
      return fallback || key;
    }
  }

  /**
   * 批量获取翻译
   */
  async translateBatch(
    keys: TranslationKey[],
    language: SupportedLanguage = DEFAULT_LANGUAGE,
    namespace: TranslationNamespace = TranslationNamespace.COMMON
  ): Promise<Record<string, string>> {
    const translations: Record<string, string> = {};

    await Promise.all(
      keys.map(async (key) => {
        translations[key] = await this.translate(key, language, { namespace });
      })
    );

    return translations;
  }

  /**
   * 获取命名空间的所有翻译
   */
  async getNamespaceTranslations(
    namespace: TranslationNamespace,
    language: SupportedLanguage = DEFAULT_LANGUAGE
  ): Promise<TranslationData> {
    await this.ensureTranslationsLoaded(language, namespace);
    return this.cache[language]?.[namespace] || {};
  }

  /**
   * 添加或更新翻译
   */
  async setTranslation(
    key: TranslationKey,
    value: string,
    language: SupportedLanguage,
    namespace: TranslationNamespace = TranslationNamespace.COMMON,
    options: {
      description?: string;
      context?: string;
      pluralForm?: string;
      userId?: string;
    } = {}
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { description, context, pluralForm, userId = 'system' } = options;

      // 检查翻译是否已存在
      const existingTranslation = await prisma.translation.findFirst({
        where: {
          namespace,
          key,
          language,
        },
      });

      if (existingTranslation) {
        // 更新现有翻译
        await prisma.translation.update({
          where: { id: existingTranslation.id },
          data: {
            value,
            description,
            context,
            pluralForm,
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });
      } else {
        // 创建新翻译
        await prisma.translation.create({
          data: {
            namespace,
            key,
            language,
            value,
            description,
            context,
            pluralForm,
            isApproved: false,
            createdBy: userId,
            updatedBy: userId,
          },
        });
      }

      // 更新缓存
      this.updateCache(language, namespace, key, value);

      return { success: true };
    } catch (error) {
      console.error('设置翻译失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '设置翻译失败',
      };
    }
  }

  /**
   * 批量设置翻译
   */
  async setTranslationsBatch(
    translations: Array<{
      key: TranslationKey;
      value: string;
      language: SupportedLanguage;
      namespace?: TranslationNamespace;
      description?: string;
      context?: string;
    }>,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await prisma.$transaction(async (tx) => {
        for (const translation of translations) {
          const {
            key,
            value,
            language,
            namespace = TranslationNamespace.COMMON,
            description,
            context,
          } = translation;

          // 检查是否已存在
          const existing = await tx.translation.findFirst({
            where: { namespace, key, language },
          });

          if (existing) {
            await tx.translation.update({
              where: { id: existing.id },
              data: {
                value,
                description,
                context,
                updatedBy: userId,
                updatedAt: new Date(),
              },
            });
          } else {
            await tx.translation.create({
              data: {
                namespace,
                key,
                language,
                value,
                description,
                context,
                isApproved: false,
                createdBy: userId,
                updatedBy: userId,
              },
            });
          }

          // 更新缓存
          this.updateCache(language, namespace, key, value);
        }
      });

      return { success: true };
    } catch (error) {
      console.error('批量设置翻译失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量设置翻译失败',
      };
    }
  }

  /**
   * 删除翻译
   */
  async deleteTranslation(
    key: TranslationKey,
    language: SupportedLanguage,
    namespace: TranslationNamespace = TranslationNamespace.COMMON
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await prisma.translation.deleteMany({
        where: {
          namespace,
          key,
          language,
        },
      });

      // 从缓存中删除
      this.removeFromCache(language, namespace, key);

      return { success: true };
    } catch (error) {
      console.error('删除翻译失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除翻译失败',
      };
    }
  }

  /**
   * 获取翻译统计信息
   */
  async getTranslationStats(
    language: SupportedLanguage,
    namespace?: TranslationNamespace
  ): Promise<TranslationStats> {
    try {
      const whereConditions: any = { language };
      if (namespace) {
        whereConditions.namespace = namespace;
      }

      const totalKeys = await prisma.translation.count({
        where: whereConditions,
      });

      const translatedKeys = await prisma.translation.count({
        where: {
          ...whereConditions,
          value: {
            not: '',
          },
        },
      });

      const missingKeys = totalKeys - translatedKeys;
      const completeness = totalKeys > 0 ? (translatedKeys / totalKeys) * 100 : 0;

      const lastUpdated = await prisma.translation.findFirst({
        where: whereConditions,
        orderBy: { updatedAt: 'desc' },
        select: { updatedAt: true },
      });

      return {
        totalKeys,
        translatedKeys,
        missingKeys,
        completeness,
        lastUpdated: lastUpdated?.updatedAt || new Date(),
      };
    } catch (error) {
      console.error('获取翻译统计失败:', error);
      return {
        totalKeys: 0,
        translatedKeys: 0,
        missingKeys: 0,
        completeness: 0,
        lastUpdated: new Date(),
      };
    }
  }

  /**
   * 搜索翻译
   */
  async searchTranslations(
    query: string,
    options: {
      language?: SupportedLanguage;
      namespace?: TranslationNamespace;
      searchInKeys?: boolean;
      searchInValues?: boolean;
      limit?: number;
    } = {}
  ): Promise<TranslationEntry[]> {
    try {
      const {
        language,
        namespace,
        searchInKeys = true,
        searchInValues = true,
        limit = 100,
      } = options;

      const whereConditions: any = {};

      if (language) {
        whereConditions.language = language;
      }

      if (namespace) {
        whereConditions.namespace = namespace;
      }

      // 构建搜索条件
      const searchConditions = [];
      if (searchInKeys) {
        searchConditions.push({
          key: {
            contains: query,
            mode: 'insensitive',
          },
        });
      }

      if (searchInValues) {
        searchConditions.push({
          value: {
            contains: query,
            mode: 'insensitive',
          },
        });
      }

      if (searchConditions.length > 0) {
        whereConditions.OR = searchConditions;
      }

      const translations = await prisma.translation.findMany({
        where: whereConditions,
        take: limit,
        orderBy: { updatedAt: 'desc' },
      });

      return translations;
    } catch (error) {
      console.error('搜索翻译失败:', error);
      return [];
    }
  }

  /**
   * 清除缓存
   */
  clearCache(language?: SupportedLanguage, namespace?: TranslationNamespace): void {
    if (language && namespace) {
      // 清除特定语言和命名空间的缓存
      if (this.cache[language]) {
        delete this.cache[language][namespace];
      }
    } else if (language) {
      // 清除特定语言的所有缓存
      delete this.cache[language];
    } else {
      // 清除所有缓存
      this.cache = {};
    }

    // 清除加载状态
    if (language && namespace) {
      this.loadingStates.delete(`${language}:${namespace}`);
    } else if (language) {
      for (const key of this.loadingStates.keys()) {
        if (key.startsWith(`${language}:`)) {
          this.loadingStates.delete(key);
        }
      }
    } else {
      this.loadingStates.clear();
    }
  }

  /**
   * 预加载翻译
   */
  async preloadTranslations(
    languages: SupportedLanguage[],
    namespaces: TranslationNamespace[]
  ): Promise<void> {
    const loadPromises = [];

    for (const language of languages) {
      for (const namespace of namespaces) {
        loadPromises.push(this.ensureTranslationsLoaded(language, namespace));
      }
    }

    await Promise.all(loadPromises);
  }

  /**
   * 获取缺失的翻译键
   */
  getMissingKeys(): string[] {
    return Array.from(this.missingKeys);
  }

  /**
   * 清除缺失键记录
   */
  clearMissingKeys(): void {
    this.missingKeys.clear();
  }

  /**
   * 确保翻译数据已加载
   */
  private async ensureTranslationsLoaded(
    language: SupportedLanguage,
    namespace: TranslationNamespace
  ): Promise<void> {
    const cacheKey = `${language}:${namespace}`;

    // 检查是否已在缓存中
    if (this.cache[language]?.[namespace]) {
      return;
    }

    // 检查是否正在加载
    const loadingState = this.loadingStates.get(cacheKey);
    if (loadingState?.isLoading) {
      // 等待加载完成
      while (this.loadingStates.get(cacheKey)?.isLoading) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      return;
    }

    // 开始加载
    this.loadingStates.set(cacheKey, {
      isLoading: true,
      loadedNamespaces: new Set(),
      failedNamespaces: new Set(),
      lastLoadTime: null,
    });

    try {
      await this.loadTranslationsFromDatabase(language, namespace);

      // 更新加载状态
      const state = this.loadingStates.get(cacheKey);
      if (state) {
        state.isLoading = false;
        state.loadedNamespaces.add(namespace);
        state.lastLoadTime = new Date();
      }
    } catch (error) {
      console.error(`加载翻译失败 ${language}:${namespace}:`, error);

      // 更新失败状态
      const state = this.loadingStates.get(cacheKey);
      if (state) {
        state.isLoading = false;
        state.failedNamespaces.add(namespace);
      }
    }
  }

  /**
   * 从数据库加载翻译
   */
  private async loadTranslationsFromDatabase(
    language: SupportedLanguage,
    namespace: TranslationNamespace
  ): Promise<void> {
    const translations = await prisma.translation.findMany({
      where: {
        language,
        namespace,
        isApproved: true,
      },
      select: {
        key: true,
        value: true,
      },
    });

    // 构建翻译数据结构
    const translationData: TranslationData = {};
    
    for (const translation of translations) {
      this.setNestedValue(translationData, translation.key, translation.value);
    }

    // 更新缓存
    if (!this.cache[language]) {
      this.cache[language] = {};
    }
    this.cache[language][namespace] = translationData;
  }

  /**
   * 从缓存获取翻译
   */
  private getTranslationFromCache(
    language: SupportedLanguage,
    namespace: TranslationNamespace,
    key: TranslationKey
  ): string | null {
    const namespaceData = this.cache[language]?.[namespace];
    if (!namespaceData) return null;

    return this.getNestedValue(namespaceData, key);
  }

  /**
   * 更新缓存
   */
  private updateCache(
    language: SupportedLanguage,
    namespace: TranslationNamespace,
    key: TranslationKey,
    value: string
  ): void {
    if (!this.cache[language]) {
      this.cache[language] = {};
    }
    if (!this.cache[language][namespace]) {
      this.cache[language][namespace] = {};
    }

    this.setNestedValue(this.cache[language][namespace], key, value);
  }

  /**
   * 从缓存删除
   */
  private removeFromCache(
    language: SupportedLanguage,
    namespace: TranslationNamespace,
    key: TranslationKey
  ): void {
    const namespaceData = this.cache[language]?.[namespace];
    if (!namespaceData) return;

    this.deleteNestedValue(namespaceData, key);
  }

  /**
   * 设置嵌套值
   */
  private setNestedValue(obj: TranslationData, key: string, value: string): void {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object') {
        current[k] = {};
      }
      current = current[k] as TranslationData;
    }

    current[keys[keys.length - 1]] = value;
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: TranslationData, key: string): string | null {
    const keys = key.split('.');
    let current: any = obj;

    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }

    return typeof current === 'string' ? current : null;
  }

  /**
   * 删除嵌套值
   */
  private deleteNestedValue(obj: TranslationData, key: string): void {
    const keys = key.split('.');
    let current = obj;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!(k in current) || typeof current[k] !== 'object') {
        return;
      }
      current = current[k] as TranslationData;
    }

    delete current[keys[keys.length - 1]];
  }

  /**
   * 处理复数形式
   */
  private handlePlural(
    translation: string,
    count: number,
    language: SupportedLanguage
  ): string {
    // 简化的复数处理逻辑
    if (translation.includes('|')) {
      const forms = translation.split('|');
      
      // 中文没有复数形式
      if (language.startsWith('zh')) {
        return forms[0];
      }

      // 英语复数规则
      if (language.startsWith('en')) {
        return count === 1 ? forms[0] : (forms[1] || forms[0]);
      }

      // 其他语言的复数规则可以在这里添加
      return count === 1 ? forms[0] : (forms[1] || forms[0]);
    }

    return translation;
  }

  /**
   * 参数插值
   */
  private interpolateParams(translation: string, params: TranslationParams): string {
    return translation.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key]?.toString() || match;
    });
  }

  /**
   * 记录缺失的翻译键
   */
  private recordMissingKey(
    language: SupportedLanguage,
    namespace: TranslationNamespace,
    key: TranslationKey
  ): void {
    const missingKey = `${language}:${namespace}:${key}`;
    this.missingKeys.add(missingKey);

    // 在开发环境下输出警告
    if (process.env.NODE_ENV === 'development') {
      console.warn(`缺失翻译: ${missingKey}`);
    }
  }
}

// 导出单例实例
export const translationService = new TranslationService();
