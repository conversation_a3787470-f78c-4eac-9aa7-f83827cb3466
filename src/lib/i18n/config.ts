/**
 * 国际化配置文件
 * 定义支持的语言、默认语言、语言切换逻辑等
 */

// 支持的语言枚举
export enum SupportedLanguage {
  ZH_CN = 'zh-CN',  // 简体中文
  ZH_TW = 'zh-TW',  // 繁体中文
  EN_US = 'en-US',  // 美式英语
  EN_GB = 'en-GB',  // 英式英语
  ES_ES = 'es-ES',  // 西班牙语
  FR_FR = 'fr-FR',  // 法语
  DE_DE = 'de-DE',  // 德语
  IT_IT = 'it-IT',  // 意大利语
  PT_PT = 'pt-PT',  // 葡萄牙语
  RU_RU = 'ru-RU',  // 俄语
  JA_JP = 'ja-JP',  // 日语
  KO_KR = 'ko-KR',  // 韩语
  AR_SA = 'ar-SA',  // 阿拉伯语
  TH_TH = 'th-TH',  // 泰语
  VI_VN = 'vi-VN',  // 越南语
}

// 语言信息接口
export interface LanguageInfo {
  code: SupportedLanguage;
  name: string;
  nativeName: string;
  flag: string;
  direction: 'ltr' | 'rtl';
  region: string;
  isActive: boolean;
  completeness: number; // 翻译完成度百分比
}

// 支持的语言配置
export const SUPPORTED_LANGUAGES: Record<SupportedLanguage, LanguageInfo> = {
  [SupportedLanguage.ZH_CN]: {
    code: SupportedLanguage.ZH_CN,
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    direction: 'ltr',
    region: 'China',
    isActive: true,
    completeness: 100,
  },
  [SupportedLanguage.ZH_TW]: {
    code: SupportedLanguage.ZH_TW,
    name: 'Chinese (Traditional)',
    nativeName: '繁體中文',
    flag: '🇹🇼',
    direction: 'ltr',
    region: 'Taiwan',
    isActive: true,
    completeness: 95,
  },
  [SupportedLanguage.EN_US]: {
    code: SupportedLanguage.EN_US,
    name: 'English (US)',
    nativeName: 'English (US)',
    flag: '🇺🇸',
    direction: 'ltr',
    region: 'United States',
    isActive: true,
    completeness: 100,
  },
  [SupportedLanguage.EN_GB]: {
    code: SupportedLanguage.EN_GB,
    name: 'English (UK)',
    nativeName: 'English (UK)',
    flag: '🇬🇧',
    direction: 'ltr',
    region: 'United Kingdom',
    isActive: true,
    completeness: 98,
  },
  [SupportedLanguage.ES_ES]: {
    code: SupportedLanguage.ES_ES,
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    direction: 'ltr',
    region: 'Spain',
    isActive: true,
    completeness: 90,
  },
  [SupportedLanguage.FR_FR]: {
    code: SupportedLanguage.FR_FR,
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    direction: 'ltr',
    region: 'France',
    isActive: true,
    completeness: 88,
  },
  [SupportedLanguage.DE_DE]: {
    code: SupportedLanguage.DE_DE,
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    direction: 'ltr',
    region: 'Germany',
    isActive: true,
    completeness: 85,
  },
  [SupportedLanguage.IT_IT]: {
    code: SupportedLanguage.IT_IT,
    name: 'Italian',
    nativeName: 'Italiano',
    flag: '🇮🇹',
    direction: 'ltr',
    region: 'Italy',
    isActive: true,
    completeness: 82,
  },
  [SupportedLanguage.PT_PT]: {
    code: SupportedLanguage.PT_PT,
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇵🇹',
    direction: 'ltr',
    region: 'Portugal',
    isActive: true,
    completeness: 80,
  },
  [SupportedLanguage.RU_RU]: {
    code: SupportedLanguage.RU_RU,
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    direction: 'ltr',
    region: 'Russia',
    isActive: true,
    completeness: 75,
  },
  [SupportedLanguage.JA_JP]: {
    code: SupportedLanguage.JA_JP,
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    direction: 'ltr',
    region: 'Japan',
    isActive: true,
    completeness: 78,
  },
  [SupportedLanguage.KO_KR]: {
    code: SupportedLanguage.KO_KR,
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    direction: 'ltr',
    region: 'South Korea',
    isActive: true,
    completeness: 70,
  },
  [SupportedLanguage.AR_SA]: {
    code: SupportedLanguage.AR_SA,
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    direction: 'rtl',
    region: 'Saudi Arabia',
    isActive: true,
    completeness: 65,
  },
  [SupportedLanguage.TH_TH]: {
    code: SupportedLanguage.TH_TH,
    name: 'Thai',
    nativeName: 'ไทย',
    flag: '🇹🇭',
    direction: 'ltr',
    region: 'Thailand',
    isActive: true,
    completeness: 60,
  },
  [SupportedLanguage.VI_VN]: {
    code: SupportedLanguage.VI_VN,
    name: 'Vietnamese',
    nativeName: 'Tiếng Việt',
    flag: '🇻🇳',
    direction: 'ltr',
    region: 'Vietnam',
    isActive: true,
    completeness: 55,
  },
};

// 默认语言
export const DEFAULT_LANGUAGE = SupportedLanguage.ZH_CN;

// 回退语言（当翻译缺失时使用）
export const FALLBACK_LANGUAGE = SupportedLanguage.EN_US;

// 语言检测配置
export const LANGUAGE_DETECTION_CONFIG = {
  // 从浏览器语言检测
  detectFromBrowser: true,
  // 从URL参数检测
  detectFromPath: true,
  // 从Cookie检测
  detectFromCookie: true,
  // 从本地存储检测
  detectFromLocalStorage: true,
  // 从用户设置检测
  detectFromUserSettings: true,
  // 检测顺序
  order: [
    'userSettings',
    'cookie',
    'localStorage',
    'path',
    'browser',
  ],
};

// Cookie配置
export const LANGUAGE_COOKIE_CONFIG = {
  name: 'cbec-erp-language',
  maxAge: 365 * 24 * 60 * 60, // 1年
  httpOnly: false,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
};

// 本地存储配置
export const LANGUAGE_STORAGE_CONFIG = {
  key: 'cbec-erp-language',
  storageType: 'localStorage' as 'localStorage' | 'sessionStorage',
};

// 翻译命名空间
export enum TranslationNamespace {
  COMMON = 'common',           // 通用翻译
  AUTH = 'auth',               // 认证相关
  DASHBOARD = 'dashboard',     // 仪表板
  PRODUCTS = 'products',       // 商品管理
  ORDERS = 'orders',           // 订单管理
  INVENTORY = 'inventory',     // 库存管理
  CUSTOMERS = 'customers',     // 客户管理
  USERS = 'users',             // 用户管理
  SETTINGS = 'settings',       // 系统设置
  REPORTS = 'reports',         // 报表分析
  INTEGRATIONS = 'integrations', // 第三方集成
  NOTIFICATIONS = 'notifications', // 通知消息
  ERRORS = 'errors',           // 错误信息
  VALIDATION = 'validation',   // 表单验证
  EMAILS = 'emails',           // 邮件模板
}

// 翻译键的类型定义
export type TranslationKey = string;

// 翻译参数类型
export type TranslationParams = Record<string, string | number | boolean>;

// 翻译选项
export interface TranslationOptions {
  namespace?: TranslationNamespace;
  fallback?: string;
  params?: TranslationParams;
  count?: number; // 用于复数形式
  context?: string; // 用于上下文相关翻译
}

// 语言切换选项
export interface LanguageSwitchOptions {
  saveToStorage?: boolean;
  saveToCookie?: boolean;
  saveToUserSettings?: boolean;
  reloadPage?: boolean;
  updateUrl?: boolean;
}

// 翻译加载状态
export interface TranslationLoadingState {
  isLoading: boolean;
  loadedNamespaces: Set<TranslationNamespace>;
  failedNamespaces: Set<TranslationNamespace>;
  lastLoadTime: Date | null;
}

// 翻译统计信息
export interface TranslationStats {
  totalKeys: number;
  translatedKeys: number;
  missingKeys: number;
  completeness: number;
  lastUpdated: Date;
}

// 语言包元数据
export interface LanguagePackMeta {
  version: string;
  author: string;
  lastModified: Date;
  description: string;
  contributors: string[];
  stats: Record<TranslationNamespace, TranslationStats>;
}

// 翻译验证规则
export interface TranslationValidationRule {
  key: string;
  required: boolean;
  maxLength?: number;
  minLength?: number;
  pattern?: RegExp;
  allowHtml?: boolean;
  allowVariables?: boolean;
}

// 翻译导入/导出格式
export enum TranslationFormat {
  JSON = 'json',
  CSV = 'csv',
  XLSX = 'xlsx',
  PO = 'po',
  XLIFF = 'xliff',
}

// 翻译导入/导出选项
export interface TranslationImportExportOptions {
  format: TranslationFormat;
  namespace?: TranslationNamespace;
  languages?: SupportedLanguage[];
  includeMetadata?: boolean;
  validateOnImport?: boolean;
  overwriteExisting?: boolean;
}

// 获取活跃语言列表
export function getActiveLanguages(): LanguageInfo[] {
  return Object.values(SUPPORTED_LANGUAGES).filter(lang => lang.isActive);
}

// 获取语言信息
export function getLanguageInfo(code: SupportedLanguage): LanguageInfo | undefined {
  return SUPPORTED_LANGUAGES[code];
}

// 检查语言是否支持
export function isLanguageSupported(code: string): code is SupportedLanguage {
  return Object.values(SupportedLanguage).includes(code as SupportedLanguage);
}

// 获取语言方向
export function getLanguageDirection(code: SupportedLanguage): 'ltr' | 'rtl' {
  return SUPPORTED_LANGUAGES[code]?.direction || 'ltr';
}

// 检查是否为RTL语言
export function isRTLLanguage(code: SupportedLanguage): boolean {
  return getLanguageDirection(code) === 'rtl';
}

// 获取浏览器首选语言
export function getBrowserLanguage(): SupportedLanguage | null {
  if (typeof window === 'undefined') return null;

  const browserLang = navigator.language || navigator.languages?.[0];
  if (!browserLang) return null;

  // 精确匹配
  if (isLanguageSupported(browserLang)) {
    return browserLang as SupportedLanguage;
  }

  // 语言代码匹配（忽略地区）
  const langCode = browserLang.split('-')[0];
  const matchedLang = Object.values(SupportedLanguage).find(lang => 
    lang.startsWith(langCode)
  );

  return matchedLang || null;
}

// 格式化语言代码
export function formatLanguageCode(code: SupportedLanguage): string {
  return code.replace('_', '-');
}

// 解析语言代码
export function parseLanguageCode(code: string): SupportedLanguage | null {
  const normalizedCode = code.replace('-', '_');
  return isLanguageSupported(normalizedCode) ? normalizedCode as SupportedLanguage : null;
}

// 获取语言的区域设置
export function getLanguageLocale(code: SupportedLanguage): string {
  return formatLanguageCode(code);
}

// 获取语言的数字格式
export function getLanguageNumberFormat(code: SupportedLanguage): Intl.NumberFormat {
  return new Intl.NumberFormat(getLanguageLocale(code));
}

// 获取语言的日期格式
export function getLanguageDateFormat(code: SupportedLanguage): Intl.DateTimeFormat {
  return new Intl.DateTimeFormat(getLanguageLocale(code));
}

// 获取语言的货币格式
export function getLanguageCurrencyFormat(
  code: SupportedLanguage,
  currency: string
): Intl.NumberFormat {
  return new Intl.NumberFormat(getLanguageLocale(code), {
    style: 'currency',
    currency,
  });
}

// 获取语言的相对时间格式
export function getLanguageRelativeTimeFormat(code: SupportedLanguage): Intl.RelativeTimeFormat {
  return new Intl.RelativeTimeFormat(getLanguageLocale(code), {
    numeric: 'auto',
  });
}

// 获取语言的列表格式
export function getLanguageListFormat(code: SupportedLanguage): Intl.ListFormat {
  return new Intl.ListFormat(getLanguageLocale(code), {
    style: 'long',
    type: 'conjunction',
  });
}

// 获取语言的复数规则
export function getLanguagePluralRules(code: SupportedLanguage): Intl.PluralRules {
  return new Intl.PluralRules(getLanguageLocale(code));
}

// 获取语言的排序规则
export function getLanguageCollator(code: SupportedLanguage): Intl.Collator {
  return new Intl.Collator(getLanguageLocale(code), {
    sensitivity: 'base',
  });
}
