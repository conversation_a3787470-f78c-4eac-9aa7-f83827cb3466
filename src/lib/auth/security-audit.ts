/**
 * 安全和审计功能
 * 实现JWT令牌验证、会话管理集成、审计日志记录
 */

import { prisma } from '@/lib/prisma';
import jwt from 'jsonwebtoken';
import { IDPType } from './idp-config';

// JWT令牌载荷接口
export interface JWTPayload {
  sub: string; // 用户ID
  email: string;
  role: string;
  permissions: string[];
  idpId?: string;
  idpType?: IDPType;
  sessionId: string;
  iat: number;
  exp: number;
  iss: string;
  aud: string;
}

// 会话信息接口
export interface SessionInfo {
  id: string;
  userId: string;
  idpId?: string;
  ipAddress: string;
  userAgent: string;
  createdAt: Date;
  lastAccessAt: Date;
  expiresAt: Date;
  isActive: boolean;
  metadata?: Record<string, any>;
}

// 审计事件类型
export enum AuditEventType {
  // 认证事件
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  LOGOUT = 'LOGOUT',
  TOKEN_REFRESH = 'TOKEN_REFRESH',
  TOKEN_REVOKE = 'TOKEN_REVOKE',
  
  // 会话事件
  SESSION_CREATE = 'SESSION_CREATE',
  SESSION_EXPIRE = 'SESSION_EXPIRE',
  SESSION_TERMINATE = 'SESSION_TERMINATE',
  
  // IDP事件
  IDP_AUTH_START = 'IDP_AUTH_START',
  IDP_AUTH_SUCCESS = 'IDP_AUTH_SUCCESS',
  IDP_AUTH_FAILURE = 'IDP_AUTH_FAILURE',
  IDP_USER_SYNC = 'IDP_USER_SYNC',
  
  // 安全事件
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  MULTIPLE_FAILED_ATTEMPTS = 'MULTIPLE_FAILED_ATTEMPTS',
  UNUSUAL_LOCATION = 'UNUSUAL_LOCATION',
  CONCURRENT_SESSIONS = 'CONCURRENT_SESSIONS',
  
  // 配置事件
  IDP_CONFIG_CREATE = 'IDP_CONFIG_CREATE',
  IDP_CONFIG_UPDATE = 'IDP_CONFIG_UPDATE',
  IDP_CONFIG_DELETE = 'IDP_CONFIG_DELETE',
  IDP_CONFIG_TEST = 'IDP_CONFIG_TEST',
}

// 审计事件接口
export interface AuditEvent {
  id: string;
  eventType: AuditEventType;
  userId?: string;
  sessionId?: string;
  idpId?: string;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  details: Record<string, any>;
  timestamp: Date;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

// 安全配置接口
export interface SecurityConfig {
  jwtSecret: string;
  jwtExpiresIn: string;
  refreshTokenExpiresIn: string;
  maxConcurrentSessions: number;
  sessionTimeoutMinutes: number;
  maxFailedAttempts: number;
  lockoutDurationMinutes: number;
  enableGeoLocationCheck: boolean;
  enableDeviceFingerprintCheck: boolean;
  auditRetentionDays: number;
}

/**
 * JWT令牌管理器
 */
export class JWTManager {
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * 生成访问令牌
   */
  generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp' | 'iss' | 'aud'>): string {
    const now = Math.floor(Date.now() / 1000);
    
    const fullPayload: JWTPayload = {
      ...payload,
      iat: now,
      exp: now + this.parseExpiresIn(this.config.jwtExpiresIn),
      iss: 'cbec-erp',
      aud: 'cbec-erp-client',
    };

    return jwt.sign(fullPayload, this.config.jwtSecret, {
      algorithm: 'HS256',
    });
  }

  /**
   * 生成刷新令牌
   */
  generateRefreshToken(userId: string, sessionId: string): string {
    const now = Math.floor(Date.now() / 1000);
    
    const payload = {
      sub: userId,
      sessionId,
      type: 'refresh',
      iat: now,
      exp: now + this.parseExpiresIn(this.config.refreshTokenExpiresIn),
      iss: 'cbec-erp',
      aud: 'cbec-erp-client',
    };

    return jwt.sign(payload, this.config.jwtSecret, {
      algorithm: 'HS256',
    });
  }

  /**
   * 验证访问令牌
   */
  verifyAccessToken(token: string): JWTPayload | null {
    try {
      const payload = jwt.verify(token, this.config.jwtSecret, {
        algorithms: ['HS256'],
        issuer: 'cbec-erp',
        audience: 'cbec-erp-client',
      }) as JWTPayload;

      return payload;
    } catch (error) {
      console.error('JWT验证失败:', error);
      return null;
    }
  }

  /**
   * 验证刷新令牌
   */
  verifyRefreshToken(token: string): { userId: string; sessionId: string } | null {
    try {
      const payload = jwt.verify(token, this.config.jwtSecret, {
        algorithms: ['HS256'],
        issuer: 'cbec-erp',
        audience: 'cbec-erp-client',
      }) as any;

      if (payload.type !== 'refresh') {
        return null;
      }

      return {
        userId: payload.sub,
        sessionId: payload.sessionId,
      };
    } catch (error) {
      console.error('刷新令牌验证失败:', error);
      return null;
    }
  }

  /**
   * 解析过期时间字符串
   */
  private parseExpiresIn(expiresIn: string): number {
    const match = expiresIn.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new Error(`无效的过期时间格式: ${expiresIn}`);
    }

    const value = parseInt(match[1]);
    const unit = match[2];

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 3600;
      case 'd': return value * 86400;
      default: throw new Error(`不支持的时间单位: ${unit}`);
    }
  }
}

/**
 * 会话管理器
 */
export class SessionManager {
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * 创建新会话
   */
  async createSession(
    userId: string,
    idpId: string | undefined,
    ipAddress: string,
    userAgent: string,
    metadata?: Record<string, any>
  ): Promise<SessionInfo> {
    // 检查并清理过期会话
    await this.cleanupExpiredSessions();

    // 检查并发会话限制
    await this.enforceSessionLimits(userId);

    const sessionId = crypto.randomUUID();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.config.sessionTimeoutMinutes * 60 * 1000);

    const session = await prisma.userSession.create({
      data: {
        id: sessionId,
        userId,
        idpId,
        ipAddress,
        userAgent,
        createdAt: now,
        lastAccessAt: now,
        expiresAt,
        isActive: true,
        metadata: metadata || {},
      },
    });

    return {
      id: session.id,
      userId: session.userId,
      idpId: session.idpId || undefined,
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      createdAt: session.createdAt,
      lastAccessAt: session.lastAccessAt,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
      metadata: session.metadata as Record<string, any>,
    };
  }

  /**
   * 获取会话信息
   */
  async getSession(sessionId: string): Promise<SessionInfo | null> {
    const session = await prisma.userSession.findUnique({
      where: { id: sessionId },
    });

    if (!session || !session.isActive || session.expiresAt < new Date()) {
      return null;
    }

    return {
      id: session.id,
      userId: session.userId,
      idpId: session.idpId || undefined,
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      createdAt: session.createdAt,
      lastAccessAt: session.lastAccessAt,
      expiresAt: session.expiresAt,
      isActive: session.isActive,
      metadata: session.metadata as Record<string, any>,
    };
  }

  /**
   * 更新会话最后访问时间
   */
  async updateSessionAccess(sessionId: string): Promise<void> {
    await prisma.userSession.update({
      where: { id: sessionId },
      data: { lastAccessAt: new Date() },
    });
  }

  /**
   * 终止会话
   */
  async terminateSession(sessionId: string): Promise<void> {
    await prisma.userSession.update({
      where: { id: sessionId },
      data: { 
        isActive: false,
        terminatedAt: new Date(),
      },
    });
  }

  /**
   * 终止用户的所有会话
   */
  async terminateAllUserSessions(userId: string): Promise<void> {
    await prisma.userSession.updateMany({
      where: { 
        userId,
        isActive: true,
      },
      data: { 
        isActive: false,
        terminatedAt: new Date(),
      },
    });
  }

  /**
   * 清理过期会话
   */
  async cleanupExpiredSessions(): Promise<void> {
    await prisma.userSession.updateMany({
      where: {
        expiresAt: { lt: new Date() },
        isActive: true,
      },
      data: { 
        isActive: false,
        terminatedAt: new Date(),
      },
    });
  }

  /**
   * 强制执行会话限制
   */
  private async enforceSessionLimits(userId: string): Promise<void> {
    const activeSessions = await prisma.userSession.count({
      where: {
        userId,
        isActive: true,
        expiresAt: { gt: new Date() },
      },
    });

    if (activeSessions >= this.config.maxConcurrentSessions) {
      // 终止最旧的会话
      const oldestSessions = await prisma.userSession.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
        orderBy: { createdAt: 'asc' },
        take: activeSessions - this.config.maxConcurrentSessions + 1,
      });

      for (const session of oldestSessions) {
        await this.terminateSession(session.id);
      }
    }
  }
}

/**
 * 审计日志管理器
 */
export class AuditLogger {
  private config: SecurityConfig;

  constructor(config: SecurityConfig) {
    this.config = config;
  }

  /**
   * 记录审计事件
   */
  async logEvent(
    eventType: AuditEventType,
    ipAddress: string,
    userAgent: string,
    options: {
      userId?: string;
      sessionId?: string;
      idpId?: string;
      success?: boolean;
      details?: Record<string, any>;
      riskLevel?: AuditEvent['riskLevel'];
    } = {}
  ): Promise<void> {
    try {
      const auditEvent: Omit<AuditEvent, 'id'> = {
        eventType,
        userId: options.userId,
        sessionId: options.sessionId,
        idpId: options.idpId,
        ipAddress,
        userAgent,
        success: options.success ?? true,
        details: options.details || {},
        timestamp: new Date(),
        riskLevel: options.riskLevel || this.calculateRiskLevel(eventType, options),
      };

      await prisma.auditLog.create({
        data: {
          ...auditEvent,
          id: crypto.randomUUID(),
        },
      });

      // 检查是否需要触发安全警报
      await this.checkSecurityAlerts(auditEvent);
    } catch (error) {
      console.error('记录审计事件失败:', error);
    }
  }

  /**
   * 获取审计日志
   */
  async getAuditLogs(options: {
    userId?: string;
    eventType?: AuditEventType;
    startDate?: Date;
    endDate?: Date;
    riskLevel?: AuditEvent['riskLevel'];
    page?: number;
    limit?: number;
  } = {}): Promise<{ logs: AuditEvent[]; total: number }> {
    const where: any = {};

    if (options.userId) where.userId = options.userId;
    if (options.eventType) where.eventType = options.eventType;
    if (options.riskLevel) where.riskLevel = options.riskLevel;
    if (options.startDate || options.endDate) {
      where.timestamp = {};
      if (options.startDate) where.timestamp.gte = options.startDate;
      if (options.endDate) where.timestamp.lte = options.endDate;
    }

    const page = options.page || 1;
    const limit = options.limit || 50;
    const skip = (page - 1) * limit;

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        orderBy: { timestamp: 'desc' },
        skip,
        take: limit,
      }),
      prisma.auditLog.count({ where }),
    ]);

    return {
      logs: logs.map(log => ({
        id: log.id,
        eventType: log.eventType as AuditEventType,
        userId: log.userId || undefined,
        sessionId: log.sessionId || undefined,
        idpId: log.idpId || undefined,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        success: log.success,
        details: log.details as Record<string, any>,
        timestamp: log.timestamp,
        riskLevel: log.riskLevel as AuditEvent['riskLevel'],
      })),
      total,
    };
  }

  /**
   * 清理过期的审计日志
   */
  async cleanupOldLogs(): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - this.config.auditRetentionDays);

    await prisma.auditLog.deleteMany({
      where: {
        timestamp: { lt: cutoffDate },
      },
    });
  }

  /**
   * 计算风险级别
   */
  private calculateRiskLevel(
    eventType: AuditEventType,
    options: { success?: boolean; details?: Record<string, any> }
  ): AuditEvent['riskLevel'] {
    // 失败的认证事件
    if (!options.success && [
      AuditEventType.LOGIN_FAILURE,
      AuditEventType.IDP_AUTH_FAILURE,
    ].includes(eventType)) {
      return 'HIGH';
    }

    // 安全相关事件
    if ([
      AuditEventType.SUSPICIOUS_ACTIVITY,
      AuditEventType.MULTIPLE_FAILED_ATTEMPTS,
      AuditEventType.UNUSUAL_LOCATION,
    ].includes(eventType)) {
      return 'CRITICAL';
    }

    // 配置变更事件
    if ([
      AuditEventType.IDP_CONFIG_CREATE,
      AuditEventType.IDP_CONFIG_UPDATE,
      AuditEventType.IDP_CONFIG_DELETE,
    ].includes(eventType)) {
      return 'MEDIUM';
    }

    // 正常事件
    return 'LOW';
  }

  /**
   * 检查安全警报
   */
  private async checkSecurityAlerts(event: Omit<AuditEvent, 'id'>): Promise<void> {
    // 检查多次失败登录
    if (event.eventType === AuditEventType.LOGIN_FAILURE && event.userId) {
      await this.checkMultipleFailedAttempts(event.userId, event.ipAddress);
    }

    // 检查异常位置登录
    if (event.eventType === AuditEventType.LOGIN_SUCCESS && event.userId) {
      await this.checkUnusualLocation(event.userId, event.ipAddress);
    }

    // 检查并发会话
    if (event.eventType === AuditEventType.SESSION_CREATE && event.userId) {
      await this.checkConcurrentSessions(event.userId);
    }
  }

  /**
   * 检查多次失败登录尝试
   */
  private async checkMultipleFailedAttempts(userId: string, ipAddress: string): Promise<void> {
    const recentFailures = await prisma.auditLog.count({
      where: {
        userId,
        eventType: AuditEventType.LOGIN_FAILURE,
        timestamp: {
          gte: new Date(Date.now() - 15 * 60 * 1000), // 15分钟内
        },
      },
    });

    if (recentFailures >= this.config.maxFailedAttempts) {
      await this.logEvent(
        AuditEventType.MULTIPLE_FAILED_ATTEMPTS,
        ipAddress,
        '',
        {
          userId,
          riskLevel: 'CRITICAL',
          details: { failureCount: recentFailures },
        }
      );
    }
  }

  /**
   * 检查异常位置登录
   */
  private async checkUnusualLocation(userId: string, ipAddress: string): Promise<void> {
    // 获取用户最近的登录位置
    const recentLogins = await prisma.auditLog.findMany({
      where: {
        userId,
        eventType: AuditEventType.LOGIN_SUCCESS,
        timestamp: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天内
        },
      },
      orderBy: { timestamp: 'desc' },
      take: 10,
    });

    const knownIPs = recentLogins.map(log => log.ipAddress);
    
    if (!knownIPs.includes(ipAddress)) {
      await this.logEvent(
        AuditEventType.UNUSUAL_LOCATION,
        ipAddress,
        '',
        {
          userId,
          riskLevel: 'HIGH',
          details: { newIpAddress: ipAddress, knownIPs },
        }
      );
    }
  }

  /**
   * 检查并发会话
   */
  private async checkConcurrentSessions(userId: string): Promise<void> {
    const activeSessions = await prisma.userSession.count({
      where: {
        userId,
        isActive: true,
        expiresAt: { gt: new Date() },
      },
    });

    if (activeSessions > this.config.maxConcurrentSessions) {
      await this.logEvent(
        AuditEventType.CONCURRENT_SESSIONS,
        '',
        '',
        {
          userId,
          riskLevel: 'MEDIUM',
          details: { activeSessionCount: activeSessions },
        }
      );
    }
  }
}
