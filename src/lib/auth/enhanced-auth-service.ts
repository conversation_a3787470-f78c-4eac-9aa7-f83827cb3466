/**
 * 增强认证服务
 * 提供多因素认证、手机号验证、单点登录等高级认证功能
 */

import { prisma } from '@/lib/prisma';
import crypto from 'crypto';
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';

// 多因素认证类型
export enum MFAType {
  SMS = 'SMS',
  EMAIL = 'EMAIL',
  TOTP = 'TOTP',
  BACKUP_CODES = 'BACKUP_CODES',
}

// 验证码类型
export enum VerificationCodeType {
  PHONE_VERIFICATION = 'PHONE_VERIFICATION',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  PASSWORD_RESET = 'PASSWORD_RESET',
  LOGIN_VERIFICATION = 'LOGIN_VERIFICATION',
}

// MFA设置接口
export interface MFASettings {
  isEnabled: boolean;
  primaryMethod: MFAType;
  enabledMethods: MFAType[];
  backupCodes: string[];
  totpSecret?: string;
  phoneNumber?: string;
  emailAddress?: string;
}

// 验证码接口
export interface VerificationCode {
  id: string;
  userId: string;
  type: VerificationCodeType;
  code: string;
  target: string; // 手机号或邮箱
  expiresAt: Date;
  isUsed: boolean;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
}

/**
 * 增强认证服务类
 */
export class EnhancedAuthService {
  /**
   * 发送手机验证码
   */
  async sendPhoneVerificationCode(
    userId: string,
    phoneNumber: string,
    type: VerificationCodeType = VerificationCodeType.PHONE_VERIFICATION
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 生成6位数字验证码
      const code = Math.floor(100000 + Math.random() * 900000).toString();
      
      // 设置过期时间（5分钟）
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000);

      // 保存验证码到数据库
      await prisma.verificationCode.create({
        data: {
          userId,
          type,
          code,
          target: phoneNumber,
          expiresAt,
          isUsed: false,
          attempts: 0,
          maxAttempts: 3,
        },
      });

      // 发送短信（这里需要集成短信服务商）
      await this.sendSMS(phoneNumber, `您的验证码是：${code}，5分钟内有效。`);

      return { success: true };
    } catch (error) {
      console.error('发送手机验证码失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '发送验证码失败',
      };
    }
  }

  /**
   * 验证手机验证码
   */
  async verifyPhoneCode(
    userId: string,
    phoneNumber: string,
    code: string,
    type: VerificationCodeType = VerificationCodeType.PHONE_VERIFICATION
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 查找验证码
      const verificationCode = await prisma.verificationCode.findFirst({
        where: {
          userId,
          type,
          target: phoneNumber,
          isUsed: false,
          expiresAt: { gt: new Date() },
        },
        orderBy: { createdAt: 'desc' },
      });

      if (!verificationCode) {
        return {
          success: false,
          error: '验证码不存在或已过期',
        };
      }

      // 检查尝试次数
      if (verificationCode.attempts >= verificationCode.maxAttempts) {
        return {
          success: false,
          error: '验证码尝试次数过多，请重新获取',
        };
      }

      // 验证码码
      if (verificationCode.code !== code) {
        // 增加尝试次数
        await prisma.verificationCode.update({
          where: { id: verificationCode.id },
          data: { attempts: verificationCode.attempts + 1 },
        });

        return {
          success: false,
          error: '验证码错误',
        };
      }

      // 标记验证码为已使用
      await prisma.verificationCode.update({
        where: { id: verificationCode.id },
        data: { isUsed: true },
      });

      // 更新用户手机号验证状态
      if (type === VerificationCodeType.PHONE_VERIFICATION) {
        await prisma.user.update({
          where: { id: userId },
          data: {
            phone: phoneNumber,
            phoneVerified: true,
            phoneVerifiedAt: new Date(),
          },
        });
      }

      return { success: true };
    } catch (error) {
      console.error('验证手机验证码失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '验证失败',
      };
    }
  }

  /**
   * 设置TOTP多因素认证
   */
  async setupTOTP(userId: string): Promise<{
    success: boolean;
    secret?: string;
    qrCodeUrl?: string;
    backupCodes?: string[];
    error?: string;
  }> {
    try {
      // 生成TOTP密钥
      const secret = speakeasy.generateSecret({
        name: `CBEC ERP (${userId})`,
        issuer: 'CBEC ERP',
        length: 32,
      });

      // 生成备用码
      const backupCodes = this.generateBackupCodes();

      // 生成QR码
      const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url!);

      // 保存到数据库（暂时未启用）
      await prisma.userMFASettings.upsert({
        where: { userId },
        create: {
          userId,
          isEnabled: false,
          primaryMethod: MFAType.TOTP,
          enabledMethods: [],
          totpSecret: secret.base32,
          backupCodes: backupCodes,
        },
        update: {
          totpSecret: secret.base32,
          backupCodes: backupCodes,
        },
      });

      return {
        success: true,
        secret: secret.base32,
        qrCodeUrl,
        backupCodes,
      };
    } catch (error) {
      console.error('设置TOTP失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '设置TOTP失败',
      };
    }
  }

  /**
   * 验证TOTP代码
   */
  async verifyTOTP(
    userId: string,
    token: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 获取用户MFA设置
      const mfaSettings = await prisma.userMFASettings.findUnique({
        where: { userId },
      });

      if (!mfaSettings?.totpSecret) {
        return {
          success: false,
          error: 'TOTP未设置',
        };
      }

      // 验证TOTP代码
      const verified = speakeasy.totp.verify({
        secret: mfaSettings.totpSecret,
        encoding: 'base32',
        token,
        window: 2, // 允许时间窗口
      });

      if (!verified) {
        return {
          success: false,
          error: 'TOTP代码无效',
        };
      }

      return { success: true };
    } catch (error) {
      console.error('验证TOTP失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '验证TOTP失败',
      };
    }
  }

  /**
   * 启用MFA
   */
  async enableMFA(
    userId: string,
    primaryMethod: MFAType,
    verificationToken: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 验证令牌
      let verified = false;
      
      switch (primaryMethod) {
        case MFAType.TOTP:
          const totpResult = await this.verifyTOTP(userId, verificationToken);
          verified = totpResult.success;
          break;
        case MFAType.SMS:
          // 这里应该验证SMS代码
          verified = true; // 简化实现
          break;
        case MFAType.EMAIL:
          // 这里应该验证邮箱代码
          verified = true; // 简化实现
          break;
      }

      if (!verified) {
        return {
          success: false,
          error: '验证失败，无法启用MFA',
        };
      }

      // 启用MFA
      await prisma.userMFASettings.update({
        where: { userId },
        data: {
          isEnabled: true,
          primaryMethod,
          enabledMethods: [primaryMethod],
        },
      });

      return { success: true };
    } catch (error) {
      console.error('启用MFA失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '启用MFA失败',
      };
    }
  }

  /**
   * 禁用MFA
   */
  async disableMFA(
    userId: string,
    verificationToken: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 获取当前MFA设置
      const mfaSettings = await prisma.userMFASettings.findUnique({
        where: { userId },
      });

      if (!mfaSettings?.isEnabled) {
        return {
          success: false,
          error: 'MFA未启用',
        };
      }

      // 验证令牌
      const verified = await this.verifyMFAToken(userId, verificationToken);
      if (!verified.success) {
        return verified;
      }

      // 禁用MFA
      await prisma.userMFASettings.update({
        where: { userId },
        data: {
          isEnabled: false,
          enabledMethods: [],
        },
      });

      return { success: true };
    } catch (error) {
      console.error('禁用MFA失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '禁用MFA失败',
      };
    }
  }

  /**
   * 验证MFA令牌
   */
  async verifyMFAToken(
    userId: string,
    token: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const mfaSettings = await prisma.userMFASettings.findUnique({
        where: { userId },
      });

      if (!mfaSettings?.isEnabled) {
        return { success: true }; // MFA未启用，直接通过
      }

      // 检查是否为备用码
      if (mfaSettings.backupCodes.includes(token)) {
        // 使用备用码后将其移除
        const updatedBackupCodes = mfaSettings.backupCodes.filter(code => code !== token);
        await prisma.userMFASettings.update({
          where: { userId },
          data: { backupCodes: updatedBackupCodes },
        });
        return { success: true };
      }

      // 根据主要方法验证
      switch (mfaSettings.primaryMethod) {
        case MFAType.TOTP:
          return await this.verifyTOTP(userId, token);
        case MFAType.SMS:
          // 这里应该验证SMS代码
          return { success: true }; // 简化实现
        case MFAType.EMAIL:
          // 这里应该验证邮箱代码
          return { success: true }; // 简化实现
        default:
          return {
            success: false,
            error: '不支持的MFA方法',
          };
      }
    } catch (error) {
      console.error('验证MFA令牌失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '验证MFA令牌失败',
      };
    }
  }

  /**
   * 获取用户MFA设置
   */
  async getMFASettings(userId: string): Promise<MFASettings | null> {
    try {
      const settings = await prisma.userMFASettings.findUnique({
        where: { userId },
      });

      if (!settings) {
        return null;
      }

      return {
        isEnabled: settings.isEnabled,
        primaryMethod: settings.primaryMethod as MFAType,
        enabledMethods: settings.enabledMethods as MFAType[],
        backupCodes: settings.backupCodes,
        totpSecret: settings.totpSecret || undefined,
        phoneNumber: settings.phoneNumber || undefined,
        emailAddress: settings.emailAddress || undefined,
      };
    } catch (error) {
      console.error('获取MFA设置失败:', error);
      return null;
    }
  }

  /**
   * 上传用户头像
   */
  async uploadAvatar(
    userId: string,
    file: File
  ): Promise<{ success: boolean; avatarUrl?: string; error?: string }> {
    try {
      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        return {
          success: false,
          error: '不支持的文件类型，请上传 JPEG、PNG、GIF 或 WebP 格式的图片',
        };
      }

      // 验证文件大小（最大5MB）
      const maxSize = 5 * 1024 * 1024;
      if (file.size > maxSize) {
        return {
          success: false,
          error: '文件大小不能超过5MB',
        };
      }

      // 生成文件名
      const fileExtension = file.name.split('.').pop();
      const fileName = `avatar_${userId}_${Date.now()}.${fileExtension}`;
      const filePath = `uploads/avatars/${fileName}`;

      // 这里应该上传到云存储服务（如AWS S3、阿里云OSS等）
      // 简化实现，假设上传成功
      const avatarUrl = `/uploads/avatars/${fileName}`;

      // 更新用户头像URL
      await prisma.user.update({
        where: { id: userId },
        data: { avatar: avatarUrl },
      });

      return {
        success: true,
        avatarUrl,
      };
    } catch (error) {
      console.error('上传头像失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传头像失败',
      };
    }
  }

  /**
   * 创建SSO会话
   */
  async createSSOSession(
    userId: string,
    provider: string,
    externalId: string
  ): Promise<{ success: boolean; sessionToken?: string; error?: string }> {
    try {
      // 生成会话令牌
      const sessionToken = crypto.randomBytes(32).toString('hex');
      const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时

      // 保存SSO会话
      await prisma.ssoSession.create({
        data: {
          userId,
          provider,
          externalId,
          sessionToken,
          expiresAt,
          isActive: true,
        },
      });

      return {
        success: true,
        sessionToken,
      };
    } catch (error) {
      console.error('创建SSO会话失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建SSO会话失败',
      };
    }
  }

  /**
   * 验证SSO会话
   */
  async validateSSOSession(
    sessionToken: string
  ): Promise<{ success: boolean; userId?: string; error?: string }> {
    try {
      const session = await prisma.ssoSession.findFirst({
        where: {
          sessionToken,
          isActive: true,
          expiresAt: { gt: new Date() },
        },
      });

      if (!session) {
        return {
          success: false,
          error: 'SSO会话无效或已过期',
        };
      }

      return {
        success: true,
        userId: session.userId,
      };
    } catch (error) {
      console.error('验证SSO会话失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '验证SSO会话失败',
      };
    }
  }

  /**
   * 生成备用码
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < 10; i++) {
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  /**
   * 发送短信（需要集成短信服务商）
   */
  private async sendSMS(phoneNumber: string, message: string): Promise<void> {
    // 这里应该集成实际的短信服务商API
    // 例如：阿里云短信、腾讯云短信、Twilio等
    console.log(`发送短信到 ${phoneNumber}: ${message}`);
    
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  /**
   * 发送邮件（需要集成邮件服务）
   */
  private async sendEmail(
    email: string,
    subject: string,
    content: string
  ): Promise<void> {
    // 这里应该集成实际的邮件服务
    // 例如：SendGrid、Amazon SES、阿里云邮件推送等
    console.log(`发送邮件到 ${email}: ${subject} - ${content}`);
    
    // 模拟发送延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// 导出单例实例
export const enhancedAuthService = new EnhancedAuthService();
