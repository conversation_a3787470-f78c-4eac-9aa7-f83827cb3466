/**
 * 通用OIDC协议支持
 * 支持标准OIDC协议的任意身份提供商
 */

import { OAuthIDPHandler, ExternalUser, AuthRequest, AuthResponse } from '../idp-handler';
import { IDPType, IDPProtocol, OAuthIDPConfig } from '../idp-config';

// 通用OIDC配置
export interface GenericOIDCConfig extends OAuthIDPConfig {
  type: IDPType.OIDC;
  protocol: IDPProtocol.OIDC;
  // OIDC发现配置
  discoveryUrl?: string;
  // 手动端点配置（如果不使用发现）
  jwksUrl?: string;
  endSessionUrl?: string;
  // 令牌验证配置
  validateIssuer?: boolean;
  expectedIssuer?: string;
  validateAudience?: boolean;
  expectedAudience?: string;
  // 用户信息映射配置
  userInfoMapping?: {
    id?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    displayName?: string;
    avatar?: string;
    groups?: string;
    roles?: string;
  };
  // 额外的请求参数
  additionalAuthParams?: Record<string, string>;
  additionalTokenParams?: Record<string, string>;
}

// OIDC发现文档接口
interface OIDCDiscoveryDocument {
  issuer: string;
  authorization_endpoint: string;
  token_endpoint: string;
  userinfo_endpoint: string;
  jwks_uri: string;
  end_session_endpoint?: string;
  scopes_supported: string[];
  response_types_supported: string[];
  response_modes_supported?: string[];
  grant_types_supported?: string[];
  subject_types_supported: string[];
  id_token_signing_alg_values_supported: string[];
  claims_supported?: string[];
}

// JWT头部接口
interface JWTHeader {
  alg: string;
  typ: string;
  kid?: string;
}

// JWT载荷接口
interface JWTPayload {
  iss: string;
  sub: string;
  aud: string | string[];
  exp: number;
  iat: number;
  auth_time?: number;
  nonce?: string;
  [key: string]: any;
}

// JWKS接口
interface JWKS {
  keys: Array<{
    kty: string;
    use?: string;
    key_ops?: string[];
    alg?: string;
    kid?: string;
    x5u?: string;
    x5c?: string[];
    x5t?: string;
    'x5t#S256'?: string;
    n?: string; // RSA modulus
    e?: string; // RSA exponent
    d?: string; // RSA private exponent
    p?: string; // RSA first prime factor
    q?: string; // RSA second prime factor
    dp?: string; // RSA first factor CRT exponent
    dq?: string; // RSA second factor CRT exponent
    qi?: string; // RSA first CRT coefficient
  }>;
}

/**
 * 通用OIDC认证处理器
 */
export class GenericOIDCHandler extends OAuthIDPHandler {
  private config: GenericOIDCConfig;
  private discoveryDocument?: OIDCDiscoveryDocument;
  private jwks?: JWKS;

  constructor(config: GenericOIDCConfig) {
    super(config);
    this.config = config;
  }

  /**
   * 初始化OIDC配置（发现端点）
   */
  async initialize(): Promise<void> {
    if (this.config.discoveryUrl) {
      await this.loadDiscoveryDocument();
    }
  }

  /**
   * 加载OIDC发现文档
   */
  private async loadDiscoveryDocument(): Promise<void> {
    if (!this.config.discoveryUrl) {
      return;
    }

    try {
      const response = await fetch(this.config.discoveryUrl);
      if (!response.ok) {
        throw new Error(`加载发现文档失败: ${response.status}`);
      }

      this.discoveryDocument = await response.json();

      // 更新配置中的端点URL
      if (this.discoveryDocument.authorization_endpoint) {
        this.config.authorizationUrl = this.discoveryDocument.authorization_endpoint;
      }
      if (this.discoveryDocument.token_endpoint) {
        this.config.tokenUrl = this.discoveryDocument.token_endpoint;
      }
      if (this.discoveryDocument.userinfo_endpoint) {
        this.config.userInfoUrl = this.discoveryDocument.userinfo_endpoint;
      }
      if (this.discoveryDocument.jwks_uri) {
        this.config.jwksUrl = this.discoveryDocument.jwks_uri;
      }
    } catch (error) {
      console.error('加载OIDC发现文档失败:', error);
      throw error;
    }
  }

  /**
   * 加载JWKS
   */
  private async loadJWKS(): Promise<void> {
    const jwksUrl = this.config.jwksUrl || this.discoveryDocument?.jwks_uri;
    if (!jwksUrl) {
      throw new Error('未配置JWKS URL');
    }

    try {
      const response = await fetch(jwksUrl);
      if (!response.ok) {
        throw new Error(`加载JWKS失败: ${response.status}`);
      }

      this.jwks = await response.json();
    } catch (error) {
      console.error('加载JWKS失败:', error);
      throw error;
    }
  }

  /**
   * 生成认证URL
   */
  async generateAuthUrl(request: AuthRequest): Promise<string> {
    // 确保已初始化
    if (this.config.discoveryUrl && !this.discoveryDocument) {
      await this.initialize();
    }

    const params = new URLSearchParams({
      client_id: this.config.clientId,
      response_type: this.config.responseType || 'code',
      redirect_uri: request.redirectUri,
      scope: (request.scopes || this.config.scopes).join(' '),
      state: request.state || this.generateState(),
      nonce: this.generateNonce(),
      ...this.config.additionalAuthParams,
      ...request.additionalParams,
    });

    return `${this.config.authorizationUrl}?${params.toString()}`;
  }

  /**
   * 处理认证回调
   */
  async handleCallback(params: Record<string, string>): Promise<AuthResponse> {
    try {
      const { code, state, error, error_description } = params;

      if (error) {
        return {
          success: false,
          error: `OIDC认证失败: ${error_description || error}`,
          state,
        };
      }

      if (!code) {
        return {
          success: false,
          error: '缺少授权码',
          state,
        };
      }

      // 交换访问令牌
      const tokenResponse = await this.exchangeCodeForToken(code, params.redirect_uri);
      
      // 验证ID令牌（如果有）
      if (tokenResponse.id_token) {
        const isValidIdToken = await this.validateIdToken(tokenResponse.id_token);
        if (!isValidIdToken) {
          return {
            success: false,
            error: 'ID令牌验证失败',
            state,
          };
        }
      }

      // 获取用户信息
      const user = await this.getUserInfo(tokenResponse.access_token);

      return {
        success: true,
        user,
        state,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'OIDC认证处理失败',
        state: params.state,
      };
    }
  }

  /**
   * 获取用户信息
   */
  async getUserInfo(accessToken: string): Promise<ExternalUser> {
    const response = await fetch(this.config.userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取用户信息失败: ${response.status}`);
    }

    const userData = await response.json();
    
    return this.mapUserData(userData);
  }

  /**
   * 映射用户数据
   */
  private mapUserData(userData: any): ExternalUser {
    const mapping = this.config.userInfoMapping || {};

    return {
      id: this.getFieldValue(userData, mapping.id || 'sub'),
      email: this.getFieldValue(userData, mapping.email || 'email'),
      firstName: this.getFieldValue(userData, mapping.firstName || 'given_name'),
      lastName: this.getFieldValue(userData, mapping.lastName || 'family_name'),
      displayName: this.getFieldValue(userData, mapping.displayName || 'name'),
      avatar: this.getFieldValue(userData, mapping.avatar || 'picture'),
      attributes: userData,
      groups: this.getArrayFieldValue(userData, mapping.groups || 'groups'),
      roles: this.getArrayFieldValue(userData, mapping.roles || 'roles'),
      idpId: this.config.id,
      idpType: this.config.type,
    };
  }

  /**
   * 获取字段值（支持嵌套路径）
   */
  private getFieldValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 获取数组字段值
   */
  private getArrayFieldValue(obj: any, path: string): string[] {
    const value = this.getFieldValue(obj, path);
    if (Array.isArray(value)) {
      return value;
    }
    if (typeof value === 'string') {
      return [value];
    }
    return [];
  }

  /**
   * 验证ID令牌
   */
  async validateIdToken(idToken: string): Promise<boolean> {
    try {
      // 解析JWT
      const [headerB64, payloadB64, signatureB64] = idToken.split('.');
      if (!headerB64 || !payloadB64 || !signatureB64) {
        return false;
      }

      const header: JWTHeader = JSON.parse(this.base64UrlDecode(headerB64));
      const payload: JWTPayload = JSON.parse(this.base64UrlDecode(payloadB64));

      // 验证基本字段
      if (!payload.iss || !payload.sub || !payload.aud || !payload.exp || !payload.iat) {
        return false;
      }

      // 验证过期时间
      const now = Math.floor(Date.now() / 1000);
      if (payload.exp < now) {
        return false;
      }

      // 验证签发时间
      if (payload.iat > now + 300) { // 允许5分钟的时钟偏差
        return false;
      }

      // 验证签发者
      if (this.config.validateIssuer && this.config.expectedIssuer) {
        if (payload.iss !== this.config.expectedIssuer) {
          return false;
        }
      }

      // 验证受众
      if (this.config.validateAudience && this.config.expectedAudience) {
        const audiences = Array.isArray(payload.aud) ? payload.aud : [payload.aud];
        if (!audiences.includes(this.config.expectedAudience)) {
          return false;
        }
      }

      // 验证签名（需要JWKS）
      if (this.config.jwksUrl || this.discoveryDocument?.jwks_uri) {
        return await this.validateSignature(idToken, header);
      }

      return true;
    } catch (error) {
      console.error('ID令牌验证失败:', error);
      return false;
    }
  }

  /**
   * 验证JWT签名
   */
  private async validateSignature(token: string, header: JWTHeader): Promise<boolean> {
    try {
      // 加载JWKS（如果尚未加载）
      if (!this.jwks) {
        await this.loadJWKS();
      }

      if (!this.jwks) {
        return false;
      }

      // 查找匹配的密钥
      const key = this.jwks.keys.find(k => k.kid === header.kid || !header.kid);
      if (!key) {
        return false;
      }

      // 这里需要实现实际的签名验证逻辑
      // 根据算法类型（RS256, HS256等）进行相应的验证
      // 由于浏览器环境限制，实际的加密验证通常在服务端进行
      
      return true; // 临时返回
    } catch (error) {
      console.error('签名验证失败:', error);
      return false;
    }
  }

  /**
   * Base64 URL解码
   */
  private base64UrlDecode(str: string): string {
    // 添加填充
    str += '='.repeat((4 - str.length % 4) % 4);
    // 替换URL安全字符
    str = str.replace(/-/g, '+').replace(/_/g, '/');
    // 解码
    return atob(str);
  }

  /**
   * 生成随机数
   */
  private generateNonce(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * 交换令牌时添加额外参数
   */
  protected async exchangeCodeForToken(code: string, redirectUri: string): Promise<any> {
    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      code,
      redirect_uri: redirectUri,
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
      ...this.config.additionalTokenParams,
    });

    const response = await fetch(this.config.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: params.toString(),
    });

    if (!response.ok) {
      throw new Error(`令牌交换失败: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * OIDC登出
   */
  async logout(accessToken: string): Promise<void> {
    const endSessionUrl = this.config.endSessionUrl || this.discoveryDocument?.end_session_endpoint;
    
    if (endSessionUrl) {
      try {
        const params = new URLSearchParams({
          id_token_hint: accessToken, // 如果是ID令牌
          post_logout_redirect_uri: window.location.origin,
        });

        // 重定向到登出端点
        window.location.href = `${endSessionUrl}?${params.toString()}`;
      } catch (error) {
        console.warn('OIDC登出失败:', error);
      }
    }
  }
}

/**
 * 创建通用OIDC配置的辅助函数
 */
export function createGenericOIDCConfig(options: {
  id: string;
  name: string;
  clientId: string;
  clientSecret: string;
  discoveryUrl?: string;
  authorizationUrl?: string;
  tokenUrl?: string;
  userInfoUrl?: string;
  jwksUrl?: string;
  scopes?: string[];
  userInfoMapping?: GenericOIDCConfig['userInfoMapping'];
}): GenericOIDCConfig {
  return {
    id: options.id,
    name: options.name,
    type: IDPType.OIDC,
    protocol: IDPProtocol.OIDC,
    enabled: true,
    isDefault: false,
    clientId: options.clientId,
    clientSecret: options.clientSecret,
    discoveryUrl: options.discoveryUrl,
    authorizationUrl: options.authorizationUrl || '',
    tokenUrl: options.tokenUrl || '',
    userInfoUrl: options.userInfoUrl || '',
    jwksUrl: options.jwksUrl,
    scopes: options.scopes || ['openid', 'profile', 'email'],
    responseType: 'code',
    validateIssuer: true,
    validateAudience: true,
    userInfoMapping: options.userInfoMapping,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}
