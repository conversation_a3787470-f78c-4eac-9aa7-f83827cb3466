/**
 * Google Workspace身份提供商集成
 * 实现Google OAuth 2.0认证和用户信息同步
 */

import { OAuthIDPHandler, ExternalUser, AuthRequest, AuthResponse } from '../idp-handler';
import { IDPType, IDPProtocol, OAuthIDPConfig } from '../idp-config';

// Google Workspace特定配置
export interface GoogleWorkspaceConfig extends OAuthIDPConfig {
  type: IDPType.GOOGLE_WORKSPACE;
  protocol: IDPProtocol.OIDC;
  // Google特定配置
  hostedDomain?: string; // 限制特定域名的用户
  includeGrantedScopes?: boolean; // 是否包含已授权的作用域
  accessType?: 'online' | 'offline'; // 访问类型
  prompt?: 'none' | 'consent' | 'select_account'; // 提示类型
  // 组织架构同步配置
  syncOrgStructure?: boolean;
  adminEmail?: string; // 管理员邮箱，用于目录API访问
  serviceAccountKey?: string; // 服务账号密钥，用于目录API
}

// Google用户信息接口
interface GoogleUserInfo {
  sub: string; // 用户唯一标识
  email: string;
  email_verified: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
  hd?: string; // 托管域名
}

// Google目录API用户信息
interface GoogleDirectoryUser {
  id: string;
  primaryEmail: string;
  name: {
    givenName: string;
    familyName: string;
    fullName: string;
  };
  orgUnitPath: string;
  isAdmin: boolean;
  isDelegatedAdmin: boolean;
  suspended: boolean;
  archived: boolean;
  customSchemas?: Record<string, any>;
  organizations?: Array<{
    name: string;
    title: string;
    department: string;
    location: string;
    type: string;
    primary: boolean;
  }>;
}

/**
 * Google Workspace认证处理器
 */
export class GoogleWorkspaceHandler extends OAuthIDPHandler {
  private config: GoogleWorkspaceConfig;

  constructor(config: GoogleWorkspaceConfig) {
    super(config);
    this.config = config;
  }

  /**
   * 生成Google OAuth认证URL
   */
  async generateAuthUrl(request: AuthRequest): Promise<string> {
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      response_type: 'code',
      redirect_uri: request.redirectUri,
      scope: this.getScopes(request.scopes).join(' '),
      state: request.state || this.generateState(),
      access_type: this.config.accessType || 'online',
      include_granted_scopes: this.config.includeGrantedScopes ? 'true' : 'false',
      ...request.additionalParams,
    });

    // 添加托管域名限制
    if (this.config.hostedDomain) {
      params.set('hd', this.config.hostedDomain);
    }

    // 添加提示参数
    if (this.config.prompt) {
      params.set('prompt', this.config.prompt);
    }

    return `${this.config.authorizationUrl}?${params.toString()}`;
  }

  /**
   * 处理Google OAuth回调
   */
  async handleCallback(params: Record<string, string>): Promise<AuthResponse> {
    try {
      const { code, state, error, error_description } = params;

      if (error) {
        return {
          success: false,
          error: `Google认证失败: ${error_description || error}`,
          state,
        };
      }

      if (!code) {
        return {
          success: false,
          error: '缺少Google授权码',
          state,
        };
      }

      // 交换访问令牌
      const tokenResponse = await this.exchangeCodeForToken(code, params.redirect_uri);
      
      // 获取用户信息
      const user = await this.getUserInfo(tokenResponse.access_token);

      // 验证托管域名
      if (this.config.hostedDomain && user.attributes.hd !== this.config.hostedDomain) {
        return {
          success: false,
          error: `用户不属于指定的域名: ${this.config.hostedDomain}`,
          state,
        };
      }

      // 获取扩展用户信息（如果配置了服务账号）
      if (this.config.syncOrgStructure && this.config.serviceAccountKey) {
        try {
          const extendedInfo = await this.getExtendedUserInfo(user.email);
          user.attributes = { ...user.attributes, ...extendedInfo };
          user.groups = this.extractGroups(extendedInfo);
          user.roles = this.extractRoles(extendedInfo);
        } catch (error) {
          console.warn('获取扩展用户信息失败:', error);
        }
      }

      return {
        success: true,
        user,
        state,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Google认证处理失败',
        state: params.state,
      };
    }
  }

  /**
   * 获取Google用户信息
   */
  async getUserInfo(accessToken: string): Promise<ExternalUser> {
    const response = await fetch(this.config.userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取Google用户信息失败: ${response.status}`);
    }

    const userData: GoogleUserInfo = await response.json();
    
    return this.mapGoogleUser(userData);
  }

  /**
   * 获取扩展用户信息（通过Directory API）
   */
  private async getExtendedUserInfo(email: string): Promise<GoogleDirectoryUser> {
    if (!this.config.serviceAccountKey) {
      throw new Error('未配置服务账号密钥');
    }

    // 这里需要实现Google Directory API的调用
    // 使用服务账号进行身份验证
    const accessToken = await this.getServiceAccountToken();
    
    const response = await fetch(
      `https://admin.googleapis.com/admin/directory/v1/users/${email}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (!response.ok) {
      throw new Error(`获取Directory API用户信息失败: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * 获取服务账号访问令牌
   */
  private async getServiceAccountToken(): Promise<string> {
    // 这里需要实现JWT签名和令牌交换
    // 使用服务账号密钥生成JWT，然后交换访问令牌
    throw new Error('服务账号令牌获取尚未实现');
  }

  /**
   * 映射Google用户数据
   */
  private mapGoogleUser(userData: GoogleUserInfo): ExternalUser {
    return {
      id: userData.sub,
      email: userData.email,
      firstName: userData.given_name,
      lastName: userData.family_name,
      displayName: userData.name,
      avatar: userData.picture,
      attributes: {
        ...userData,
        emailVerified: userData.email_verified,
        locale: userData.locale,
        hostedDomain: userData.hd,
      },
      groups: [],
      roles: [],
      idpId: this.config.id,
      idpType: this.config.type,
    };
  }

  /**
   * 从扩展信息中提取用户组
   */
  private extractGroups(extendedInfo: GoogleDirectoryUser): string[] {
    const groups: string[] = [];
    
    // 添加组织单位路径作为组
    if (extendedInfo.orgUnitPath) {
      groups.push(extendedInfo.orgUnitPath);
    }

    // 添加部门信息作为组
    if (extendedInfo.organizations) {
      extendedInfo.organizations.forEach(org => {
        if (org.department) {
          groups.push(`department:${org.department}`);
        }
        if (org.location) {
          groups.push(`location:${org.location}`);
        }
      });
    }

    return groups;
  }

  /**
   * 从扩展信息中提取用户角色
   */
  private extractRoles(extendedInfo: GoogleDirectoryUser): string[] {
    const roles: string[] = [];
    
    // 管理员角色
    if (extendedInfo.isAdmin) {
      roles.push('admin');
    }
    
    if (extendedInfo.isDelegatedAdmin) {
      roles.push('delegated_admin');
    }

    // 根据组织信息确定角色
    if (extendedInfo.organizations) {
      extendedInfo.organizations.forEach(org => {
        if (org.title) {
          roles.push(`title:${org.title}`);
        }
      });
    }

    return roles;
  }

  /**
   * 获取默认作用域
   */
  private getScopes(requestedScopes?: string[]): string[] {
    const defaultScopes = [
      'openid',
      'email',
      'profile',
    ];

    // 如果需要同步组织架构，添加目录API作用域
    if (this.config.syncOrgStructure) {
      defaultScopes.push('https://www.googleapis.com/auth/admin.directory.user.readonly');
    }

    return requestedScopes || this.config.scopes || defaultScopes;
  }

  /**
   * 验证Google ID令牌
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      // 对于Google OIDC，可以验证ID令牌
      const response = await fetch(
        `https://oauth2.googleapis.com/tokeninfo?id_token=${token}`
      );

      if (!response.ok) {
        return false;
      }

      const tokenInfo = await response.json();
      
      // 验证客户端ID
      if (tokenInfo.aud !== this.config.clientId) {
        return false;
      }

      // 验证托管域名
      if (this.config.hostedDomain && tokenInfo.hd !== this.config.hostedDomain) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Google登出
   */
  async logout(accessToken: string): Promise<void> {
    try {
      await fetch(`https://oauth2.googleapis.com/revoke?token=${accessToken}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
    } catch (error) {
      console.warn('Google令牌撤销失败:', error);
    }
  }
}

/**
 * 创建Google Workspace配置的辅助函数
 */
export function createGoogleWorkspaceConfig(options: {
  id: string;
  name: string;
  clientId: string;
  clientSecret: string;
  hostedDomain?: string;
  syncOrgStructure?: boolean;
  adminEmail?: string;
  serviceAccountKey?: string;
}): GoogleWorkspaceConfig {
  return {
    id: options.id,
    name: options.name,
    type: IDPType.GOOGLE_WORKSPACE,
    protocol: IDPProtocol.OIDC,
    enabled: true,
    isDefault: false,
    clientId: options.clientId,
    clientSecret: options.clientSecret,
    authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',
    tokenUrl: 'https://oauth2.googleapis.com/token',
    userInfoUrl: 'https://openidconnect.googleapis.com/v1/userinfo',
    discoveryUrl: 'https://accounts.google.com/.well-known/openid_configuration',
    scopes: ['openid', 'email', 'profile'],
    responseType: 'code',
    hostedDomain: options.hostedDomain,
    syncOrgStructure: options.syncOrgStructure || false,
    adminEmail: options.adminEmail,
    serviceAccountKey: options.serviceAccountKey,
    accessType: 'online',
    includeGrantedScopes: false,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}
