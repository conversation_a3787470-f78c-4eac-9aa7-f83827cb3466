/**
 * 用户同步服务
 * 处理外部IDP用户与本地用户的映射和同步
 */

import { prisma } from '@/lib/prisma';
import { ExternalUser, LocalUser, UserSyncResult } from './idp-handler';
import { IDPType } from './idp-config';

// 用户属性映射配置
export interface UserAttributeMapping {
  localAttribute: string;
  externalAttribute: string;
  required: boolean;
  defaultValue?: string;
  transformer?: string;
  condition?: string;
}

// 角色映射配置
export interface RoleMapping {
  externalRole: string;
  localRole: string;
  conditions?: Record<string, any>;
  priority: number;
}

// 组映射配置
export interface GroupMapping {
  externalGroup: string;
  localRole: string;
  localPermissions: string[];
  conditions?: Record<string, any>;
}

// 同步策略配置
export interface SyncStrategy {
  createNewUsers: boolean;
  updateExistingUsers: boolean;
  deactivateRemovedUsers: boolean;
  syncUserAttributes: boolean;
  syncUserRoles: boolean;
  syncUserGroups: boolean;
  conflictResolution: 'external_wins' | 'local_wins' | 'merge';
}

// 同步统计信息
export interface SyncStatistics {
  totalProcessed: number;
  usersCreated: number;
  usersUpdated: number;
  usersDeactivated: number;
  usersSkipped: number;
  errors: Array<{
    userId: string;
    error: string;
    timestamp: Date;
  }>;
}

/**
 * 用户同步服务类
 */
export class UserSyncService {
  private attributeMappings: Map<string, UserAttributeMapping[]> = new Map();
  private roleMappings: Map<string, RoleMapping[]> = new Map();
  private groupMappings: Map<string, GroupMapping[]> = new Map();
  private syncStrategies: Map<string, SyncStrategy> = new Map();

  /**
   * 设置IDP的属性映射配置
   */
  setAttributeMappings(idpId: string, mappings: UserAttributeMapping[]): void {
    this.attributeMappings.set(idpId, mappings);
  }

  /**
   * 设置IDP的角色映射配置
   */
  setRoleMappings(idpId: string, mappings: RoleMapping[]): void {
    // 按优先级排序
    const sortedMappings = mappings.sort((a, b) => a.priority - b.priority);
    this.roleMappings.set(idpId, sortedMappings);
  }

  /**
   * 设置IDP的组映射配置
   */
  setGroupMappings(idpId: string, mappings: GroupMapping[]): void {
    this.groupMappings.set(idpId, mappings);
  }

  /**
   * 设置IDP的同步策略
   */
  setSyncStrategy(idpId: string, strategy: SyncStrategy): void {
    this.syncStrategies.set(idpId, strategy);
  }

  /**
   * 同步单个外部用户到本地系统
   */
  async syncUser(externalUser: ExternalUser): Promise<UserSyncResult> {
    try {
      const strategy = this.syncStrategies.get(externalUser.idpId) || this.getDefaultSyncStrategy();
      
      // 查找现有用户
      const existingUser = await this.findExistingUser(externalUser);
      
      if (existingUser) {
        if (strategy.updateExistingUsers) {
          // 更新现有用户
          const updatedUser = await this.updateUser(existingUser, externalUser, strategy);
          return {
            success: true,
            user: updatedUser,
            created: false,
            updated: true,
          };
        } else {
          // 跳过更新
          return {
            success: true,
            user: existingUser,
            created: false,
            updated: false,
          };
        }
      } else {
        if (strategy.createNewUsers) {
          // 创建新用户
          const newUser = await this.createUser(externalUser, strategy);
          return {
            success: true,
            user: newUser,
            created: true,
            updated: false,
          };
        } else {
          // 不允许创建新用户
          return {
            success: false,
            errors: ['不允许创建新用户'],
            created: false,
            updated: false,
          };
        }
      }
    } catch (error) {
      return {
        success: false,
        errors: [error instanceof Error ? error.message : '用户同步失败'],
        created: false,
        updated: false,
      };
    }
  }

  /**
   * 批量同步用户
   */
  async syncUsers(externalUsers: ExternalUser[]): Promise<SyncStatistics> {
    const statistics: SyncStatistics = {
      totalProcessed: 0,
      usersCreated: 0,
      usersUpdated: 0,
      usersDeactivated: 0,
      usersSkipped: 0,
      errors: [],
    };

    for (const externalUser of externalUsers) {
      try {
        statistics.totalProcessed++;
        
        const result = await this.syncUser(externalUser);
        
        if (result.success) {
          if (result.created) {
            statistics.usersCreated++;
          } else if (result.updated) {
            statistics.usersUpdated++;
          } else {
            statistics.usersSkipped++;
          }
        } else {
          statistics.errors.push({
            userId: externalUser.id,
            error: result.errors?.join(', ') || '未知错误',
            timestamp: new Date(),
          });
        }
      } catch (error) {
        statistics.errors.push({
          userId: externalUser.id,
          error: error instanceof Error ? error.message : '同步失败',
          timestamp: new Date(),
        });
      }
    }

    return statistics;
  }

  /**
   * 查找现有用户
   */
  private async findExistingUser(externalUser: ExternalUser): Promise<LocalUser | null> {
    // 首先通过外部ID查找
    let user = await prisma.user.findFirst({
      where: {
        externalId: externalUser.id,
        idpId: externalUser.idpId,
      },
    });

    // 如果没找到，通过邮箱查找
    if (!user && externalUser.email) {
      user = await prisma.user.findUnique({
        where: { email: externalUser.email },
      });
    }

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName || undefined,
      lastName: user.lastName || undefined,
      role: user.role,
      permissions: [], // TODO: 从角色获取权限
      isActive: user.isActive,
      externalId: user.externalId || undefined,
      idpId: user.idpId || undefined,
    };
  }

  /**
   * 创建新用户
   */
  private async createUser(externalUser: ExternalUser, strategy: SyncStrategy): Promise<LocalUser> {
    // 映射用户属性
    const mappedAttributes = this.mapUserAttributes(externalUser);
    
    // 映射用户角色
    const mappedRole = this.mapUserRole(externalUser);
    
    // 映射用户权限
    const mappedPermissions = this.mapUserPermissions(externalUser);

    // 创建用户数据
    const userData = {
      id: crypto.randomUUID(),
      email: externalUser.email,
      firstName: mappedAttributes.firstName || externalUser.firstName,
      lastName: mappedAttributes.lastName || externalUser.lastName,
      role: mappedRole,
      isActive: true,
      emailVerified: true, // 来自外部IDP的用户默认已验证
      externalId: externalUser.id,
      idpId: externalUser.idpId,
      lastLoginAt: new Date(),
      ...mappedAttributes,
    };

    // 保存到数据库
    const createdUser = await prisma.user.create({
      data: userData,
    });

    // 记录同步日志
    await this.logSyncEvent('USER_CREATED', externalUser.idpId, externalUser.id, {
      localUserId: createdUser.id,
      mappedRole,
      mappedPermissions,
    });

    return {
      id: createdUser.id,
      email: createdUser.email,
      firstName: createdUser.firstName || undefined,
      lastName: createdUser.lastName || undefined,
      role: createdUser.role,
      permissions: mappedPermissions,
      isActive: createdUser.isActive,
      externalId: createdUser.externalId || undefined,
      idpId: createdUser.idpId || undefined,
    };
  }

  /**
   * 更新现有用户
   */
  private async updateUser(
    existingUser: LocalUser,
    externalUser: ExternalUser,
    strategy: SyncStrategy
  ): Promise<LocalUser> {
    const updateData: any = {};
    let hasChanges = false;

    // 更新用户属性
    if (strategy.syncUserAttributes) {
      const mappedAttributes = this.mapUserAttributes(externalUser);
      
      for (const [key, value] of Object.entries(mappedAttributes)) {
        if (value !== undefined && value !== (existingUser as any)[key]) {
          updateData[key] = value;
          hasChanges = true;
        }
      }
    }

    // 更新用户角色
    if (strategy.syncUserRoles) {
      const mappedRole = this.mapUserRole(externalUser);
      if (mappedRole !== existingUser.role) {
        updateData.role = mappedRole;
        hasChanges = true;
      }
    }

    // 更新外部ID关联
    if (!existingUser.externalId || !existingUser.idpId) {
      updateData.externalId = externalUser.id;
      updateData.idpId = externalUser.idpId;
      hasChanges = true;
    }

    // 更新最后登录时间
    updateData.lastLoginAt = new Date();
    hasChanges = true;

    if (hasChanges) {
      const updatedUser = await prisma.user.update({
        where: { id: existingUser.id },
        data: updateData,
      });

      // 记录同步日志
      await this.logSyncEvent('USER_UPDATED', externalUser.idpId, externalUser.id, {
        localUserId: existingUser.id,
        changes: updateData,
      });

      return {
        ...existingUser,
        ...updateData,
      };
    }

    return existingUser;
  }

  /**
   * 映射用户属性
   */
  private mapUserAttributes(externalUser: ExternalUser): Record<string, any> {
    const mappings = this.attributeMappings.get(externalUser.idpId) || [];
    const mapped: Record<string, any> = {};

    for (const mapping of mappings) {
      let value = this.getNestedValue(externalUser.attributes, mapping.externalAttribute);
      
      // 应用转换器
      if (value !== undefined && mapping.transformer) {
        value = this.applyTransformer(value, mapping.transformer);
      }
      
      // 使用默认值
      if (value === undefined && mapping.defaultValue !== undefined) {
        value = mapping.defaultValue;
      }
      
      // 检查必需字段
      if (value === undefined && mapping.required) {
        throw new Error(`必需的属性 ${mapping.localAttribute} 缺失`);
      }
      
      if (value !== undefined) {
        mapped[mapping.localAttribute] = value;
      }
    }

    return mapped;
  }

  /**
   * 映射用户角色
   */
  private mapUserRole(externalUser: ExternalUser): string {
    const mappings = this.roleMappings.get(externalUser.idpId) || [];
    
    for (const mapping of mappings) {
      if (this.matchesRoleCondition(externalUser, mapping)) {
        return mapping.localRole;
      }
    }
    
    // 默认角色
    return 'USER';
  }

  /**
   * 映射用户权限
   */
  private mapUserPermissions(externalUser: ExternalUser): string[] {
    const mappings = this.groupMappings.get(externalUser.idpId) || [];
    const permissions: Set<string> = new Set();
    
    for (const mapping of mappings) {
      if (this.matchesGroupCondition(externalUser, mapping)) {
        mapping.localPermissions.forEach(permission => permissions.add(permission));
      }
    }
    
    return Array.from(permissions);
  }

  /**
   * 检查角色映射条件
   */
  private matchesRoleCondition(externalUser: ExternalUser, mapping: RoleMapping): boolean {
    // 检查角色匹配
    if (externalUser.roles?.includes(mapping.externalRole)) {
      return true;
    }
    
    // 检查组匹配
    if (externalUser.groups?.includes(mapping.externalRole)) {
      return true;
    }
    
    // 检查额外条件
    if (mapping.conditions) {
      return this.evaluateConditions(externalUser, mapping.conditions);
    }
    
    return false;
  }

  /**
   * 检查组映射条件
   */
  private matchesGroupCondition(externalUser: ExternalUser, mapping: GroupMapping): boolean {
    // 检查组匹配
    if (externalUser.groups?.includes(mapping.externalGroup)) {
      return true;
    }
    
    // 检查额外条件
    if (mapping.conditions) {
      return this.evaluateConditions(externalUser, mapping.conditions);
    }
    
    return false;
  }

  /**
   * 评估条件表达式
   */
  private evaluateConditions(externalUser: ExternalUser, conditions: Record<string, any>): boolean {
    for (const [key, expectedValue] of Object.entries(conditions)) {
      const actualValue = this.getNestedValue(externalUser.attributes, key);
      
      if (Array.isArray(expectedValue)) {
        if (!expectedValue.includes(actualValue)) {
          return false;
        }
      } else if (actualValue !== expectedValue) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  /**
   * 应用数据转换器
   */
  private applyTransformer(value: any, transformer: string): any {
    switch (transformer) {
      case 'toLowerCase':
        return typeof value === 'string' ? value.toLowerCase() : value;
      case 'toUpperCase':
        return typeof value === 'string' ? value.toUpperCase() : value;
      case 'trim':
        return typeof value === 'string' ? value.trim() : value;
      case 'parseDate':
        return new Date(value);
      case 'parseNumber':
        return Number(value);
      case 'parseBoolean':
        return Boolean(value);
      default:
        return value;
    }
  }

  /**
   * 记录同步事件
   */
  private async logSyncEvent(
    eventType: string,
    idpId: string,
    externalUserId: string,
    details: any
  ): Promise<void> {
    try {
      await prisma.userSyncLog.create({
        data: {
          eventType,
          idpId,
          externalUserId,
          details,
          timestamp: new Date(),
        },
      });
    } catch (error) {
      console.error('记录同步事件失败:', error);
    }
  }

  /**
   * 获取默认同步策略
   */
  private getDefaultSyncStrategy(): SyncStrategy {
    return {
      createNewUsers: true,
      updateExistingUsers: true,
      deactivateRemovedUsers: false,
      syncUserAttributes: true,
      syncUserRoles: true,
      syncUserGroups: true,
      conflictResolution: 'external_wins',
    };
  }
}
