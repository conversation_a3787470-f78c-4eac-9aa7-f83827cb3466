/**
 * 报表分析系统配置文件
 * 定义报表类型、数据源、图表配置等
 */

// 报表类型枚举
export enum ReportType {
  SALES = 'SALES',                    // 销售报表
  INVENTORY = 'INVENTORY',            // 库存报表
  FINANCIAL = 'FINANCIAL',            // 财务报表
  CUSTOMER = 'CUSTOMER',              // 客户报表
  PRODUCT = 'PRODUCT',                // 商品报表
  ORDER = 'ORDER',                    // 订单报表
  SHIPPING = 'SHIPPING',              // 物流报表
  MARKETING = 'MARKETING',            // 营销报表
  OPERATIONAL = 'OPERATIONAL',        // 运营报表
  PERFORMANCE = 'PERFORMANCE',        // 绩效报表
}

// 报表周期枚举
export enum ReportPeriod {
  DAILY = 'DAILY',                    // 日报
  WEEKLY = 'WEEKLY',                  // 周报
  MONTHLY = 'MONTHLY',                // 月报
  QUARTERLY = 'QUARTERLY',            // 季报
  YEARLY = 'YEARLY',                  // 年报
  CUSTOM = 'CUSTOM',                  // 自定义
}

// 图表类型枚举
export enum ChartType {
  LINE = 'LINE',                      // 折线图
  BAR = 'BAR',                        // 柱状图
  PIE = 'PIE',                        // 饼图
  AREA = 'AREA',                      // 面积图
  SCATTER = 'SCATTER',                // 散点图
  HEATMAP = 'HEATMAP',                // 热力图
  GAUGE = 'GAUGE',                    // 仪表盘
  FUNNEL = 'FUNNEL',                  // 漏斗图
  RADAR = 'RADAR',                    // 雷达图
  TREEMAP = 'TREEMAP',                // 树状图
  TABLE = 'TABLE',                    // 表格
  CARD = 'CARD',                      // 卡片
}

// 数据聚合方式枚举
export enum AggregationType {
  SUM = 'SUM',                        // 求和
  COUNT = 'COUNT',                    // 计数
  AVG = 'AVG',                        // 平均值
  MAX = 'MAX',                        // 最大值
  MIN = 'MIN',                        // 最小值
  MEDIAN = 'MEDIAN',                  // 中位数
  DISTINCT_COUNT = 'DISTINCT_COUNT',  // 去重计数
  PERCENTAGE = 'PERCENTAGE',          // 百分比
  GROWTH_RATE = 'GROWTH_RATE',        // 增长率
  CUMULATIVE = 'CUMULATIVE',          // 累计值
}

// 报表状态枚举
export enum ReportStatus {
  DRAFT = 'DRAFT',                    // 草稿
  PUBLISHED = 'PUBLISHED',            // 已发布
  SCHEDULED = 'SCHEDULED',            // 已调度
  ARCHIVED = 'ARCHIVED',              // 已归档
  ERROR = 'ERROR',                    // 错误
}

// 报表配置接口
export interface ReportConfig {
  id: string;
  name: string;
  description?: string;
  type: ReportType;
  category: string;
  dataSource: string;
  query: string;
  parameters: ReportParameter[];
  charts: ChartConfig[];
  filters: FilterConfig[];
  layout: LayoutConfig;
  schedule?: ScheduleConfig;
  permissions: string[];
  status: ReportStatus;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 报表参数接口
export interface ReportParameter {
  name: string;
  label: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'select' | 'multiselect';
  required: boolean;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// 图表配置接口
export interface ChartConfig {
  id: string;
  title: string;
  type: ChartType;
  dataKey: string;
  xAxis?: string;
  yAxis?: string;
  series?: SeriesConfig[];
  options?: ChartOptions;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
}

// 系列配置接口
export interface SeriesConfig {
  name: string;
  dataKey: string;
  color?: string;
  type?: ChartType;
  aggregation?: AggregationType;
  format?: string;
}

// 图表选项接口
export interface ChartOptions {
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  showDataLabels?: boolean;
  colors?: string[];
  theme?: 'light' | 'dark';
  animation?: boolean;
  responsive?: boolean;
  [key: string]: any;
}

// 过滤器配置接口
export interface FilterConfig {
  field: string;
  label: string;
  type: 'text' | 'number' | 'date' | 'select' | 'multiselect' | 'range';
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between';
  value?: any;
  options?: Array<{ label: string; value: any }>;
  required?: boolean;
}

// 布局配置接口
export interface LayoutConfig {
  type: 'grid' | 'flex' | 'absolute';
  columns?: number;
  rows?: number;
  gap?: number;
  padding?: number;
  responsive?: boolean;
}

// 调度配置接口
export interface ScheduleConfig {
  enabled: boolean;
  frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
  time?: string;
  dayOfWeek?: number;
  dayOfMonth?: number;
  timezone?: string;
  recipients?: string[];
  format?: 'pdf' | 'excel' | 'csv' | 'html';
}

// 报表数据接口
export interface ReportData {
  reportId: string;
  generatedAt: Date;
  parameters: Record<string, any>;
  data: any[];
  summary?: Record<string, any>;
  metadata: {
    totalRecords: number;
    executionTime: number;
    dataSource: string;
    query: string;
  };
}

// 预定义报表配置
export const PREDEFINED_REPORTS: Record<string, Partial<ReportConfig>> = {
  // 销售报表
  SALES_OVERVIEW: {
    name: '销售概览',
    type: ReportType.SALES,
    category: '销售分析',
    description: '展示销售总额、订单数量、平均订单价值等关键销售指标',
    charts: [
      {
        id: 'sales_trend',
        title: '销售趋势',
        type: ChartType.LINE,
        dataKey: 'sales_by_date',
        xAxis: 'date',
        yAxis: 'amount',
        position: { x: 0, y: 0, width: 12, height: 6 },
      },
      {
        id: 'top_products',
        title: '热销商品',
        type: ChartType.BAR,
        dataKey: 'top_products',
        xAxis: 'product_name',
        yAxis: 'quantity',
        position: { x: 0, y: 6, width: 6, height: 6 },
      },
      {
        id: 'sales_by_channel',
        title: '渠道销售分布',
        type: ChartType.PIE,
        dataKey: 'sales_by_channel',
        position: { x: 6, y: 6, width: 6, height: 6 },
      },
    ],
  },

  INVENTORY_STATUS: {
    name: '库存状态',
    type: ReportType.INVENTORY,
    category: '库存管理',
    description: '展示库存水平、库存周转率、缺货预警等库存指标',
    charts: [
      {
        id: 'inventory_levels',
        title: '库存水平',
        type: ChartType.BAR,
        dataKey: 'inventory_levels',
        xAxis: 'product_name',
        yAxis: 'quantity',
        position: { x: 0, y: 0, width: 8, height: 6 },
      },
      {
        id: 'low_stock_alert',
        title: '低库存预警',
        type: ChartType.TABLE,
        dataKey: 'low_stock_products',
        position: { x: 8, y: 0, width: 4, height: 6 },
      },
      {
        id: 'inventory_turnover',
        title: '库存周转率',
        type: ChartType.GAUGE,
        dataKey: 'turnover_rate',
        position: { x: 0, y: 6, width: 6, height: 6 },
      },
      {
        id: 'inventory_value',
        title: '库存价值分布',
        type: ChartType.TREEMAP,
        dataKey: 'inventory_value',
        position: { x: 6, y: 6, width: 6, height: 6 },
      },
    ],
  },

  FINANCIAL_SUMMARY: {
    name: '财务汇总',
    type: ReportType.FINANCIAL,
    category: '财务分析',
    description: '展示收入、支出、利润、现金流等财务指标',
    charts: [
      {
        id: 'revenue_trend',
        title: '收入趋势',
        type: ChartType.AREA,
        dataKey: 'revenue_by_month',
        xAxis: 'month',
        yAxis: 'revenue',
        position: { x: 0, y: 0, width: 8, height: 6 },
      },
      {
        id: 'profit_margin',
        title: '利润率',
        type: ChartType.LINE,
        dataKey: 'profit_margin',
        xAxis: 'month',
        yAxis: 'margin',
        position: { x: 8, y: 0, width: 4, height: 6 },
      },
      {
        id: 'expense_breakdown',
        title: '支出分解',
        type: ChartType.PIE,
        dataKey: 'expenses_by_category',
        position: { x: 0, y: 6, width: 6, height: 6 },
      },
      {
        id: 'cash_flow',
        title: '现金流',
        type: ChartType.BAR,
        dataKey: 'cash_flow',
        xAxis: 'month',
        yAxis: 'amount',
        position: { x: 6, y: 6, width: 6, height: 6 },
      },
    ],
  },

  CUSTOMER_ANALYSIS: {
    name: '客户分析',
    type: ReportType.CUSTOMER,
    category: '客户管理',
    description: '展示客户获取、留存、价值等客户相关指标',
    charts: [
      {
        id: 'customer_acquisition',
        title: '客户获取',
        type: ChartType.LINE,
        dataKey: 'new_customers',
        xAxis: 'date',
        yAxis: 'count',
        position: { x: 0, y: 0, width: 6, height: 6 },
      },
      {
        id: 'customer_retention',
        title: '客户留存率',
        type: ChartType.AREA,
        dataKey: 'retention_rate',
        xAxis: 'cohort',
        yAxis: 'rate',
        position: { x: 6, y: 0, width: 6, height: 6 },
      },
      {
        id: 'customer_lifetime_value',
        title: '客户生命周期价值',
        type: ChartType.SCATTER,
        dataKey: 'clv_data',
        xAxis: 'orders_count',
        yAxis: 'total_value',
        position: { x: 0, y: 6, width: 8, height: 6 },
      },
      {
        id: 'top_customers',
        title: '高价值客户',
        type: ChartType.TABLE,
        dataKey: 'top_customers',
        position: { x: 8, y: 6, width: 4, height: 6 },
      },
    ],
  },
};

// 数据源配置
export const DATA_SOURCES = {
  ORDERS: {
    name: '订单数据',
    table: 'orders',
    fields: [
      { name: 'id', type: 'string', label: '订单ID' },
      { name: 'orderNumber', type: 'string', label: '订单号' },
      { name: 'customerId', type: 'string', label: '客户ID' },
      { name: 'totalAmount', type: 'number', label: '订单金额' },
      { name: 'status', type: 'string', label: '订单状态' },
      { name: 'createdAt', type: 'date', label: '创建时间' },
      { name: 'shippedAt', type: 'date', label: '发货时间' },
      { name: 'deliveredAt', type: 'date', label: '送达时间' },
    ],
  },
  PRODUCTS: {
    name: '商品数据',
    table: 'products',
    fields: [
      { name: 'id', type: 'string', label: '商品ID' },
      { name: 'name', type: 'string', label: '商品名称' },
      { name: 'sku', type: 'string', label: 'SKU' },
      { name: 'basePrice', type: 'number', label: '基础价格' },
      { name: 'costPrice', type: 'number', label: '成本价格' },
      { name: 'category', type: 'string', label: '商品分类' },
      { name: 'brand', type: 'string', label: '品牌' },
      { name: 'status', type: 'string', label: '状态' },
    ],
  },
  INVENTORY: {
    name: '库存数据',
    table: 'inventory',
    fields: [
      { name: 'id', type: 'string', label: '库存ID' },
      { name: 'productId', type: 'string', label: '商品ID' },
      { name: 'warehouseId', type: 'string', label: '仓库ID' },
      { name: 'quantity', type: 'number', label: '库存数量' },
      { name: 'reservedQuantity', type: 'number', label: '预留数量' },
      { name: 'availableQuantity', type: 'number', label: '可用数量' },
      { name: 'lastUpdated', type: 'date', label: '最后更新时间' },
    ],
  },
  CUSTOMERS: {
    name: '客户数据',
    table: 'customers',
    fields: [
      { name: 'id', type: 'string', label: '客户ID' },
      { name: 'email', type: 'string', label: '邮箱' },
      { name: 'firstName', type: 'string', label: '名' },
      { name: 'lastName', type: 'string', label: '姓' },
      { name: 'phone', type: 'string', label: '电话' },
      { name: 'country', type: 'string', label: '国家' },
      { name: 'registeredAt', type: 'date', label: '注册时间' },
      { name: 'lastOrderAt', type: 'date', label: '最后下单时间' },
    ],
  },
};

// 图表颜色主题
export const CHART_THEMES = {
  DEFAULT: [
    '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
    '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
  ],
  BUSINESS: [
    '#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83',
    '#F79D84', '#748CAB', '#3E92CC', '#F4A261', '#E76F51'
  ],
  PASTEL: [
    '#FFB3BA', '#FFDFBA', '#FFFFBA', '#BAFFC9', '#BAE1FF',
    '#E1BAFF', '#FFBAE1', '#C9FFBA', '#BAFFDF', '#DFBAFF'
  ],
  DARK: [
    '#2C3E50', '#E74C3C', '#3498DB', '#F39C12', '#9B59B6',
    '#1ABC9C', '#34495E', '#E67E22', '#95A5A6', '#16A085'
  ],
};

// 报表导出格式
export const EXPORT_FORMATS = {
  PDF: {
    name: 'PDF',
    extension: 'pdf',
    mimeType: 'application/pdf',
    description: 'PDF格式，适合打印和分享',
  },
  EXCEL: {
    name: 'Excel',
    extension: 'xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    description: 'Excel格式，适合数据分析',
  },
  CSV: {
    name: 'CSV',
    extension: 'csv',
    mimeType: 'text/csv',
    description: 'CSV格式，适合数据导入',
  },
  PNG: {
    name: 'PNG',
    extension: 'png',
    mimeType: 'image/png',
    description: 'PNG图片格式',
  },
  HTML: {
    name: 'HTML',
    extension: 'html',
    mimeType: 'text/html',
    description: 'HTML格式，适合网页展示',
  },
};

// 获取报表配置
export function getReportConfig(reportId: string): Partial<ReportConfig> | undefined {
  return PREDEFINED_REPORTS[reportId];
}

// 获取数据源配置
export function getDataSourceConfig(dataSource: string) {
  return DATA_SOURCES[dataSource as keyof typeof DATA_SOURCES];
}

// 获取图表主题颜色
export function getChartThemeColors(theme: string = 'DEFAULT'): string[] {
  return CHART_THEMES[theme as keyof typeof CHART_THEMES] || CHART_THEMES.DEFAULT;
}

// 验证报表配置
export function validateReportConfig(config: Partial<ReportConfig>): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!config.name || config.name.trim().length === 0) {
    errors.push('报表名称不能为空');
  }

  if (!config.type) {
    errors.push('报表类型不能为空');
  }

  if (!config.dataSource) {
    errors.push('数据源不能为空');
  }

  if (!config.charts || config.charts.length === 0) {
    errors.push('至少需要一个图表');
  }

  if (config.charts) {
    config.charts.forEach((chart, index) => {
      if (!chart.title) {
        errors.push(`图表${index + 1}标题不能为空`);
      }
      if (!chart.type) {
        errors.push(`图表${index + 1}类型不能为空`);
      }
      if (!chart.dataKey) {
        errors.push(`图表${index + 1}数据键不能为空`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 生成默认布局
export function generateDefaultLayout(chartCount: number): LayoutConfig {
  const columns = Math.ceil(Math.sqrt(chartCount));
  const rows = Math.ceil(chartCount / columns);

  return {
    type: 'grid',
    columns,
    rows,
    gap: 16,
    padding: 16,
    responsive: true,
  };
}

// 格式化数值
export function formatValue(value: any, format?: string): string {
  if (value === null || value === undefined) {
    return '-';
  }

  if (!format) {
    return String(value);
  }

  switch (format) {
    case 'currency':
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
      }).format(Number(value));
    case 'percentage':
      return `${(Number(value) * 100).toFixed(2)}%`;
    case 'number':
      return new Intl.NumberFormat('zh-CN').format(Number(value));
    case 'date':
      return new Date(value).toLocaleDateString('zh-CN');
    case 'datetime':
      return new Date(value).toLocaleString('zh-CN');
    default:
      return String(value);
  }
}
