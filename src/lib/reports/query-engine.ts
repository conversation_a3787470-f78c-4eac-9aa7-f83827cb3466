/**
 * 报表数据查询引擎
 * 提供数据查询、聚合、过滤等功能
 */

import { 
  ReportConfig, 
  ReportData, 
  FilterConfig, 
  AggregationType,
  DATA_SOURCES,
  getDataSourceConfig,
} from './config';
import { prisma } from '@/lib/prisma';

// 查询条件接口
export interface QueryCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'between' | 'is_null' | 'is_not_null';
  value?: any;
  values?: any[];
}

// 聚合配置接口
export interface AggregationConfig {
  field: string;
  type: AggregationType;
  alias?: string;
}

// 分组配置接口
export interface GroupByConfig {
  field: string;
  alias?: string;
  dateFormat?: 'year' | 'month' | 'week' | 'day' | 'hour';
}

// 排序配置接口
export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

// 查询选项接口
export interface QueryOptions {
  conditions?: QueryCondition[];
  aggregations?: AggregationConfig[];
  groupBy?: GroupByConfig[];
  sort?: SortConfig[];
  limit?: number;
  offset?: number;
  includeMetadata?: boolean;
}

/**
 * 报表数据查询引擎类
 */
export class ReportQueryEngine {
  /**
   * 执行报表查询
   */
  async executeQuery(
    reportConfig: ReportConfig,
    parameters: Record<string, any> = {},
    options: QueryOptions = {}
  ): Promise<ReportData> {
    const startTime = Date.now();

    try {
      // 解析查询参数
      const parsedQuery = this.parseQuery(reportConfig.query, parameters);
      
      // 构建查询条件
      const queryConditions = this.buildQueryConditions(reportConfig.filters, parameters, options.conditions);
      
      // 执行数据查询
      const rawData = await this.executeRawQuery(
        reportConfig.dataSource,
        parsedQuery,
        queryConditions,
        options
      );

      // 处理数据聚合
      const aggregatedData = this.processAggregations(rawData, options.aggregations);
      
      // 应用分组
      const groupedData = this.applyGrouping(aggregatedData, options.groupBy);
      
      // 应用排序
      const sortedData = this.applySorting(groupedData, options.sort);
      
      // 应用分页
      const paginatedData = this.applyPagination(sortedData, options.limit, options.offset);
      
      // 计算汇总信息
      const summary = this.calculateSummary(paginatedData);

      const executionTime = Date.now() - startTime;

      return {
        reportId: reportConfig.id,
        generatedAt: new Date(),
        parameters,
        data: paginatedData,
        summary,
        metadata: {
          totalRecords: sortedData.length,
          executionTime,
          dataSource: reportConfig.dataSource,
          query: parsedQuery,
        },
      };
    } catch (error) {
      console.error('报表查询执行失败:', error);
      throw new Error(`报表查询执行失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 执行销售报表查询
   */
  async executeSalesQuery(
    startDate: Date,
    endDate: Date,
    filters: Record<string, any> = {}
  ): Promise<any[]> {
    const whereConditions: any = {
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
      status: {
        not: 'CANCELLED',
      },
    };

    // 应用过滤条件
    if (filters.customerId) {
      whereConditions.customerId = filters.customerId;
    }

    if (filters.productId) {
      whereConditions.orderItems = {
        some: {
          productId: filters.productId,
        },
      };
    }

    if (filters.country) {
      whereConditions.shippingAddress = {
        country: filters.country,
      };
    }

    const orders = await prisma.order.findMany({
      where: whereConditions,
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        customer: true,
        shippingAddress: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return this.transformSalesData(orders);
  }

  /**
   * 执行库存报表查询
   */
  async executeInventoryQuery(
    warehouseId?: string,
    productCategory?: string
  ): Promise<any[]> {
    const whereConditions: any = {};

    if (warehouseId) {
      whereConditions.warehouseId = warehouseId;
    }

    if (productCategory) {
      whereConditions.product = {
        category: productCategory,
      };
    }

    const inventory = await prisma.inventory.findMany({
      where: whereConditions,
      include: {
        product: {
          include: {
            category: true,
          },
        },
        warehouse: true,
      },
      orderBy: {
        product: {
          name: 'asc',
        },
      },
    });

    return this.transformInventoryData(inventory);
  }

  /**
   * 执行财务报表查询
   */
  async executeFinancialQuery(
    startDate: Date,
    endDate: Date,
    currency: string = 'USD'
  ): Promise<any[]> {
    // 收入数据
    const revenue = await prisma.order.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: {
          in: ['DELIVERED', 'COMPLETED'],
        },
        currency,
      },
      _sum: {
        totalAmount: true,
        subtotal: true,
        taxAmount: true,
        shippingAmount: true,
      },
      _count: {
        id: true,
      },
    });

    // 支出数据
    const expenses = await prisma.expense.aggregate({
      where: {
        date: {
          gte: startDate,
          lte: endDate,
        },
        currency,
      },
      _sum: {
        amount: true,
      },
    });

    // 退款数据
    const refunds = await prisma.refund.aggregate({
      where: {
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
        status: 'COMPLETED',
        currency,
      },
      _sum: {
        amount: true,
      },
    });

    return this.transformFinancialData({
      revenue: revenue._sum.totalAmount || 0,
      expenses: expenses._sum.amount || 0,
      refunds: refunds._sum.amount || 0,
      orderCount: revenue._count.id,
      subtotal: revenue._sum.subtotal || 0,
      taxAmount: revenue._sum.taxAmount || 0,
      shippingAmount: revenue._sum.shippingAmount || 0,
    });
  }

  /**
   * 执行客户分析查询
   */
  async executeCustomerAnalysisQuery(
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    // 新客户数据
    const newCustomers = await prisma.customer.count({
      where: {
        registeredAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // 活跃客户数据
    const activeCustomers = await prisma.customer.count({
      where: {
        orders: {
          some: {
            createdAt: {
              gte: startDate,
              lte: endDate,
            },
          },
        },
      },
    });

    // 客户生命周期价值
    const customerLTV = await prisma.customer.findMany({
      include: {
        orders: {
          where: {
            status: {
              in: ['DELIVERED', 'COMPLETED'],
            },
          },
          select: {
            totalAmount: true,
            createdAt: true,
          },
        },
      },
    });

    return this.transformCustomerData({
      newCustomers,
      activeCustomers,
      customerLTV,
    });
  }

  /**
   * 解析查询语句
   */
  private parseQuery(query: string, parameters: Record<string, any>): string {
    let parsedQuery = query;

    // 替换参数占位符
    Object.entries(parameters).forEach(([key, value]) => {
      const placeholder = new RegExp(`\\$\\{${key}\\}`, 'g');
      parsedQuery = parsedQuery.replace(placeholder, String(value));
    });

    return parsedQuery;
  }

  /**
   * 构建查询条件
   */
  private buildQueryConditions(
    filters: FilterConfig[],
    parameters: Record<string, any>,
    additionalConditions?: QueryCondition[]
  ): QueryCondition[] {
    const conditions: QueryCondition[] = [];

    // 处理过滤器
    filters.forEach(filter => {
      const paramValue = parameters[filter.field];
      if (paramValue !== undefined && paramValue !== null && paramValue !== '') {
        conditions.push({
          field: filter.field,
          operator: filter.operator,
          value: paramValue,
        });
      }
    });

    // 添加额外条件
    if (additionalConditions) {
      conditions.push(...additionalConditions);
    }

    return conditions;
  }

  /**
   * 执行原始查询
   */
  private async executeRawQuery(
    dataSource: string,
    query: string,
    conditions: QueryCondition[],
    options: QueryOptions
  ): Promise<any[]> {
    // 这里简化实现，实际应该根据数据源类型执行不同的查询
    const dataSourceConfig = getDataSourceConfig(dataSource);
    if (!dataSourceConfig) {
      throw new Error(`未知的数据源: ${dataSource}`);
    }

    // 构建Prisma查询
    const whereClause = this.buildWhereClause(conditions);
    
    switch (dataSource) {
      case 'ORDERS':
        return await prisma.order.findMany({
          where: whereClause,
          include: {
            orderItems: {
              include: {
                product: true,
              },
            },
            customer: true,
            shippingAddress: true,
          },
        });
        
      case 'PRODUCTS':
        return await prisma.product.findMany({
          where: whereClause,
          include: {
            category: true,
          },
        });
        
      case 'INVENTORY':
        return await prisma.inventory.findMany({
          where: whereClause,
          include: {
            product: true,
            warehouse: true,
          },
        });
        
      case 'CUSTOMERS':
        return await prisma.customer.findMany({
          where: whereClause,
          include: {
            orders: true,
            addresses: true,
          },
        });
        
      default:
        throw new Error(`不支持的数据源: ${dataSource}`);
    }
  }

  /**
   * 构建WHERE子句
   */
  private buildWhereClause(conditions: QueryCondition[]): any {
    const whereClause: any = {};

    conditions.forEach(condition => {
      switch (condition.operator) {
        case 'equals':
          whereClause[condition.field] = condition.value;
          break;
        case 'not_equals':
          whereClause[condition.field] = { not: condition.value };
          break;
        case 'greater_than':
          whereClause[condition.field] = { gt: condition.value };
          break;
        case 'less_than':
          whereClause[condition.field] = { lt: condition.value };
          break;
        case 'contains':
          whereClause[condition.field] = { contains: condition.value, mode: 'insensitive' };
          break;
        case 'in':
          whereClause[condition.field] = { in: condition.values || [condition.value] };
          break;
        case 'between':
          if (condition.values && condition.values.length === 2) {
            whereClause[condition.field] = {
              gte: condition.values[0],
              lte: condition.values[1],
            };
          }
          break;
        case 'is_null':
          whereClause[condition.field] = null;
          break;
        case 'is_not_null':
          whereClause[condition.field] = { not: null };
          break;
      }
    });

    return whereClause;
  }

  /**
   * 处理数据聚合
   */
  private processAggregations(data: any[], aggregations?: AggregationConfig[]): any[] {
    if (!aggregations || aggregations.length === 0) {
      return data;
    }

    // 简化实现，实际应该根据聚合类型进行计算
    return data.map(item => {
      const aggregatedItem = { ...item };
      
      aggregations.forEach(agg => {
        const value = this.getNestedValue(item, agg.field);
        const alias = agg.alias || `${agg.field}_${agg.type}`;
        
        switch (agg.type) {
          case AggregationType.SUM:
            aggregatedItem[alias] = Number(value) || 0;
            break;
          case AggregationType.COUNT:
            aggregatedItem[alias] = Array.isArray(value) ? value.length : 1;
            break;
          case AggregationType.AVG:
            aggregatedItem[alias] = Number(value) || 0;
            break;
          case AggregationType.MAX:
            aggregatedItem[alias] = Number(value) || 0;
            break;
          case AggregationType.MIN:
            aggregatedItem[alias] = Number(value) || 0;
            break;
          default:
            aggregatedItem[alias] = value;
        }
      });
      
      return aggregatedItem;
    });
  }

  /**
   * 应用分组
   */
  private applyGrouping(data: any[], groupBy?: GroupByConfig[]): any[] {
    if (!groupBy || groupBy.length === 0) {
      return data;
    }

    // 简化实现
    const grouped = new Map();
    
    data.forEach(item => {
      const groupKey = groupBy.map(group => {
        const value = this.getNestedValue(item, group.field);
        
        if (group.dateFormat && value instanceof Date) {
          return this.formatDateForGrouping(value, group.dateFormat);
        }
        
        return String(value);
      }).join('|');
      
      if (!grouped.has(groupKey)) {
        grouped.set(groupKey, []);
      }
      
      grouped.get(groupKey).push(item);
    });

    return Array.from(grouped.values()).map(group => group[0]);
  }

  /**
   * 应用排序
   */
  private applySorting(data: any[], sort?: SortConfig[]): any[] {
    if (!sort || sort.length === 0) {
      return data;
    }

    return data.sort((a, b) => {
      for (const sortConfig of sort) {
        const aValue = this.getNestedValue(a, sortConfig.field);
        const bValue = this.getNestedValue(b, sortConfig.field);
        
        let comparison = 0;
        
        if (aValue < bValue) {
          comparison = -1;
        } else if (aValue > bValue) {
          comparison = 1;
        }
        
        if (comparison !== 0) {
          return sortConfig.direction === 'desc' ? -comparison : comparison;
        }
      }
      
      return 0;
    });
  }

  /**
   * 应用分页
   */
  private applyPagination(data: any[], limit?: number, offset?: number): any[] {
    if (!limit && !offset) {
      return data;
    }

    const start = offset || 0;
    const end = limit ? start + limit : undefined;
    
    return data.slice(start, end);
  }

  /**
   * 计算汇总信息
   */
  private calculateSummary(data: any[]): Record<string, any> {
    if (data.length === 0) {
      return {};
    }

    const summary: Record<string, any> = {
      totalRecords: data.length,
    };

    // 计算数值字段的汇总
    const numericFields = ['totalAmount', 'quantity', 'price', 'amount'];
    
    numericFields.forEach(field => {
      const values = data
        .map(item => this.getNestedValue(item, field))
        .filter(value => typeof value === 'number' && !isNaN(value));
      
      if (values.length > 0) {
        summary[`${field}_sum`] = values.reduce((sum, value) => sum + value, 0);
        summary[`${field}_avg`] = summary[`${field}_sum`] / values.length;
        summary[`${field}_max`] = Math.max(...values);
        summary[`${field}_min`] = Math.min(...values);
      }
    });

    return summary;
  }

  /**
   * 获取嵌套值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  /**
   * 格式化日期用于分组
   */
  private formatDateForGrouping(date: Date, format: string): string {
    switch (format) {
      case 'year':
        return date.getFullYear().toString();
      case 'month':
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      case 'week':
        const weekNumber = this.getWeekNumber(date);
        return `${date.getFullYear()}-W${String(weekNumber).padStart(2, '0')}`;
      case 'day':
        return date.toISOString().split('T')[0];
      case 'hour':
        return `${date.toISOString().split('T')[0]} ${String(date.getHours()).padStart(2, '0')}:00`;
      default:
        return date.toISOString();
    }
  }

  /**
   * 获取周数
   */
  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  /**
   * 转换销售数据
   */
  private transformSalesData(orders: any[]): any[] {
    return orders.map(order => ({
      orderId: order.id,
      orderNumber: order.orderNumber,
      customerId: order.customerId,
      customerName: `${order.customer?.firstName || ''} ${order.customer?.lastName || ''}`.trim(),
      totalAmount: order.totalAmount,
      subtotal: order.subtotal,
      taxAmount: order.taxAmount,
      shippingAmount: order.shippingAmount,
      status: order.status,
      createdAt: order.createdAt,
      country: order.shippingAddress?.country,
      itemCount: order.orderItems?.length || 0,
      products: order.orderItems?.map((item: any) => ({
        productId: item.productId,
        productName: item.product?.name,
        quantity: item.quantity,
        price: item.price,
        total: item.quantity * item.price,
      })) || [],
    }));
  }

  /**
   * 转换库存数据
   */
  private transformInventoryData(inventory: any[]): any[] {
    return inventory.map(item => ({
      inventoryId: item.id,
      productId: item.productId,
      productName: item.product?.name,
      sku: item.product?.sku,
      category: item.product?.category?.name,
      warehouseId: item.warehouseId,
      warehouseName: item.warehouse?.name,
      quantity: item.quantity,
      reservedQuantity: item.reservedQuantity,
      availableQuantity: item.availableQuantity,
      reorderLevel: item.reorderLevel,
      maxLevel: item.maxLevel,
      lastUpdated: item.lastUpdated,
      value: item.quantity * (item.product?.costPrice || 0),
    }));
  }

  /**
   * 转换财务数据
   */
  private transformFinancialData(data: any): any[] {
    const profit = data.revenue - data.expenses - data.refunds;
    const profitMargin = data.revenue > 0 ? (profit / data.revenue) * 100 : 0;
    const averageOrderValue = data.orderCount > 0 ? data.revenue / data.orderCount : 0;

    return [{
      revenue: data.revenue,
      expenses: data.expenses,
      refunds: data.refunds,
      profit,
      profitMargin,
      orderCount: data.orderCount,
      averageOrderValue,
      subtotal: data.subtotal,
      taxAmount: data.taxAmount,
      shippingAmount: data.shippingAmount,
    }];
  }

  /**
   * 转换客户数据
   */
  private transformCustomerData(data: any): any[] {
    const customerLTVData = data.customerLTV.map((customer: any) => {
      const totalValue = customer.orders.reduce((sum: number, order: any) => sum + order.totalAmount, 0);
      const orderCount = customer.orders.length;
      const firstOrderDate = customer.orders.length > 0 
        ? new Date(Math.min(...customer.orders.map((o: any) => new Date(o.createdAt).getTime())))
        : null;
      const lastOrderDate = customer.orders.length > 0
        ? new Date(Math.max(...customer.orders.map((o: any) => new Date(o.createdAt).getTime())))
        : null;

      return {
        customerId: customer.id,
        customerName: `${customer.firstName || ''} ${customer.lastName || ''}`.trim(),
        email: customer.email,
        totalValue,
        orderCount,
        averageOrderValue: orderCount > 0 ? totalValue / orderCount : 0,
        firstOrderDate,
        lastOrderDate,
        registeredAt: customer.registeredAt,
      };
    });

    return [{
      newCustomers: data.newCustomers,
      activeCustomers: data.activeCustomers,
      totalCustomers: data.customerLTV.length,
      customerLTV: customerLTVData,
    }];
  }
}

// 导出单例实例
export const reportQueryEngine = new ReportQueryEngine();
