/**
 * 报表管理服务
 * 提供报表创建、执行、导出、调度等功能
 */

import { 
  ReportConfig, 
  ReportData, 
  ReportType,
  ReportStatus,
  ReportPeriod,
  ChartConfig,
  ScheduleConfig,
  PREDEFINED_REPORTS,
  validateReportConfig,
  generateDefaultLayout,
  EXPORT_FORMATS,
} from './config';
import { reportQueryEngine } from './query-engine';
import { prisma } from '@/lib/prisma';
import * as XLSX from 'xlsx';
import PDFDocument from 'pdfkit';

// 报表创建请求
export interface CreateReportRequest {
  name: string;
  description?: string;
  type: ReportType;
  category: string;
  dataSource: string;
  query: string;
  parameters?: any[];
  charts: ChartConfig[];
  filters?: any[];
  schedule?: ScheduleConfig;
  permissions?: string[];
}

// 报表执行请求
export interface ExecuteReportRequest {
  reportId: string;
  parameters?: Record<string, any>;
  filters?: Record<string, any>;
  format?: 'json' | 'excel' | 'csv' | 'pdf';
  includeCharts?: boolean;
}

// 报表导出选项
export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv' | 'png' | 'html';
  includeCharts?: boolean;
  includeData?: boolean;
  template?: string;
  orientation?: 'portrait' | 'landscape';
  pageSize?: 'A4' | 'A3' | 'Letter';
}

/**
 * 报表管理服务类
 */
export class ReportService {
  /**
   * 创建报表
   */
  async createReport(
    request: CreateReportRequest,
    userId: string = 'system'
  ): Promise<{ success: boolean; report?: ReportConfig; error?: string }> {
    try {
      // 验证报表配置
      const validation = validateReportConfig(request);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', '),
        };
      }

      // 检查报表名称是否已存在
      const existingReport = await prisma.report.findFirst({
        where: {
          name: request.name,
          status: { not: ReportStatus.ARCHIVED },
        },
      });

      if (existingReport) {
        return {
          success: false,
          error: '报表名称已存在',
        };
      }

      // 生成默认布局
      const layout = generateDefaultLayout(request.charts.length);

      // 创建报表
      const createdReport = await prisma.report.create({
        data: {
          ...request,
          layout,
          status: ReportStatus.DRAFT,
          createdBy: userId,
          updatedBy: userId,
        },
      });

      return {
        success: true,
        report: this.mapDatabaseReportToConfig(createdReport),
      };
    } catch (error) {
      console.error('创建报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建报表失败',
      };
    }
  }

  /**
   * 更新报表
   */
  async updateReport(
    reportId: string,
    updates: Partial<CreateReportRequest>,
    userId: string = 'system'
  ): Promise<{ success: boolean; report?: ReportConfig; error?: string }> {
    try {
      // 检查报表是否存在
      const existingReport = await prisma.report.findUnique({
        where: { id: reportId },
      });

      if (!existingReport) {
        return {
          success: false,
          error: '报表不存在',
        };
      }

      // 验证更新数据
      if (Object.keys(updates).length > 0) {
        const validation = validateReportConfig(updates);
        if (!validation.isValid) {
          return {
            success: false,
            error: validation.errors.join(', '),
          };
        }
      }

      // 更新报表
      const updatedReport = await prisma.report.update({
        where: { id: reportId },
        data: {
          ...updates,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });

      return {
        success: true,
        report: this.mapDatabaseReportToConfig(updatedReport),
      };
    } catch (error) {
      console.error('更新报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新报表失败',
      };
    }
  }

  /**
   * 删除报表
   */
  async deleteReport(
    reportId: string,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 软删除：将状态设置为ARCHIVED
      await prisma.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.ARCHIVED,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });

      return { success: true };
    } catch (error) {
      console.error('删除报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除报表失败',
      };
    }
  }

  /**
   * 获取报表列表
   */
  async getReports(
    filters: {
      type?: ReportType;
      category?: string;
      status?: ReportStatus;
      search?: string;
      userId?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ success: boolean; reports?: ReportConfig[]; total?: number; error?: string }> {
    try {
      const {
        type,
        category,
        status,
        search,
        userId,
        page = 1,
        limit = 20,
      } = filters;

      const whereConditions: any = {};

      if (type) {
        whereConditions.type = type;
      }

      if (category) {
        whereConditions.category = category;
      }

      if (status) {
        whereConditions.status = status;
      } else {
        whereConditions.status = { not: ReportStatus.ARCHIVED };
      }

      if (search) {
        whereConditions.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (userId) {
        whereConditions.OR = [
          { createdBy: userId },
          { permissions: { has: userId } },
          { permissions: { has: 'public' } },
        ];
      }

      const [reports, total] = await Promise.all([
        prisma.report.findMany({
          where: whereConditions,
          orderBy: { updatedAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        prisma.report.count({ where: whereConditions }),
      ]);

      return {
        success: true,
        reports: reports.map(report => this.mapDatabaseReportToConfig(report)),
        total,
      };
    } catch (error) {
      console.error('获取报表列表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取报表列表失败',
      };
    }
  }

  /**
   * 获取报表详情
   */
  async getReport(reportId: string): Promise<{ success: boolean; report?: ReportConfig; error?: string }> {
    try {
      const report = await prisma.report.findUnique({
        where: { id: reportId },
      });

      if (!report) {
        return {
          success: false,
          error: '报表不存在',
        };
      }

      return {
        success: true,
        report: this.mapDatabaseReportToConfig(report),
      };
    } catch (error) {
      console.error('获取报表详情失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取报表详情失败',
      };
    }
  }

  /**
   * 执行报表
   */
  async executeReport(
    request: ExecuteReportRequest
  ): Promise<{ success: boolean; data?: ReportData; error?: string }> {
    try {
      const { reportId, parameters = {}, filters = {} } = request;

      // 获取报表配置
      const reportResult = await this.getReport(reportId);
      if (!reportResult.success || !reportResult.report) {
        return {
          success: false,
          error: reportResult.error || '报表不存在',
        };
      }

      const reportConfig = reportResult.report;

      // 合并参数和过滤器
      const allParameters = { ...parameters, ...filters };

      // 执行查询
      const reportData = await reportQueryEngine.executeQuery(
        reportConfig,
        allParameters
      );

      // 保存执行记录
      await this.saveExecutionRecord(reportId, allParameters, reportData);

      return {
        success: true,
        data: reportData,
      };
    } catch (error) {
      console.error('执行报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行报表失败',
      };
    }
  }

  /**
   * 导出报表
   */
  async exportReport(
    reportId: string,
    options: ExportOptions,
    parameters: Record<string, any> = {}
  ): Promise<{ success: boolean; data?: Buffer; filename?: string; error?: string }> {
    try {
      // 执行报表
      const executeResult = await this.executeReport({
        reportId,
        parameters,
        includeCharts: options.includeCharts,
      });

      if (!executeResult.success || !executeResult.data) {
        return {
          success: false,
          error: executeResult.error || '报表执行失败',
        };
      }

      const reportData = executeResult.data;
      const reportConfig = (await this.getReport(reportId)).report!;

      // 根据格式导出
      switch (options.format) {
        case 'excel':
          return await this.exportToExcel(reportConfig, reportData);
        case 'csv':
          return await this.exportToCSV(reportConfig, reportData);
        case 'pdf':
          return await this.exportToPDF(reportConfig, reportData, options);
        case 'html':
          return await this.exportToHTML(reportConfig, reportData);
        default:
          return {
            success: false,
            error: `不支持的导出格式: ${options.format}`,
          };
      }
    } catch (error) {
      console.error('导出报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出报表失败',
      };
    }
  }

  /**
   * 发布报表
   */
  async publishReport(
    reportId: string,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await prisma.report.update({
        where: { id: reportId },
        data: {
          status: ReportStatus.PUBLISHED,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });

      return { success: true };
    } catch (error) {
      console.error('发布报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '发布报表失败',
      };
    }
  }

  /**
   * 调度报表
   */
  async scheduleReport(
    reportId: string,
    schedule: ScheduleConfig,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await prisma.report.update({
        where: { id: reportId },
        data: {
          schedule,
          status: ReportStatus.SCHEDULED,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });

      // 这里应该添加到调度系统
      // await this.addToScheduler(reportId, schedule);

      return { success: true };
    } catch (error) {
      console.error('调度报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '调度报表失败',
      };
    }
  }

  /**
   * 获取预定义报表
   */
  getPredefinedReports(): Array<{ id: string; config: Partial<ReportConfig> }> {
    return Object.entries(PREDEFINED_REPORTS).map(([id, config]) => ({
      id,
      config,
    }));
  }

  /**
   * 创建预定义报表
   */
  async createPredefinedReport(
    predefinedId: string,
    userId: string = 'system'
  ): Promise<{ success: boolean; report?: ReportConfig; error?: string }> {
    try {
      const predefinedConfig = PREDEFINED_REPORTS[predefinedId];
      if (!predefinedConfig) {
        return {
          success: false,
          error: '预定义报表不存在',
        };
      }

      // 创建报表
      const createRequest: CreateReportRequest = {
        name: predefinedConfig.name!,
        description: predefinedConfig.description,
        type: predefinedConfig.type!,
        category: predefinedConfig.category!,
        dataSource: predefinedConfig.dataSource || 'ORDERS',
        query: predefinedConfig.query || 'SELECT * FROM orders',
        charts: predefinedConfig.charts || [],
        parameters: predefinedConfig.parameters,
        filters: predefinedConfig.filters,
        permissions: ['public'],
      };

      return await this.createReport(createRequest, userId);
    } catch (error) {
      console.error('创建预定义报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建预定义报表失败',
      };
    }
  }

  /**
   * 导出到Excel
   */
  private async exportToExcel(
    reportConfig: ReportConfig,
    reportData: ReportData
  ): Promise<{ success: boolean; data?: Buffer; filename?: string; error?: string }> {
    try {
      const workbook = XLSX.utils.book_new();
      
      // 添加数据工作表
      const worksheet = XLSX.utils.json_to_sheet(reportData.data);
      XLSX.utils.book_append_sheet(workbook, worksheet, '数据');

      // 添加汇总工作表
      if (reportData.summary) {
        const summaryWorksheet = XLSX.utils.json_to_sheet([reportData.summary]);
        XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '汇总');
      }

      // 生成Excel文件
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      
      const filename = `${reportConfig.name}_${new Date().toISOString().split('T')[0]}.xlsx`;

      return {
        success: true,
        data: buffer,
        filename,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Excel导出失败',
      };
    }
  }

  /**
   * 导出到CSV
   */
  private async exportToCSV(
    reportConfig: ReportConfig,
    reportData: ReportData
  ): Promise<{ success: boolean; data?: Buffer; filename?: string; error?: string }> {
    try {
      const worksheet = XLSX.utils.json_to_sheet(reportData.data);
      const csv = XLSX.utils.sheet_to_csv(worksheet);
      
      const filename = `${reportConfig.name}_${new Date().toISOString().split('T')[0]}.csv`;

      return {
        success: true,
        data: Buffer.from(csv, 'utf-8'),
        filename,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'CSV导出失败',
      };
    }
  }

  /**
   * 导出到PDF
   */
  private async exportToPDF(
    reportConfig: ReportConfig,
    reportData: ReportData,
    options: ExportOptions
  ): Promise<{ success: boolean; data?: Buffer; filename?: string; error?: string }> {
    try {
      const doc = new PDFDocument({
        size: options.pageSize || 'A4',
        layout: options.orientation || 'portrait',
      });

      const buffers: Buffer[] = [];
      doc.on('data', buffers.push.bind(buffers));

      // 添加标题
      doc.fontSize(20).text(reportConfig.name, 50, 50);
      doc.fontSize(12).text(`生成时间: ${reportData.generatedAt.toLocaleString('zh-CN')}`, 50, 80);

      // 添加汇总信息
      if (reportData.summary) {
        doc.fontSize(16).text('汇总信息', 50, 120);
        let yPosition = 150;
        
        Object.entries(reportData.summary).forEach(([key, value]) => {
          doc.fontSize(12).text(`${key}: ${value}`, 50, yPosition);
          yPosition += 20;
        });
      }

      // 添加数据表格（简化实现）
      if (options.includeData && reportData.data.length > 0) {
        doc.addPage();
        doc.fontSize(16).text('详细数据', 50, 50);
        
        // 这里应该添加表格渲染逻辑
        doc.fontSize(10).text(JSON.stringify(reportData.data.slice(0, 10), null, 2), 50, 80);
      }

      doc.end();

      return new Promise((resolve) => {
        doc.on('end', () => {
          const pdfBuffer = Buffer.concat(buffers);
          const filename = `${reportConfig.name}_${new Date().toISOString().split('T')[0]}.pdf`;
          
          resolve({
            success: true,
            data: pdfBuffer,
            filename,
          });
        });
      });
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'PDF导出失败',
      };
    }
  }

  /**
   * 导出到HTML
   */
  private async exportToHTML(
    reportConfig: ReportConfig,
    reportData: ReportData
  ): Promise<{ success: boolean; data?: Buffer; filename?: string; error?: string }> {
    try {
      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>${reportConfig.name}</title>
          <meta charset="utf-8">
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; }
            table { border-collapse: collapse; width: 100%; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; }
            .summary { background-color: #f9f9f9; padding: 15px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <h1>${reportConfig.name}</h1>
          <p>生成时间: ${reportData.generatedAt.toLocaleString('zh-CN')}</p>
          
          ${reportData.summary ? `
            <div class="summary">
              <h2>汇总信息</h2>
              ${Object.entries(reportData.summary).map(([key, value]) => 
                `<p><strong>${key}:</strong> ${value}</p>`
              ).join('')}
            </div>
          ` : ''}
          
          <h2>详细数据</h2>
          <table>
            <thead>
              <tr>
                ${Object.keys(reportData.data[0] || {}).map(key => `<th>${key}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
              ${reportData.data.map(row => `
                <tr>
                  ${Object.values(row).map(value => `<td>${value}</td>`).join('')}
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
        </html>
      `;

      const filename = `${reportConfig.name}_${new Date().toISOString().split('T')[0]}.html`;

      return {
        success: true,
        data: Buffer.from(html, 'utf-8'),
        filename,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'HTML导出失败',
      };
    }
  }

  /**
   * 保存执行记录
   */
  private async saveExecutionRecord(
    reportId: string,
    parameters: Record<string, any>,
    reportData: ReportData
  ): Promise<void> {
    try {
      await prisma.reportExecution.create({
        data: {
          reportId,
          parameters,
          executionTime: reportData.metadata.executionTime,
          recordCount: reportData.metadata.totalRecords,
          status: 'SUCCESS',
          executedAt: reportData.generatedAt,
          executedBy: 'system',
        },
      });
    } catch (error) {
      console.error('保存执行记录失败:', error);
    }
  }

  /**
   * 映射数据库报表到配置
   */
  private mapDatabaseReportToConfig(dbReport: any): ReportConfig {
    return {
      id: dbReport.id,
      name: dbReport.name,
      description: dbReport.description,
      type: dbReport.type as ReportType,
      category: dbReport.category,
      dataSource: dbReport.dataSource,
      query: dbReport.query,
      parameters: dbReport.parameters || [],
      charts: dbReport.charts || [],
      filters: dbReport.filters || [],
      layout: dbReport.layout || generateDefaultLayout(dbReport.charts?.length || 0),
      schedule: dbReport.schedule,
      permissions: dbReport.permissions || [],
      status: dbReport.status as ReportStatus,
      createdAt: dbReport.createdAt,
      updatedAt: dbReport.updatedAt,
      createdBy: dbReport.createdBy,
      updatedBy: dbReport.updatedBy,
    };
  }
}

// 导出单例实例
export const reportService = new ReportService();
