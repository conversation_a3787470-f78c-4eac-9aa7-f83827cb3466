/**
 * API限流工具
 * 基于Redis实现的分布式限流机制
 */

import { Redis } from 'ioredis';

// Redis客户端实例
let redis: Redis | null = null;

// 初始化Redis连接
function getRedisClient(): Redis {
  if (!redis) {
    redis = new Redis(process.env.REDIS_URL || 'redis://localhost:6379', {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
    });

    redis.on('error', (error) => {
      console.error('Redis连接错误:', error);
    });

    redis.on('connect', () => {
      console.log('Redis连接成功');
    });
  }

  return redis;
}

// 限流配置接口
interface RateLimitConfig {
  windowMs: number;     // 时间窗口（毫秒）
  maxRequests: number;  // 最大请求数
  keyGenerator?: (identifier: string) => string; // 键生成器
  skipSuccessfulRequests?: boolean; // 是否跳过成功请求
  skipFailedRequests?: boolean;     // 是否跳过失败请求
}

// 默认限流配置
const DEFAULT_CONFIG: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15分钟
  maxRequests: 100,         // 100次请求
  keyGenerator: (identifier: string) => `rate_limit:${identifier}`,
};

/**
 * 基础限流函数
 */
export async function rateLimit(
  identifier: string,
  config: Partial<RateLimitConfig> = {}
): Promise<boolean> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const redis = getRedisClient();

  try {
    const key = finalConfig.keyGenerator!(identifier);
    const window = Math.floor(Date.now() / finalConfig.windowMs);
    const windowKey = `${key}:${window}`;

    // 使用Redis管道提高性能
    const pipeline = redis.pipeline();
    pipeline.incr(windowKey);
    pipeline.expire(windowKey, Math.ceil(finalConfig.windowMs / 1000));
    
    const results = await pipeline.exec();
    
    if (!results || results.length < 1) {
      throw new Error('Redis管道执行失败');
    }

    const count = results[0][1] as number;
    return count <= finalConfig.maxRequests;
  } catch (error) {
    console.error('限流检查失败:', error);
    // 在Redis不可用时，默认允许请求通过
    return true;
  }
}

/**
 * API限流中间件
 */
export async function apiRateLimit(identifier: string): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: number;
}> {
  const config = {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '900000'), // 15分钟
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  };

  const redis = getRedisClient();

  try {
    const key = `api_rate_limit:${identifier}`;
    const window = Math.floor(Date.now() / config.windowMs);
    const windowKey = `${key}:${window}`;

    const pipeline = redis.pipeline();
    pipeline.incr(windowKey);
    pipeline.expire(windowKey, Math.ceil(config.windowMs / 1000));
    
    const results = await pipeline.exec();
    
    if (!results || results.length < 1) {
      throw new Error('Redis管道执行失败');
    }

    const count = results[0][1] as number;
    const remaining = Math.max(0, config.maxRequests - count);
    const resetTime = (window + 1) * config.windowMs;

    return {
      allowed: count <= config.maxRequests,
      remaining,
      resetTime,
    };
  } catch (error) {
    console.error('API限流检查失败:', error);
    return {
      allowed: true,
      remaining: config.maxRequests,
      resetTime: Date.now() + config.windowMs,
    };
  }
}

/**
 * 登录限流
 */
export async function loginRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 15 * 60 * 1000, // 15分钟
    maxRequests: 5,           // 5次登录尝试
    keyGenerator: (id: string) => `login_rate_limit:${id}`,
  });
}

/**
 * 密码重置限流
 */
export async function passwordResetRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 60 * 60 * 1000, // 1小时
    maxRequests: 3,           // 3次密码重置请求
    keyGenerator: (id: string) => `password_reset_rate_limit:${id}`,
  });
}

/**
 * 邮件发送限流
 */
export async function emailRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 1,           // 1封邮件
    keyGenerator: (id: string) => `email_rate_limit:${id}`,
  });
}

/**
 * 短信发送限流
 */
export async function smsRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 1,           // 1条短信
    keyGenerator: (id: string) => `sms_rate_limit:${id}`,
  });
}

/**
 * 文件上传限流
 */
export async function uploadRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 10,          // 10次上传
    keyGenerator: (id: string) => `upload_rate_limit:${id}`,
  });
}

/**
 * 搜索限流
 */
export async function searchRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 60 * 1000,      // 1分钟
    maxRequests: 30,          // 30次搜索
    keyGenerator: (id: string) => `search_rate_limit:${id}`,
  });
}

/**
 * 导出限流
 */
export async function exportRateLimit(identifier: string): Promise<boolean> {
  return rateLimit(identifier, {
    windowMs: 60 * 60 * 1000, // 1小时
    maxRequests: 5,           // 5次导出
    keyGenerator: (id: string) => `export_rate_limit:${id}`,
  });
}

/**
 * 清除限流记录
 */
export async function clearRateLimit(identifier: string, type: string = 'default'): Promise<void> {
  const redis = getRedisClient();
  
  try {
    const pattern = `${type}_rate_limit:${identifier}:*`;
    const keys = await redis.keys(pattern);
    
    if (keys.length > 0) {
      await redis.del(...keys);
    }
  } catch (error) {
    console.error('清除限流记录失败:', error);
  }
}

/**
 * 获取限流状态
 */
export async function getRateLimitStatus(
  identifier: string,
  config: Partial<RateLimitConfig> = {}
): Promise<{
  remaining: number;
  resetTime: number;
  totalRequests: number;
}> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const redis = getRedisClient();

  try {
    const key = finalConfig.keyGenerator!(identifier);
    const window = Math.floor(Date.now() / finalConfig.windowMs);
    const windowKey = `${key}:${window}`;

    const count = await redis.get(windowKey);
    const totalRequests = count ? parseInt(count) : 0;
    const remaining = Math.max(0, finalConfig.maxRequests - totalRequests);
    const resetTime = (window + 1) * finalConfig.windowMs;

    return {
      remaining,
      resetTime,
      totalRequests,
    };
  } catch (error) {
    console.error('获取限流状态失败:', error);
    return {
      remaining: finalConfig.maxRequests,
      resetTime: Date.now() + finalConfig.windowMs,
      totalRequests: 0,
    };
  }
}

/**
 * 滑动窗口限流
 */
export async function slidingWindowRateLimit(
  identifier: string,
  windowMs: number,
  maxRequests: number
): Promise<boolean> {
  const redis = getRedisClient();

  try {
    const key = `sliding_rate_limit:${identifier}`;
    const now = Date.now();
    const windowStart = now - windowMs;

    // 使用Redis事务确保原子性
    const multi = redis.multi();
    
    // 移除过期的请求记录
    multi.zremrangebyscore(key, '-inf', windowStart);
    
    // 添加当前请求
    multi.zadd(key, now, `${now}-${Math.random()}`);
    
    // 获取当前窗口内的请求数量
    multi.zcard(key);
    
    // 设置过期时间
    multi.expire(key, Math.ceil(windowMs / 1000));

    const results = await multi.exec();
    
    if (!results || results.length < 3) {
      throw new Error('Redis事务执行失败');
    }

    const count = results[2][1] as number;
    return count <= maxRequests;
  } catch (error) {
    console.error('滑动窗口限流检查失败:', error);
    return true;
  }
}

/**
 * 令牌桶限流
 */
export async function tokenBucketRateLimit(
  identifier: string,
  capacity: number,
  refillRate: number,
  tokensRequested: number = 1
): Promise<boolean> {
  const redis = getRedisClient();

  try {
    const key = `token_bucket:${identifier}`;
    const now = Date.now();

    // Lua脚本实现令牌桶算法
    const luaScript = `
      local key = KEYS[1]
      local capacity = tonumber(ARGV[1])
      local refill_rate = tonumber(ARGV[2])
      local tokens_requested = tonumber(ARGV[3])
      local now = tonumber(ARGV[4])
      
      local bucket = redis.call('HMGET', key, 'tokens', 'last_refill')
      local tokens = tonumber(bucket[1]) or capacity
      local last_refill = tonumber(bucket[2]) or now
      
      -- 计算需要添加的令牌数
      local time_passed = (now - last_refill) / 1000
      local tokens_to_add = math.floor(time_passed * refill_rate)
      tokens = math.min(capacity, tokens + tokens_to_add)
      
      -- 检查是否有足够的令牌
      if tokens >= tokens_requested then
        tokens = tokens - tokens_requested
        redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', key, 3600)
        return 1
      else
        redis.call('HMSET', key, 'tokens', tokens, 'last_refill', now)
        redis.call('EXPIRE', key, 3600)
        return 0
      end
    `;

    const result = await redis.eval(
      luaScript,
      1,
      key,
      capacity.toString(),
      refillRate.toString(),
      tokensRequested.toString(),
      now.toString()
    ) as number;

    return result === 1;
  } catch (error) {
    console.error('令牌桶限流检查失败:', error);
    return true;
  }
}

/**
 * 关闭Redis连接
 */
export async function closeRedisConnection(): Promise<void> {
  if (redis) {
    await redis.quit();
    redis = null;
  }
}
