/**
 * AI模块类型定义
 * 定义所有AI服务的请求和响应类型
 */

// 基础AI服务接口
export interface BaseAIService {
  initialize(): Promise<void>;
  isReady(): boolean;
  dispose(): void;
}

// ==================== 商品推荐系统类型 ====================

export interface RecommendationRequest {
  userId: string;
  productId?: string;
  category?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  limit?: number;
  excludeIds?: string[];
}

export interface RecommendationItem {
  productId: string;
  score: number;
  reason: string;
  confidence: number;
}

export interface RecommendationResponse {
  recommendations: RecommendationItem[];
  algorithm: 'collaborative' | 'content' | 'hybrid';
  timestamp: Date;
  metadata: {
    totalProducts: number;
    processingTime: number;
    cacheHit: boolean;
  };
}

// ==================== 智能定价策略类型 ====================

export interface PricingRequest {
  productId: string;
  currentPrice: number;
  cost: number;
  competitorPrices?: number[];
  salesHistory?: SalesDataPoint[];
  marketConditions?: MarketCondition;
}

export interface SalesDataPoint {
  date: Date;
  price: number;
  quantity: number;
  revenue: number;
}

export interface MarketCondition {
  demand: 'low' | 'medium' | 'high';
  competition: 'low' | 'medium' | 'high';
  seasonality: number; // -1 to 1
}

export interface PricingResponse {
  recommendedPrice: number;
  priceChange: number;
  priceChangePercent: number;
  expectedRevenue: number;
  confidence: number;
  strategy: 'penetration' | 'skimming' | 'competitive' | 'value_based';
  reasoning: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

// ==================== 智能库存管理类型 ====================

export interface InventoryPredictionRequest {
  productId: string;
  currentStock: number;
  salesHistory: SalesDataPoint[];
  leadTime: number; // 天数
  seasonalFactors?: SeasonalFactor[];
  promotions?: PromotionEvent[];
}

export interface SeasonalFactor {
  month: number;
  factor: number; // 季节性因子
}

export interface PromotionEvent {
  startDate: Date;
  endDate: Date;
  expectedImpact: number; // 预期销量影响倍数
}

export interface InventoryPredictionResponse {
  predictedDemand: DemandForecast[];
  reorderPoint: number;
  optimalOrderQuantity: number;
  safetyStock: number;
  stockoutRisk: number;
  recommendations: InventoryRecommendation[];
}

export interface DemandForecast {
  date: Date;
  predictedDemand: number;
  confidence: number;
}

export interface InventoryRecommendation {
  type: 'reorder' | 'reduce' | 'maintain' | 'increase';
  urgency: 'low' | 'medium' | 'high';
  message: string;
  suggestedAction: string;
}

// ==================== 智能客户服务类型 ====================

export interface CustomerServiceRequest {
  message: string;
  customerId?: string;
  language: string;
  context?: CustomerContext;
  intent?: string;
}

export interface CustomerContext {
  orderHistory: OrderSummary[];
  customerTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  previousInteractions: InteractionHistory[];
  preferences: CustomerPreferences;
}

export interface OrderSummary {
  orderId: string;
  date: Date;
  status: string;
  total: number;
  products: string[];
}

export interface InteractionHistory {
  date: Date;
  channel: 'chat' | 'email' | 'phone';
  issue: string;
  resolution: string;
  satisfaction: number;
}

export interface CustomerPreferences {
  language: string;
  communicationChannel: string;
  responseTime: 'immediate' | 'within_hour' | 'within_day';
}

export interface CustomerServiceResponse {
  response: string;
  intent: string;
  confidence: number;
  sentiment: 'positive' | 'neutral' | 'negative';
  suggestedActions: ServiceAction[];
  escalationRequired: boolean;
  language: string;
}

export interface ServiceAction {
  type: 'refund' | 'replacement' | 'discount' | 'escalate' | 'information';
  description: string;
  priority: 'low' | 'medium' | 'high';
  automated: boolean;
}

// ==================== 机器学习模型类型 ====================

export interface MLModel {
  name: string;
  version: string;
  type: 'tensorflow' | 'custom';
  url?: string;
  localPath?: string;
  inputShape: number[];
  outputShape: number[];
}

export interface ModelMetrics {
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1Score?: number;
  mse?: number;
  mae?: number;
  lastUpdated: Date;
}

export interface TrainingData {
  features: number[][];
  labels: number[][];
  metadata?: Record<string, any>;
}

// ==================== 数据处理类型 ====================

export interface DataPreprocessor {
  normalize(data: number[]): number[];
  denormalize(data: number[]): number[];
  encode(data: any[]): number[];
  decode(data: number[]): any[];
}

export interface FeatureExtractor {
  extractUserFeatures(userId: string): Promise<number[]>;
  extractProductFeatures(productId: string): Promise<number[]>;
  extractTimeFeatures(date: Date): number[];
}

// ==================== 缓存和存储类型 ====================

export interface CacheEntry<T> {
  data: T;
  timestamp: Date;
  expiry: Date;
  hits: number;
}

export interface ModelStorage {
  save(modelName: string, model: any): Promise<void>;
  load(modelName: string): Promise<any>;
  exists(modelName: string): Promise<boolean>;
  delete(modelName: string): Promise<void>;
}
