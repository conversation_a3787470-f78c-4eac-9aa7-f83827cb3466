/**
 * 智能定价策略服务
 * 基于机器学习算法实现动态定价、竞争对手价格监控和利润优化
 */

import * as tf from '@tensorflow/tfjs';
import { 
  BaseAIService, 
  PricingRequest, 
  PricingResponse,
  SalesDataPoint,
  MarketCondition
} from '../types';
import { AI_CONFIG } from '../index';

export class PricingService implements BaseAIService {
  private pricingModel: tf.LayersModel | null = null;
  private demandModel: tf.LayersModel | null = null;
  private isInitialized = false;
  
  // 价格历史数据
  private priceHistory = new Map<string, SalesDataPoint[]>();
  
  // 竞争对手价格数据
  private competitorPrices = new Map<string, number[]>();
  
  // 市场条件缓存
  private marketConditionsCache = new Map<string, MarketCondition>();

  constructor() {}

  /**
   * 初始化定价服务
   */
  async initialize(): Promise<void> {
    try {
      await this.loadModels();
      await this.loadHistoricalData();
      this.isInitialized = true;
      console.log('智能定价服务初始化完成');
    } catch (error) {
      console.error('定价服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查服务是否就绪
   */
  isReady(): boolean {
    return this.isInitialized && 
           this.pricingModel !== null && 
           this.demandModel !== null;
  }

  /**
   * 加载定价模型
   */
  private async loadModels(): Promise<void> {
    try {
      // 创建价格优化模型
      this.pricingModel = await this.createPricingModel();
      
      // 创建需求预测模型
      this.demandModel = await this.createDemandModel();
      
      console.log('定价模型加载完成');
    } catch (error) {
      console.error('定价模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 创建价格优化模型
   */
  private async createPricingModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        // 输入层 - 包含价格、成本、竞争对手价格、市场条件等特征
        tf.layers.dense({
          units: 128,
          activation: 'relu',
          inputShape: [20], // 特征维度
          name: 'pricing_input'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        tf.layers.dense({
          units: 64,
          activation: 'relu',
          name: 'pricing_hidden_1'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        tf.layers.dense({
          units: 32,
          activation: 'relu',
          name: 'pricing_hidden_2'
        }),
        
        // 输出层 - 预测最优价格
        tf.layers.dense({
          units: 1,
          activation: 'linear',
          name: 'pricing_output'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * 创建需求预测模型
   */
  private async createDemandModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        // LSTM层用于时间序列预测
        tf.layers.lstm({
          units: 50,
          returnSequences: true,
          inputShape: [30, 5], // 30天历史，5个特征
          name: 'demand_lstm_1'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        tf.layers.lstm({
          units: 25,
          returnSequences: false,
          name: 'demand_lstm_2'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        tf.layers.dense({
          units: 25,
          activation: 'relu',
          name: 'demand_dense'
        }),
        
        // 输出层 - 预测需求量
        tf.layers.dense({
          units: 1,
          activation: 'relu',
          name: 'demand_output'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * 加载历史数据
   */
  private async loadHistoricalData(): Promise<void> {
    try {
      // 这里应该从数据库加载实际的历史销售数据
      // 暂时使用模拟数据
      await this.loadMockPricingData();
      console.log('定价历史数据加载完成');
    } catch (error) {
      console.error('历史数据加载失败:', error);
      throw error;
    }
  }

  /**
   * 加载模拟定价数据
   */
  private async loadMockPricingData(): Promise<void> {
    // 模拟商品价格历史
    for (let i = 1; i <= 100; i++) {
      const productId = `product_${i}`;
      const history: SalesDataPoint[] = [];
      
      for (let j = 0; j < 90; j++) {
        const date = new Date();
        date.setDate(date.getDate() - j);
        
        history.push({
          date,
          price: 100 + Math.random() * 50,
          quantity: Math.floor(Math.random() * 100),
          revenue: 0 // 会在后面计算
        });
      }
      
      // 计算收入
      history.forEach(point => {
        point.revenue = point.price * point.quantity;
      });
      
      this.priceHistory.set(productId, history);
    }

    // 模拟竞争对手价格
    for (let i = 1; i <= 100; i++) {
      const productId = `product_${i}`;
      const prices = Array.from({ length: 5 }, () => 90 + Math.random() * 60);
      this.competitorPrices.set(productId, prices);
    }
  }

  /**
   * 获取智能定价建议
   */
  async getPricingRecommendation(request: PricingRequest): Promise<PricingResponse> {
    if (!this.isReady()) {
      throw new Error('定价服务未就绪');
    }

    try {
      // 准备特征数据
      const features = await this.prepareFeatures(request);
      
      // 预测最优价格
      const predictedPrice = await this.predictOptimalPrice(features);
      
      // 预测需求
      const predictedDemand = await this.predictDemand(request);
      
      // 计算价格变化
      const priceChange = predictedPrice - request.currentPrice;
      const priceChangePercent = (priceChange / request.currentPrice) * 100;
      
      // 应用价格变化限制
      const constrainedPrice = this.applyPriceConstraints(
        predictedPrice, 
        request.currentPrice, 
        request.cost
      );
      
      // 计算预期收入
      const expectedRevenue = constrainedPrice * predictedDemand;
      
      // 确定定价策略
      const strategy = this.determinePricingStrategy(request, constrainedPrice);
      
      // 生成推理说明
      const reasoning = this.generateReasoning(request, constrainedPrice, strategy);
      
      // 评估风险等级
      const riskLevel = this.assessRiskLevel(priceChangePercent);
      
      // 计算置信度
      const confidence = this.calculateConfidence(request, features);

      return {
        recommendedPrice: constrainedPrice,
        priceChange: constrainedPrice - request.currentPrice,
        priceChangePercent: ((constrainedPrice - request.currentPrice) / request.currentPrice) * 100,
        expectedRevenue,
        confidence,
        strategy,
        reasoning,
        riskLevel
      };
    } catch (error) {
      console.error('定价推荐生成失败:', error);
      throw error;
    }
  }

  /**
   * 准备模型特征
   */
  private async prepareFeatures(request: PricingRequest): Promise<number[]> {
    const features: number[] = [];
    
    // 基础价格特征
    features.push(
      request.currentPrice,
      request.cost,
      request.currentPrice / request.cost, // 利润率
    );
    
    // 竞争对手价格特征
    if (request.competitorPrices && request.competitorPrices.length > 0) {
      const avgCompetitorPrice = request.competitorPrices.reduce((a, b) => a + b, 0) / request.competitorPrices.length;
      const minCompetitorPrice = Math.min(...request.competitorPrices);
      const maxCompetitorPrice = Math.max(...request.competitorPrices);
      
      features.push(
        avgCompetitorPrice,
        minCompetitorPrice,
        maxCompetitorPrice,
        request.currentPrice / avgCompetitorPrice // 相对竞争位置
      );
    } else {
      features.push(0, 0, 0, 1); // 默认值
    }
    
    // 销售历史特征
    if (request.salesHistory && request.salesHistory.length > 0) {
      const recentSales = request.salesHistory.slice(-30); // 最近30天
      const avgQuantity = recentSales.reduce((sum, point) => sum + point.quantity, 0) / recentSales.length;
      const avgRevenue = recentSales.reduce((sum, point) => sum + point.revenue, 0) / recentSales.length;
      const salesTrend = this.calculateSalesTrend(recentSales);
      
      features.push(avgQuantity, avgRevenue, salesTrend);
    } else {
      features.push(0, 0, 0); // 默认值
    }
    
    // 市场条件特征
    if (request.marketConditions) {
      const demandScore = this.encodeMarketLevel(request.marketConditions.demand);
      const competitionScore = this.encodeMarketLevel(request.marketConditions.competition);
      
      features.push(
        demandScore,
        competitionScore,
        request.marketConditions.seasonality
      );
    } else {
      features.push(0.5, 0.5, 0); // 默认中等水平
    }
    
    // 时间特征
    const now = new Date();
    features.push(
      now.getMonth() / 11, // 月份归一化
      now.getDay() / 6,    // 星期归一化
      now.getHours() / 23  // 小时归一化
    );
    
    // 确保特征数量正确
    while (features.length < 20) {
      features.push(0);
    }
    
    return features.slice(0, 20);
  }

  /**
   * 预测最优价格
   */
  private async predictOptimalPrice(features: number[]): Promise<number> {
    if (!this.pricingModel) {
      throw new Error('定价模型未加载');
    }

    const inputTensor = tf.tensor2d([features]);
    const prediction = this.pricingModel.predict(inputTensor) as tf.Tensor;
    const price = await prediction.data();
    
    inputTensor.dispose();
    prediction.dispose();
    
    return price[0];
  }

  /**
   * 预测需求
   */
  private async predictDemand(request: PricingRequest): Promise<number> {
    if (!this.demandModel || !request.salesHistory || request.salesHistory.length < 30) {
      // 如果没有足够的历史数据，使用简单的需求模型
      return this.estimateDemandFromPrice(request.currentPrice, request.cost);
    }

    // 准备时间序列数据
    const timeSeriesData = this.prepareTimeSeriesData(request.salesHistory);
    const inputTensor = tf.tensor3d([timeSeriesData]);
    
    const prediction = this.demandModel.predict(inputTensor) as tf.Tensor;
    const demand = await prediction.data();
    
    inputTensor.dispose();
    prediction.dispose();
    
    return Math.max(0, demand[0]);
  }

  /**
   * 应用价格约束
   */
  private applyPriceConstraints(predictedPrice: number, currentPrice: number, cost: number): number {
    // 最大价格变化限制
    const maxChange = currentPrice * AI_CONFIG.pricing.maxPriceChange;
    const minPrice = currentPrice - maxChange;
    const maxPrice = currentPrice + maxChange;
    
    // 最小利润率限制
    const minPriceForMargin = cost * (1 + AI_CONFIG.pricing.minMargin);
    
    // 应用所有约束
    return Math.max(
      minPriceForMargin,
      Math.min(maxPrice, Math.max(minPrice, predictedPrice))
    );
  }

  /**
   * 确定定价策略
   */
  private determinePricingStrategy(request: PricingRequest, recommendedPrice: number): 'penetration' | 'skimming' | 'competitive' | 'value_based' {
    const currentPrice = request.currentPrice;
    const cost = request.cost;
    
    // 如果推荐价格显著低于当前价格，使用渗透定价
    if (recommendedPrice < currentPrice * 0.9) {
      return 'penetration';
    }
    
    // 如果推荐价格显著高于当前价格，使用撇脂定价
    if (recommendedPrice > currentPrice * 1.1) {
      return 'skimming';
    }
    
    // 如果有竞争对手价格数据，使用竞争定价
    if (request.competitorPrices && request.competitorPrices.length > 0) {
      return 'competitive';
    }
    
    // 默认使用价值定价
    return 'value_based';
  }

  /**
   * 生成推理说明
   */
  private generateReasoning(request: PricingRequest, recommendedPrice: number, strategy: string): string[] {
    const reasoning: string[] = [];
    const priceChange = recommendedPrice - request.currentPrice;
    const changePercent = (priceChange / request.currentPrice) * 100;
    
    // 基于策略的说明
    switch (strategy) {
      case 'penetration':
        reasoning.push('采用渗透定价策略，通过降价获取更多市场份额');
        break;
      case 'skimming':
        reasoning.push('采用撇脂定价策略，通过提价最大化利润');
        break;
      case 'competitive':
        reasoning.push('采用竞争定价策略，基于竞争对手价格调整');
        break;
      case 'value_based':
        reasoning.push('采用价值定价策略，基于产品价值确定价格');
        break;
    }
    
    // 价格变化说明
    if (Math.abs(changePercent) > 5) {
      reasoning.push(`建议${priceChange > 0 ? '提价' : '降价'} ${Math.abs(changePercent).toFixed(1)}%`);
    } else {
      reasoning.push('建议维持当前价格水平');
    }
    
    // 市场条件说明
    if (request.marketConditions) {
      if (request.marketConditions.demand === 'high') {
        reasoning.push('当前市场需求旺盛，支持适度提价');
      } else if (request.marketConditions.demand === 'low') {
        reasoning.push('当前市场需求疲软，建议谨慎调价');
      }
    }
    
    // 竞争情况说明
    if (request.competitorPrices && request.competitorPrices.length > 0) {
      const avgCompetitorPrice = request.competitorPrices.reduce((a, b) => a + b, 0) / request.competitorPrices.length;
      if (recommendedPrice < avgCompetitorPrice * 0.95) {
        reasoning.push('推荐价格低于竞争对手平均价格，具有价格优势');
      } else if (recommendedPrice > avgCompetitorPrice * 1.05) {
        reasoning.push('推荐价格高于竞争对手平均价格，需要确保产品差异化');
      }
    }
    
    return reasoning;
  }

  /**
   * 评估风险等级
   */
  private assessRiskLevel(priceChangePercent: number): 'low' | 'medium' | 'high' {
    const absChange = Math.abs(priceChangePercent);
    
    if (absChange < 5) return 'low';
    if (absChange < 15) return 'medium';
    return 'high';
  }

  /**
   * 计算置信度
   */
  private calculateConfidence(request: PricingRequest, features: number[]): number {
    let confidence = 0.5; // 基础置信度
    
    // 如果有销售历史，增加置信度
    if (request.salesHistory && request.salesHistory.length > 30) {
      confidence += 0.2;
    }
    
    // 如果有竞争对手价格，增加置信度
    if (request.competitorPrices && request.competitorPrices.length > 0) {
      confidence += 0.15;
    }
    
    // 如果有市场条件信息，增加置信度
    if (request.marketConditions) {
      confidence += 0.15;
    }
    
    return Math.min(1.0, confidence);
  }

  /**
   * 辅助方法
   */
  private encodeMarketLevel(level: 'low' | 'medium' | 'high'): number {
    switch (level) {
      case 'low': return 0.2;
      case 'medium': return 0.5;
      case 'high': return 0.8;
    }
  }

  private calculateSalesTrend(salesData: SalesDataPoint[]): number {
    if (salesData.length < 2) return 0;
    
    const firstHalf = salesData.slice(0, Math.floor(salesData.length / 2));
    const secondHalf = salesData.slice(Math.floor(salesData.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, point) => sum + point.quantity, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, point) => sum + point.quantity, 0) / secondHalf.length;
    
    return (secondAvg - firstAvg) / firstAvg;
  }

  private estimateDemandFromPrice(price: number, cost: number): number {
    // 简单的需求-价格弹性模型
    const elasticity = -1.5; // 假设弹性系数
    const basePrice = cost * 1.5; // 基准价格
    const priceRatio = price / basePrice;
    const baseDemand = 100; // 基准需求
    
    return baseDemand * Math.pow(priceRatio, elasticity);
  }

  private prepareTimeSeriesData(salesHistory: SalesDataPoint[]): number[][] {
    // 准备30天的时间序列数据，每天5个特征
    const data: number[][] = [];
    const recent30Days = salesHistory.slice(-30);
    
    recent30Days.forEach(point => {
      data.push([
        point.price,
        point.quantity,
        point.revenue,
        point.date.getDay(), // 星期几
        point.date.getMonth() // 月份
      ]);
    });
    
    // 如果数据不足30天，用零填充
    while (data.length < 30) {
      data.unshift([0, 0, 0, 0, 0]);
    }
    
    return data;
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.pricingModel) {
      this.pricingModel.dispose();
      this.pricingModel = null;
    }
    
    if (this.demandModel) {
      this.demandModel.dispose();
      this.demandModel = null;
    }
    
    this.priceHistory.clear();
    this.competitorPrices.clear();
    this.marketConditionsCache.clear();
    this.isInitialized = false;
    
    console.log('定价服务资源已清理');
  }
}
