/**
 * AI智能功能模块入口文件
 * 提供统一的AI服务接口，包括商品推荐、定价策略、库存管理和客户服务等功能
 */

export { RecommendationService } from './recommendation/RecommendationService';
export { PricingService } from './pricing/PricingService';
export { InventoryService } from './inventory/InventoryService';
export { CustomerService } from './customer/CustomerService';

export type {
  RecommendationRequest,
  RecommendationResponse,
  PricingRequest,
  PricingResponse,
  InventoryPredictionRequest,
  InventoryPredictionResponse,
  CustomerServiceRequest,
  CustomerServiceResponse
} from './types';

// AI模块配置
export const AI_CONFIG = {
  // TensorFlow.js配置
  tensorflow: {
    backend: 'webgl', // 默认使用WebGL后端
    enableProfiling: false,
    memoryGrowth: true
  },
  
  // 推荐系统配置
  recommendation: {
    maxRecommendations: 10,
    minSimilarity: 0.1,
    cacheExpiry: 3600000 // 1小时
  },
  
  // 定价策略配置
  pricing: {
    maxPriceChange: 0.3, // 最大价格变动30%
    minMargin: 0.1, // 最小利润率10%
    updateInterval: 86400000 // 24小时
  },
  
  // 库存预测配置
  inventory: {
    predictionDays: 30,
    safetyStock: 0.2, // 安全库存20%
    reorderPoint: 0.1 // 再订货点10%
  },
  
  // 客户服务配置
  customerService: {
    confidenceThreshold: 0.8,
    maxResponseLength: 500,
    supportedLanguages: ['zh-CN', 'en-US', 'ja-JP', 'ko-KR']
  }
};
