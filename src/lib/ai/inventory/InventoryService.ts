/**
 * 智能库存管理系统服务
 * 基于机器学习实现需求预测、自动补货建议、滞销商品识别和库存优化策略
 */

import * as tf from '@tensorflow/tfjs';
import { 
  BaseAIService, 
  InventoryPredictionRequest, 
  InventoryPredictionResponse,
  DemandForecast,
  InventoryRecommendation,
  SalesDataPoint,
  SeasonalFactor,
  PromotionEvent
} from '../types';
import { AI_CONFIG } from '../index';

export class InventoryService implements BaseAIService {
  private demandForecastModel: tf.LayersModel | null = null;
  private seasonalityModel: tf.LayersModel | null = null;
  private isInitialized = false;
  
  // 历史销售数据缓存
  private salesDataCache = new Map<string, SalesDataPoint[]>();
  
  // 季节性因子缓存
  private seasonalFactorsCache = new Map<string, SeasonalFactor[]>();
  
  // 库存优化参数
  private optimizationParams = {
    serviceLevel: 0.95, // 服务水平95%
    holdingCostRate: 0.2, // 持有成本率20%
    orderCostFixed: 50, // 固定订货成本
    stockoutCostRate: 0.5 // 缺货成本率50%
  };

  constructor() {}

  /**
   * 初始化库存服务
   */
  async initialize(): Promise<void> {
    try {
      await this.loadModels();
      await this.loadHistoricalData();
      this.isInitialized = true;
      console.log('智能库存管理服务初始化完成');
    } catch (error) {
      console.error('库存服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查服务是否就绪
   */
  isReady(): boolean {
    return this.isInitialized && 
           this.demandForecastModel !== null && 
           this.seasonalityModel !== null;
  }

  /**
   * 加载库存预测模型
   */
  private async loadModels(): Promise<void> {
    try {
      // 创建需求预测模型
      this.demandForecastModel = await this.createDemandForecastModel();
      
      // 创建季节性分析模型
      this.seasonalityModel = await this.createSeasonalityModel();
      
      console.log('库存预测模型加载完成');
    } catch (error) {
      console.error('库存模型加载失败:', error);
      throw error;
    }
  }

  /**
   * 创建需求预测模型 - 使用LSTM进行时间序列预测
   */
  private async createDemandForecastModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        // 第一层LSTM - 处理长期趋势
        tf.layers.lstm({
          units: 64,
          returnSequences: true,
          inputShape: [60, 8], // 60天历史，8个特征
          name: 'demand_lstm_1'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        // 第二层LSTM - 处理短期模式
        tf.layers.lstm({
          units: 32,
          returnSequences: true,
          name: 'demand_lstm_2'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        // 第三层LSTM - 最终特征提取
        tf.layers.lstm({
          units: 16,
          returnSequences: false,
          name: 'demand_lstm_3'
        }),
        
        // 全连接层
        tf.layers.dense({
          units: 32,
          activation: 'relu',
          name: 'demand_dense_1'
        }),
        
        tf.layers.dropout({ rate: 0.1 }),
        
        tf.layers.dense({
          units: 16,
          activation: 'relu',
          name: 'demand_dense_2'
        }),
        
        // 输出层 - 预测未来30天的需求
        tf.layers.dense({
          units: 30,
          activation: 'relu',
          name: 'demand_output'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae', 'mape']
    });

    return model;
  }

  /**
   * 创建季节性分析模型
   */
  private async createSeasonalityModel(): Promise<tf.LayersModel> {
    const model = tf.sequential({
      layers: [
        // 输入层 - 时间特征
        tf.layers.dense({
          units: 64,
          activation: 'relu',
          inputShape: [12], // 12个月的特征
          name: 'seasonal_input'
        }),
        
        tf.layers.dropout({ rate: 0.2 }),
        
        tf.layers.dense({
          units: 32,
          activation: 'relu',
          name: 'seasonal_hidden_1'
        }),
        
        tf.layers.dense({
          units: 16,
          activation: 'relu',
          name: 'seasonal_hidden_2'
        }),
        
        // 输出层 - 季节性因子
        tf.layers.dense({
          units: 12,
          activation: 'linear',
          name: 'seasonal_output'
        })
      ]
    });

    model.compile({
      optimizer: tf.train.adam(0.001),
      loss: 'meanSquaredError',
      metrics: ['mae']
    });

    return model;
  }

  /**
   * 加载历史数据
   */
  private async loadHistoricalData(): Promise<void> {
    try {
      // 这里应该从数据库加载实际的历史销售数据
      await this.loadMockInventoryData();
      console.log('库存历史数据加载完成');
    } catch (error) {
      console.error('库存历史数据加载失败:', error);
      throw error;
    }
  }

  /**
   * 加载模拟库存数据
   */
  private async loadMockInventoryData(): Promise<void> {
    // 模拟商品销售历史数据
    for (let i = 1; i <= 100; i++) {
      const productId = `product_${i}`;
      const salesHistory: SalesDataPoint[] = [];
      
      // 生成过去180天的销售数据
      for (let j = 0; j < 180; j++) {
        const date = new Date();
        date.setDate(date.getDate() - j);
        
        // 添加季节性和趋势
        const seasonalFactor = 1 + 0.3 * Math.sin((date.getMonth() / 12) * 2 * Math.PI);
        const trendFactor = 1 + (j / 1000); // 轻微增长趋势
        const randomFactor = 0.8 + Math.random() * 0.4;
        
        const baseQuantity = 50;
        const quantity = Math.floor(baseQuantity * seasonalFactor * trendFactor * randomFactor);
        const price = 100 + Math.random() * 20;
        
        salesHistory.push({
          date,
          price,
          quantity,
          revenue: price * quantity
        });
      }
      
      this.salesDataCache.set(productId, salesHistory.reverse()); // 按时间正序排列
    }

    // 模拟季节性因子
    for (let i = 1; i <= 100; i++) {
      const productId = `product_${i}`;
      const seasonalFactors: SeasonalFactor[] = [];
      
      for (let month = 1; month <= 12; month++) {
        // 模拟不同商品的季节性模式
        const factor = 1 + 0.3 * Math.sin((month / 12) * 2 * Math.PI + Math.random());
        seasonalFactors.push({ month, factor });
      }
      
      this.seasonalFactorsCache.set(productId, seasonalFactors);
    }
  }

  /**
   * 获取库存预测和建议
   */
  async getInventoryPrediction(request: InventoryPredictionRequest): Promise<InventoryPredictionResponse> {
    if (!this.isReady()) {
      throw new Error('库存服务未就绪');
    }

    try {
      // 预测未来需求
      const demandForecast = await this.forecastDemand(request);
      
      // 计算库存优化参数
      const { reorderPoint, optimalOrderQuantity, safetyStock } = 
        await this.calculateInventoryParameters(request, demandForecast);
      
      // 评估缺货风险
      const stockoutRisk = this.calculateStockoutRisk(request.currentStock, demandForecast, reorderPoint);
      
      // 生成库存建议
      const recommendations = this.generateInventoryRecommendations(
        request, 
        demandForecast, 
        reorderPoint, 
        optimalOrderQuantity,
        stockoutRisk
      );

      return {
        predictedDemand: demandForecast,
        reorderPoint,
        optimalOrderQuantity,
        safetyStock,
        stockoutRisk,
        recommendations
      };
    } catch (error) {
      console.error('库存预测生成失败:', error);
      throw error;
    }
  }

  /**
   * 预测未来需求
   */
  private async forecastDemand(request: InventoryPredictionRequest): Promise<DemandForecast[]> {
    if (!this.demandForecastModel) {
      throw new Error('需求预测模型未加载');
    }

    // 准备输入数据
    const inputData = this.prepareForecastInput(request);
    const inputTensor = tf.tensor3d([inputData]);
    
    // 执行预测
    const prediction = this.demandForecastModel.predict(inputTensor) as tf.Tensor;
    const forecastData = await prediction.data();
    
    // 清理张量
    inputTensor.dispose();
    prediction.dispose();
    
    // 转换为预测结果
    const forecasts: DemandForecast[] = [];
    const today = new Date();
    
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(date.getDate() + i + 1);
      
      // 应用季节性调整
      const seasonalAdjustment = this.getSeasonalAdjustment(request.productId, date);
      const adjustedDemand = Math.max(0, forecastData[i] * seasonalAdjustment);
      
      // 应用促销活动影响
      const promotionAdjustment = this.getPromotionAdjustment(request.promotions, date);
      const finalDemand = adjustedDemand * promotionAdjustment;
      
      // 计算置信度（基于历史数据的变异性）
      const confidence = this.calculateForecastConfidence(request.salesHistory, i);
      
      forecasts.push({
        date,
        predictedDemand: Math.round(finalDemand),
        confidence
      });
    }
    
    return forecasts;
  }

  /**
   * 准备预测输入数据
   */
  private prepareForecastInput(request: InventoryPredictionRequest): number[][] {
    const inputData: number[][] = [];
    const salesHistory = request.salesHistory.slice(-60); // 使用最近60天的数据
    
    // 如果历史数据不足60天，用零填充
    while (salesHistory.length < 60) {
      const defaultPoint: SalesDataPoint = {
        date: new Date(),
        price: 0,
        quantity: 0,
        revenue: 0
      };
      salesHistory.unshift(defaultPoint);
    }
    
    salesHistory.forEach((point, index) => {
      const features = [
        point.quantity,                    // 销量
        point.price,                       // 价格
        point.revenue,                     // 收入
        point.date.getDay(),              // 星期几
        point.date.getMonth(),            // 月份
        point.date.getDate(),             // 日期
        this.getMovingAverage(salesHistory, index, 7),  // 7天移动平均
        this.getMovingAverage(salesHistory, index, 30)  // 30天移动平均
      ];
      inputData.push(features);
    });
    
    return inputData;
  }

  /**
   * 计算库存优化参数
   */
  private async calculateInventoryParameters(
    request: InventoryPredictionRequest, 
    forecast: DemandForecast[]
  ): Promise<{ reorderPoint: number; optimalOrderQuantity: number; safetyStock: number }> {
    
    // 计算平均需求和需求标准差
    const avgDemand = forecast.reduce((sum, f) => sum + f.predictedDemand, 0) / forecast.length;
    const demandVariance = forecast.reduce((sum, f) => 
      sum + Math.pow(f.predictedDemand - avgDemand, 2), 0) / forecast.length;
    const demandStdDev = Math.sqrt(demandVariance);
    
    // 计算安全库存（基于服务水平和需求变异性）
    const zScore = this.getZScoreForServiceLevel(this.optimizationParams.serviceLevel);
    const leadTimeDemand = avgDemand * request.leadTime;
    const leadTimeDemandStdDev = demandStdDev * Math.sqrt(request.leadTime);
    const safetyStock = Math.ceil(zScore * leadTimeDemandStdDev);
    
    // 计算再订货点
    const reorderPoint = Math.ceil(leadTimeDemand + safetyStock);
    
    // 计算经济订货量（EOQ）
    const annualDemand = avgDemand * 365;
    const holdingCost = this.optimizationParams.holdingCostRate;
    const orderCost = this.optimizationParams.orderCostFixed;
    const optimalOrderQuantity = Math.ceil(
      Math.sqrt((2 * annualDemand * orderCost) / holdingCost)
    );
    
    return {
      reorderPoint: Math.max(reorderPoint, 0),
      optimalOrderQuantity: Math.max(optimalOrderQuantity, 1),
      safetyStock: Math.max(safetyStock, 0)
    };
  }

  /**
   * 计算缺货风险
   */
  private calculateStockoutRisk(
    currentStock: number, 
    forecast: DemandForecast[], 
    reorderPoint: number
  ): number {
    if (currentStock > reorderPoint) {
      return 0; // 库存充足，无缺货风险
    }
    
    // 计算在当前库存水平下的缺货概率
    const nearTermDemand = forecast.slice(0, 7).reduce((sum, f) => sum + f.predictedDemand, 0);
    const riskFactor = Math.max(0, (nearTermDemand - currentStock) / nearTermDemand);
    
    return Math.min(1, riskFactor);
  }

  /**
   * 生成库存建议
   */
  private generateInventoryRecommendations(
    request: InventoryPredictionRequest,
    forecast: DemandForecast[],
    reorderPoint: number,
    optimalOrderQuantity: number,
    stockoutRisk: number
  ): InventoryRecommendation[] {
    const recommendations: InventoryRecommendation[] = [];
    const currentStock = request.currentStock;
    
    // 缺货风险建议
    if (stockoutRisk > 0.7) {
      recommendations.push({
        type: 'reorder',
        urgency: 'high',
        message: '库存严重不足，存在高缺货风险',
        suggestedAction: `立即补货 ${optimalOrderQuantity} 件，当前库存仅剩 ${currentStock} 件`
      });
    } else if (stockoutRisk > 0.3) {
      recommendations.push({
        type: 'reorder',
        urgency: 'medium',
        message: '库存偏低，建议及时补货',
        suggestedAction: `建议补货 ${optimalOrderQuantity} 件，预防缺货风险`
      });
    }
    
    // 再订货点建议
    if (currentStock <= reorderPoint) {
      recommendations.push({
        type: 'reorder',
        urgency: currentStock <= reorderPoint * 0.5 ? 'high' : 'medium',
        message: '已达到再订货点',
        suggestedAction: `当前库存 ${currentStock} 件已达到再订货点 ${reorderPoint} 件，建议订购 ${optimalOrderQuantity} 件`
      });
    }
    
    // 库存过多建议
    const maxReasonableStock = reorderPoint + optimalOrderQuantity * 2;
    if (currentStock > maxReasonableStock) {
      recommendations.push({
        type: 'reduce',
        urgency: 'low',
        message: '库存过多，占用资金',
        suggestedAction: `当前库存 ${currentStock} 件过多，建议通过促销等方式减少库存`
      });
    }
    
    // 滞销商品识别
    const recentSales = forecast.slice(0, 7).reduce((sum, f) => sum + f.predictedDemand, 0);
    if (recentSales < currentStock * 0.1) {
      recommendations.push({
        type: 'reduce',
        urgency: 'medium',
        message: '商品销售缓慢，可能成为滞销品',
        suggestedAction: '考虑调整价格策略或营销推广，加快库存周转'
      });
    }
    
    // 季节性建议
    const seasonalTrend = this.analyzeSeasonalTrend(request.productId);
    if (seasonalTrend.isUpcoming) {
      recommendations.push({
        type: 'increase',
        urgency: 'medium',
        message: '即将进入销售旺季',
        suggestedAction: `预计销量将增长 ${(seasonalTrend.factor * 100).toFixed(0)}%，建议提前备货`
      });
    }
    
    return recommendations;
  }

  /**
   * 辅助方法
   */
  private getSeasonalAdjustment(productId: string, date: Date): number {
    const seasonalFactors = this.seasonalFactorsCache.get(productId);
    if (!seasonalFactors) return 1;
    
    const monthFactor = seasonalFactors.find(f => f.month === date.getMonth() + 1);
    return monthFactor ? monthFactor.factor : 1;
  }

  private getPromotionAdjustment(promotions: PromotionEvent[] | undefined, date: Date): number {
    if (!promotions) return 1;
    
    const activePromotion = promotions.find(p => 
      date >= p.startDate && date <= p.endDate
    );
    
    return activePromotion ? activePromotion.expectedImpact : 1;
  }

  private calculateForecastConfidence(salesHistory: SalesDataPoint[], dayIndex: number): number {
    // 基于历史数据的变异性计算置信度
    const recentVariability = this.calculateVariability(salesHistory.slice(-30));
    const baseConfidence = 0.8;
    const variabilityPenalty = Math.min(0.3, recentVariability * 0.1);
    const timePenalty = dayIndex * 0.01; // 预测时间越远，置信度越低
    
    return Math.max(0.3, baseConfidence - variabilityPenalty - timePenalty);
  }

  private calculateVariability(salesData: SalesDataPoint[]): number {
    if (salesData.length < 2) return 0;
    
    const quantities = salesData.map(d => d.quantity);
    const mean = quantities.reduce((sum, q) => sum + q, 0) / quantities.length;
    const variance = quantities.reduce((sum, q) => sum + Math.pow(q - mean, 2), 0) / quantities.length;
    
    return Math.sqrt(variance) / mean; // 变异系数
  }

  private getMovingAverage(data: SalesDataPoint[], index: number, window: number): number {
    const start = Math.max(0, index - window + 1);
    const end = index + 1;
    const subset = data.slice(start, end);
    
    if (subset.length === 0) return 0;
    
    return subset.reduce((sum, point) => sum + point.quantity, 0) / subset.length;
  }

  private getZScoreForServiceLevel(serviceLevel: number): number {
    // 常用服务水平对应的Z分数
    const zScores: { [key: number]: number } = {
      0.90: 1.28,
      0.95: 1.65,
      0.99: 2.33
    };
    
    return zScores[serviceLevel] || 1.65;
  }

  private analyzeSeasonalTrend(productId: string): { isUpcoming: boolean; factor: number } {
    const seasonalFactors = this.seasonalFactorsCache.get(productId);
    if (!seasonalFactors) return { isUpcoming: false, factor: 1 };
    
    const currentMonth = new Date().getMonth() + 1;
    const nextMonth = currentMonth === 12 ? 1 : currentMonth + 1;
    
    const currentFactor = seasonalFactors.find(f => f.month === currentMonth)?.factor || 1;
    const nextFactor = seasonalFactors.find(f => f.month === nextMonth)?.factor || 1;
    
    return {
      isUpcoming: nextFactor > currentFactor * 1.1,
      factor: nextFactor / currentFactor
    };
  }

  /**
   * 清理资源
   */
  dispose(): void {
    if (this.demandForecastModel) {
      this.demandForecastModel.dispose();
      this.demandForecastModel = null;
    }
    
    if (this.seasonalityModel) {
      this.seasonalityModel.dispose();
      this.seasonalityModel = null;
    }
    
    this.salesDataCache.clear();
    this.seasonalFactorsCache.clear();
    this.isInitialized = false;
    
    console.log('库存服务资源已清理');
  }
}
