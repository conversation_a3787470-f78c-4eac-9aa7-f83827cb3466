/**
 * 系统配置管理服务
 * 提供基础配置、集成配置、安全配置等系统参数管理功能
 */

import { prisma } from '@/lib/prisma';

// 配置类型枚举
export enum ConfigType {
  SYSTEM = 'SYSTEM',           // 系统配置
  INTEGRATION = 'INTEGRATION', // 集成配置
  SECURITY = 'SECURITY',       // 安全配置
  BUSINESS = 'BUSINESS',       // 业务配置
  UI = 'UI',                   // 界面配置
  NOTIFICATION = 'NOTIFICATION', // 通知配置
  PAYMENT = 'PAYMENT',         // 支付配置
  SHIPPING = 'SHIPPING',       // 物流配置
  TAX = 'TAX',                 // 税务配置
  CURRENCY = 'CURRENCY',       // 货币配置
}

// 配置数据类型枚举
export enum ConfigDataType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  ARRAY = 'ARRAY',
  DATE = 'DATE',
  PASSWORD = 'PASSWORD',
  URL = 'URL',
  EMAIL = 'EMAIL',
  FILE = 'FILE',
}

// 配置项接口
export interface ConfigItem {
  id: string;
  key: string;
  name: string;
  description?: string;
  type: ConfigType;
  dataType: ConfigDataType;
  value: any;
  defaultValue?: any;
  isRequired: boolean;
  isEncrypted: boolean;
  isReadonly: boolean;
  validation?: ConfigValidation;
  group?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 配置验证规则接口
export interface ConfigValidation {
  min?: number;
  max?: number;
  pattern?: string;
  options?: string[];
  customValidator?: string;
  errorMessage?: string;
}

// 配置更新请求接口
export interface ConfigUpdateRequest {
  key: string;
  value: any;
  userId?: string;
}

// 配置批量更新请求接口
export interface ConfigBatchUpdateRequest {
  configs: ConfigUpdateRequest[];
  userId?: string;
}

/**
 * 系统配置管理服务类
 */
export class ConfigService {
  private configCache: Map<string, ConfigItem> = new Map();
  private cacheTimestamp: Date | null = null;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 获取配置项
   */
  async getConfig(key: string): Promise<ConfigItem | null> {
    try {
      // 检查缓存
      if (this.isCacheValid() && this.configCache.has(key)) {
        return this.configCache.get(key)!;
      }

      // 从数据库获取
      const config = await prisma.systemConfig.findUnique({
        where: { key },
      });

      if (!config) {
        return null;
      }

      const configItem = this.mapDatabaseConfigToItem(config);
      
      // 更新缓存
      this.configCache.set(key, configItem);
      
      return configItem;
    } catch (error) {
      console.error('获取配置失败:', error);
      return null;
    }
  }

  /**
   * 获取配置值
   */
  async getConfigValue<T = any>(key: string, defaultValue?: T): Promise<T> {
    try {
      const config = await this.getConfig(key);
      
      if (!config) {
        return defaultValue as T;
      }

      return this.parseConfigValue(config.value, config.dataType) as T;
    } catch (error) {
      console.error('获取配置值失败:', error);
      return defaultValue as T;
    }
  }

  /**
   * 设置配置值
   */
  async setConfig(
    key: string,
    value: any,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 获取现有配置
      const existingConfig = await this.getConfig(key);
      
      if (!existingConfig) {
        return {
          success: false,
          error: '配置项不存在',
        };
      }

      // 检查是否只读
      if (existingConfig.isReadonly) {
        return {
          success: false,
          error: '配置项为只读，无法修改',
        };
      }

      // 验证配置值
      const validation = this.validateConfigValue(value, existingConfig);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // 处理加密
      const processedValue = existingConfig.isEncrypted 
        ? await this.encryptValue(value)
        : value;

      // 更新数据库
      await prisma.systemConfig.update({
        where: { key },
        data: {
          value: processedValue,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });

      // 清除缓存
      this.clearCache();

      // 触发配置变更事件
      await this.triggerConfigChangeEvent(key, value, userId);

      return { success: true };
    } catch (error) {
      console.error('设置配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '设置配置失败',
      };
    }
  }

  /**
   * 批量更新配置
   */
  async batchUpdateConfigs(
    request: ConfigBatchUpdateRequest
  ): Promise<{ success: boolean; errors?: string[]; error?: string }> {
    try {
      const { configs, userId = 'system' } = request;
      const errors: string[] = [];

      // 使用事务批量更新
      await prisma.$transaction(async (tx) => {
        for (const configUpdate of configs) {
          try {
            const result = await this.setConfig(configUpdate.key, configUpdate.value, userId);
            if (!result.success) {
              errors.push(`${configUpdate.key}: ${result.error}`);
            }
          } catch (error) {
            errors.push(`${configUpdate.key}: ${error instanceof Error ? error.message : '更新失败'}`);
          }
        }
      });

      return {
        success: errors.length === 0,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error('批量更新配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量更新配置失败',
      };
    }
  }

  /**
   * 获取配置组
   */
  async getConfigGroup(
    type: ConfigType,
    group?: string
  ): Promise<{ success: boolean; configs?: ConfigItem[]; error?: string }> {
    try {
      const whereConditions: any = { type };
      
      if (group) {
        whereConditions.group = group;
      }

      const configs = await prisma.systemConfig.findMany({
        where: whereConditions,
        orderBy: [
          { group: 'asc' },
          { order: 'asc' },
          { name: 'asc' },
        ],
      });

      const configItems = configs.map(config => this.mapDatabaseConfigToItem(config));

      return {
        success: true,
        configs: configItems,
      };
    } catch (error) {
      console.error('获取配置组失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取配置组失败',
      };
    }
  }

  /**
   * 创建配置项
   */
  async createConfig(
    config: Omit<ConfigItem, 'id' | 'createdAt' | 'updatedAt'>,
    userId: string = 'system'
  ): Promise<{ success: boolean; config?: ConfigItem; error?: string }> {
    try {
      // 检查配置键是否已存在
      const existingConfig = await prisma.systemConfig.findUnique({
        where: { key: config.key },
      });

      if (existingConfig) {
        return {
          success: false,
          error: '配置键已存在',
        };
      }

      // 验证配置值
      const validation = this.validateConfigValue(config.value, config);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // 处理加密
      const processedValue = config.isEncrypted 
        ? await this.encryptValue(config.value)
        : config.value;

      // 创建配置
      const createdConfig = await prisma.systemConfig.create({
        data: {
          ...config,
          value: processedValue,
          createdBy: userId,
          updatedBy: userId,
        },
      });

      const configItem = this.mapDatabaseConfigToItem(createdConfig);

      // 清除缓存
      this.clearCache();

      return {
        success: true,
        config: configItem,
      };
    } catch (error) {
      console.error('创建配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建配置失败',
      };
    }
  }

  /**
   * 删除配置项
   */
  async deleteConfig(
    key: string,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 检查配置是否存在
      const existingConfig = await this.getConfig(key);
      if (!existingConfig) {
        return {
          success: false,
          error: '配置项不存在',
        };
      }

      // 检查是否为必需配置
      if (existingConfig.isRequired) {
        return {
          success: false,
          error: '必需配置项无法删除',
        };
      }

      // 删除配置
      await prisma.systemConfig.delete({
        where: { key },
      });

      // 清除缓存
      this.clearCache();

      return { success: true };
    } catch (error) {
      console.error('删除配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除配置失败',
      };
    }
  }

  /**
   * 重置配置为默认值
   */
  async resetConfig(
    key: string,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const config = await this.getConfig(key);
      if (!config) {
        return {
          success: false,
          error: '配置项不存在',
        };
      }

      if (config.defaultValue === undefined) {
        return {
          success: false,
          error: '配置项没有默认值',
        };
      }

      return await this.setConfig(key, config.defaultValue, userId);
    } catch (error) {
      console.error('重置配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '重置配置失败',
      };
    }
  }

  /**
   * 导出配置
   */
  async exportConfigs(
    type?: ConfigType,
    includeEncrypted: boolean = false
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const whereConditions: any = {};
      
      if (type) {
        whereConditions.type = type;
      }

      const configs = await prisma.systemConfig.findMany({
        where: whereConditions,
        orderBy: [
          { type: 'asc' },
          { group: 'asc' },
          { order: 'asc' },
        ],
      });

      const exportData = configs.map(config => {
        const configItem = this.mapDatabaseConfigToItem(config);
        
        // 如果不包含加密配置，则隐藏加密值
        if (configItem.isEncrypted && !includeEncrypted) {
          configItem.value = '***ENCRYPTED***';
        }

        return {
          key: configItem.key,
          name: configItem.name,
          description: configItem.description,
          type: configItem.type,
          dataType: configItem.dataType,
          value: configItem.value,
          defaultValue: configItem.defaultValue,
          group: configItem.group,
          isRequired: configItem.isRequired,
          isEncrypted: configItem.isEncrypted,
          isReadonly: configItem.isReadonly,
          validation: configItem.validation,
        };
      });

      return {
        success: true,
        data: {
          exportedAt: new Date().toISOString(),
          configs: exportData,
        },
      };
    } catch (error) {
      console.error('导出配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '导出配置失败',
      };
    }
  }

  /**
   * 导入配置
   */
  async importConfigs(
    data: any,
    userId: string = 'system',
    overwrite: boolean = false
  ): Promise<{ success: boolean; imported?: number; skipped?: number; errors?: string[]; error?: string }> {
    try {
      if (!data.configs || !Array.isArray(data.configs)) {
        return {
          success: false,
          error: '无效的配置数据格式',
        };
      }

      let imported = 0;
      let skipped = 0;
      const errors: string[] = [];

      for (const configData of data.configs) {
        try {
          const existingConfig = await this.getConfig(configData.key);
          
          if (existingConfig && !overwrite) {
            skipped++;
            continue;
          }

          if (existingConfig && overwrite) {
            // 更新现有配置
            const result = await this.setConfig(configData.key, configData.value, userId);
            if (result.success) {
              imported++;
            } else {
              errors.push(`${configData.key}: ${result.error}`);
            }
          } else {
            // 创建新配置
            const result = await this.createConfig({
              ...configData,
              createdBy: userId,
              updatedBy: userId,
            }, userId);
            
            if (result.success) {
              imported++;
            } else {
              errors.push(`${configData.key}: ${result.error}`);
            }
          }
        } catch (error) {
          errors.push(`${configData.key}: ${error instanceof Error ? error.message : '处理失败'}`);
        }
      }

      return {
        success: true,
        imported,
        skipped,
        errors: errors.length > 0 ? errors : undefined,
      };
    } catch (error) {
      console.error('导入配置失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '导入配置失败',
      };
    }
  }

  /**
   * 获取所有配置类型
   */
  getConfigTypes(): Array<{ value: ConfigType; label: string; description: string }> {
    return [
      { value: ConfigType.SYSTEM, label: '系统配置', description: '系统基础设置和参数' },
      { value: ConfigType.INTEGRATION, label: '集成配置', description: '第三方服务集成设置' },
      { value: ConfigType.SECURITY, label: '安全配置', description: '安全策略和认证设置' },
      { value: ConfigType.BUSINESS, label: '业务配置', description: '业务规则和流程设置' },
      { value: ConfigType.UI, label: '界面配置', description: '用户界面和显示设置' },
      { value: ConfigType.NOTIFICATION, label: '通知配置', description: '消息通知和提醒设置' },
      { value: ConfigType.PAYMENT, label: '支付配置', description: '支付网关和支付设置' },
      { value: ConfigType.SHIPPING, label: '物流配置', description: '物流服务和运输设置' },
      { value: ConfigType.TAX, label: '税务配置', description: '税率和税务计算设置' },
      { value: ConfigType.CURRENCY, label: '货币配置', description: '汇率和货币转换设置' },
    ];
  }

  /**
   * 验证配置值
   */
  private validateConfigValue(
    value: any,
    config: Partial<ConfigItem>
  ): { isValid: boolean; error?: string } {
    try {
      // 检查必需值
      if (config.isRequired && (value === null || value === undefined || value === '')) {
        return {
          isValid: false,
          error: '必需配置项不能为空',
        };
      }

      // 数据类型验证
      if (!this.validateDataType(value, config.dataType!)) {
        return {
          isValid: false,
          error: `配置值类型不匹配，期望类型: ${config.dataType}`,
        };
      }

      // 自定义验证规则
      if (config.validation) {
        const validationResult = this.validateWithRules(value, config.validation);
        if (!validationResult.isValid) {
          return validationResult;
        }
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '配置验证失败',
      };
    }
  }

  /**
   * 验证数据类型
   */
  private validateDataType(value: any, dataType: ConfigDataType): boolean {
    switch (dataType) {
      case ConfigDataType.STRING:
        return typeof value === 'string';
      case ConfigDataType.NUMBER:
        return typeof value === 'number' && !isNaN(value);
      case ConfigDataType.BOOLEAN:
        return typeof value === 'boolean';
      case ConfigDataType.JSON:
        try {
          if (typeof value === 'string') {
            JSON.parse(value);
          }
          return true;
        } catch {
          return false;
        }
      case ConfigDataType.ARRAY:
        return Array.isArray(value);
      case ConfigDataType.DATE:
        return value instanceof Date || !isNaN(Date.parse(value));
      case ConfigDataType.PASSWORD:
        return typeof value === 'string' && value.length > 0;
      case ConfigDataType.URL:
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case ConfigDataType.EMAIL:
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return typeof value === 'string' && emailRegex.test(value);
      case ConfigDataType.FILE:
        return typeof value === 'string'; // 文件路径
      default:
        return true;
    }
  }

  /**
   * 使用验证规则验证
   */
  private validateWithRules(
    value: any,
    validation: ConfigValidation
  ): { isValid: boolean; error?: string } {
    // 数值范围验证
    if (typeof value === 'number') {
      if (validation.min !== undefined && value < validation.min) {
        return {
          isValid: false,
          error: `值不能小于 ${validation.min}`,
        };
      }
      
      if (validation.max !== undefined && value > validation.max) {
        return {
          isValid: false,
          error: `值不能大于 ${validation.max}`,
        };
      }
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (validation.min !== undefined && value.length < validation.min) {
        return {
          isValid: false,
          error: `长度不能少于 ${validation.min} 个字符`,
        };
      }
      
      if (validation.max !== undefined && value.length > validation.max) {
        return {
          isValid: false,
          error: `长度不能超过 ${validation.max} 个字符`,
        };
      }
    }

    // 正则表达式验证
    if (validation.pattern && typeof value === 'string') {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        return {
          isValid: false,
          error: validation.errorMessage || '值格式不正确',
        };
      }
    }

    // 选项验证
    if (validation.options && validation.options.length > 0) {
      if (!validation.options.includes(value)) {
        return {
          isValid: false,
          error: `值必须是以下选项之一: ${validation.options.join(', ')}`,
        };
      }
    }

    return { isValid: true };
  }

  /**
   * 解析配置值
   */
  private parseConfigValue(value: any, dataType: ConfigDataType): any {
    switch (dataType) {
      case ConfigDataType.NUMBER:
        return typeof value === 'string' ? parseFloat(value) : value;
      case ConfigDataType.BOOLEAN:
        if (typeof value === 'string') {
          return value.toLowerCase() === 'true';
        }
        return Boolean(value);
      case ConfigDataType.JSON:
        if (typeof value === 'string') {
          try {
            return JSON.parse(value);
          } catch {
            return value;
          }
        }
        return value;
      case ConfigDataType.DATE:
        return value instanceof Date ? value : new Date(value);
      case ConfigDataType.ARRAY:
        if (typeof value === 'string') {
          try {
            return JSON.parse(value);
          } catch {
            return value.split(',').map(item => item.trim());
          }
        }
        return Array.isArray(value) ? value : [value];
      default:
        return value;
    }
  }

  /**
   * 加密值
   */
  private async encryptValue(value: string): Promise<string> {
    // 这里应该使用实际的加密算法
    // 简化实现，实际应该使用 crypto 模块
    return Buffer.from(value).toString('base64');
  }

  /**
   * 解密值
   */
  private async decryptValue(encryptedValue: string): Promise<string> {
    // 这里应该使用实际的解密算法
    // 简化实现，实际应该使用 crypto 模块
    return Buffer.from(encryptedValue, 'base64').toString();
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    if (!this.cacheTimestamp) {
      return false;
    }
    
    const now = new Date();
    return (now.getTime() - this.cacheTimestamp.getTime()) < this.CACHE_DURATION;
  }

  /**
   * 清除缓存
   */
  private clearCache(): void {
    this.configCache.clear();
    this.cacheTimestamp = null;
  }

  /**
   * 触发配置变更事件
   */
  private async triggerConfigChangeEvent(
    key: string,
    value: any,
    userId: string
  ): Promise<void> {
    try {
      // 记录配置变更日志
      await prisma.configChangeLog.create({
        data: {
          configKey: key,
          oldValue: null, // 这里应该记录旧值
          newValue: value,
          changedBy: userId,
          changedAt: new Date(),
        },
      });

      // 这里可以添加其他事件处理逻辑
      // 例如：发送通知、触发Webhook等
    } catch (error) {
      console.error('触发配置变更事件失败:', error);
    }
  }

  /**
   * 映射数据库配置到配置项
   */
  private mapDatabaseConfigToItem(dbConfig: any): ConfigItem {
    return {
      id: dbConfig.id,
      key: dbConfig.key,
      name: dbConfig.name,
      description: dbConfig.description,
      type: dbConfig.type as ConfigType,
      dataType: dbConfig.dataType as ConfigDataType,
      value: dbConfig.isEncrypted ? this.decryptValue(dbConfig.value) : dbConfig.value,
      defaultValue: dbConfig.defaultValue,
      isRequired: dbConfig.isRequired,
      isEncrypted: dbConfig.isEncrypted,
      isReadonly: dbConfig.isReadonly,
      validation: dbConfig.validation,
      group: dbConfig.group,
      order: dbConfig.order,
      createdAt: dbConfig.createdAt,
      updatedAt: dbConfig.updatedAt,
      createdBy: dbConfig.createdBy,
      updatedBy: dbConfig.updatedBy,
    };
  }
}

// 导出单例实例
export const configService = new ConfigService();
