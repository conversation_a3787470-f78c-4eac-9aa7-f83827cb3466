/**
 * 数据行级别权限验证服务
 * 提供基于数据行的细粒度权限控制
 */

import { prisma } from '@/lib/prisma';

// 行级权限规则接口
export interface RowLevelRule {
  resource: string;
  condition: (userId: string, resourceData: any) => Promise<boolean>;
  description: string;
}

// 权限上下文接口
export interface PermissionContext {
  userId: string;
  userRoles: string[];
  userDepartment?: string;
  userRegion?: string;
}

/**
 * 行级权限验证服务
 */
export class RowLevelSecurityService {
  private rules: Map<string, RowLevelRule[]> = new Map();

  constructor() {
    this.initializeRules();
  }

  /**
   * 初始化行级权限规则
   */
  private initializeRules() {
    // 订单行级权限规则
    this.addRule('orders', {
      resource: 'orders',
      description: '销售人员只能访问自己创建的订单',
      condition: async (userId: string, orderData: any) => {
        const user = await this.getUserContext(userId);
        
        // 超级管理员和管理员可以访问所有订单
        if (user.userRoles.includes('super_admin') || user.userRoles.includes('admin')) {
          return true;
        }

        // 订单管理员可以访问所有订单
        if (user.userRoles.includes('order_manager')) {
          return true;
        }

        // 销售人员只能访问自己创建的订单
        if (user.userRoles.includes('sales_rep')) {
          return orderData.createdBy === userId;
        }

        // 客服人员可以访问所有订单（只读）
        if (user.userRoles.includes('customer_service')) {
          return true;
        }

        return false;
      },
    });

    // 客户行级权限规则
    this.addRule('customers', {
      resource: 'customers',
      description: '销售人员只能访问自己负责的客户',
      condition: async (userId: string, customerData: any) => {
        const user = await this.getUserContext(userId);
        
        // 超级管理员和管理员可以访问所有客户
        if (user.userRoles.includes('super_admin') || user.userRoles.includes('admin')) {
          return true;
        }

        // 销售人员只能访问自己负责的客户
        if (user.userRoles.includes('sales_rep')) {
          return customerData.assignedSalesRep === userId;
        }

        // 客服人员可以访问所有客户
        if (user.userRoles.includes('customer_service')) {
          return true;
        }

        return false;
      },
    });

    // 商品行级权限规则
    this.addRule('products', {
      resource: 'products',
      description: '商品管理员可以访问所有商品，其他角色根据分类权限访问',
      condition: async (userId: string, productData: any) => {
        const user = await this.getUserContext(userId);
        
        // 超级管理员和管理员可以访问所有商品
        if (user.userRoles.includes('super_admin') || user.userRoles.includes('admin')) {
          return true;
        }

        // 商品管理员可以访问所有商品
        if (user.userRoles.includes('product_manager')) {
          return true;
        }

        // 其他角色可以查看已发布的商品
        return productData.status === 'ACTIVE';
      },
    });

    // 库存行级权限规则
    this.addRule('inventory', {
      resource: 'inventory',
      description: '仓库人员只能访问自己负责仓库的库存',
      condition: async (userId: string, inventoryData: any) => {
        const user = await this.getUserContext(userId);
        
        // 超级管理员和管理员可以访问所有库存
        if (user.userRoles.includes('super_admin') || user.userRoles.includes('admin')) {
          return true;
        }

        // 商品管理员可以访问所有库存
        if (user.userRoles.includes('product_manager')) {
          return true;
        }

        // 仓库人员只能访问自己负责仓库的库存
        if (user.userRoles.includes('warehouse_staff')) {
          const userWarehouses = await this.getUserWarehouses(userId);
          return userWarehouses.includes(inventoryData.warehouseId);
        }

        return false;
      },
    });

    // 用户行级权限规则
    this.addRule('users', {
      resource: 'users',
      description: '用户只能访问自己的信息，管理员可以访问所有用户',
      condition: async (userId: string, userData: any) => {
        const user = await this.getUserContext(userId);
        
        // 超级管理员可以访问所有用户
        if (user.userRoles.includes('super_admin')) {
          return true;
        }

        // 管理员可以访问非超级管理员用户
        if (user.userRoles.includes('admin')) {
          return !userData.roles?.includes('super_admin');
        }

        // 用户只能访问自己的信息
        return userData.id === userId;
      },
    });

    // 财务数据行级权限规则
    this.addRule('financial', {
      resource: 'financial',
      description: '财务数据只有特定角色可以访问',
      condition: async (userId: string, financialData: any) => {
        const user = await this.getUserContext(userId);
        
        // 超级管理员和管理员可以访问所有财务数据
        if (user.userRoles.includes('super_admin') || user.userRoles.includes('admin')) {
          return true;
        }

        // 分析师可以访问汇总财务数据
        if (user.userRoles.includes('analyst')) {
          return financialData.type === 'summary';
        }

        return false;
      },
    });
  }

  /**
   * 添加行级权限规则
   */
  addRule(resource: string, rule: RowLevelRule) {
    if (!this.rules.has(resource)) {
      this.rules.set(resource, []);
    }
    this.rules.get(resource)!.push(rule);
  }

  /**
   * 检查行级权限
   */
  async checkRowPermission(
    userId: string,
    resource: string,
    resourceData: any
  ): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const rules = this.rules.get(resource);
      if (!rules || rules.length === 0) {
        // 没有配置行级权限规则，默认允许
        return { allowed: true };
      }

      // 检查所有规则，只要有一个规则通过就允许访问
      for (const rule of rules) {
        const allowed = await rule.condition(userId, resourceData);
        if (allowed) {
          return { allowed: true };
        }
      }

      return { 
        allowed: false, 
        reason: `行级权限验证失败：用户 ${userId} 无权访问 ${resource} 资源` 
      };
    } catch (error) {
      console.error('行级权限检查错误:', error);
      return { 
        allowed: false, 
        reason: '行级权限检查失败' 
      };
    }
  }

  /**
   * 批量检查行级权限
   */
  async checkMultipleRowPermissions(
    userId: string,
    resource: string,
    resourceDataList: any[]
  ): Promise<{ allowed: any[]; denied: any[] }> {
    const allowed: any[] = [];
    const denied: any[] = [];

    for (const resourceData of resourceDataList) {
      const result = await this.checkRowPermission(userId, resource, resourceData);
      if (result.allowed) {
        allowed.push(resourceData);
      } else {
        denied.push(resourceData);
      }
    }

    return { allowed, denied };
  }

  /**
   * 为查询添加行级权限过滤条件
   */
  async addRowLevelFilter(
    userId: string,
    resource: string,
    baseQuery: any
  ): Promise<any> {
    const user = await this.getUserContext(userId);
    
    // 超级管理员不需要行级过滤
    if (user.userRoles.includes('super_admin')) {
      return baseQuery;
    }

    switch (resource) {
      case 'orders':
        if (user.userRoles.includes('admin') || user.userRoles.includes('order_manager')) {
          return baseQuery;
        }
        if (user.userRoles.includes('sales_rep')) {
          return {
            ...baseQuery,
            where: {
              ...baseQuery.where,
              createdBy: userId,
            },
          };
        }
        break;

      case 'customers':
        if (user.userRoles.includes('admin')) {
          return baseQuery;
        }
        if (user.userRoles.includes('sales_rep')) {
          return {
            ...baseQuery,
            where: {
              ...baseQuery.where,
              assignedSalesRep: userId,
            },
          };
        }
        break;

      case 'products':
        if (user.userRoles.includes('admin') || user.userRoles.includes('product_manager')) {
          return baseQuery;
        }
        return {
          ...baseQuery,
          where: {
            ...baseQuery.where,
            status: 'ACTIVE',
          },
        };

      case 'inventory':
        if (user.userRoles.includes('admin') || user.userRoles.includes('product_manager')) {
          return baseQuery;
        }
        if (user.userRoles.includes('warehouse_staff')) {
          const userWarehouses = await this.getUserWarehouses(userId);
          return {
            ...baseQuery,
            where: {
              ...baseQuery.where,
              warehouseId: { in: userWarehouses },
            },
          };
        }
        break;

      case 'users':
        if (user.userRoles.includes('super_admin')) {
          return baseQuery;
        }
        if (user.userRoles.includes('admin')) {
          return {
            ...baseQuery,
            where: {
              ...baseQuery.where,
              userRoles: {
                none: {
                  role: {
                    name: 'super_admin',
                  },
                },
              },
            },
          };
        }
        return {
          ...baseQuery,
          where: {
            ...baseQuery.where,
            id: userId,
          },
        };
    }

    return baseQuery;
  }

  /**
   * 获取用户上下文信息
   */
  private async getUserContext(userId: string): Promise<PermissionContext> {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      throw new Error(`用户 ${userId} 不存在`);
    }

    return {
      userId: user.id,
      userRoles: user.userRoles.map(ur => ur.role.name),
      userDepartment: user.department,
      userRegion: user.region,
    };
  }

  /**
   * 获取用户负责的仓库列表
   */
  private async getUserWarehouses(userId: string): Promise<string[]> {
    // 这里应该从数据库查询用户负责的仓库
    // 暂时返回模拟数据
    const userWarehouses = await prisma.userWarehouse.findMany({
      where: { userId },
      select: { warehouseId: true },
    });

    return userWarehouses.map(uw => uw.warehouseId);
  }
}

// 导出单例实例
export const rowLevelSecurity = new RowLevelSecurityService();
