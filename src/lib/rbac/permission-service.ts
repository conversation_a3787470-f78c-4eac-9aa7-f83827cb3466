/**
 * 权限验证服务
 * 提供细粒度的权限控制和验证功能
 */

import { prisma } from '@/lib/prisma';
import { UserSession } from '@/lib/auth';

// 权限上下文接口
export interface PermissionContext {
  userId: string;
  resource: string;
  action: string;
  resourceId?: string;
  metadata?: Record<string, any>;
}

// 权限检查结果接口
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  conditions?: Record<string, any>;
}

// 权限条件类型
export interface PermissionCondition {
  type: 'owner' | 'department' | 'location' | 'time' | 'custom';
  operator: 'equals' | 'in' | 'not_in' | 'greater_than' | 'less_than';
  value: any;
  field?: string;
}

// 用户权限缓存
interface UserPermissionCache {
  userId: string;
  permissions: string[];
  roles: string[];
  cachedAt: Date;
  expiresAt: Date;
}

class PermissionService {
  private permissionCache = new Map<string, UserPermissionCache>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  /**
   * 检查用户权限
   */
  async checkPermission(context: PermissionContext): Promise<PermissionCheckResult> {
    try {
      // 获取用户权限
      const userPermissions = await this.getUserPermissions(context.userId);
      
      // 构建权限名称
      const permissionName = `${context.resource}.${context.action}`;
      
      // 检查是否为超级管理员
      if (userPermissions.roles.includes('super_admin')) {
        return { allowed: true, reason: 'Super admin access' };
      }

      // 检查基础权限
      if (!userPermissions.permissions.includes(permissionName)) {
        return { 
          allowed: false, 
          reason: `Missing permission: ${permissionName}` 
        };
      }

      // 检查资源级别的权限条件
      if (context.resourceId) {
        const conditionResult = await this.checkResourceConditions(context, userPermissions);
        if (!conditionResult.allowed) {
          return conditionResult;
        }
      }

      return { allowed: true };
    } catch (error) {
      console.error('权限检查错误:', error);
      return { 
        allowed: false, 
        reason: 'Permission check failed' 
      };
    }
  }

  /**
   * 批量检查权限
   */
  async checkMultiplePermissions(
    userId: string, 
    permissions: Array<{ resource: string; action: string; resourceId?: string }>
  ): Promise<Record<string, PermissionCheckResult>> {
    const results: Record<string, PermissionCheckResult> = {};
    
    for (const perm of permissions) {
      const key = `${perm.resource}.${perm.action}${perm.resourceId ? `.${perm.resourceId}` : ''}`;
      results[key] = await this.checkPermission({
        userId,
        resource: perm.resource,
        action: perm.action,
        resourceId: perm.resourceId,
      });
    }
    
    return results;
  }

  /**
   * 获取用户权限（带缓存）
   */
  private async getUserPermissions(userId: string): Promise<{ permissions: string[]; roles: string[] }> {
    // 检查缓存
    const cached = this.permissionCache.get(userId);
    if (cached && cached.expiresAt > new Date()) {
      return {
        permissions: cached.permissions,
        roles: cached.roles,
      };
    }

    // 从数据库查询用户权限
    const userWithRoles = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        userRoles: {
          where: {
            OR: [
              { expiresAt: null },
              { expiresAt: { gt: new Date() } },
            ],
          },
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!userWithRoles) {
      throw new Error('用户不存在');
    }

    // 提取角色和权限
    const roles = userWithRoles.userRoles.map(ur => ur.role.name);
    const permissions = new Set<string>();

    userWithRoles.userRoles.forEach(userRole => {
      userRole.role.rolePermissions.forEach(rp => {
        permissions.add(rp.permission.name);
      });
    });

    const result = {
      permissions: Array.from(permissions),
      roles,
    };

    // 更新缓存
    this.permissionCache.set(userId, {
      userId,
      permissions: result.permissions,
      roles: result.roles,
      cachedAt: new Date(),
      expiresAt: new Date(Date.now() + this.CACHE_TTL),
    });

    return result;
  }

  /**
   * 检查资源级别的权限条件
   */
  private async checkResourceConditions(
    context: PermissionContext,
    userPermissions: { permissions: string[]; roles: string[] }
  ): Promise<PermissionCheckResult> {
    // 根据资源类型检查特定条件
    switch (context.resource) {
      case 'orders':
        return this.checkOrderPermissions(context, userPermissions);
      case 'customers':
        return this.checkCustomerPermissions(context, userPermissions);
      case 'products':
        return this.checkProductPermissions(context, userPermissions);
      case 'users':
        return this.checkUserPermissions(context, userPermissions);
      default:
        return { allowed: true }; // 默认允许
    }
  }

  /**
   * 检查订单权限
   */
  private async checkOrderPermissions(
    context: PermissionContext,
    userPermissions: { permissions: string[]; roles: string[] }
  ): Promise<PermissionCheckResult> {
    if (!context.resourceId) {
      return { allowed: true };
    }

    // 销售人员只能查看自己创建的订单
    if (userPermissions.roles.includes('sales_rep')) {
      const order = await prisma.order.findUnique({
        where: { id: context.resourceId },
        select: { createdBy: true },
      });

      if (order && order.createdBy !== context.userId) {
        return { 
          allowed: false, 
          reason: 'Sales representatives can only access their own orders' 
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 检查客户权限
   */
  private async checkCustomerPermissions(
    context: PermissionContext,
    userPermissions: { permissions: string[]; roles: string[] }
  ): Promise<PermissionCheckResult> {
    if (!context.resourceId) {
      return { allowed: true };
    }

    // 客服人员只能查看分配给自己的客户
    if (userPermissions.roles.includes('customer_service')) {
      // 这里可以实现客户分配逻辑
      // 暂时允许所有客服访问所有客户
      return { allowed: true };
    }

    return { allowed: true };
  }

  /**
   * 检查商品权限
   */
  private async checkProductPermissions(
    context: PermissionContext,
    userPermissions: { permissions: string[]; roles: string[] }
  ): Promise<PermissionCheckResult> {
    // 商品管理员可以访问所有商品
    if (userPermissions.roles.includes('product_manager')) {
      return { allowed: true };
    }

    // 普通用户只能查看已发布的商品
    if (context.action === 'read' && context.resourceId) {
      const product = await prisma.product.findUnique({
        where: { id: context.resourceId },
        select: { status: true },
      });

      if (product && product.status !== 'ACTIVE') {
        return { 
          allowed: false, 
          reason: 'Can only access active products' 
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 检查用户权限
   */
  private async checkUserPermissions(
    context: PermissionContext,
    userPermissions: { permissions: string[]; roles: string[] }
  ): Promise<PermissionCheckResult> {
    // 用户只能修改自己的信息
    if (context.action === 'update' && context.resourceId) {
      if (context.resourceId !== context.userId && !userPermissions.roles.includes('admin')) {
        return { 
          allowed: false, 
          reason: 'Users can only update their own information' 
        };
      }
    }

    return { allowed: true };
  }

  /**
   * 清除用户权限缓存
   */
  clearUserPermissionCache(userId: string): void {
    this.permissionCache.delete(userId);
  }

  /**
   * 清除所有权限缓存
   */
  clearAllPermissionCache(): void {
    this.permissionCache.clear();
  }

  /**
   * 获取用户可访问的资源列表
   */
  async getUserAccessibleResources(
    userId: string, 
    resource: string, 
    action: string
  ): Promise<string[]> {
    const userPermissions = await this.getUserPermissions(userId);
    const permissionName = `${resource}.${action}`;

    // 检查基础权限
    if (!userPermissions.permissions.includes(permissionName)) {
      return [];
    }

    // 如果是超级管理员，返回所有资源
    if (userPermissions.roles.includes('super_admin')) {
      // 这里可以查询所有资源ID
      return ['*']; // 表示所有资源
    }

    // 根据角色和权限条件返回可访问的资源
    // 这里可以实现更复杂的逻辑
    return ['*'];
  }
}

// 导出单例实例
export const permissionService = new PermissionService();

// 权限验证装饰器
export function RequirePermission(resource: string, action: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 假设第一个参数包含用户信息
      const context = args[0];
      if (!context?.userId) {
        throw new Error('未提供用户认证信息');
      }

      const permissionCheck = await permissionService.checkPermission({
        userId: context.userId,
        resource,
        action,
        resourceId: context.resourceId,
        metadata: context.metadata,
      });

      if (!permissionCheck.allowed) {
        throw new Error(`权限不足: ${permissionCheck.reason}`);
      }

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}
