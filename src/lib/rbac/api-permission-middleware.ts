/**
 * API权限验证中间件
 * 提供API级别的权限控制和验证
 */

import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/auth';
import { permissionService } from './permission-service';

// API权限配置接口
export interface ApiPermissionConfig {
  resource: string;
  action: string;
  requireAuth?: boolean;
  allowedRoles?: string[];
  customValidator?: (req: NextRequest, user: any) => Promise<boolean>;
}

// API权限映射
const API_PERMISSIONS: Record<string, Record<string, ApiPermissionConfig>> = {
  // 商品管理API权限
  '/api/products': {
    GET: { resource: 'products', action: 'read' },
    POST: { resource: 'products', action: 'create' },
    PUT: { resource: 'products', action: 'update' },
    DELETE: { resource: 'products', action: 'delete' },
  },
  '/api/products/[id]': {
    GET: { resource: 'products', action: 'read' },
    PUT: { resource: 'products', action: 'update' },
    DELETE: { resource: 'products', action: 'delete' },
  },
  '/api/products/import': {
    POST: { resource: 'products', action: 'import' },
  },
  '/api/products/export': {
    GET: { resource: 'products', action: 'export' },
  },

  // 订单管理API权限
  '/api/orders': {
    GET: { resource: 'orders', action: 'read' },
    POST: { resource: 'orders', action: 'create' },
    PUT: { resource: 'orders', action: 'update' },
    DELETE: { resource: 'orders', action: 'delete' },
  },
  '/api/orders/[id]': {
    GET: { resource: 'orders', action: 'read' },
    PUT: { resource: 'orders', action: 'update' },
    DELETE: { resource: 'orders', action: 'delete' },
  },
  '/api/orders/[id]/process': {
    POST: { resource: 'orders', action: 'process' },
  },
  '/api/orders/[id]/ship': {
    POST: { resource: 'orders', action: 'ship' },
  },
  '/api/orders/[id]/cancel': {
    POST: { resource: 'orders', action: 'cancel' },
  },
  '/api/orders/[id]/refund': {
    POST: { resource: 'orders', action: 'refund' },
  },

  // 客户管理API权限
  '/api/customers': {
    GET: { resource: 'customers', action: 'read' },
    POST: { resource: 'customers', action: 'create' },
    PUT: { resource: 'customers', action: 'update' },
    DELETE: { resource: 'customers', action: 'delete' },
  },
  '/api/customers/[id]': {
    GET: { resource: 'customers', action: 'read' },
    PUT: { resource: 'customers', action: 'update' },
    DELETE: { resource: 'customers', action: 'delete' },
  },
  '/api/customers/export': {
    GET: { resource: 'customers', action: 'export' },
  },

  // 库存管理API权限
  '/api/inventory': {
    GET: { resource: 'inventory', action: 'read' },
    POST: { resource: 'inventory', action: 'create' },
    PUT: { resource: 'inventory', action: 'update' },
    DELETE: { resource: 'inventory', action: 'delete' },
  },
  '/api/inventory/adjust': {
    POST: { resource: 'inventory', action: 'adjust' },
  },
  '/api/inventory/transfer': {
    POST: { resource: 'inventory', action: 'transfer' },
  },
  '/api/inventory/count': {
    POST: { resource: 'inventory', action: 'count' },
  },

  // 用户管理API权限
  '/api/users': {
    GET: { resource: 'users', action: 'read' },
    POST: { resource: 'users', action: 'create' },
    PUT: { resource: 'users', action: 'update' },
    DELETE: { resource: 'users', action: 'delete' },
  },
  '/api/users/[id]': {
    GET: { resource: 'users', action: 'read' },
    PUT: { 
      resource: 'users', 
      action: 'update',
      customValidator: async (req, user) => {
        // 用户只能修改自己的信息，除非是管理员
        const url = new URL(req.url);
        const userId = url.pathname.split('/').pop();
        return userId === user.id || user.roles.includes('admin') || user.roles.includes('super_admin');
      }
    },
    DELETE: { resource: 'users', action: 'delete' },
  },
  '/api/users/[id]/roles': {
    GET: { resource: 'users', action: 'manage_roles' },
    POST: { resource: 'users', action: 'manage_roles' },
    DELETE: { resource: 'users', action: 'manage_roles' },
  },

  // 角色管理API权限
  '/api/roles': {
    GET: { resource: 'roles', action: 'read' },
    POST: { resource: 'roles', action: 'create' },
    PUT: { resource: 'roles', action: 'update' },
    DELETE: { resource: 'roles', action: 'delete' },
  },
  '/api/roles/[id]': {
    GET: { resource: 'roles', action: 'read' },
    PUT: { resource: 'roles', action: 'update' },
    DELETE: { resource: 'roles', action: 'delete' },
  },
  '/api/roles/[id]/permissions': {
    GET: { resource: 'roles', action: 'assign_permissions' },
    POST: { resource: 'roles', action: 'assign_permissions' },
    DELETE: { resource: 'roles', action: 'assign_permissions' },
  },

  // 权限管理API权限
  '/api/permissions': {
    GET: { resource: 'permissions', action: 'read' },
    POST: { resource: 'permissions', action: 'create' },
    PUT: { resource: 'permissions', action: 'update' },
    DELETE: { resource: 'permissions', action: 'delete' },
  },

  // 报表API权限
  '/api/reports': {
    GET: { resource: 'reports', action: 'read' },
    POST: { resource: 'reports', action: 'create' },
  },
  '/api/reports/export': {
    GET: { resource: 'reports', action: 'export' },
  },

  // 系统设置API权限
  '/api/settings': {
    GET: { resource: 'settings', action: 'read' },
    PUT: { resource: 'settings', action: 'update' },
  },
  '/api/settings/backup': {
    POST: { resource: 'settings', action: 'backup' },
  },
  '/api/settings/restore': {
    POST: { resource: 'settings', action: 'restore' },
  },

  // 审计日志API权限
  '/api/audit': {
    GET: { resource: 'audit', action: 'read' },
  },
  '/api/audit/export': {
    GET: { resource: 'audit', action: 'export' },
  },
};

/**
 * API权限验证中间件
 */
export async function apiPermissionMiddleware(
  request: NextRequest,
  pathname: string
): Promise<NextResponse | null> {
  try {
    // 获取API权限配置
    const permissionConfig = getApiPermissionConfig(pathname, request.method);
    
    if (!permissionConfig) {
      // 没有配置权限要求，允许访问
      return null;
    }

    // 检查是否需要认证
    if (permissionConfig.requireAuth !== false) {
      // 获取用户信息
      const token = request.cookies.get('auth-token')?.value || 
                   request.headers.get('authorization')?.replace('Bearer ', '');
      
      if (!token) {
        return NextResponse.json(
          { success: false, error: { code: 'UNAUTHORIZED', message: '未提供认证信息' } },
          { status: 401 }
        );
      }

      let user;
      try {
        user = await verifyToken(token);
      } catch (error) {
        return NextResponse.json(
          { success: false, error: { code: 'INVALID_TOKEN', message: '认证信息无效' } },
          { status: 401 }
        );
      }

      // 检查角色权限
      if (permissionConfig.allowedRoles && permissionConfig.allowedRoles.length > 0) {
        const hasRole = permissionConfig.allowedRoles.some(role => user.roles.includes(role));
        if (!hasRole) {
          return NextResponse.json(
            { success: false, error: { code: 'FORBIDDEN', message: '角色权限不足' } },
            { status: 403 }
          );
        }
      }

      // 自定义验证器
      if (permissionConfig.customValidator) {
        const isValid = await permissionConfig.customValidator(request, user);
        if (!isValid) {
          return NextResponse.json(
            { success: false, error: { code: 'FORBIDDEN', message: '自定义权限验证失败' } },
            { status: 403 }
          );
        }
      }

      // 检查具体权限
      const permissionCheck = await permissionService.checkPermission({
        userId: user.id,
        resource: permissionConfig.resource,
        action: permissionConfig.action,
        resourceId: extractResourceId(pathname),
      });

      if (!permissionCheck.allowed) {
        return NextResponse.json(
          { 
            success: false, 
            error: { 
              code: 'FORBIDDEN', 
              message: permissionCheck.reason || '权限不足' 
            } 
          },
          { status: 403 }
        );
      }

      // 将用户信息添加到请求头
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', user.id);
      requestHeaders.set('x-user-email', user.email);
      requestHeaders.set('x-user-roles', JSON.stringify(user.roles));
      
      return NextResponse.next({
        request: {
          headers: requestHeaders,
        },
      });
    }

    return null;
  } catch (error) {
    console.error('API权限验证中间件错误:', error);
    return NextResponse.json(
      { success: false, error: { code: 'INTERNAL_ERROR', message: '权限验证失败' } },
      { status: 500 }
    );
  }
}

/**
 * 获取API权限配置
 */
function getApiPermissionConfig(pathname: string, method: string): ApiPermissionConfig | null {
  // 精确匹配
  if (API_PERMISSIONS[pathname] && API_PERMISSIONS[pathname][method]) {
    return API_PERMISSIONS[pathname][method];
  }

  // 动态路径匹配
  for (const [pattern, methods] of Object.entries(API_PERMISSIONS)) {
    if (matchDynamicPath(pattern, pathname) && methods[method]) {
      return methods[method];
    }
  }

  return null;
}

/**
 * 匹配动态路径
 */
function matchDynamicPath(pattern: string, pathname: string): boolean {
  const patternParts = pattern.split('/');
  const pathnameParts = pathname.split('/');

  if (patternParts.length !== pathnameParts.length) {
    return false;
  }

  for (let i = 0; i < patternParts.length; i++) {
    const patternPart = patternParts[i];
    const pathnamePart = pathnameParts[i];

    if (patternPart.startsWith('[') && patternPart.endsWith(']')) {
      // 动态路径段，跳过检查
      continue;
    }

    if (patternPart !== pathnamePart) {
      return false;
    }
  }

  return true;
}

/**
 * 从路径中提取资源ID
 */
function extractResourceId(pathname: string): string | undefined {
  const parts = pathname.split('/');
  // 假设资源ID是路径中的最后一个UUID格式的部分
  for (let i = parts.length - 1; i >= 0; i--) {
    const part = parts[i];
    if (part && /^[a-zA-Z0-9-_]{8,}$/.test(part)) {
      return part;
    }
  }
  return undefined;
}
