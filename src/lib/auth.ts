/**
 * 身份认证和授权工具函数
 * 提供JWT令牌管理、密码加密、权限验证等功能
 */

import { SignJWT, jwtVerify } from 'jose';
import bcrypt from 'bcryptjs';
import { cookies } from 'next/headers';
import { NextRequest } from 'next/server';
import prisma from './prisma';

// JWT配置
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || 'your-super-secret-jwt-key'
);
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '30d';

// 用户会话接口
export interface UserSession {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  roles: string[];
  permissions: string[];
  iat: number;
  exp: number;
}

// 登录凭据接口
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// 认证结果接口
export interface AuthResult {
  success: boolean;
  user?: UserSession;
  tokens?: {
    accessToken: string;
    refreshToken: string;
    expiresIn: number;
  };
  error?: string;
}

/**
 * 密码加密
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

/**
 * 密码验证
 */
export async function verifyPassword(
  password: string,
  hashedPassword: string
): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

/**
 * 生成JWT令牌
 */
export async function generateTokens(userId: string): Promise<{
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}> {
  // 获取用户信息和权限
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      userRoles: {
        include: {
          role: {
            include: {
              rolePermissions: {
                include: {
                  permission: true,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!user) {
    throw new Error('用户不存在');
  }

  // 提取角色和权限
  const roles = user.userRoles.map(ur => ur.role.name);
  const permissions = user.userRoles.flatMap(ur =>
    ur.role.rolePermissions.map(rp => rp.permission.name)
  );

  // 生成访问令牌
  const accessToken = await new SignJWT({
    sub: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    roles,
    permissions,
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(JWT_EXPIRES_IN)
    .sign(JWT_SECRET);

  // 生成刷新令牌
  const refreshToken = await new SignJWT({
    sub: user.id,
    type: 'refresh',
  })
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime(JWT_REFRESH_EXPIRES_IN)
    .sign(JWT_SECRET);

  // 计算过期时间（秒）
  const expiresIn = JWT_EXPIRES_IN.includes('d')
    ? parseInt(JWT_EXPIRES_IN) * 24 * 60 * 60
    : parseInt(JWT_EXPIRES_IN);

  return {
    accessToken,
    refreshToken,
    expiresIn,
  };
}

/**
 * 验证JWT令牌
 */
export async function verifyToken(token: string): Promise<UserSession | null> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET);
    
    return {
      id: payload.sub as string,
      email: payload.email as string,
      firstName: payload.firstName as string,
      lastName: payload.lastName as string,
      roles: payload.roles as string[],
      permissions: payload.permissions as string[],
      iat: payload.iat as number,
      exp: payload.exp as number,
    };
  } catch (error) {
    console.error('JWT验证失败:', error);
    return null;
  }
}

/**
 * 刷新访问令牌
 */
export async function refreshAccessToken(refreshToken: string): Promise<{
  accessToken: string;
  expiresIn: number;
} | null> {
  try {
    const { payload } = await jwtVerify(refreshToken, JWT_SECRET);
    
    if (payload.type !== 'refresh') {
      throw new Error('无效的刷新令牌');
    }

    const userId = payload.sub as string;
    const tokens = await generateTokens(userId);
    
    return {
      accessToken: tokens.accessToken,
      expiresIn: tokens.expiresIn,
    };
  } catch (error) {
    console.error('刷新令牌失败:', error);
    return null;
  }
}

/**
 * 用户登录
 */
export async function login(credentials: LoginCredentials): Promise<AuthResult> {
  try {
    const { email, password, rememberMe } = credentials;

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      include: {
        userRoles: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!user) {
      return {
        success: false,
        error: '用户不存在',
      };
    }

    // 检查用户状态
    if (user.status !== 'ACTIVE') {
      return {
        success: false,
        error: '账户已被禁用',
      };
    }

    // 验证密码
    const isPasswordValid = await verifyPassword(password, user.passwordHash);
    if (!isPasswordValid) {
      return {
        success: false,
        error: '密码错误',
      };
    }

    // 生成令牌
    const tokens = await generateTokens(user.id);

    // 更新最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // 创建用户会话记录
    await prisma.userSession.create({
      data: {
        userId: user.id,
        token: tokens.accessToken,
        ipAddress: '', // 在中间件中设置
        userAgent: '', // 在中间件中设置
        expiresAt: new Date(Date.now() + tokens.expiresIn * 1000),
      },
    });

    // 提取用户信息
    const roles = user.userRoles.map(ur => ur.role.name);
    const permissions = user.userRoles.flatMap(ur =>
      ur.role.rolePermissions.map(rp => rp.permission.name)
    );

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName || undefined,
        lastName: user.lastName || undefined,
        roles,
        permissions,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + tokens.expiresIn,
      },
      tokens,
    };
  } catch (error) {
    console.error('登录失败:', error);
    return {
      success: false,
      error: '登录过程中发生错误',
    };
  }
}

/**
 * 用户登出
 */
export async function logout(token: string): Promise<void> {
  try {
    // 删除用户会话记录
    await prisma.userSession.deleteMany({
      where: { token },
    });
  } catch (error) {
    console.error('登出失败:', error);
  }
}

/**
 * 从请求中获取当前用户
 */
export async function getCurrentUser(request: NextRequest): Promise<UserSession | null> {
  try {
    // 从Authorization头获取令牌
    const authHeader = request.headers.get('authorization');
    let token: string | undefined;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    } else {
      // 从Cookie获取令牌
      const cookieStore = cookies();
      token = cookieStore.get('access_token')?.value;
    }

    if (!token) {
      return null;
    }

    return await verifyToken(token);
  } catch (error) {
    console.error('获取当前用户失败:', error);
    return null;
  }
}

/**
 * 检查用户权限
 */
export async function checkPermission(
  user: UserSession,
  resource: string,
  action: string
): Promise<boolean> {
  // 超级管理员拥有所有权限
  if (user.roles.includes('super_admin')) {
    return true;
  }

  // 检查具体权限
  const permissionName = `${resource}.${action}`;
  return user.permissions.includes(permissionName);
}

/**
 * 权限验证装饰器
 */
export function requirePermission(resource: string, action: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const user = args[0] as UserSession; // 假设第一个参数是用户信息
      
      if (!user) {
        throw new Error('未认证的用户');
      }

      const hasPermission = await checkPermission(user, resource, action);
      if (!hasPermission) {
        throw new Error('权限不足');
      }

      return originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

/**
 * 生成安全的随机字符串
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}
