/**
 * 数据验证模式定义
 * 使用Zod库定义各种数据验证规则
 */

import { z } from 'zod';

// =============================================================================
// 基础验证规则
// =============================================================================

// 邮箱验证
export const emailSchema = z
  .string()
  .email('邮箱格式不正确')
  .min(1, '邮箱不能为空');

// 密码验证
export const passwordSchema = z
  .string()
  .min(8, '密码至少8位')
  .max(128, '密码不能超过128位')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, '密码必须包含大小写字母和数字');

// 手机号验证（中国）
export const phoneSchema = z
  .string()
  .regex(/^1[3-9]\d{9}$/, '手机号格式不正确')
  .optional();

// URL验证
export const urlSchema = z
  .string()
  .url('URL格式不正确')
  .optional();

// 货币金额验证
export const currencySchema = z
  .number()
  .positive('金额必须大于0')
  .max(999999999.99, '金额过大');

// SKU验证
export const skuSchema = z
  .string()
  .min(3, 'SKU至少3位')
  .max(50, 'SKU不能超过50位')
  .regex(/^[A-Z0-9-_]+$/, 'SKU只能包含大写字母、数字、连字符和下划线');

// =============================================================================
// 用户相关验证
// =============================================================================

// 用户登录验证
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, '密码不能为空'),
  rememberMe: z.boolean().optional().default(false),
});

// 用户注册验证
export const registerSchema = z.object({
  email: emailSchema,
  username: z
    .string()
    .min(3, '用户名至少3位')
    .max(20, '用户名不能超过20位')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线')
    .optional(),
  password: passwordSchema,
  confirmPassword: z.string(),
  firstName: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50位'),
  lastName: z.string().max(50, '姓名不能超过50位').optional(),
  phone: phoneSchema,
  languageCode: z.string().default('zh-CN'),
  timezone: z.string().default('Asia/Shanghai'),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

// 用户更新验证
export const updateUserSchema = z.object({
  firstName: z.string().min(1, '姓名不能为空').max(50, '姓名不能超过50位').optional(),
  lastName: z.string().max(50, '姓名不能超过50位').optional(),
  phone: phoneSchema,
  languageCode: z.string().optional(),
  timezone: z.string().optional(),
  status: z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']).optional(),
  roles: z.array(z.string()).optional(),
});

// 密码重置验证
export const resetPasswordSchema = z.object({
  token: z.string().min(1, '重置令牌不能为空'),
  password: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

// =============================================================================
// 商品相关验证
// =============================================================================

// 商品创建验证
export const createProductSchema = z.object({
  sku: skuSchema,
  name: z.string().min(1, '商品名称不能为空').max(200, '商品名称不能超过200位'),
  description: z.string().max(5000, '商品描述不能超过5000位').optional(),
  shortDescription: z.string().max(500, '简短描述不能超过500位').optional(),
  categoryId: z.string().optional(),
  brand: z.string().max(100, '品牌名称不能超过100位').optional(),
  model: z.string().max(100, '型号不能超过100位').optional(),
  weight: z.number().positive('重量必须大于0').optional(),
  dimensions: z.object({
    length: z.number().positive('长度必须大于0'),
    width: z.number().positive('宽度必须大于0'),
    height: z.number().positive('高度必须大于0'),
    unit: z.enum(['cm', 'inch']),
  }).optional(),
  basePrice: currencySchema,
  costPrice: currencySchema.optional(),
  currency: z.string().default('USD'),
  taxClass: z.string().optional(),
  isDigital: z.boolean().default(false),
  requiresShipping: z.boolean().default(true),
  trackInventory: z.boolean().default(true),
  allowBackorder: z.boolean().default(false),
  metaData: z.record(z.any()).optional(),
  seoTitle: z.string().max(200, 'SEO标题不能超过200位').optional(),
  seoDescription: z.string().max(500, 'SEO描述不能超过500位').optional(),
});

// 商品更新验证
export const updateProductSchema = createProductSchema.partial();

// 商品分类验证
export const createCategorySchema = z.object({
  parentId: z.string().optional(),
  name: z.string().min(1, '分类名称不能为空').max(100, '分类名称不能超过100位'),
  slug: z.string().min(1, 'URL别名不能为空').max(100, 'URL别名不能超过100位'),
  description: z.string().max(1000, '分类描述不能超过1000位').optional(),
  imageUrl: urlSchema,
  sortOrder: z.number().int().min(0, '排序必须大于等于0').default(0),
  isActive: z.boolean().default(true),
  seoTitle: z.string().max(200, 'SEO标题不能超过200位').optional(),
  seoDescription: z.string().max(500, 'SEO描述不能超过500位').optional(),
});

// =============================================================================
// 订单相关验证
// =============================================================================

// 地址验证
export const addressSchema = z.object({
  firstName: z.string().max(50, '姓名不能超过50位').optional(),
  lastName: z.string().max(50, '姓名不能超过50位').optional(),
  company: z.string().max(100, '公司名称不能超过100位').optional(),
  addressLine1: z.string().min(1, '地址不能为空').max(200, '地址不能超过200位'),
  addressLine2: z.string().max(200, '地址不能超过200位').optional(),
  city: z.string().min(1, '城市不能为空').max(100, '城市不能超过100位'),
  stateProvince: z.string().max(100, '省份不能超过100位').optional(),
  postalCode: z.string().max(20, '邮编不能超过20位').optional(),
  countryCode: z.string().length(2, '国家代码必须是2位'),
  phone: phoneSchema,
});

// 订单商品验证
export const orderItemSchema = z.object({
  productId: z.string().optional(),
  variantId: z.string().optional(),
  sku: z.string().min(1, 'SKU不能为空'),
  name: z.string().min(1, '商品名称不能为空'),
  quantity: z.number().int().positive('数量必须大于0'),
  unitPrice: currencySchema,
  totalPrice: currencySchema,
  costPrice: currencySchema.optional(),
  weight: z.number().positive().optional(),
  productData: z.record(z.any()).optional(),
});

// 订单创建验证
export const createOrderSchema = z.object({
  customerId: z.string().optional(),
  currency: z.string().default('USD'),
  exchangeRate: z.number().positive('汇率必须大于0').default(1.0),
  subtotal: currencySchema,
  taxAmount: currencySchema.default(0),
  shippingAmount: currencySchema.default(0),
  discountAmount: currencySchema.default(0),
  totalAmount: currencySchema,
  billingAddress: addressSchema,
  shippingAddress: addressSchema,
  notes: z.string().max(1000, '备注不能超过1000位').optional(),
  internalNotes: z.string().max(1000, '内部备注不能超过1000位').optional(),
  source: z.string().optional(),
  sourceOrderId: z.string().optional(),
  items: z.array(orderItemSchema).min(1, '订单必须包含至少一个商品'),
});

// =============================================================================
// 客户相关验证
// =============================================================================

// 客户创建验证
export const createCustomerSchema = z.object({
  email: emailSchema,
  firstName: z.string().max(50, '姓名不能超过50位').optional(),
  lastName: z.string().max(50, '姓名不能超过50位').optional(),
  phone: phoneSchema,
  dateOfBirth: z.string().optional(),
  gender: z.enum(['MALE', 'FEMALE', 'OTHER']).optional(),
  languageCode: z.string().default('zh-CN'),
  currencyCode: z.string().default('USD'),
  timezone: z.string().default('Asia/Shanghai'),
  customerGroup: z.string().default('default'),
  notes: z.string().max(1000, '备注不能超过1000位').optional(),
  tags: z.array(z.string()).default([]),
  marketingConsent: z.boolean().default(false),
});

// 客户地址验证
export const customerAddressSchema = addressSchema.extend({
  type: z.enum(['BILLING', 'SHIPPING']).default('SHIPPING'),
  isDefault: z.boolean().default(false),
});

// =============================================================================
// 库存相关验证
// =============================================================================

// 仓库创建验证
export const createWarehouseSchema = z.object({
  code: z.string().min(1, '仓库代码不能为空').max(20, '仓库代码不能超过20位'),
  name: z.string().min(1, '仓库名称不能为空').max(100, '仓库名称不能超过100位'),
  address: addressSchema,
  contactInfo: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

// 库存调整验证
export const inventoryAdjustmentSchema = z.object({
  warehouseId: z.string().min(1, '仓库ID不能为空'),
  productId: z.string().optional(),
  variantId: z.string().optional(),
  sku: z.string().min(1, 'SKU不能为空'),
  quantityChange: z.number().int('数量变化必须是整数'),
  reason: z.string().min(1, '调整原因不能为空').max(200, '调整原因不能超过200位'),
  notes: z.string().max(500, '备注不能超过500位').optional(),
  costPerUnit: currencySchema.optional(),
});

// =============================================================================
// 支付相关验证
// =============================================================================

// 支付创建验证
export const createPaymentSchema = z.object({
  orderId: z.string().min(1, '订单ID不能为空'),
  paymentMethod: z.string().min(1, '支付方式不能为空'),
  paymentProvider: z.string().min(1, '支付提供商不能为空'),
  amount: currencySchema,
  currency: z.string().default('USD'),
  exchangeRate: z.number().positive('汇率必须大于0').default(1.0),
  providerPaymentId: z.string().optional(),
  providerResponse: z.record(z.any()).optional(),
});

// 退款创建验证
export const createRefundSchema = z.object({
  paymentId: z.string().min(1, '支付ID不能为空'),
  orderId: z.string().min(1, '订单ID不能为空'),
  amount: currencySchema,
  currency: z.string().default('USD'),
  reason: z.string().max(500, '退款原因不能超过500位').optional(),
});

// =============================================================================
// 物流相关验证
// =============================================================================

// 发货创建验证
export const createShipmentSchema = z.object({
  orderId: z.string().min(1, '订单ID不能为空'),
  carrierId: z.string().min(1, '承运商ID不能为空'),
  trackingNumber: z.string().optional(),
  serviceType: z.string().optional(),
  shippingCost: currencySchema.optional(),
  weight: z.number().positive().optional(),
  dimensions: z.object({
    length: z.number().positive(),
    width: z.number().positive(),
    height: z.number().positive(),
    unit: z.enum(['cm', 'inch']),
  }).optional(),
  shippingAddress: addressSchema,
  estimatedDelivery: z.string().optional(),
});

// =============================================================================
// 分页和过滤验证
// =============================================================================

// 分页参数验证
export const paginationSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).default('desc'),
});

// 搜索过滤验证
export const searchFilterSchema = z.object({
  search: z.string().optional(),
  status: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
});

// =============================================================================
// 导出验证模式类型
// =============================================================================

export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type UpdateUserInput = z.infer<typeof updateUserSchema>;
export type CreateProductInput = z.infer<typeof createProductSchema>;
export type UpdateProductInput = z.infer<typeof updateProductSchema>;
export type CreateCategoryInput = z.infer<typeof createCategorySchema>;
export type CreateOrderInput = z.infer<typeof createOrderSchema>;
export type CreateCustomerInput = z.infer<typeof createCustomerSchema>;
export type CreateWarehouseInput = z.infer<typeof createWarehouseSchema>;
export type InventoryAdjustmentInput = z.infer<typeof inventoryAdjustmentSchema>;
export type CreatePaymentInput = z.infer<typeof createPaymentSchema>;
export type CreateRefundInput = z.infer<typeof createRefundSchema>;
export type CreateShipmentInput = z.infer<typeof createShipmentSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;
export type SearchFilterInput = z.infer<typeof searchFilterSchema>;
export type AddressInput = z.infer<typeof addressSchema>;
