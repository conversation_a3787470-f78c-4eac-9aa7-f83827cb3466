/**
 * 货币服务
 * 提供货币格式化、价格转换、本地化显示等功能
 */

import { 
  SupportedCurrency, 
  getCurrencyInfo, 
  getCurrencyFormatter,
  getCurrencyPrecision,
  getCurrencyRounding,
  DEFAULT_CURRENCY,
  PRICE_PRECISION_CONFIG,
} from './config';
import { exchangeRateService, CurrencyConversionResult } from './exchange-rate-service';

// 价格格式化选项
export interface PriceFormatOptions {
  currency?: SupportedCurrency;
  locale?: string;
  showSymbol?: boolean;
  showCode?: boolean;
  precision?: number;
  useGrouping?: boolean;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  style?: 'currency' | 'decimal' | 'percent';
}

// 价格转换选项
export interface PriceConversionOptions {
  fromCurrency: SupportedCurrency;
  toCurrency: SupportedCurrency;
  useCache?: boolean;
  maxAge?: number;
  roundToMinorUnit?: boolean;
}

// 多货币价格接口
export interface MultiCurrencyPrice {
  baseCurrency: SupportedCurrency;
  baseAmount: number;
  prices: Array<{
    currency: SupportedCurrency;
    amount: number;
    rate: number;
    lastUpdated: Date;
  }>;
}

// 价格范围接口
export interface PriceRange {
  currency: SupportedCurrency;
  minPrice: number;
  maxPrice: number;
  averagePrice?: number;
}

/**
 * 货币服务类
 */
export class CurrencyService {
  private defaultCurrency: SupportedCurrency = DEFAULT_CURRENCY;
  private defaultLocale: string = 'en-US';

  /**
   * 设置默认货币
   */
  setDefaultCurrency(currency: SupportedCurrency): void {
    this.defaultCurrency = currency;
  }

  /**
   * 获取默认货币
   */
  getDefaultCurrency(): SupportedCurrency {
    return this.defaultCurrency;
  }

  /**
   * 设置默认区域设置
   */
  setDefaultLocale(locale: string): void {
    this.defaultLocale = locale;
  }

  /**
   * 格式化价格
   */
  formatPrice(
    amount: number,
    options: PriceFormatOptions = {}
  ): string {
    const {
      currency = this.defaultCurrency,
      locale = this.defaultLocale,
      showSymbol = true,
      showCode = false,
      precision,
      useGrouping = true,
      minimumFractionDigits,
      maximumFractionDigits,
      style = 'currency',
    } = options;

    try {
      const currencyInfo = getCurrencyInfo(currency);
      if (!currencyInfo) {
        return amount.toString();
      }

      // 确定小数位数
      const decimalPlaces = precision !== undefined 
        ? precision 
        : minimumFractionDigits !== undefined 
          ? minimumFractionDigits 
          : currencyInfo.decimalPlaces;

      const maxDecimalPlaces = maximumFractionDigits !== undefined 
        ? maximumFractionDigits 
        : decimalPlaces;

      if (style === 'currency' && showSymbol) {
        // 使用Intl.NumberFormat进行货币格式化
        const formatter = new Intl.NumberFormat(locale, {
          style: 'currency',
          currency: currency,
          minimumFractionDigits: decimalPlaces,
          maximumFractionDigits: maxDecimalPlaces,
          useGrouping,
        });

        let formatted = formatter.format(amount);

        // 如果需要显示货币代码
        if (showCode) {
          formatted += ` ${currency}`;
        }

        return formatted;
      } else {
        // 自定义格式化
        const formatter = new Intl.NumberFormat(locale, {
          style: style === 'percent' ? 'percent' : 'decimal',
          minimumFractionDigits: decimalPlaces,
          maximumFractionDigits: maxDecimalPlaces,
          useGrouping,
        });

        let formatted = formatter.format(amount);

        if (showSymbol && style !== 'percent') {
          const symbol = currencyInfo.symbol;
          formatted = currencyInfo.symbolPosition === 'before' 
            ? `${symbol}${formatted}` 
            : `${formatted}${symbol}`;
        }

        if (showCode) {
          formatted += ` ${currency}`;
        }

        return formatted;
      }
    } catch (error) {
      console.error('价格格式化失败:', error);
      return amount.toString();
    }
  }

  /**
   * 解析价格字符串
   */
  parsePrice(
    priceString: string,
    currency: SupportedCurrency = this.defaultCurrency
  ): number | null {
    try {
      const currencyInfo = getCurrencyInfo(currency);
      if (!currencyInfo) return null;

      // 移除货币符号和代码
      let cleanString = priceString
        .replace(new RegExp(currencyInfo.symbol, 'g'), '')
        .replace(new RegExp(currency, 'g'), '')
        .trim();

      // 处理千位分隔符和小数分隔符
      if (currencyInfo.thousandsSeparator !== currencyInfo.decimalSeparator) {
        // 先移除千位分隔符
        cleanString = cleanString.replace(
          new RegExp(`\\${currencyInfo.thousandsSeparator}`, 'g'), 
          ''
        );
        
        // 将小数分隔符转换为点号
        if (currencyInfo.decimalSeparator !== '.') {
          cleanString = cleanString.replace(
            new RegExp(`\\${currencyInfo.decimalSeparator}`, 'g'), 
            '.'
          );
        }
      }

      const parsed = parseFloat(cleanString);
      return isNaN(parsed) ? null : parsed;
    } catch (error) {
      console.error('价格解析失败:', error);
      return null;
    }
  }

  /**
   * 转换价格
   */
  async convertPrice(
    amount: number,
    options: PriceConversionOptions
  ): Promise<{ success: boolean; result?: CurrencyConversionResult; error?: string }> {
    try {
      const {
        fromCurrency,
        toCurrency,
        useCache = true,
        maxAge = 30,
        roundToMinorUnit = true,
      } = options;

      const conversionResult = await exchangeRateService.convertCurrency(
        amount,
        fromCurrency,
        toCurrency,
        { useCache, maxAge }
      );

      if (!conversionResult.success || !conversionResult.result) {
        return {
          success: false,
          error: conversionResult.error,
        };
      }

      let result = conversionResult.result;

      // 舍入到最小货币单位
      if (roundToMinorUnit) {
        const rounding = getCurrencyRounding(toCurrency);
        result.toAmount = Math.round(result.toAmount / rounding) * rounding;
      }

      return {
        success: true,
        result,
      };
    } catch (error) {
      console.error('价格转换失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '价格转换失败',
      };
    }
  }

  /**
   * 批量价格转换
   */
  async convertPricesBatch(
    prices: Array<{
      amount: number;
      fromCurrency: SupportedCurrency;
      toCurrency: SupportedCurrency;
    }>,
    options: {
      useCache?: boolean;
      maxAge?: number;
      roundToMinorUnit?: boolean;
    } = {}
  ): Promise<{ success: boolean; results?: CurrencyConversionResult[]; error?: string }> {
    try {
      const {
        useCache = true,
        maxAge = 30,
        roundToMinorUnit = true,
      } = options;

      const results: CurrencyConversionResult[] = [];

      for (const price of prices) {
        const conversionResult = await this.convertPrice(price.amount, {
          fromCurrency: price.fromCurrency,
          toCurrency: price.toCurrency,
          useCache,
          maxAge,
          roundToMinorUnit,
        });

        if (conversionResult.success && conversionResult.result) {
          results.push(conversionResult.result);
        } else {
          return {
            success: false,
            error: conversionResult.error,
          };
        }
      }

      return {
        success: true,
        results,
      };
    } catch (error) {
      console.error('批量价格转换失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量价格转换失败',
      };
    }
  }

  /**
   * 创建多货币价格
   */
  async createMultiCurrencyPrice(
    baseAmount: number,
    baseCurrency: SupportedCurrency,
    targetCurrencies: SupportedCurrency[]
  ): Promise<{ success: boolean; price?: MultiCurrencyPrice; error?: string }> {
    try {
      const prices: MultiCurrencyPrice['prices'] = [];

      for (const currency of targetCurrencies) {
        if (currency === baseCurrency) {
          prices.push({
            currency,
            amount: baseAmount,
            rate: 1,
            lastUpdated: new Date(),
          });
          continue;
        }

        const conversionResult = await this.convertPrice(baseAmount, {
          fromCurrency: baseCurrency,
          toCurrency: currency,
        });

        if (conversionResult.success && conversionResult.result) {
          prices.push({
            currency,
            amount: conversionResult.result.toAmount,
            rate: conversionResult.result.rate,
            lastUpdated: conversionResult.result.timestamp,
          });
        } else {
          return {
            success: false,
            error: `转换到${currency}失败: ${conversionResult.error}`,
          };
        }
      }

      const multiCurrencyPrice: MultiCurrencyPrice = {
        baseCurrency,
        baseAmount,
        prices,
      };

      return {
        success: true,
        price: multiCurrencyPrice,
      };
    } catch (error) {
      console.error('创建多货币价格失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建多货币价格失败',
      };
    }
  }

  /**
   * 比较价格
   */
  async comparePrices(
    price1: { amount: number; currency: SupportedCurrency },
    price2: { amount: number; currency: SupportedCurrency },
    baseCurrency?: SupportedCurrency
  ): Promise<{ success: boolean; comparison?: number; error?: string }> {
    try {
      const base = baseCurrency || this.defaultCurrency;

      // 将两个价格都转换为基础货币
      let amount1 = price1.amount;
      let amount2 = price2.amount;

      if (price1.currency !== base) {
        const conversion1 = await this.convertPrice(price1.amount, {
          fromCurrency: price1.currency,
          toCurrency: base,
        });

        if (!conversion1.success || !conversion1.result) {
          return {
            success: false,
            error: conversion1.error,
          };
        }

        amount1 = conversion1.result.toAmount;
      }

      if (price2.currency !== base) {
        const conversion2 = await this.convertPrice(price2.amount, {
          fromCurrency: price2.currency,
          toCurrency: base,
        });

        if (!conversion2.success || !conversion2.result) {
          return {
            success: false,
            error: conversion2.error,
          };
        }

        amount2 = conversion2.result.toAmount;
      }

      // 比较价格
      // 返回值: -1 表示 price1 < price2, 0 表示相等, 1 表示 price1 > price2
      const comparison = amount1 < amount2 ? -1 : amount1 > amount2 ? 1 : 0;

      return {
        success: true,
        comparison,
      };
    } catch (error) {
      console.error('价格比较失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '价格比较失败',
      };
    }
  }

  /**
   * 计算价格范围
   */
  calculatePriceRange(
    prices: Array<{ amount: number; currency: SupportedCurrency }>,
    targetCurrency: SupportedCurrency = this.defaultCurrency
  ): Promise<{ success: boolean; range?: PriceRange; error?: string }> {
    // 这个方法需要异步处理，因为涉及货币转换
    return new Promise(async (resolve) => {
      try {
        if (prices.length === 0) {
          resolve({
            success: false,
            error: '价格列表为空',
          });
          return;
        }

        const convertedPrices: number[] = [];

        for (const price of prices) {
          if (price.currency === targetCurrency) {
            convertedPrices.push(price.amount);
          } else {
            const conversionResult = await this.convertPrice(price.amount, {
              fromCurrency: price.currency,
              toCurrency: targetCurrency,
            });

            if (conversionResult.success && conversionResult.result) {
              convertedPrices.push(conversionResult.result.toAmount);
            } else {
              resolve({
                success: false,
                error: conversionResult.error,
              });
              return;
            }
          }
        }

        const minPrice = Math.min(...convertedPrices);
        const maxPrice = Math.max(...convertedPrices);
        const averagePrice = convertedPrices.reduce((sum, price) => sum + price, 0) / convertedPrices.length;

        const range: PriceRange = {
          currency: targetCurrency,
          minPrice,
          maxPrice,
          averagePrice,
        };

        resolve({
          success: true,
          range,
        });
      } catch (error) {
        console.error('计算价格范围失败:', error);
        resolve({
          success: false,
          error: error instanceof Error ? error.message : '计算价格范围失败',
        });
      }
    });
  }

  /**
   * 舍入价格到货币精度
   */
  roundPrice(
    amount: number,
    currency: SupportedCurrency = this.defaultCurrency
  ): number {
    const rounding = getCurrencyRounding(currency);
    return Math.round(amount / rounding) * rounding;
  }

  /**
   * 验证价格格式
   */
  validatePrice(
    amount: number,
    currency: SupportedCurrency = this.defaultCurrency
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查是否为有效数字
    if (isNaN(amount) || !isFinite(amount)) {
      errors.push('价格必须是有效数字');
    }

    // 检查是否为负数
    if (amount < 0) {
      errors.push('价格不能为负数');
    }

    // 检查精度
    const precision = getCurrencyPrecision(currency);
    const decimalPlaces = (amount.toString().split('.')[1] || '').length;
    if (decimalPlaces > precision) {
      errors.push(`价格精度不能超过${precision}位小数`);
    }

    // 检查最大值
    const maxValue = Number.MAX_SAFE_INTEGER / 100; // 避免溢出
    if (amount > maxValue) {
      errors.push('价格超出最大允许值');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取货币显示信息
   */
  getCurrencyDisplayInfo(currency: SupportedCurrency): {
    code: string;
    symbol: string;
    name: string;
    precision: number;
  } | null {
    const currencyInfo = getCurrencyInfo(currency);
    if (!currencyInfo) return null;

    return {
      code: currencyInfo.code,
      symbol: currencyInfo.symbol,
      name: currencyInfo.name,
      precision: currencyInfo.decimalPlaces,
    };
  }

  /**
   * 格式化价格差异
   */
  formatPriceDifference(
    oldPrice: number,
    newPrice: number,
    currency: SupportedCurrency = this.defaultCurrency,
    options: {
      showPercentage?: boolean;
      showDirection?: boolean;
      locale?: string;
    } = {}
  ): string {
    const {
      showPercentage = false,
      showDirection = true,
      locale = this.defaultLocale,
    } = options;

    const difference = newPrice - oldPrice;
    const percentageChange = oldPrice !== 0 ? (difference / oldPrice) * 100 : 0;

    let result = '';

    if (showDirection) {
      result += difference > 0 ? '+' : '';
    }

    if (showPercentage) {
      const formatter = new Intl.NumberFormat(locale, {
        style: 'percent',
        minimumFractionDigits: 1,
        maximumFractionDigits: 2,
      });
      result += formatter.format(percentageChange / 100);
    } else {
      result += this.formatPrice(Math.abs(difference), { currency, locale });
    }

    return result;
  }

  /**
   * 获取价格趋势
   */
  getPriceTrend(
    prices: Array<{ amount: number; date: Date }>,
    currency: SupportedCurrency = this.defaultCurrency
  ): {
    trend: 'up' | 'down' | 'stable';
    change: number;
    changePercentage: number;
    formattedChange: string;
  } | null {
    if (prices.length < 2) return null;

    // 按日期排序
    const sortedPrices = prices.sort((a, b) => a.date.getTime() - b.date.getTime());
    const firstPrice = sortedPrices[0].amount;
    const lastPrice = sortedPrices[sortedPrices.length - 1].amount;

    const change = lastPrice - firstPrice;
    const changePercentage = firstPrice !== 0 ? (change / firstPrice) * 100 : 0;

    let trend: 'up' | 'down' | 'stable';
    if (Math.abs(changePercentage) < 0.01) {
      trend = 'stable';
    } else if (change > 0) {
      trend = 'up';
    } else {
      trend = 'down';
    }

    const formattedChange = this.formatPriceDifference(firstPrice, lastPrice, currency, {
      showPercentage: true,
      showDirection: true,
    });

    return {
      trend,
      change,
      changePercentage,
      formattedChange,
    };
  }
}

// 导出单例实例
export const currencyService = new CurrencyService();
