/**
 * 多货币配置文件
 * 定义支持的货币、汇率管理、价格格式化等
 */

// 支持的货币枚举
export enum SupportedCurrency {
  // 主要货币
  USD = 'USD', // 美元
  EUR = 'EUR', // 欧元
  GBP = 'GBP', // 英镑
  JPY = 'JPY', // 日元
  CNY = 'CNY', // 人民币
  
  // 亚太货币
  HKD = 'HKD', // 港币
  TWD = 'TWD', // 新台币
  SGD = 'SGD', // 新加坡元
  KRW = 'KRW', // 韩元
  THB = 'THB', // 泰铢
  VND = 'VND', // 越南盾
  MYR = 'MYR', // 马来西亚林吉特
  IDR = 'IDR', // 印尼盾
  PHP = 'PHP', // 菲律宾比索
  INR = 'INR', // 印度卢比
  
  // 欧洲货币
  CHF = 'CHF', // 瑞士法郎
  SEK = 'SEK', // 瑞典克朗
  NOK = 'NOK', // 挪威克朗
  DKK = 'DKK', // 丹麦克朗
  PLN = 'PLN', // 波兰兹罗提
  CZK = 'CZK', // 捷克克朗
  HUF = 'HUF', // 匈牙利福林
  
  // 美洲货币
  CAD = 'CAD', // 加拿大元
  AUD = 'AUD', // 澳大利亚元
  NZD = 'NZD', // 新西兰元
  BRL = 'BRL', // 巴西雷亚尔
  MXN = 'MXN', // 墨西哥比索
  ARS = 'ARS', // 阿根廷比索
  CLP = 'CLP', // 智利比索
  
  // 中东非洲货币
  AED = 'AED', // 阿联酋迪拉姆
  SAR = 'SAR', // 沙特里亚尔
  QAR = 'QAR', // 卡塔尔里亚尔
  KWD = 'KWD', // 科威特第纳尔
  BHD = 'BHD', // 巴林第纳尔
  OMR = 'OMR', // 阿曼里亚尔
  ILS = 'ILS', // 以色列新谢克尔
  TRY = 'TRY', // 土耳其里拉
  ZAR = 'ZAR', // 南非兰特
  EGP = 'EGP', // 埃及镑
  
  // 其他货币
  RUB = 'RUB', // 俄罗斯卢布
  UAH = 'UAH', // 乌克兰格里夫纳
}

// 货币信息接口
export interface CurrencyInfo {
  code: SupportedCurrency;
  name: string;
  symbol: string;
  symbolPosition: 'before' | 'after';
  decimalPlaces: number;
  thousandsSeparator: string;
  decimalSeparator: string;
  isActive: boolean;
  region: string;
  country: string;
  minorUnit: number; // 最小货币单位（如美分）
  rounding: number; // 舍入精度
}

// 支持的货币配置
export const SUPPORTED_CURRENCIES: Record<SupportedCurrency, CurrencyInfo> = {
  [SupportedCurrency.USD]: {
    code: SupportedCurrency.USD,
    name: 'US Dollar',
    symbol: '$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'North America',
    country: 'United States',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.EUR]: {
    code: SupportedCurrency.EUR,
    name: 'Euro',
    symbol: '€',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'European Union',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.GBP]: {
    code: SupportedCurrency.GBP,
    name: 'British Pound',
    symbol: '£',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Europe',
    country: 'United Kingdom',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.JPY]: {
    code: SupportedCurrency.JPY,
    name: 'Japanese Yen',
    symbol: '¥',
    symbolPosition: 'before',
    decimalPlaces: 0,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Japan',
    minorUnit: 1,
    rounding: 1,
  },
  [SupportedCurrency.CNY]: {
    code: SupportedCurrency.CNY,
    name: 'Chinese Yuan',
    symbol: '¥',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'China',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.HKD]: {
    code: SupportedCurrency.HKD,
    name: 'Hong Kong Dollar',
    symbol: 'HK$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Hong Kong',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.TWD]: {
    code: SupportedCurrency.TWD,
    name: 'Taiwan Dollar',
    symbol: 'NT$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Taiwan',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.SGD]: {
    code: SupportedCurrency.SGD,
    name: 'Singapore Dollar',
    symbol: 'S$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Singapore',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.KRW]: {
    code: SupportedCurrency.KRW,
    name: 'South Korean Won',
    symbol: '₩',
    symbolPosition: 'before',
    decimalPlaces: 0,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'South Korea',
    minorUnit: 1,
    rounding: 1,
  },
  [SupportedCurrency.THB]: {
    code: SupportedCurrency.THB,
    name: 'Thai Baht',
    symbol: '฿',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Thailand',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.VND]: {
    code: SupportedCurrency.VND,
    name: 'Vietnamese Dong',
    symbol: '₫',
    symbolPosition: 'after',
    decimalPlaces: 0,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'Asia',
    country: 'Vietnam',
    minorUnit: 1,
    rounding: 1,
  },
  [SupportedCurrency.MYR]: {
    code: SupportedCurrency.MYR,
    name: 'Malaysian Ringgit',
    symbol: 'RM',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Malaysia',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.IDR]: {
    code: SupportedCurrency.IDR,
    name: 'Indonesian Rupiah',
    symbol: 'Rp',
    symbolPosition: 'before',
    decimalPlaces: 0,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'Asia',
    country: 'Indonesia',
    minorUnit: 1,
    rounding: 1,
  },
  [SupportedCurrency.PHP]: {
    code: SupportedCurrency.PHP,
    name: 'Philippine Peso',
    symbol: '₱',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'Philippines',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.INR]: {
    code: SupportedCurrency.INR,
    name: 'Indian Rupee',
    symbol: '₹',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Asia',
    country: 'India',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.CHF]: {
    code: SupportedCurrency.CHF,
    name: 'Swiss Franc',
    symbol: 'CHF',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: "'",
    decimalSeparator: '.',
    isActive: true,
    region: 'Europe',
    country: 'Switzerland',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.SEK]: {
    code: SupportedCurrency.SEK,
    name: 'Swedish Krona',
    symbol: 'kr',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Sweden',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.NOK]: {
    code: SupportedCurrency.NOK,
    name: 'Norwegian Krone',
    symbol: 'kr',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Norway',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.DKK]: {
    code: SupportedCurrency.DKK,
    name: 'Danish Krone',
    symbol: 'kr',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Denmark',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.PLN]: {
    code: SupportedCurrency.PLN,
    name: 'Polish Zloty',
    symbol: 'zł',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Poland',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.CZK]: {
    code: SupportedCurrency.CZK,
    name: 'Czech Koruna',
    symbol: 'Kč',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Czech Republic',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.HUF]: {
    code: SupportedCurrency.HUF,
    name: 'Hungarian Forint',
    symbol: 'Ft',
    symbolPosition: 'after',
    decimalPlaces: 0,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Hungary',
    minorUnit: 1,
    rounding: 1,
  },
  [SupportedCurrency.CAD]: {
    code: SupportedCurrency.CAD,
    name: 'Canadian Dollar',
    symbol: 'C$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'North America',
    country: 'Canada',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.AUD]: {
    code: SupportedCurrency.AUD,
    name: 'Australian Dollar',
    symbol: 'A$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Oceania',
    country: 'Australia',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.NZD]: {
    code: SupportedCurrency.NZD,
    name: 'New Zealand Dollar',
    symbol: 'NZ$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Oceania',
    country: 'New Zealand',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.BRL]: {
    code: SupportedCurrency.BRL,
    name: 'Brazilian Real',
    symbol: 'R$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'South America',
    country: 'Brazil',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.MXN]: {
    code: SupportedCurrency.MXN,
    name: 'Mexican Peso',
    symbol: '$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'North America',
    country: 'Mexico',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.ARS]: {
    code: SupportedCurrency.ARS,
    name: 'Argentine Peso',
    symbol: '$',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'South America',
    country: 'Argentina',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.CLP]: {
    code: SupportedCurrency.CLP,
    name: 'Chilean Peso',
    symbol: '$',
    symbolPosition: 'before',
    decimalPlaces: 0,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'South America',
    country: 'Chile',
    minorUnit: 1,
    rounding: 1,
  },
  [SupportedCurrency.AED]: {
    code: SupportedCurrency.AED,
    name: 'UAE Dirham',
    symbol: 'د.إ',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'United Arab Emirates',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.SAR]: {
    code: SupportedCurrency.SAR,
    name: 'Saudi Riyal',
    symbol: '﷼',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'Saudi Arabia',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.QAR]: {
    code: SupportedCurrency.QAR,
    name: 'Qatari Riyal',
    symbol: '﷼',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'Qatar',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.KWD]: {
    code: SupportedCurrency.KWD,
    name: 'Kuwaiti Dinar',
    symbol: 'د.ك',
    symbolPosition: 'after',
    decimalPlaces: 3,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'Kuwait',
    minorUnit: 1000,
    rounding: 0.001,
  },
  [SupportedCurrency.BHD]: {
    code: SupportedCurrency.BHD,
    name: 'Bahraini Dinar',
    symbol: '.د.ب',
    symbolPosition: 'after',
    decimalPlaces: 3,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'Bahrain',
    minorUnit: 1000,
    rounding: 0.001,
  },
  [SupportedCurrency.OMR]: {
    code: SupportedCurrency.OMR,
    name: 'Omani Rial',
    symbol: '﷼',
    symbolPosition: 'after',
    decimalPlaces: 3,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'Oman',
    minorUnit: 1000,
    rounding: 0.001,
  },
  [SupportedCurrency.ILS]: {
    code: SupportedCurrency.ILS,
    name: 'Israeli Shekel',
    symbol: '₪',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Middle East',
    country: 'Israel',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.TRY]: {
    code: SupportedCurrency.TRY,
    name: 'Turkish Lira',
    symbol: '₺',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: '.',
    decimalSeparator: ',',
    isActive: true,
    region: 'Middle East',
    country: 'Turkey',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.ZAR]: {
    code: SupportedCurrency.ZAR,
    name: 'South African Rand',
    symbol: 'R',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: '.',
    isActive: true,
    region: 'Africa',
    country: 'South Africa',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.EGP]: {
    code: SupportedCurrency.EGP,
    name: 'Egyptian Pound',
    symbol: '£',
    symbolPosition: 'before',
    decimalPlaces: 2,
    thousandsSeparator: ',',
    decimalSeparator: '.',
    isActive: true,
    region: 'Africa',
    country: 'Egypt',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.RUB]: {
    code: SupportedCurrency.RUB,
    name: 'Russian Ruble',
    symbol: '₽',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Russia',
    minorUnit: 100,
    rounding: 0.01,
  },
  [SupportedCurrency.UAH]: {
    code: SupportedCurrency.UAH,
    name: 'Ukrainian Hryvnia',
    symbol: '₴',
    symbolPosition: 'after',
    decimalPlaces: 2,
    thousandsSeparator: ' ',
    decimalSeparator: ',',
    isActive: true,
    region: 'Europe',
    country: 'Ukraine',
    minorUnit: 100,
    rounding: 0.01,
  },
};

// 默认货币
export const DEFAULT_CURRENCY = SupportedCurrency.USD;

// 基础货币（用于汇率计算）
export const BASE_CURRENCY = SupportedCurrency.USD;

// 汇率更新间隔（分钟）
export const EXCHANGE_RATE_UPDATE_INTERVAL = 60;

// 汇率缓存时间（分钟）
export const EXCHANGE_RATE_CACHE_DURATION = 30;

// 价格精度配置
export const PRICE_PRECISION_CONFIG = {
  // 默认显示精度
  defaultDisplayPrecision: 2,
  // 计算精度
  calculationPrecision: 6,
  // 存储精度
  storagePrecision: 4,
  // 最大精度
  maxPrecision: 8,
};

// 汇率来源枚举
export enum ExchangeRateSource {
  MANUAL = 'MANUAL',           // 手动设置
  FIXER = 'FIXER',             // Fixer.io API
  EXCHANGERATE = 'EXCHANGERATE', // ExchangeRate-API
  OPENEXCHANGE = 'OPENEXCHANGE', // Open Exchange Rates
  CURRENCYLAYER = 'CURRENCYLAYER', // CurrencyLayer
  ECB = 'ECB',                 // 欧洲央行
  BANK_OF_CHINA = 'BANK_OF_CHINA', // 中国银行
}

// 汇率配置
export const EXCHANGE_RATE_CONFIG = {
  // 默认汇率来源
  defaultSource: ExchangeRateSource.FIXER,
  // 备用汇率来源
  fallbackSources: [
    ExchangeRateSource.EXCHANGERATE,
    ExchangeRateSource.OPENEXCHANGE,
    ExchangeRateSource.MANUAL,
  ],
  // API配置
  apiConfigs: {
    [ExchangeRateSource.FIXER]: {
      baseUrl: 'https://api.fixer.io/v1',
      apiKey: process.env.FIXER_API_KEY,
      rateLimit: 1000, // 每月请求限制
    },
    [ExchangeRateSource.EXCHANGERATE]: {
      baseUrl: 'https://api.exchangerate-api.com/v4',
      apiKey: process.env.EXCHANGERATE_API_KEY,
      rateLimit: 1500,
    },
    [ExchangeRateSource.OPENEXCHANGE]: {
      baseUrl: 'https://openexchangerates.org/api',
      apiKey: process.env.OPENEXCHANGE_API_KEY,
      rateLimit: 1000,
    },
    [ExchangeRateSource.CURRENCYLAYER]: {
      baseUrl: 'http://api.currencylayer.com',
      apiKey: process.env.CURRENCYLAYER_API_KEY,
      rateLimit: 1000,
    },
  },
};

// 获取活跃货币列表
export function getActiveCurrencies(): CurrencyInfo[] {
  return Object.values(SUPPORTED_CURRENCIES).filter(currency => currency.isActive);
}

// 获取货币信息
export function getCurrencyInfo(code: SupportedCurrency): CurrencyInfo | undefined {
  return SUPPORTED_CURRENCIES[code];
}

// 检查货币是否支持
export function isCurrencySupported(code: string): code is SupportedCurrency {
  return Object.values(SupportedCurrency).includes(code as SupportedCurrency);
}

// 获取货币符号
export function getCurrencySymbol(code: SupportedCurrency): string {
  return SUPPORTED_CURRENCIES[code]?.symbol || code;
}

// 获取货币精度
export function getCurrencyPrecision(code: SupportedCurrency): number {
  return SUPPORTED_CURRENCIES[code]?.decimalPlaces || 2;
}

// 获取货币舍入精度
export function getCurrencyRounding(code: SupportedCurrency): number {
  return SUPPORTED_CURRENCIES[code]?.rounding || 0.01;
}

// 按地区分组货币
export function getCurrenciesByRegion(): Record<string, CurrencyInfo[]> {
  const currenciesByRegion: Record<string, CurrencyInfo[]> = {};
  
  Object.values(SUPPORTED_CURRENCIES).forEach(currency => {
    if (currency.isActive) {
      if (!currenciesByRegion[currency.region]) {
        currenciesByRegion[currency.region] = [];
      }
      currenciesByRegion[currency.region].push(currency);
    }
  });
  
  return currenciesByRegion;
}

// 获取主要货币
export function getMajorCurrencies(): CurrencyInfo[] {
  const majorCurrencyCodes = [
    SupportedCurrency.USD,
    SupportedCurrency.EUR,
    SupportedCurrency.GBP,
    SupportedCurrency.JPY,
    SupportedCurrency.CNY,
    SupportedCurrency.CAD,
    SupportedCurrency.AUD,
    SupportedCurrency.CHF,
  ];
  
  return majorCurrencyCodes
    .map(code => SUPPORTED_CURRENCIES[code])
    .filter(currency => currency.isActive);
}

// 格式化货币代码
export function formatCurrencyCode(code: SupportedCurrency): string {
  return code.toUpperCase();
}

// 获取货币的本地化格式器
export function getCurrencyFormatter(
  code: SupportedCurrency,
  locale?: string
): Intl.NumberFormat {
  const currencyInfo = getCurrencyInfo(code);
  
  return new Intl.NumberFormat(locale || 'en-US', {
    style: 'currency',
    currency: code,
    minimumFractionDigits: currencyInfo?.decimalPlaces || 2,
    maximumFractionDigits: currencyInfo?.decimalPlaces || 2,
  });
}

// 检查是否为零小数位货币
export function isZeroDecimalCurrency(code: SupportedCurrency): boolean {
  const currencyInfo = getCurrencyInfo(code);
  return currencyInfo?.decimalPlaces === 0;
}

// 检查是否为三小数位货币
export function isThreeDecimalCurrency(code: SupportedCurrency): boolean {
  const currencyInfo = getCurrencyInfo(code);
  return currencyInfo?.decimalPlaces === 3;
}
