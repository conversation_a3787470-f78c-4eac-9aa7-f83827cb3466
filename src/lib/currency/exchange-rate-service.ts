/**
 * 汇率管理服务
 * 提供汇率获取、缓存、转换、历史记录等功能
 */

import axios from 'axios';
import { 
  SupportedCurrency, 
  ExchangeRateSource, 
  BASE_CURRENCY,
  EXCHANGE_RATE_CONFIG,
  EXCHANGE_RATE_UPDATE_INTERVAL,
  EXCHANGE_RATE_CACHE_DURATION,
  getCurrencyInfo,
} from './config';
import { prisma } from '@/lib/prisma';

// 汇率数据接口
export interface ExchangeRate {
  id: string;
  fromCurrency: SupportedCurrency;
  toCurrency: SupportedCurrency;
  rate: number;
  source: ExchangeRateSource;
  timestamp: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 汇率查询选项
export interface ExchangeRateOptions {
  source?: ExchangeRateSource;
  useCache?: boolean;
  maxAge?: number; // 最大缓存时间（分钟）
  fallbackToManual?: boolean;
}

// 汇率转换结果
export interface CurrencyConversionResult {
  fromCurrency: SupportedCurrency;
  toCurrency: SupportedCurrency;
  fromAmount: number;
  toAmount: number;
  rate: number;
  source: ExchangeRateSource;
  timestamp: Date;
}

// 汇率历史数据
export interface ExchangeRateHistory {
  currency: SupportedCurrency;
  baseCurrency: SupportedCurrency;
  rates: Array<{
    date: Date;
    rate: number;
    source: ExchangeRateSource;
  }>;
  period: 'day' | 'week' | 'month' | 'year';
}

// 汇率缓存
interface ExchangeRateCache {
  [key: string]: {
    rate: number;
    timestamp: Date;
    source: ExchangeRateSource;
  };
}

/**
 * 汇率管理服务类
 */
export class ExchangeRateService {
  private cache: ExchangeRateCache = {};
  private updateInterval?: NodeJS.Timeout;

  constructor() {
    this.startAutoUpdate();
  }

  /**
   * 获取汇率
   */
  async getExchangeRate(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    options: ExchangeRateOptions = {}
  ): Promise<{ success: boolean; rate?: number; source?: ExchangeRateSource; error?: string }> {
    try {
      const {
        source = EXCHANGE_RATE_CONFIG.defaultSource,
        useCache = true,
        maxAge = EXCHANGE_RATE_CACHE_DURATION,
        fallbackToManual = true,
      } = options;

      // 相同货币返回1
      if (fromCurrency === toCurrency) {
        return {
          success: true,
          rate: 1,
          source: ExchangeRateSource.MANUAL,
        };
      }

      // 检查缓存
      if (useCache) {
        const cachedRate = this.getCachedRate(fromCurrency, toCurrency, maxAge);
        if (cachedRate) {
          return {
            success: true,
            rate: cachedRate.rate,
            source: cachedRate.source,
          };
        }
      }

      // 从数据库获取最新汇率
      const dbRate = await this.getLatestRateFromDatabase(fromCurrency, toCurrency);
      if (dbRate && this.isRateValid(dbRate, maxAge)) {
        this.setCachedRate(fromCurrency, toCurrency, dbRate.rate, dbRate.source);
        return {
          success: true,
          rate: dbRate.rate,
          source: dbRate.source,
        };
      }

      // 从API获取汇率
      const apiRate = await this.fetchRateFromAPI(fromCurrency, toCurrency, source);
      if (apiRate.success && apiRate.rate) {
        // 保存到数据库
        await this.saveRateToDatabase(fromCurrency, toCurrency, apiRate.rate, source);
        
        // 更新缓存
        this.setCachedRate(fromCurrency, toCurrency, apiRate.rate, source);
        
        return apiRate;
      }

      // 尝试备用数据源
      for (const fallbackSource of EXCHANGE_RATE_CONFIG.fallbackSources) {
        if (fallbackSource === source) continue;
        
        const fallbackRate = await this.fetchRateFromAPI(fromCurrency, toCurrency, fallbackSource);
        if (fallbackRate.success && fallbackRate.rate) {
          await this.saveRateToDatabase(fromCurrency, toCurrency, fallbackRate.rate, fallbackSource);
          this.setCachedRate(fromCurrency, toCurrency, fallbackRate.rate, fallbackSource);
          return fallbackRate;
        }
      }

      // 使用手动设置的汇率
      if (fallbackToManual) {
        const manualRate = await this.getManualRate(fromCurrency, toCurrency);
        if (manualRate) {
          return {
            success: true,
            rate: manualRate,
            source: ExchangeRateSource.MANUAL,
          };
        }
      }

      return {
        success: false,
        error: '无法获取汇率',
      };
    } catch (error) {
      console.error('获取汇率失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取汇率失败',
      };
    }
  }

  /**
   * 货币转换
   */
  async convertCurrency(
    amount: number,
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    options: ExchangeRateOptions = {}
  ): Promise<{ success: boolean; result?: CurrencyConversionResult; error?: string }> {
    try {
      const rateResult = await this.getExchangeRate(fromCurrency, toCurrency, options);
      
      if (!rateResult.success || !rateResult.rate) {
        return {
          success: false,
          error: rateResult.error || '无法获取汇率',
        };
      }

      const convertedAmount = amount * rateResult.rate;
      
      // 根据目标货币精度进行舍入
      const toCurrencyInfo = getCurrencyInfo(toCurrency);
      const roundedAmount = this.roundToDecimalPlaces(
        convertedAmount,
        toCurrencyInfo?.decimalPlaces || 2
      );

      const result: CurrencyConversionResult = {
        fromCurrency,
        toCurrency,
        fromAmount: amount,
        toAmount: roundedAmount,
        rate: rateResult.rate,
        source: rateResult.source!,
        timestamp: new Date(),
      };

      return {
        success: true,
        result,
      };
    } catch (error) {
      console.error('货币转换失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '货币转换失败',
      };
    }
  }

  /**
   * 批量货币转换
   */
  async convertCurrencyBatch(
    amounts: Array<{
      amount: number;
      fromCurrency: SupportedCurrency;
      toCurrency: SupportedCurrency;
    }>,
    options: ExchangeRateOptions = {}
  ): Promise<{ success: boolean; results?: CurrencyConversionResult[]; error?: string }> {
    try {
      const results: CurrencyConversionResult[] = [];

      for (const item of amounts) {
        const conversionResult = await this.convertCurrency(
          item.amount,
          item.fromCurrency,
          item.toCurrency,
          options
        );

        if (conversionResult.success && conversionResult.result) {
          results.push(conversionResult.result);
        } else {
          return {
            success: false,
            error: conversionResult.error,
          };
        }
      }

      return {
        success: true,
        results,
      };
    } catch (error) {
      console.error('批量货币转换失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量货币转换失败',
      };
    }
  }

  /**
   * 设置手动汇率
   */
  async setManualRate(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    rate: number,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await this.saveRateToDatabase(
        fromCurrency,
        toCurrency,
        rate,
        ExchangeRateSource.MANUAL,
        userId
      );

      // 更新缓存
      this.setCachedRate(fromCurrency, toCurrency, rate, ExchangeRateSource.MANUAL);

      return { success: true };
    } catch (error) {
      console.error('设置手动汇率失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '设置手动汇率失败',
      };
    }
  }

  /**
   * 获取汇率历史
   */
  async getExchangeRateHistory(
    currency: SupportedCurrency,
    baseCurrency: SupportedCurrency = BASE_CURRENCY,
    period: 'day' | 'week' | 'month' | 'year' = 'month',
    limit: number = 100
  ): Promise<{ success: boolean; history?: ExchangeRateHistory; error?: string }> {
    try {
      const startDate = this.getStartDateForPeriod(period);

      const rates = await prisma.exchangeRate.findMany({
        where: {
          fromCurrency: baseCurrency,
          toCurrency: currency,
          timestamp: {
            gte: startDate,
          },
          isActive: true,
        },
        orderBy: { timestamp: 'desc' },
        take: limit,
      });

      const history: ExchangeRateHistory = {
        currency,
        baseCurrency,
        rates: rates.map(rate => ({
          date: rate.timestamp,
          rate: rate.rate,
          source: rate.source as ExchangeRateSource,
        })),
        period,
      };

      return {
        success: true,
        history,
      };
    } catch (error) {
      console.error('获取汇率历史失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取汇率历史失败',
      };
    }
  }

  /**
   * 更新所有汇率
   */
  async updateAllRates(
    source: ExchangeRateSource = EXCHANGE_RATE_CONFIG.defaultSource
  ): Promise<{ success: boolean; updated?: number; error?: string }> {
    try {
      const activeCurrencies = Object.values(SupportedCurrency).filter(currency => 
        getCurrencyInfo(currency)?.isActive
      );

      let updatedCount = 0;

      for (const currency of activeCurrencies) {
        if (currency === BASE_CURRENCY) continue;

        const rateResult = await this.fetchRateFromAPI(BASE_CURRENCY, currency, source);
        if (rateResult.success && rateResult.rate) {
          await this.saveRateToDatabase(BASE_CURRENCY, currency, rateResult.rate, source);
          this.setCachedRate(BASE_CURRENCY, currency, rateResult.rate, source);
          updatedCount++;
        }
      }

      return {
        success: true,
        updated: updatedCount,
      };
    } catch (error) {
      console.error('更新所有汇率失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新所有汇率失败',
      };
    }
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache = {};
  }

  /**
   * 启动自动更新
   */
  private startAutoUpdate(): void {
    this.updateInterval = setInterval(async () => {
      console.log('开始自动更新汇率...');
      await this.updateAllRates();
    }, EXCHANGE_RATE_UPDATE_INTERVAL * 60 * 1000);
  }

  /**
   * 停止自动更新
   */
  stopAutoUpdate(): void {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = undefined;
    }
  }

  /**
   * 从API获取汇率
   */
  private async fetchRateFromAPI(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    source: ExchangeRateSource
  ): Promise<{ success: boolean; rate?: number; error?: string }> {
    try {
      switch (source) {
        case ExchangeRateSource.FIXER:
          return await this.fetchFromFixer(fromCurrency, toCurrency);
        case ExchangeRateSource.EXCHANGERATE:
          return await this.fetchFromExchangeRate(fromCurrency, toCurrency);
        case ExchangeRateSource.OPENEXCHANGE:
          return await this.fetchFromOpenExchange(fromCurrency, toCurrency);
        case ExchangeRateSource.CURRENCYLAYER:
          return await this.fetchFromCurrencyLayer(fromCurrency, toCurrency);
        default:
          return {
            success: false,
            error: `不支持的汇率来源: ${source}`,
          };
      }
    } catch (error) {
      console.error(`从${source}获取汇率失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取汇率失败',
      };
    }
  }

  /**
   * 从Fixer.io获取汇率
   */
  private async fetchFromFixer(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<{ success: boolean; rate?: number; error?: string }> {
    const config = EXCHANGE_RATE_CONFIG.apiConfigs[ExchangeRateSource.FIXER];
    if (!config.apiKey) {
      return { success: false, error: 'Fixer API密钥未配置' };
    }

    const response = await axios.get(`${config.baseUrl}/latest`, {
      params: {
        access_key: config.apiKey,
        base: fromCurrency,
        symbols: toCurrency,
      },
    });

    if (response.data.success && response.data.rates[toCurrency]) {
      return {
        success: true,
        rate: response.data.rates[toCurrency],
      };
    }

    return {
      success: false,
      error: response.data.error?.info || '获取汇率失败',
    };
  }

  /**
   * 从ExchangeRate-API获取汇率
   */
  private async fetchFromExchangeRate(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<{ success: boolean; rate?: number; error?: string }> {
    const config = EXCHANGE_RATE_CONFIG.apiConfigs[ExchangeRateSource.EXCHANGERATE];
    
    const response = await axios.get(`${config.baseUrl}/latest/${fromCurrency}`);

    if (response.data.rates && response.data.rates[toCurrency]) {
      return {
        success: true,
        rate: response.data.rates[toCurrency],
      };
    }

    return {
      success: false,
      error: '获取汇率失败',
    };
  }

  /**
   * 从Open Exchange Rates获取汇率
   */
  private async fetchFromOpenExchange(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<{ success: boolean; rate?: number; error?: string }> {
    const config = EXCHANGE_RATE_CONFIG.apiConfigs[ExchangeRateSource.OPENEXCHANGE];
    if (!config.apiKey) {
      return { success: false, error: 'Open Exchange Rates API密钥未配置' };
    }

    const response = await axios.get(`${config.baseUrl}/latest.json`, {
      params: {
        app_id: config.apiKey,
        base: fromCurrency,
        symbols: toCurrency,
      },
    });

    if (response.data.rates && response.data.rates[toCurrency]) {
      return {
        success: true,
        rate: response.data.rates[toCurrency],
      };
    }

    return {
      success: false,
      error: '获取汇率失败',
    };
  }

  /**
   * 从CurrencyLayer获取汇率
   */
  private async fetchFromCurrencyLayer(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<{ success: boolean; rate?: number; error?: string }> {
    const config = EXCHANGE_RATE_CONFIG.apiConfigs[ExchangeRateSource.CURRENCYLAYER];
    if (!config.apiKey) {
      return { success: false, error: 'CurrencyLayer API密钥未配置' };
    }

    const response = await axios.get(`${config.baseUrl}/live`, {
      params: {
        access_key: config.apiKey,
        source: fromCurrency,
        currencies: toCurrency,
      },
    });

    const quoteKey = `${fromCurrency}${toCurrency}`;
    if (response.data.success && response.data.quotes[quoteKey]) {
      return {
        success: true,
        rate: response.data.quotes[quoteKey],
      };
    }

    return {
      success: false,
      error: response.data.error?.info || '获取汇率失败',
    };
  }

  /**
   * 从数据库获取最新汇率
   */
  private async getLatestRateFromDatabase(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<ExchangeRate | null> {
    return await prisma.exchangeRate.findFirst({
      where: {
        fromCurrency,
        toCurrency,
        isActive: true,
      },
      orderBy: { timestamp: 'desc' },
    });
  }

  /**
   * 保存汇率到数据库
   */
  private async saveRateToDatabase(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    rate: number,
    source: ExchangeRateSource,
    userId: string = 'system'
  ): Promise<void> {
    await prisma.exchangeRate.create({
      data: {
        fromCurrency,
        toCurrency,
        rate,
        source,
        timestamp: new Date(),
        isActive: true,
        createdBy: userId,
        updatedBy: userId,
      },
    });
  }

  /**
   * 获取手动设置的汇率
   */
  private async getManualRate(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency
  ): Promise<number | null> {
    const manualRate = await prisma.exchangeRate.findFirst({
      where: {
        fromCurrency,
        toCurrency,
        source: ExchangeRateSource.MANUAL,
        isActive: true,
      },
      orderBy: { timestamp: 'desc' },
    });

    return manualRate?.rate || null;
  }

  /**
   * 获取缓存的汇率
   */
  private getCachedRate(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    maxAge: number
  ): { rate: number; source: ExchangeRateSource } | null {
    const cacheKey = `${fromCurrency}_${toCurrency}`;
    const cached = this.cache[cacheKey];

    if (cached) {
      const ageInMinutes = (Date.now() - cached.timestamp.getTime()) / (1000 * 60);
      if (ageInMinutes <= maxAge) {
        return {
          rate: cached.rate,
          source: cached.source,
        };
      }
    }

    return null;
  }

  /**
   * 设置缓存的汇率
   */
  private setCachedRate(
    fromCurrency: SupportedCurrency,
    toCurrency: SupportedCurrency,
    rate: number,
    source: ExchangeRateSource
  ): void {
    const cacheKey = `${fromCurrency}_${toCurrency}`;
    this.cache[cacheKey] = {
      rate,
      timestamp: new Date(),
      source,
    };
  }

  /**
   * 检查汇率是否有效
   */
  private isRateValid(rate: ExchangeRate, maxAge: number): boolean {
    const ageInMinutes = (Date.now() - rate.timestamp.getTime()) / (1000 * 60);
    return ageInMinutes <= maxAge;
  }

  /**
   * 获取时间段的开始日期
   */
  private getStartDateForPeriod(period: 'day' | 'week' | 'month' | 'year'): Date {
    const now = new Date();
    
    switch (period) {
      case 'day':
        return new Date(now.getTime() - 24 * 60 * 60 * 1000);
      case 'week':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case 'month':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case 'year':
        return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  /**
   * 按小数位数舍入
   */
  private roundToDecimalPlaces(value: number, decimalPlaces: number): number {
    const factor = Math.pow(10, decimalPlaces);
    return Math.round(value * factor) / factor;
  }
}

// 导出单例实例
export const exchangeRateService = new ExchangeRateService();
