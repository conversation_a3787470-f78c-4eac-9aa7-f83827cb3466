/**
 * Prisma数据库客户端配置
 * 提供全局的数据库连接实例，支持连接池和查询优化
 */

import { PrismaClient } from '@prisma/client';

// 扩展Prisma客户端类型，添加自定义方法
declare global {
  // eslint-disable-next-line no-var
  var __prisma: PrismaClient | undefined;
}

// Prisma客户端配置选项
const prismaOptions = {
  // 日志配置
  log: process.env.NODE_ENV === 'development' 
    ? ['query', 'error', 'warn'] as const
    : ['error'] as const,
  
  // 错误格式化
  errorFormat: 'pretty' as const,
  
  // 数据源配置
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
};

// 创建Prisma客户端实例
function createPrismaClient() {
  const prisma = new PrismaClient(prismaOptions);

  // 添加中间件用于查询性能监控
  prisma.$use(async (params, next) => {
    const before = Date.now();
    const result = await next(params);
    const after = Date.now();
    
    // 记录慢查询（超过1秒）
    if (after - before > 1000) {
      console.warn(`慢查询检测: ${params.model}.${params.action} 耗时 ${after - before}ms`);
    }
    
    return result;
  });

  // 添加软删除中间件
  prisma.$use(async (params, next) => {
    // 对于删除操作，转换为软删除
    if (params.action === 'delete') {
      params.action = 'update';
      params.args['data'] = { deletedAt: new Date() };
    }
    
    // 对于查询操作，过滤已删除的记录
    if (params.action === 'findMany' || params.action === 'findFirst') {
      if (params.args.where) {
        if (params.args.where.deletedAt === undefined) {
          params.args.where['deletedAt'] = null;
        }
      } else {
        params.args['where'] = { deletedAt: null };
      }
    }
    
    return next(params);
  });

  return prisma;
}

// 全局Prisma客户端实例
const prisma = globalThis.__prisma ?? createPrismaClient();

// 在开发环境中将实例挂载到全局对象，避免热重载时重复创建连接
if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

// 数据库连接健康检查
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    return false;
  }
}

// 数据库连接统计信息
export async function getDatabaseStats() {
  try {
    const stats = await prisma.$queryRaw`
      SELECT 
        schemaname,
        tablename,
        attname,
        n_distinct,
        correlation
      FROM pg_stats 
      WHERE schemaname = 'public'
      LIMIT 10
    `;
    return stats;
  } catch (error) {
    console.error('获取数据库统计信息失败:', error);
    return null;
  }
}

// 优雅关闭数据库连接
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('关闭数据库连接时出错:', error);
  }
}

// 数据库事务辅助函数
export async function withTransaction<T>(
  fn: (prisma: PrismaClient) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(fn);
}

// 批量操作辅助函数
export async function batchOperation<T>(
  operations: Array<() => Promise<T>>,
  batchSize: number = 100
): Promise<T[]> {
  const results: T[] = [];
  
  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(op => op()));
    results.push(...batchResults);
  }
  
  return results;
}

// 分页查询辅助函数
export interface PaginationOptions {
  page: number;
  limit: number;
  orderBy?: Record<string, 'asc' | 'desc'>;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export async function paginate<T>(
  model: any,
  options: PaginationOptions,
  where?: any
): Promise<PaginationResult<T>> {
  const { page, limit, orderBy } = options;
  const skip = (page - 1) * limit;
  
  const [data, total] = await Promise.all([
    model.findMany({
      where,
      skip,
      take: limit,
      orderBy,
    }),
    model.count({ where }),
  ]);
  
  const totalPages = Math.ceil(total / limit);
  
  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
}

// 导出Prisma客户端实例
export default prisma;
