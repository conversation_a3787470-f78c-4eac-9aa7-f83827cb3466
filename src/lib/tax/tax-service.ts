/**
 * 税务管理服务
 * 提供税务规则管理、税务计算、税务报表等功能
 */

import { 
  TaxRule, 
  TaxCalculationContext, 
  TaxCalculationResult,
  TaxType,
  TaxCalculationMethod,
  TaxStatus,
  TaxScope,
  TaxCondition,
  TaxExemption,
  TaxReportType,
  getDefaultTaxRate,
  getCountryTaxConfig,
  validateTaxId,
  formatTaxRate,
  formatTaxAmount,
} from './config';
import { taxCalculationEngine } from './tax-calculation-engine';
import { prisma } from '@/lib/prisma';

// 税务规则创建请求
export interface CreateTaxRuleRequest {
  name: string;
  description?: string;
  taxType: TaxType;
  calculationMethod: TaxCalculationMethod;
  rate: number;
  fixedAmount?: number;
  minAmount?: number;
  maxAmount?: number;
  scope: TaxScope;
  scopeValue: string;
  priority: number;
  effectiveDate: Date;
  expiryDate?: Date;
  conditions?: TaxCondition[];
  exemptions?: TaxExemption[];
}

// 税务规则更新请求
export interface UpdateTaxRuleRequest extends Partial<CreateTaxRuleRequest> {
  id: string;
  status?: TaxStatus;
}

// 税务报表请求
export interface TaxReportRequest {
  reportType: TaxReportType;
  startDate: Date;
  endDate: Date;
  country?: string;
  state?: string;
  taxType?: TaxType;
  currency?: string;
  includeExemptions?: boolean;
  groupBy?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

// 税务报表数据
export interface TaxReportData {
  reportType: TaxReportType;
  period: {
    startDate: Date;
    endDate: Date;
  };
  summary: {
    totalTaxAmount: number;
    totalTaxableAmount: number;
    totalExemptAmount: number;
    averageTaxRate: number;
    transactionCount: number;
  };
  breakdown: Array<{
    category: string;
    taxAmount: number;
    taxableAmount: number;
    exemptAmount: number;
    transactionCount: number;
    averageRate: number;
  }>;
  details: Array<{
    date: Date;
    orderId?: string;
    customerId?: string;
    productId?: string;
    taxableAmount: number;
    taxAmount: number;
    taxType: TaxType;
    rate: number;
    exemptionReason?: string;
  }>;
  metadata: {
    generatedAt: Date;
    generatedBy: string;
    currency: string;
    filters: Record<string, any>;
  };
}

/**
 * 税务管理服务类
 */
export class TaxService {
  /**
   * 创建税务规则
   */
  async createTaxRule(
    request: CreateTaxRuleRequest,
    userId: string = 'system'
  ): Promise<{ success: boolean; rule?: TaxRule; error?: string }> {
    try {
      // 验证请求数据
      const validation = this.validateTaxRuleRequest(request);
      if (!validation.isValid) {
        return {
          success: false,
          error: validation.errors.join(', '),
        };
      }

      // 检查规则名称是否已存在
      const existingRule = await prisma.taxRule.findFirst({
        where: {
          name: request.name,
          status: { not: TaxStatus.INACTIVE },
        },
      });

      if (existingRule) {
        return {
          success: false,
          error: '税务规则名称已存在',
        };
      }

      // 创建税务规则
      const createdRule = await prisma.taxRule.create({
        data: {
          ...request,
          status: TaxStatus.ACTIVE,
          createdBy: userId,
          updatedBy: userId,
        },
        include: {
          conditions: true,
          exemptions: true,
        },
      });

      // 清除计算引擎缓存
      taxCalculationEngine.clearCache();

      return {
        success: true,
        rule: this.mapDatabaseRuleToTaxRule(createdRule),
      };
    } catch (error) {
      console.error('创建税务规则失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建税务规则失败',
      };
    }
  }

  /**
   * 更新税务规则
   */
  async updateTaxRule(
    request: UpdateTaxRuleRequest,
    userId: string = 'system'
  ): Promise<{ success: boolean; rule?: TaxRule; error?: string }> {
    try {
      const { id, ...updateData } = request;

      // 检查规则是否存在
      const existingRule = await prisma.taxRule.findUnique({
        where: { id },
      });

      if (!existingRule) {
        return {
          success: false,
          error: '税务规则不存在',
        };
      }

      // 验证更新数据
      if (updateData.name || updateData.rate !== undefined) {
        const validation = this.validateTaxRuleRequest(updateData as CreateTaxRuleRequest);
        if (!validation.isValid) {
          return {
            success: false,
            error: validation.errors.join(', '),
          };
        }
      }

      // 更新税务规则
      const updatedRule = await prisma.taxRule.update({
        where: { id },
        data: {
          ...updateData,
          updatedBy: userId,
          updatedAt: new Date(),
        },
        include: {
          conditions: true,
          exemptions: true,
        },
      });

      // 清除计算引擎缓存
      taxCalculationEngine.clearCache();

      return {
        success: true,
        rule: this.mapDatabaseRuleToTaxRule(updatedRule),
      };
    } catch (error) {
      console.error('更新税务规则失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新税务规则失败',
      };
    }
  }

  /**
   * 删除税务规则
   */
  async deleteTaxRule(
    ruleId: string,
    userId: string = 'system'
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 软删除：将状态设置为INACTIVE
      await prisma.taxRule.update({
        where: { id: ruleId },
        data: {
          status: TaxStatus.INACTIVE,
          updatedBy: userId,
          updatedAt: new Date(),
        },
      });

      // 清除计算引擎缓存
      taxCalculationEngine.clearCache();

      return { success: true };
    } catch (error) {
      console.error('删除税务规则失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除税务规则失败',
      };
    }
  }

  /**
   * 获取税务规则列表
   */
  async getTaxRules(
    filters: {
      taxType?: TaxType;
      status?: TaxStatus;
      scope?: TaxScope;
      country?: string;
      search?: string;
      page?: number;
      limit?: number;
    } = {}
  ): Promise<{ success: boolean; rules?: TaxRule[]; total?: number; error?: string }> {
    try {
      const {
        taxType,
        status,
        scope,
        country,
        search,
        page = 1,
        limit = 50,
      } = filters;

      const whereConditions: any = {};

      if (taxType) {
        whereConditions.taxType = taxType;
      }

      if (status) {
        whereConditions.status = status;
      } else {
        whereConditions.status = { not: TaxStatus.INACTIVE };
      }

      if (scope) {
        whereConditions.scope = scope;
      }

      if (country) {
        whereConditions.OR = [
          { scope: TaxScope.COUNTRY, scopeValue: country },
          { scope: TaxScope.GLOBAL, scopeValue: '*' },
        ];
      }

      if (search) {
        whereConditions.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [rules, total] = await Promise.all([
        prisma.taxRule.findMany({
          where: whereConditions,
          include: {
            conditions: true,
            exemptions: true,
          },
          orderBy: [
            { priority: 'desc' },
            { createdAt: 'desc' },
          ],
          skip: (page - 1) * limit,
          take: limit,
        }),
        prisma.taxRule.count({ where: whereConditions }),
      ]);

      return {
        success: true,
        rules: rules.map(rule => this.mapDatabaseRuleToTaxRule(rule)),
        total,
      };
    } catch (error) {
      console.error('获取税务规则列表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取税务规则列表失败',
      };
    }
  }

  /**
   * 计算税务
   */
  async calculateTax(
    context: TaxCalculationContext
  ): Promise<{ success: boolean; result?: TaxCalculationResult; error?: string }> {
    try {
      const result = await taxCalculationEngine.calculateTax(context);
      return {
        success: true,
        result,
      };
    } catch (error) {
      console.error('税务计算失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '税务计算失败',
      };
    }
  }

  /**
   * 批量计算税务
   */
  async calculateTaxBatch(
    contexts: TaxCalculationContext[]
  ): Promise<{ success: boolean; results?: TaxCalculationResult[]; error?: string }> {
    try {
      const results = await taxCalculationEngine.calculateTaxBatch(contexts);
      return {
        success: true,
        results,
      };
    } catch (error) {
      console.error('批量税务计算失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量税务计算失败',
      };
    }
  }

  /**
   * 生成税务报表
   */
  async generateTaxReport(
    request: TaxReportRequest,
    userId: string = 'system'
  ): Promise<{ success: boolean; report?: TaxReportData; error?: string }> {
    try {
      const {
        reportType,
        startDate,
        endDate,
        country,
        state,
        taxType,
        currency = 'USD',
        includeExemptions = true,
        groupBy = 'month',
      } = request;

      // 构建查询条件
      const whereConditions: any = {
        calculationDate: {
          gte: startDate,
          lte: endDate,
        },
      };

      if (country) {
        whereConditions.country = country;
      }

      if (state) {
        whereConditions.state = state;
      }

      if (currency) {
        whereConditions.currency = currency;
      }

      // 获取税务计算记录
      const calculations = await prisma.taxCalculation.findMany({
        where: whereConditions,
        orderBy: { calculationDate: 'asc' },
      });

      // 过滤特定税务类型
      let filteredCalculations = calculations;
      if (taxType) {
        filteredCalculations = calculations.filter(calc => 
          calc.taxBreakdown.some((item: any) => item.taxType === taxType)
        );
      }

      // 计算汇总数据
      const summary = this.calculateReportSummary(filteredCalculations, taxType);

      // 生成明细数据
      const breakdown = this.generateReportBreakdown(filteredCalculations, groupBy, taxType);

      // 生成详细数据
      const details = this.generateReportDetails(filteredCalculations, taxType);

      const report: TaxReportData = {
        reportType,
        period: { startDate, endDate },
        summary,
        breakdown,
        details,
        metadata: {
          generatedAt: new Date(),
          generatedBy: userId,
          currency,
          filters: {
            country,
            state,
            taxType,
            includeExemptions,
            groupBy,
          },
        },
      };

      return {
        success: true,
        report,
      };
    } catch (error) {
      console.error('生成税务报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成税务报表失败',
      };
    }
  }

  /**
   * 验证税务ID
   */
  validateTaxId(taxId: string, countryCode: string): { isValid: boolean; error?: string } {
    try {
      const isValid = validateTaxId(taxId, countryCode);
      
      if (!isValid) {
        const config = getCountryTaxConfig(countryCode);
        return {
          isValid: false,
          error: `税务ID格式不正确，应符合${config?.taxIdLabel || '税务ID'}格式`,
        };
      }

      return { isValid: true };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '税务ID验证失败',
      };
    }
  }

  /**
   * 获取默认税率
   */
  getDefaultTaxRate(countryCode: string, productCategory?: string): number {
    return getDefaultTaxRate(countryCode, productCategory);
  }

  /**
   * 格式化税率
   */
  formatTaxRate(rate: number, precision: number = 2): string {
    return formatTaxRate(rate, precision);
  }

  /**
   * 格式化税额
   */
  formatTaxAmount(amount: number, currency: string = 'USD', precision: number = 2): string {
    return formatTaxAmount(amount, currency, precision);
  }

  /**
   * 验证税务规则请求
   */
  private validateTaxRuleRequest(request: Partial<CreateTaxRuleRequest>): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (request.name && request.name.trim().length === 0) {
      errors.push('规则名称不能为空');
    }

    if (request.rate !== undefined) {
      if (request.rate < 0 || request.rate > 1) {
        errors.push('税率必须在0-100%之间');
      }
    }

    if (request.fixedAmount !== undefined && request.fixedAmount < 0) {
      errors.push('固定金额不能为负数');
    }

    if (request.minAmount !== undefined && request.minAmount < 0) {
      errors.push('最小金额不能为负数');
    }

    if (request.maxAmount !== undefined && request.minAmount !== undefined) {
      if (request.maxAmount <= request.minAmount) {
        errors.push('最大金额必须大于最小金额');
      }
    }

    if (request.priority !== undefined) {
      if (request.priority < 0 || request.priority > 100) {
        errors.push('优先级必须在0-100之间');
      }
    }

    if (request.effectiveDate && request.expiryDate) {
      if (request.effectiveDate >= request.expiryDate) {
        errors.push('生效日期必须早于失效日期');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 计算报表汇总数据
   */
  private calculateReportSummary(calculations: any[], taxType?: TaxType): any {
    let totalTaxAmount = 0;
    let totalTaxableAmount = 0;
    let totalExemptAmount = 0;
    let transactionCount = calculations.length;

    for (const calc of calculations) {
      if (taxType) {
        const relevantBreakdown = calc.taxBreakdown.filter((item: any) => item.taxType === taxType);
        totalTaxAmount += relevantBreakdown.reduce((sum: number, item: any) => sum + item.taxAmount, 0);
        totalTaxableAmount += relevantBreakdown.reduce((sum: number, item: any) => sum + item.taxableAmount, 0);
      } else {
        totalTaxAmount += calc.totalTaxAmount;
        totalTaxableAmount += calc.amount;
      }

      if (calc.exemptions && calc.exemptions.length > 0) {
        totalExemptAmount += calc.amount;
      }
    }

    const averageTaxRate = totalTaxableAmount > 0 ? totalTaxAmount / totalTaxableAmount : 0;

    return {
      totalTaxAmount,
      totalTaxableAmount,
      totalExemptAmount,
      averageTaxRate,
      transactionCount,
    };
  }

  /**
   * 生成报表分组数据
   */
  private generateReportBreakdown(calculations: any[], groupBy: string, taxType?: TaxType): any[] {
    // 简化实现，按国家分组
    const groups: Record<string, any> = {};

    for (const calc of calculations) {
      const key = calc.country || 'Unknown';
      
      if (!groups[key]) {
        groups[key] = {
          category: key,
          taxAmount: 0,
          taxableAmount: 0,
          exemptAmount: 0,
          transactionCount: 0,
          averageRate: 0,
        };
      }

      groups[key].transactionCount++;
      
      if (taxType) {
        const relevantBreakdown = calc.taxBreakdown.filter((item: any) => item.taxType === taxType);
        groups[key].taxAmount += relevantBreakdown.reduce((sum: number, item: any) => sum + item.taxAmount, 0);
        groups[key].taxableAmount += relevantBreakdown.reduce((sum: number, item: any) => sum + item.taxableAmount, 0);
      } else {
        groups[key].taxAmount += calc.totalTaxAmount;
        groups[key].taxableAmount += calc.amount;
      }

      if (calc.exemptions && calc.exemptions.length > 0) {
        groups[key].exemptAmount += calc.amount;
      }
    }

    // 计算平均税率
    Object.values(groups).forEach((group: any) => {
      group.averageRate = group.taxableAmount > 0 ? group.taxAmount / group.taxableAmount : 0;
    });

    return Object.values(groups);
  }

  /**
   * 生成报表详细数据
   */
  private generateReportDetails(calculations: any[], taxType?: TaxType): any[] {
    return calculations.map(calc => {
      const details: any = {
        date: calc.calculationDate,
        orderId: calc.orderId,
        customerId: calc.customerId,
        productId: calc.productId,
        taxableAmount: calc.amount,
        taxAmount: calc.totalTaxAmount,
        exemptionReason: calc.exemptions?.[0],
      };

      if (taxType) {
        const relevantBreakdown = calc.taxBreakdown.find((item: any) => item.taxType === taxType);
        if (relevantBreakdown) {
          details.taxAmount = relevantBreakdown.taxAmount;
          details.taxType = relevantBreakdown.taxType;
          details.rate = relevantBreakdown.rate;
        }
      }

      return details;
    });
  }

  /**
   * 映射数据库规则到税务规则
   */
  private mapDatabaseRuleToTaxRule(dbRule: any): TaxRule {
    return {
      id: dbRule.id,
      name: dbRule.name,
      description: dbRule.description,
      taxType: dbRule.taxType as TaxType,
      calculationMethod: dbRule.calculationMethod as TaxCalculationMethod,
      rate: dbRule.rate,
      fixedAmount: dbRule.fixedAmount,
      minAmount: dbRule.minAmount,
      maxAmount: dbRule.maxAmount,
      scope: dbRule.scope as TaxScope,
      scopeValue: dbRule.scopeValue,
      priority: dbRule.priority,
      status: dbRule.status as TaxStatus,
      effectiveDate: dbRule.effectiveDate,
      expiryDate: dbRule.expiryDate,
      conditions: dbRule.conditions || [],
      exemptions: dbRule.exemptions || [],
      createdAt: dbRule.createdAt,
      updatedAt: dbRule.updatedAt,
      createdBy: dbRule.createdBy,
      updatedBy: dbRule.updatedBy,
    };
  }
}

// 导出单例实例
export const taxService = new TaxService();
