/**
 * 税务计算引擎
 * 提供税务计算、规则匹配、税额计算等核心功能
 */

import { 
  TaxRule, 
  TaxCalculationContext, 
  TaxCalculationResult, 
  TaxBreakdownItem,
  TaxCondition,
  TaxExemption,
  TaxType,
  TaxCalculationMethod,
  TaxStatus,
  TieredTaxRate,
  TAX_CALCULATION_CONFIG,
  isTaxRuleValid,
  isTaxExemptionValid,
  getDefaultTaxRate,
  getCountryTaxConfig,
  getProductCategoryTaxConfig,
} from './config';
import { prisma } from '@/lib/prisma';

/**
 * 税务计算引擎类
 */
export class TaxCalculationEngine {
  private ruleCache: Map<string, TaxRule[]> = new Map();
  private cacheTimestamp: Map<string, Date> = new Map();

  /**
   * 计算税务
   */
  async calculateTax(context: TaxCalculationContext): Promise<TaxCalculationResult> {
    try {
      // 获取适用的税务规则
      const applicableRules = await this.getApplicableRules(context);
      
      // 计算税务明细
      const taxBreakdown: TaxBreakdownItem[] = [];
      const appliedRules: string[] = [];
      const exemptions: string[] = [];
      let totalTaxAmount = 0;

      for (const rule of applicableRules) {
        // 检查豁免条件
        const exemption = this.checkExemptions(rule, context);
        if (exemption) {
          exemptions.push(exemption);
          continue;
        }

        // 计算税额
        const taxAmount = this.calculateTaxAmount(rule, context);
        if (taxAmount > 0) {
          const breakdownItem: TaxBreakdownItem = {
            ruleId: rule.id,
            ruleName: rule.name,
            taxType: rule.taxType,
            rate: rule.rate,
            taxableAmount: this.getTaxableAmount(rule, context),
            taxAmount,
            calculationMethod: rule.calculationMethod,
            description: rule.description,
          };

          taxBreakdown.push(breakdownItem);
          appliedRules.push(rule.id);
          totalTaxAmount += taxAmount;
        }
      }

      // 应用舍入规则
      totalTaxAmount = this.roundTaxAmount(totalTaxAmount);

      // 构建计算结果
      const result: TaxCalculationResult = {
        totalTaxAmount,
        totalAmountWithTax: context.amount + totalTaxAmount,
        totalAmountWithoutTax: context.amount,
        taxBreakdown,
        appliedRules,
        exemptions,
        calculationDate: new Date(),
        context,
      };

      // 保存计算记录
      await this.saveTaxCalculation(result);

      return result;
    } catch (error) {
      console.error('税务计算失败:', error);
      throw new Error(`税务计算失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 批量计算税务
   */
  async calculateTaxBatch(
    contexts: TaxCalculationContext[]
  ): Promise<TaxCalculationResult[]> {
    const results: TaxCalculationResult[] = [];

    for (const context of contexts) {
      try {
        const result = await this.calculateTax(context);
        results.push(result);
      } catch (error) {
        console.error(`批量税务计算失败 - 上下文: ${JSON.stringify(context)}`, error);
        // 创建错误结果
        const errorResult: TaxCalculationResult = {
          totalTaxAmount: 0,
          totalAmountWithTax: context.amount,
          totalAmountWithoutTax: context.amount,
          taxBreakdown: [],
          appliedRules: [],
          exemptions: [`计算错误: ${error instanceof Error ? error.message : '未知错误'}`],
          calculationDate: new Date(),
          context,
        };
        results.push(errorResult);
      }
    }

    return results;
  }

  /**
   * 获取适用的税务规则
   */
  private async getApplicableRules(context: TaxCalculationContext): Promise<TaxRule[]> {
    const cacheKey = this.generateCacheKey(context);
    
    // 检查缓存
    if (this.isCacheValid(cacheKey)) {
      const cachedRules = this.ruleCache.get(cacheKey);
      if (cachedRules) {
        return cachedRules;
      }
    }

    // 从数据库获取规则
    const rules = await this.fetchRulesFromDatabase(context);
    
    // 过滤有效规则
    const validRules = rules.filter(rule => isTaxRuleValid(rule));
    
    // 按优先级排序
    validRules.sort((a, b) => b.priority - a.priority);
    
    // 更新缓存
    this.ruleCache.set(cacheKey, validRules);
    this.cacheTimestamp.set(cacheKey, new Date());

    return validRules;
  }

  /**
   * 从数据库获取税务规则
   */
  private async fetchRulesFromDatabase(context: TaxCalculationContext): Promise<TaxRule[]> {
    const whereConditions: any = {
      status: TaxStatus.ACTIVE,
      effectiveDate: {
        lte: new Date(),
      },
      OR: [
        { expiryDate: null },
        { expiryDate: { gte: new Date() } },
      ],
    };

    // 添加地理位置条件
    const locationConditions = [];
    
    if (context.country) {
      locationConditions.push({
        scope: 'COUNTRY',
        scopeValue: context.country,
      });
    }
    
    if (context.state) {
      locationConditions.push({
        scope: 'STATE',
        scopeValue: context.state,
      });
    }
    
    if (context.city) {
      locationConditions.push({
        scope: 'CITY',
        scopeValue: context.city,
      });
    }
    
    if (context.postalCode) {
      locationConditions.push({
        scope: 'POSTAL_CODE',
        scopeValue: context.postalCode,
      });
    }

    // 添加商品类别条件
    if (context.productCategory) {
      locationConditions.push({
        scope: 'PRODUCT_CATEGORY',
        scopeValue: context.productCategory,
      });
    }

    // 添加客户类型条件
    if (context.customerType) {
      locationConditions.push({
        scope: 'CUSTOMER_TYPE',
        scopeValue: context.customerType,
      });
    }

    // 添加全球规则
    locationConditions.push({
      scope: 'GLOBAL',
      scopeValue: '*',
    });

    if (locationConditions.length > 0) {
      whereConditions.OR = [
        ...whereConditions.OR,
        ...locationConditions,
      ];
    }

    const rules = await prisma.taxRule.findMany({
      where: whereConditions,
      include: {
        conditions: true,
        exemptions: true,
      },
      orderBy: { priority: 'desc' },
    });

    return rules.map(rule => this.mapDatabaseRuleToTaxRule(rule));
  }

  /**
   * 计算税额
   */
  private calculateTaxAmount(rule: TaxRule, context: TaxCalculationContext): number {
    const taxableAmount = this.getTaxableAmount(rule, context);
    
    switch (rule.calculationMethod) {
      case TaxCalculationMethod.PERCENTAGE:
        return taxableAmount * rule.rate;
        
      case TaxCalculationMethod.FIXED_AMOUNT:
        return rule.fixedAmount || 0;
        
      case TaxCalculationMethod.TIERED:
        return this.calculateTieredTax(rule, taxableAmount);
        
      case TaxCalculationMethod.COMPOUND:
        return this.calculateCompoundTax(rule, context);
        
      case TaxCalculationMethod.INCLUSIVE:
        return this.calculateInclusiveTax(rule, taxableAmount);
        
      case TaxCalculationMethod.EXCLUSIVE:
        return taxableAmount * rule.rate;
        
      default:
        return 0;
    }
  }

  /**
   * 获取应税金额
   */
  private getTaxableAmount(rule: TaxRule, context: TaxCalculationContext): number {
    let taxableAmount = context.amount;

    // 减去运费（如果规则不包含运费）
    if (context.shippingAmount && !this.includesShipping(rule)) {
      taxableAmount -= context.shippingAmount;
    }

    // 减去折扣（如果规则应用折扣后金额）
    if (context.discountAmount && this.appliesAfterDiscount(rule)) {
      taxableAmount -= context.discountAmount;
    }

    // 应用最小和最大金额限制
    if (rule.minAmount && taxableAmount < rule.minAmount) {
      return 0;
    }

    if (rule.maxAmount && taxableAmount > rule.maxAmount) {
      taxableAmount = rule.maxAmount;
    }

    return Math.max(0, taxableAmount);
  }

  /**
   * 计算阶梯税率
   */
  private calculateTieredTax(rule: TaxRule, taxableAmount: number): number {
    // 这里需要从规则中获取阶梯税率配置
    // 简化实现，假设有阶梯税率数据
    const tieredRates: TieredTaxRate[] = this.getTieredRates(rule);
    
    let totalTax = 0;
    let remainingAmount = taxableAmount;

    for (const tier of tieredRates) {
      if (remainingAmount <= 0) break;

      const tierAmount = tier.maxAmount 
        ? Math.min(remainingAmount, tier.maxAmount - tier.minAmount)
        : remainingAmount;

      if (tierAmount > 0) {
        totalTax += tierAmount * tier.rate + (tier.fixedAmount || 0);
        remainingAmount -= tierAmount;
      }
    }

    return totalTax;
  }

  /**
   * 计算复合税率
   */
  private calculateCompoundTax(rule: TaxRule, context: TaxCalculationContext): number {
    // 复合税率计算逻辑
    // 这里简化实现
    const baseAmount = this.getTaxableAmount(rule, context);
    return baseAmount * rule.rate;
  }

  /**
   * 计算含税价格的税额
   */
  private calculateInclusiveTax(rule: TaxRule, taxableAmount: number): number {
    // 含税价格计算：税额 = 含税价格 * 税率 / (1 + 税率)
    return (taxableAmount * rule.rate) / (1 + rule.rate);
  }

  /**
   * 检查豁免条件
   */
  private checkExemptions(rule: TaxRule, context: TaxCalculationContext): string | null {
    for (const exemption of rule.exemptions) {
      if (!isTaxExemptionValid(exemption)) {
        continue;
      }

      switch (exemption.type) {
        case 'CUSTOMER_TYPE':
          if (context.customerType === exemption.value) {
            return `客户类型豁免: ${exemption.value}`;
          }
          break;
          
        case 'PRODUCT_CATEGORY':
          if (context.productCategory === exemption.value) {
            return `商品类别豁免: ${exemption.value}`;
          }
          break;
          
        case 'AMOUNT_THRESHOLD':
          const threshold = parseFloat(exemption.value);
          if (context.amount < threshold) {
            return `金额阈值豁免: 低于 ${threshold}`;
          }
          break;
          
        case 'LOCATION':
          if (this.matchesLocation(exemption.value, context)) {
            return `地理位置豁免: ${exemption.value}`;
          }
          break;
          
        case 'CERTIFICATE':
          if (context.taxExemptionCertificate === exemption.certificateNumber) {
            return `证书豁免: ${exemption.certificateNumber}`;
          }
          break;
      }
    }

    return null;
  }

  /**
   * 检查条件是否匹配
   */
  private checkConditions(conditions: TaxCondition[], context: TaxCalculationContext): boolean {
    if (conditions.length === 0) return true;

    let result = true;
    let currentLogicalOperator: 'AND' | 'OR' = 'AND';

    for (const condition of conditions) {
      const conditionResult = this.evaluateCondition(condition, context);
      
      if (currentLogicalOperator === 'AND') {
        result = result && conditionResult;
      } else {
        result = result || conditionResult;
      }

      currentLogicalOperator = condition.logicalOperator || 'AND';
    }

    return result;
  }

  /**
   * 评估单个条件
   */
  private evaluateCondition(condition: TaxCondition, context: TaxCalculationContext): boolean {
    const contextValue = this.getContextValue(condition.field, context);
    
    switch (condition.operator) {
      case 'equals':
        return contextValue === condition.value;
      case 'not_equals':
        return contextValue !== condition.value;
      case 'greater_than':
        return Number(contextValue) > Number(condition.value);
      case 'less_than':
        return Number(contextValue) < Number(condition.value);
      case 'contains':
        return String(contextValue).includes(String(condition.value));
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(contextValue);
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(contextValue);
      default:
        return false;
    }
  }

  /**
   * 获取上下文值
   */
  private getContextValue(field: string, context: TaxCalculationContext): any {
    const fieldMap: Record<string, any> = {
      'amount': context.amount,
      'currency': context.currency,
      'country': context.country,
      'state': context.state,
      'city': context.city,
      'postalCode': context.postalCode,
      'productId': context.productId,
      'productCategory': context.productCategory,
      'productType': context.productType,
      'customerId': context.customerId,
      'customerType': context.customerType,
      'orderId': context.orderId,
      'orderDate': context.orderDate,
      'shippingAmount': context.shippingAmount,
      'discountAmount': context.discountAmount,
    };

    return fieldMap[field] || context.metadata?.[field];
  }

  /**
   * 舍入税额
   */
  private roundTaxAmount(amount: number): number {
    const precision = Math.pow(10, TAX_CALCULATION_CONFIG.displayPrecision);
    
    switch (TAX_CALCULATION_CONFIG.roundingMode) {
      case 'ROUND_HALF_UP':
        return Math.round(amount * precision) / precision;
      case 'ROUND_HALF_DOWN':
        return Math.floor(amount * precision + 0.5) / precision;
      case 'ROUND_UP':
        return Math.ceil(amount * precision) / precision;
      case 'ROUND_DOWN':
        return Math.floor(amount * precision) / precision;
      default:
        return Math.round(amount * precision) / precision;
    }
  }

  /**
   * 保存税务计算记录
   */
  private async saveTaxCalculation(result: TaxCalculationResult): Promise<void> {
    try {
      await prisma.taxCalculation.create({
        data: {
          orderId: result.context.orderId,
          customerId: result.context.customerId,
          productId: result.context.productId,
          amount: result.context.amount,
          currency: result.context.currency,
          country: result.context.country,
          state: result.context.state,
          city: result.context.city,
          postalCode: result.context.postalCode,
          totalTaxAmount: result.totalTaxAmount,
          totalAmountWithTax: result.totalAmountWithTax,
          appliedRules: result.appliedRules,
          exemptions: result.exemptions,
          taxBreakdown: result.taxBreakdown,
          calculationDate: result.calculationDate,
          context: result.context,
          createdBy: 'system',
        },
      });
    } catch (error) {
      console.error('保存税务计算记录失败:', error);
      // 不抛出错误，避免影响主要计算流程
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(context: TaxCalculationContext): string {
    const keyParts = [
      context.country,
      context.state || '',
      context.city || '',
      context.postalCode || '',
      context.productCategory || '',
      context.customerType || '',
    ];
    
    return keyParts.join('|');
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamp.get(cacheKey);
    if (!timestamp) return false;
    
    const now = new Date();
    const cacheAge = (now.getTime() - timestamp.getTime()) / (1000 * 60); // 分钟
    
    return cacheAge < TAX_CALCULATION_CONFIG.cacheTimeout;
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.ruleCache.clear();
    this.cacheTimestamp.clear();
  }

  /**
   * 辅助方法
   */
  private getTieredRates(rule: TaxRule): TieredTaxRate[] {
    // 从规则的元数据中获取阶梯税率配置
    // 这里简化实现
    return [
      { minAmount: 0, maxAmount: 1000, rate: 0.05 },
      { minAmount: 1000, maxAmount: 5000, rate: 0.10 },
      { minAmount: 5000, rate: 0.15 },
    ];
  }

  private includesShipping(rule: TaxRule): boolean {
    // 检查规则是否包含运费
    return rule.description?.includes('包含运费') || false;
  }

  private appliesAfterDiscount(rule: TaxRule): boolean {
    // 检查规则是否在折扣后应用
    return rule.description?.includes('折扣后') || false;
  }

  private matchesLocation(locationValue: string, context: TaxCalculationContext): boolean {
    // 检查地理位置是否匹配
    return locationValue === context.country || 
           locationValue === context.state || 
           locationValue === context.city || 
           locationValue === context.postalCode;
  }

  private mapDatabaseRuleToTaxRule(dbRule: any): TaxRule {
    return {
      id: dbRule.id,
      name: dbRule.name,
      description: dbRule.description,
      taxType: dbRule.taxType as TaxType,
      calculationMethod: dbRule.calculationMethod as TaxCalculationMethod,
      rate: dbRule.rate,
      fixedAmount: dbRule.fixedAmount,
      minAmount: dbRule.minAmount,
      maxAmount: dbRule.maxAmount,
      scope: dbRule.scope,
      scopeValue: dbRule.scopeValue,
      priority: dbRule.priority,
      status: dbRule.status as TaxStatus,
      effectiveDate: dbRule.effectiveDate,
      expiryDate: dbRule.expiryDate,
      conditions: dbRule.conditions || [],
      exemptions: dbRule.exemptions || [],
      createdAt: dbRule.createdAt,
      updatedAt: dbRule.updatedAt,
      createdBy: dbRule.createdBy,
      updatedBy: dbRule.updatedBy,
    };
  }
}

// 导出单例实例
export const taxCalculationEngine = new TaxCalculationEngine();
