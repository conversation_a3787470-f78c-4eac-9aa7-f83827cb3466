/**
 * 税务配置文件
 * 定义税务类型、税率规则、计算方式等
 */

// 税务类型枚举
export enum TaxType {
  VAT = 'VAT',                    // 增值税
  GST = 'GST',                    // 商品和服务税
  SALES_TAX = 'SALES_TAX',        // 销售税
  EXCISE_TAX = 'EXCISE_TAX',      // 消费税
  IMPORT_DUTY = 'IMPORT_DUTY',    // 进口关税
  EXPORT_TAX = 'EXPORT_TAX',      // 出口税
  WITHHOLDING_TAX = 'WITHHOLDING_TAX', // 预扣税
  CORPORATE_TAX = 'CORPORATE_TAX', // 企业所得税
  CUSTOMS_DUTY = 'CUSTOMS_DUTY',   // 海关税
  ENVIRONMENTAL_TAX = 'ENVIRONMENTAL_TAX', // 环境税
}

// 税率计算方式枚举
export enum TaxCalculationMethod {
  PERCENTAGE = 'PERCENTAGE',       // 百分比
  FIXED_AMOUNT = 'FIXED_AMOUNT',   // 固定金额
  TIERED = 'TIERED',              // 阶梯税率
  COMPOUND = 'COMPOUND',          // 复合税率
  INCLUSIVE = 'INCLUSIVE',        // 含税价格
  EXCLUSIVE = 'EXCLUSIVE',        // 不含税价格
}

// 税务状态枚举
export enum TaxStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  EXPIRED = 'EXPIRED',
  SUSPENDED = 'SUSPENDED',
}

// 税务适用范围枚举
export enum TaxScope {
  GLOBAL = 'GLOBAL',              // 全球
  COUNTRY = 'COUNTRY',            // 国家
  STATE = 'STATE',                // 州/省
  CITY = 'CITY',                  // 城市
  POSTAL_CODE = 'POSTAL_CODE',    // 邮政编码
  PRODUCT_CATEGORY = 'PRODUCT_CATEGORY', // 商品类别
  CUSTOMER_TYPE = 'CUSTOMER_TYPE', // 客户类型
}

// 税务规则接口
export interface TaxRule {
  id: string;
  name: string;
  description?: string;
  taxType: TaxType;
  calculationMethod: TaxCalculationMethod;
  rate: number;
  fixedAmount?: number;
  minAmount?: number;
  maxAmount?: number;
  scope: TaxScope;
  scopeValue: string;
  priority: number;
  status: TaxStatus;
  effectiveDate: Date;
  expiryDate?: Date;
  conditions: TaxCondition[];
  exemptions: TaxExemption[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

// 税务条件接口
export interface TaxCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'in' | 'not_in';
  value: any;
  logicalOperator?: 'AND' | 'OR';
}

// 税务豁免接口
export interface TaxExemption {
  type: 'CUSTOMER_TYPE' | 'PRODUCT_CATEGORY' | 'AMOUNT_THRESHOLD' | 'LOCATION' | 'CERTIFICATE';
  value: string;
  description?: string;
  certificateNumber?: string;
  validFrom?: Date;
  validTo?: Date;
}

// 阶梯税率接口
export interface TieredTaxRate {
  minAmount: number;
  maxAmount?: number;
  rate: number;
  fixedAmount?: number;
}

// 税务计算上下文接口
export interface TaxCalculationContext {
  // 基本信息
  amount: number;
  currency: string;
  
  // 地理位置
  country: string;
  state?: string;
  city?: string;
  postalCode?: string;
  
  // 商品信息
  productId?: string;
  productCategory?: string;
  productType?: string;
  
  // 客户信息
  customerId?: string;
  customerType?: 'INDIVIDUAL' | 'BUSINESS' | 'GOVERNMENT' | 'NON_PROFIT';
  taxExemptionCertificate?: string;
  
  // 订单信息
  orderId?: string;
  orderDate?: Date;
  shippingAmount?: number;
  discountAmount?: number;
  
  // 其他上下文
  metadata?: Record<string, any>;
}

// 税务计算结果接口
export interface TaxCalculationResult {
  totalTaxAmount: number;
  totalAmountWithTax: number;
  totalAmountWithoutTax: number;
  taxBreakdown: TaxBreakdownItem[];
  appliedRules: string[];
  exemptions: string[];
  calculationDate: Date;
  context: TaxCalculationContext;
}

// 税务明细项接口
export interface TaxBreakdownItem {
  ruleId: string;
  ruleName: string;
  taxType: TaxType;
  rate: number;
  taxableAmount: number;
  taxAmount: number;
  calculationMethod: TaxCalculationMethod;
  description?: string;
}

// 国家税务配置
export const COUNTRY_TAX_CONFIGS: Record<string, {
  defaultTaxType: TaxType;
  defaultRate: number;
  taxIdFormat: RegExp;
  taxIdLabel: string;
  supportedTaxTypes: TaxType[];
  requiresTaxId: boolean;
  taxInclusivePricing: boolean;
}> = {
  // 美国
  US: {
    defaultTaxType: TaxType.SALES_TAX,
    defaultRate: 0.08,
    taxIdFormat: /^\d{2}-\d{7}$/,
    taxIdLabel: 'EIN',
    supportedTaxTypes: [TaxType.SALES_TAX, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: false,
  },
  
  // 中国
  CN: {
    defaultTaxType: TaxType.VAT,
    defaultRate: 0.13,
    taxIdFormat: /^[0-9A-Z]{18}$/,
    taxIdLabel: '统一社会信用代码',
    supportedTaxTypes: [TaxType.VAT, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY, TaxType.EXPORT_TAX],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
  
  // 英国
  GB: {
    defaultTaxType: TaxType.VAT,
    defaultRate: 0.20,
    taxIdFormat: /^GB\d{9}$/,
    taxIdLabel: 'VAT Number',
    supportedTaxTypes: [TaxType.VAT, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
  
  // 德国
  DE: {
    defaultTaxType: TaxType.VAT,
    defaultRate: 0.19,
    taxIdFormat: /^DE\d{9}$/,
    taxIdLabel: 'USt-IdNr.',
    supportedTaxTypes: [TaxType.VAT, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
  
  // 法国
  FR: {
    defaultTaxType: TaxType.VAT,
    defaultRate: 0.20,
    taxIdFormat: /^FR[A-Z0-9]{2}\d{9}$/,
    taxIdLabel: 'Numéro de TVA',
    supportedTaxTypes: [TaxType.VAT, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
  
  // 日本
  JP: {
    defaultTaxType: TaxType.VAT,
    defaultRate: 0.10,
    taxIdFormat: /^T\d{13}$/,
    taxIdLabel: '適格請求書発行事業者登録番号',
    supportedTaxTypes: [TaxType.VAT, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
  
  // 加拿大
  CA: {
    defaultTaxType: TaxType.GST,
    defaultRate: 0.05,
    taxIdFormat: /^\d{9}RT\d{4}$/,
    taxIdLabel: 'GST/HST Number',
    supportedTaxTypes: [TaxType.GST, TaxType.SALES_TAX, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: false,
  },
  
  // 澳大利亚
  AU: {
    defaultTaxType: TaxType.GST,
    defaultRate: 0.10,
    taxIdFormat: /^\d{11}$/,
    taxIdLabel: 'ABN',
    supportedTaxTypes: [TaxType.GST, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
  
  // 新加坡
  SG: {
    defaultTaxType: TaxType.GST,
    defaultRate: 0.07,
    taxIdFormat: /^\d{8}[A-Z]$/,
    taxIdLabel: 'GST Registration Number',
    supportedTaxTypes: [TaxType.GST, TaxType.EXCISE_TAX, TaxType.IMPORT_DUTY],
    requiresTaxId: true,
    taxInclusivePricing: true,
  },
};

// 商品类别税务配置
export const PRODUCT_CATEGORY_TAX_CONFIGS: Record<string, {
  defaultTaxType: TaxType;
  exemptCountries: string[];
  reducedRateCountries: Record<string, number>;
  specialRules: string[];
}> = {
  // 食品
  FOOD: {
    defaultTaxType: TaxType.VAT,
    exemptCountries: ['GB', 'IE'],
    reducedRateCountries: {
      'DE': 0.07,
      'FR': 0.055,
      'IT': 0.04,
      'ES': 0.04,
    },
    specialRules: ['ORGANIC_FOOD_EXEMPTION', 'BABY_FOOD_EXEMPTION'],
  },
  
  // 书籍
  BOOKS: {
    defaultTaxType: TaxType.VAT,
    exemptCountries: ['GB', 'IE', 'CA'],
    reducedRateCountries: {
      'DE': 0.07,
      'FR': 0.055,
      'IT': 0.04,
      'ES': 0.04,
    },
    specialRules: ['EDUCATIONAL_BOOK_EXEMPTION'],
  },
  
  // 医疗用品
  MEDICAL: {
    defaultTaxType: TaxType.VAT,
    exemptCountries: ['GB', 'IE', 'CA', 'AU'],
    reducedRateCountries: {
      'DE': 0.07,
      'FR': 0.055,
    },
    specialRules: ['PRESCRIPTION_EXEMPTION', 'MEDICAL_DEVICE_EXEMPTION'],
  },
  
  // 奢侈品
  LUXURY: {
    defaultTaxType: TaxType.EXCISE_TAX,
    exemptCountries: [],
    reducedRateCountries: {},
    specialRules: ['LUXURY_TAX_THRESHOLD'],
  },
  
  // 电子产品
  ELECTRONICS: {
    defaultTaxType: TaxType.VAT,
    exemptCountries: [],
    reducedRateCountries: {},
    specialRules: ['WEEE_TAX', 'BATTERY_TAX'],
  },
};

// 税务计算精度配置
export const TAX_CALCULATION_CONFIG = {
  // 计算精度（小数位数）
  calculationPrecision: 6,
  // 显示精度（小数位数）
  displayPrecision: 2,
  // 舍入方式
  roundingMode: 'ROUND_HALF_UP' as 'ROUND_HALF_UP' | 'ROUND_HALF_DOWN' | 'ROUND_UP' | 'ROUND_DOWN',
  // 最小税额
  minimumTaxAmount: 0.01,
  // 最大税率
  maximumTaxRate: 1.0,
  // 缓存时间（分钟）
  cacheTimeout: 60,
};

// 税务报表类型枚举
export enum TaxReportType {
  VAT_RETURN = 'VAT_RETURN',
  SALES_TAX_RETURN = 'SALES_TAX_RETURN',
  GST_RETURN = 'GST_RETURN',
  EXCISE_TAX_RETURN = 'EXCISE_TAX_RETURN',
  IMPORT_DUTY_SUMMARY = 'IMPORT_DUTY_SUMMARY',
  TAX_LIABILITY_REPORT = 'TAX_LIABILITY_REPORT',
  TAX_EXEMPTION_REPORT = 'TAX_EXEMPTION_REPORT',
  TAX_AUDIT_TRAIL = 'TAX_AUDIT_TRAIL',
}

// 税务报表配置
export const TAX_REPORT_CONFIGS: Record<TaxReportType, {
  name: string;
  description: string;
  frequency: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY' | 'ON_DEMAND';
  requiredFields: string[];
  supportedCountries: string[];
  template?: string;
}> = {
  [TaxReportType.VAT_RETURN]: {
    name: 'VAT Return',
    description: '增值税申报表',
    frequency: 'QUARTERLY',
    requiredFields: ['taxPeriod', 'totalSales', 'totalPurchases', 'vatCollected', 'vatPaid'],
    supportedCountries: ['GB', 'DE', 'FR', 'IT', 'ES', 'NL', 'BE'],
    template: 'vat_return_template',
  },
  
  [TaxReportType.SALES_TAX_RETURN]: {
    name: 'Sales Tax Return',
    description: '销售税申报表',
    frequency: 'MONTHLY',
    requiredFields: ['taxPeriod', 'grossSales', 'exemptSales', 'taxableSales', 'taxCollected'],
    supportedCountries: ['US'],
    template: 'sales_tax_return_template',
  },
  
  [TaxReportType.GST_RETURN]: {
    name: 'GST Return',
    description: 'GST申报表',
    frequency: 'MONTHLY',
    requiredFields: ['taxPeriod', 'totalSales', 'totalPurchases', 'gstCollected', 'gstPaid'],
    supportedCountries: ['CA', 'AU', 'SG', 'IN'],
    template: 'gst_return_template',
  },
  
  [TaxReportType.EXCISE_TAX_RETURN]: {
    name: 'Excise Tax Return',
    description: '消费税申报表',
    frequency: 'MONTHLY',
    requiredFields: ['taxPeriod', 'excisableGoods', 'exciseTaxRate', 'exciseTaxAmount'],
    supportedCountries: ['US', 'CN', 'GB', 'DE', 'FR'],
    template: 'excise_tax_return_template',
  },
  
  [TaxReportType.IMPORT_DUTY_SUMMARY]: {
    name: 'Import Duty Summary',
    description: '进口关税汇总表',
    frequency: 'MONTHLY',
    requiredFields: ['importPeriod', 'totalImportValue', 'dutyRate', 'dutyAmount'],
    supportedCountries: ['US', 'CN', 'GB', 'DE', 'FR', 'CA', 'AU'],
    template: 'import_duty_summary_template',
  },
  
  [TaxReportType.TAX_LIABILITY_REPORT]: {
    name: 'Tax Liability Report',
    description: '税务负债报告',
    frequency: 'MONTHLY',
    requiredFields: ['reportPeriod', 'totalTaxLiability', 'paidTaxAmount', 'outstandingTaxAmount'],
    supportedCountries: ['*'],
    template: 'tax_liability_report_template',
  },
  
  [TaxReportType.TAX_EXEMPTION_REPORT]: {
    name: 'Tax Exemption Report',
    description: '税务豁免报告',
    frequency: 'QUARTERLY',
    requiredFields: ['reportPeriod', 'exemptionType', 'exemptionAmount', 'exemptionReason'],
    supportedCountries: ['*'],
    template: 'tax_exemption_report_template',
  },
  
  [TaxReportType.TAX_AUDIT_TRAIL]: {
    name: 'Tax Audit Trail',
    description: '税务审计跟踪',
    frequency: 'ON_DEMAND',
    requiredFields: ['auditPeriod', 'transactionDetails', 'taxCalculations', 'supportingDocuments'],
    supportedCountries: ['*'],
    template: 'tax_audit_trail_template',
  },
};

// 获取国家税务配置
export function getCountryTaxConfig(countryCode: string) {
  return COUNTRY_TAX_CONFIGS[countryCode.toUpperCase()];
}

// 获取商品类别税务配置
export function getProductCategoryTaxConfig(category: string) {
  return PRODUCT_CATEGORY_TAX_CONFIGS[category.toUpperCase()];
}

// 检查税务规则是否有效
export function isTaxRuleValid(rule: TaxRule, date: Date = new Date()): boolean {
  if (rule.status !== TaxStatus.ACTIVE) {
    return false;
  }
  
  if (date < rule.effectiveDate) {
    return false;
  }
  
  if (rule.expiryDate && date > rule.expiryDate) {
    return false;
  }
  
  return true;
}

// 检查税务豁免是否有效
export function isTaxExemptionValid(exemption: TaxExemption, date: Date = new Date()): boolean {
  if (exemption.validFrom && date < exemption.validFrom) {
    return false;
  }
  
  if (exemption.validTo && date > exemption.validTo) {
    return false;
  }
  
  return true;
}

// 格式化税率
export function formatTaxRate(rate: number, precision: number = 2): string {
  return `${(rate * 100).toFixed(precision)}%`;
}

// 格式化税额
export function formatTaxAmount(amount: number, currency: string = 'USD', precision: number = 2): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: precision,
    maximumFractionDigits: precision,
  }).format(amount);
}

// 验证税务ID格式
export function validateTaxId(taxId: string, countryCode: string): boolean {
  const config = getCountryTaxConfig(countryCode);
  if (!config) {
    return false;
  }
  
  return config.taxIdFormat.test(taxId);
}

// 获取默认税率
export function getDefaultTaxRate(countryCode: string, productCategory?: string): number {
  const countryConfig = getCountryTaxConfig(countryCode);
  if (!countryConfig) {
    return 0;
  }
  
  if (productCategory) {
    const categoryConfig = getProductCategoryTaxConfig(productCategory);
    if (categoryConfig?.reducedRateCountries[countryCode]) {
      return categoryConfig.reducedRateCountries[countryCode];
    }
    
    if (categoryConfig?.exemptCountries.includes(countryCode)) {
      return 0;
    }
  }
  
  return countryConfig.defaultRate;
}
