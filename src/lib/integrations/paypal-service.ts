/**
 * PayPal支付集成服务
 * 提供PayPal支付、PayPal Express、批量支付、争议处理等功能
 */

import axios, { AxiosInstance } from 'axios';
import { prisma } from '@/lib/prisma';
import { financialService } from '@/lib/services/financial-service';

// PayPal环境枚举
export enum PayPalEnvironment {
  SANDBOX = 'sandbox',
  LIVE = 'live',
}

// PayPal支付状态枚举
export enum PayPalPaymentStatus {
  CREATED = 'CREATED',
  SAVED = 'SAVED',
  APPROVED = 'APPROVED',
  VOIDED = 'VOIDED',
  COMPLETED = 'COMPLETED',
  PAYER_ACTION_REQUIRED = 'PAYER_ACTION_REQUIRED',
}

// PayPal配置接口
export interface PayPalConfig {
  clientId: string;
  clientSecret: string;
  environment: PayPalEnvironment;
  webhookId?: string;
}

// 创建订单请求接口
export interface CreateOrderRequest {
  amount: number;
  currency: string;
  orderId: string;
  description?: string;
  returnUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, any>;
}

// 批量支付请求接口
export interface BatchPayoutRequest {
  senderBatchId: string;
  emailSubject?: string;
  emailMessage?: string;
  items: PayoutItem[];
}

export interface PayoutItem {
  recipientType: 'EMAIL' | 'PHONE' | 'PAYPAL_ID';
  amount: number;
  currency: string;
  receiver: string;
  note?: string;
  senderItemId?: string;
}

// PayPal订单响应接口
export interface PayPalOrderResponse {
  id: string;
  status: PayPalPaymentStatus;
  links: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

/**
 * PayPal支付服务类
 */
export class PayPalService {
  private config: PayPalConfig;
  private apiClient: AxiosInstance;
  private accessToken?: string;
  private tokenExpiresAt?: Date;

  constructor() {
    this.config = {
      clientId: process.env.PAYPAL_CLIENT_ID!,
      clientSecret: process.env.PAYPAL_CLIENT_SECRET!,
      environment: (process.env.PAYPAL_ENVIRONMENT as PayPalEnvironment) || PayPalEnvironment.SANDBOX,
      webhookId: process.env.PAYPAL_WEBHOOK_ID,
    };

    const baseURL = this.config.environment === PayPalEnvironment.SANDBOX
      ? 'https://api-m.sandbox.paypal.com'
      : 'https://api-m.paypal.com';

    this.apiClient = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // 请求拦截器：自动添加访问令牌
    this.apiClient.interceptors.request.use(async (config) => {
      const token = await this.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  /**
   * 创建PayPal订单
   */
  async createOrder(
    request: CreateOrderRequest
  ): Promise<{ success: boolean; data?: PayPalOrderResponse; error?: string }> {
    try {
      const {
        amount,
        currency,
        orderId,
        description,
        returnUrl,
        cancelUrl,
        metadata = {},
      } = request;

      // 验证本地订单
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { customer: true },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 构建PayPal订单数据
      const orderData = {
        intent: 'CAPTURE',
        purchase_units: [
          {
            reference_id: orderId,
            description: description || `订单 ${order.orderNumber}`,
            custom_id: orderId,
            amount: {
              currency_code: currency.toUpperCase(),
              value: amount.toFixed(2),
            },
          },
        ],
        application_context: {
          brand_name: 'CBEC ERP系统',
          landing_page: 'BILLING',
          user_action: 'PAY_NOW',
          return_url: returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/payment/success`,
          cancel_url: cancelUrl || `${process.env.NEXT_PUBLIC_APP_URL}/payment/cancel`,
        },
      };

      // 调用PayPal API创建订单
      const response = await this.apiClient.post('/v2/checkout/orders', orderData);
      const paypalOrder = response.data as PayPalOrderResponse;

      // 保存支付记录
      await prisma.payment.create({
        data: {
          id: `paypal_${paypalOrder.id}`,
          orderId,
          customerId: order.customerId,
          amount,
          currency: currency.toUpperCase(),
          paymentMethod: 'PAYPAL',
          status: 'PENDING',
          externalId: paypalOrder.id,
          externalData: paypalOrder,
          metadata,
          createdBy: 'system',
        },
      });

      return {
        success: true,
        data: paypalOrder,
      };
    } catch (error) {
      console.error('创建PayPal订单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建PayPal订单失败',
      };
    }
  }

  /**
   * 捕获PayPal订单
   */
  async captureOrder(
    paypalOrderId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 调用PayPal API捕获订单
      const response = await this.apiClient.post(`/v2/checkout/orders/${paypalOrderId}/capture`);
      const captureResult = response.data;

      // 更新本地支付记录
      await prisma.payment.updateMany({
        where: { externalId: paypalOrderId },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
          externalData: captureResult,
        },
      });

      // 更新订单状态
      const payment = await prisma.payment.findFirst({
        where: { externalId: paypalOrderId },
      });

      if (payment) {
        await prisma.order.update({
          where: { id: payment.orderId },
          data: {
            paymentStatus: 'PAID',
            paidAt: new Date(),
          },
        });

        // 调用财务服务处理支付回调
        await financialService.handlePaymentCallback(
          payment.id,
          'COMPLETED' as any,
          paypalOrderId,
          { paypalCapture: captureResult }
        );
      }

      return {
        success: true,
        data: captureResult,
      };
    } catch (error) {
      console.error('捕获PayPal订单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '捕获PayPal订单失败',
      };
    }
  }

  /**
   * 创建退款
   */
  async createRefund(
    captureId: string,
    amount?: number,
    currency?: string,
    note?: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const refundData: any = {};
      
      if (amount && currency) {
        refundData.amount = {
          value: amount.toFixed(2),
          currency_code: currency.toUpperCase(),
        };
      }

      if (note) {
        refundData.note_to_payer = note;
      }

      // 调用PayPal API创建退款
      const response = await this.apiClient.post(`/v2/payments/captures/${captureId}/refund`, refundData);
      const refundResult = response.data;

      // 创建本地退款记录
      const payment = await prisma.payment.findFirst({
        where: {
          externalData: {
            path: ['purchase_units', '0', 'payments', 'captures'],
            array_contains: [{ id: captureId }],
          },
        },
      });

      if (payment) {
        await prisma.refund.create({
          data: {
            id: `paypal_${refundResult.id}`,
            paymentId: payment.id,
            amount: parseFloat(refundResult.amount?.value || '0'),
            currency: refundResult.amount?.currency_code || 'USD',
            reason: 'requested_by_customer',
            status: 'COMPLETED',
            externalId: refundResult.id,
            externalData: refundResult,
            createdBy: 'system',
          },
        });

        // 更新支付状态
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            status: refundResult.amount?.value === payment.amount.toString() ? 'REFUNDED' : 'PARTIAL_REFUND',
          },
        });
      }

      return {
        success: true,
        data: refundResult,
      };
    } catch (error) {
      console.error('创建PayPal退款失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建PayPal退款失败',
      };
    }
  }

  /**
   * 创建批量支付
   */
  async createBatchPayout(
    request: BatchPayoutRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const { senderBatchId, emailSubject, emailMessage, items } = request;

      // 构建批量支付数据
      const payoutData = {
        sender_batch_header: {
          sender_batch_id: senderBatchId,
          email_subject: emailSubject || '您收到了一笔付款',
          email_message: emailMessage || '感谢您的业务合作',
        },
        items: items.map(item => ({
          recipient_type: item.recipientType,
          amount: {
            value: item.amount.toFixed(2),
            currency: item.currency.toUpperCase(),
          },
          receiver: item.receiver,
          note: item.note || '',
          sender_item_id: item.senderItemId || `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        })),
      };

      // 调用PayPal API创建批量支付
      const response = await this.apiClient.post('/v1/payments/payouts', payoutData);
      const payoutResult = response.data;

      // 保存批量支付记录
      await prisma.batchPayout.create({
        data: {
          id: `paypal_${payoutResult.batch_header.payout_batch_id}`,
          senderBatchId,
          status: payoutResult.batch_header.batch_status,
          totalAmount: items.reduce((sum, item) => sum + item.amount, 0),
          totalItems: items.length,
          externalId: payoutResult.batch_header.payout_batch_id,
          externalData: payoutResult,
          createdBy: 'system',
        },
      });

      return {
        success: true,
        data: payoutResult,
      };
    } catch (error) {
      console.error('创建PayPal批量支付失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建PayPal批量支付失败',
      };
    }
  }

  /**
   * 处理Webhook事件
   */
  async handleWebhook(
    payload: any,
    headers: Record<string, string>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 验证Webhook签名
      const isValid = await this.verifyWebhookSignature(payload, headers);
      if (!isValid) {
        return {
          success: false,
          error: 'Webhook签名验证失败',
        };
      }

      const event = payload;
      console.log(`收到PayPal Webhook事件: ${event.event_type}`);

      // 处理不同类型的事件
      switch (event.event_type) {
        case 'CHECKOUT.ORDER.APPROVED':
          await this.handleOrderApproved(event.resource);
          break;

        case 'PAYMENT.CAPTURE.COMPLETED':
          await this.handleCaptureCompleted(event.resource);
          break;

        case 'PAYMENT.CAPTURE.DENIED':
          await this.handleCaptureDenied(event.resource);
          break;

        case 'CUSTOMER.DISPUTE.CREATED':
          await this.handleDisputeCreated(event.resource);
          break;

        case 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED':
          await this.handlePayoutItemSucceeded(event.resource);
          break;

        case 'PAYMENT.PAYOUTS-ITEM.FAILED':
          await this.handlePayoutItemFailed(event.resource);
          break;

        default:
          console.log(`未处理的PayPal Webhook事件类型: ${event.event_type}`);
      }

      return { success: true };
    } catch (error) {
      console.error('处理PayPal Webhook失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '处理Webhook失败',
      };
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      // 检查现有令牌是否仍然有效
      if (this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt) {
        return this.accessToken;
      }

      // 获取新的访问令牌
      const auth = Buffer.from(`${this.config.clientId}:${this.config.clientSecret}`).toString('base64');
      
      const response = await axios.post(
        `${this.apiClient.defaults.baseURL}/v1/oauth2/token`,
        'grant_type=client_credentials',
        {
          headers: {
            'Authorization': `Basic ${auth}`,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (tokenData.expires_in - 60) * 1000); // 提前60秒过期

      return this.accessToken;
    } catch (error) {
      console.error('获取PayPal访问令牌失败:', error);
      return null;
    }
  }

  /**
   * 验证Webhook签名
   */
  private async verifyWebhookSignature(
    payload: any,
    headers: Record<string, string>
  ): Promise<boolean> {
    try {
      if (!this.config.webhookId) {
        console.warn('未配置PayPal Webhook ID，跳过签名验证');
        return true;
      }

      // 这里应该实现PayPal Webhook签名验证逻辑
      // 由于PayPal的签名验证比较复杂，这里简化处理
      return true;
    } catch (error) {
      console.error('验证PayPal Webhook签名失败:', error);
      return false;
    }
  }

  // Webhook事件处理方法
  private async handleOrderApproved(resource: any): Promise<void> {
    console.log('PayPal订单已批准:', resource.id);
  }

  private async handleCaptureCompleted(resource: any): Promise<void> {
    console.log('PayPal支付已完成:', resource.id);
  }

  private async handleCaptureDenied(resource: any): Promise<void> {
    console.log('PayPal支付被拒绝:', resource.id);
  }

  private async handleDisputeCreated(resource: any): Promise<void> {
    console.log('PayPal争议已创建:', resource.dispute_id);
    
    // 创建争议记录
    await prisma.dispute.create({
      data: {
        id: `paypal_${resource.dispute_id}`,
        amount: parseFloat(resource.disputed_transactions?.[0]?.gross_amount?.value || '0'),
        currency: resource.disputed_transactions?.[0]?.gross_amount?.currency_code || 'USD',
        reason: resource.reason,
        status: resource.status,
        externalId: resource.dispute_id,
        externalData: resource,
        createdBy: 'system',
      },
    });
  }

  private async handlePayoutItemSucceeded(resource: any): Promise<void> {
    console.log('PayPal批量支付项目成功:', resource.payout_item_id);
  }

  private async handlePayoutItemFailed(resource: any): Promise<void> {
    console.log('PayPal批量支付项目失败:', resource.payout_item_id);
  }
}

// 导出单例实例
export const paypalService = new PayPalService();
