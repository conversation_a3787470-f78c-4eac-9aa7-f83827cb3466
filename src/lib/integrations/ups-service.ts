/**
 * UPS物流服务集成
 * 提供UPS运费计算、运单创建、物流跟踪等功能
 */

import axios, { AxiosInstance } from 'axios';
import { prisma } from '@/lib/prisma';

// UPS环境枚举
export enum UPSEnvironment {
  SANDBOX = 'sandbox',
  PRODUCTION = 'production',
}

// UPS服务类型枚举
export enum UPSServiceType {
  UPS_GROUND = '03',
  UPS_3_DAY_SELECT = '12',
  UPS_2ND_DAY_AIR = '02',
  UPS_2ND_DAY_AIR_AM = '59',
  UPS_NEXT_DAY_AIR = '01',
  UPS_NEXT_DAY_AIR_SAVER = '13',
  UPS_NEXT_DAY_AIR_EARLY = '14',
  UPS_STANDARD = '11',
  UPS_WORLDWIDE_EXPRESS = '07',
  UPS_WORLDWIDE_EXPEDITED = '08',
  UPS_WORLDWIDE_SAVER = '65',
}

// UPS配置接口
export interface UPSConfig {
  apiKey: string;
  username: string;
  password: string;
  accountNumber: string;
  environment: UPSEnvironment;
}

// UPS地址接口
export interface UPSAddress {
  name: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postalCode: string;
  countryCode: string;
  state?: string;
  phone?: string;
  email?: string;
}

// UPS包裹接口
export interface UPSPackage {
  weight: number;
  length: number;
  width: number;
  height: number;
  description?: string;
  value?: number;
}

// UPS运费查询请求
export interface UPSRateRequest {
  originAddress: UPSAddress;
  destinationAddress: UPSAddress;
  packages: UPSPackage[];
  serviceType?: UPSServiceType;
  shipDate?: Date;
  currency?: string;
}

// UPS运单创建请求
export interface UPSShipmentRequest {
  originAddress: UPSAddress;
  destinationAddress: UPSAddress;
  packages: UPSPackage[];
  serviceType: UPSServiceType;
  shipDate: Date;
  orderId: string;
  reference?: string;
  specialInstructions?: string;
  insuranceValue?: number;
  signatureRequired?: boolean;
}

/**
 * UPS物流服务类
 */
export class UPSService {
  private config: UPSConfig;
  private apiClient: AxiosInstance;
  private accessToken?: string;
  private tokenExpiresAt?: Date;

  constructor() {
    this.config = {
      apiKey: process.env.UPS_API_KEY!,
      username: process.env.UPS_USERNAME!,
      password: process.env.UPS_PASSWORD!,
      accountNumber: process.env.UPS_ACCOUNT_NUMBER!,
      environment: (process.env.UPS_ENVIRONMENT as UPSEnvironment) || UPSEnvironment.SANDBOX,
    };

    const baseURL = this.config.environment === UPSEnvironment.SANDBOX
      ? 'https://wwwcie.ups.com/api'
      : 'https://onlinetools.ups.com/api';

    this.apiClient = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // 请求拦截器：自动添加访问令牌
    this.apiClient.interceptors.request.use(async (config) => {
      const token = await this.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  /**
   * 计算运费
   */
  async calculateRates(
    request: UPSRateRequest
  ): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        serviceType,
        shipDate = new Date(),
        currency = 'USD',
      } = request;

      // 构建UPS API请求数据
      const rateRequestData = {
        RateRequest: {
          Request: {
            RequestOption: 'Rate',
            TransactionReference: {
              CustomerContext: 'Rate Request',
            },
          },
          Shipment: {
            Shipper: {
              Name: originAddress.name,
              ShipperNumber: this.config.accountNumber,
              Address: this.formatAddress(originAddress),
            },
            ShipTo: {
              Name: destinationAddress.name,
              Address: this.formatAddress(destinationAddress),
            },
            ShipFrom: {
              Name: originAddress.name,
              Address: this.formatAddress(originAddress),
            },
            PaymentInformation: {
              ShipmentCharge: {
                Type: '01', // Transportation
                BillShipper: {
                  AccountNumber: this.config.accountNumber,
                },
              },
            },
            Service: serviceType ? {
              Code: serviceType,
            } : undefined,
            Package: packages.map((pkg, index) => ({
              PackagingType: {
                Code: '02', // Customer Supplied Package
              },
              Dimensions: {
                UnitOfMeasurement: {
                  Code: 'CM',
                },
                Length: pkg.length.toString(),
                Width: pkg.width.toString(),
                Height: pkg.height.toString(),
              },
              PackageWeight: {
                UnitOfMeasurement: {
                  Code: 'KGS',
                },
                Weight: pkg.weight.toString(),
              },
              PackageServiceOptions: pkg.value ? {
                DeclaredValue: {
                  CurrencyCode: currency,
                  MonetaryValue: pkg.value.toString(),
                },
              } : undefined,
            })),
            ShipmentRatingOptions: {
              NegotiatedRatesIndicator: '',
            },
          },
        },
      };

      // 调用UPS API
      const response = await this.apiClient.post('/rating/v1/Rate', rateRequestData);
      const rateData = response.data;

      // 解析响应数据
      const rates = rateData.RateResponse?.RatedShipment?.map((shipment: any) => ({
        serviceType: shipment.Service?.Code,
        serviceName: shipment.Service?.Name || this.getServiceName(shipment.Service?.Code),
        totalPrice: parseFloat(shipment.TotalCharges?.MonetaryValue || '0'),
        currency: shipment.TotalCharges?.CurrencyCode || currency,
        transitTime: parseInt(shipment.GuaranteedDelivery?.BusinessDaysInTransit || '0'),
        deliveryDate: shipment.GuaranteedDelivery?.DeliveryByTime ? 
          new Date(shipment.GuaranteedDelivery.DeliveryByTime) : 
          new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        breakdown: {
          basePrice: parseFloat(shipment.TransportationCharges?.MonetaryValue || '0'),
          fuelSurcharge: parseFloat(shipment.ServiceOptionsCharges?.MonetaryValue || '0'),
          taxes: parseFloat(shipment.TaxCharges?.MonetaryValue || '0'),
          fees: parseFloat(shipment.AccessorialCharges?.MonetaryValue || '0'),
        },
      })) || [];

      return {
        success: true,
        data: Array.isArray(rates) ? rates : [rates],
      };
    } catch (error) {
      console.error('UPS运费计算失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'UPS运费计算失败',
      };
    }
  }

  /**
   * 创建运单
   */
  async createShipment(
    request: UPSShipmentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        serviceType,
        shipDate,
        orderId,
        reference,
        specialInstructions,
        insuranceValue,
        signatureRequired = false,
      } = request;

      // 验证订单
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { customer: true },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 构建UPS运单请求数据
      const shipmentRequestData = {
        ShipmentRequest: {
          Request: {
            RequestOption: 'nonvalidate',
            TransactionReference: {
              CustomerContext: 'Ship Request',
            },
          },
          Shipment: {
            Description: specialInstructions || '商品运输',
            Shipper: {
              Name: originAddress.name,
              AttentionName: originAddress.name,
              CompanyDisplayableName: originAddress.company || originAddress.name,
              ShipperNumber: this.config.accountNumber,
              Address: this.formatAddress(originAddress),
              Phone: {
                Number: originAddress.phone || '',
              },
            },
            ShipTo: {
              Name: destinationAddress.name,
              AttentionName: destinationAddress.name,
              CompanyDisplayableName: destinationAddress.company || destinationAddress.name,
              Address: this.formatAddress(destinationAddress),
              Phone: {
                Number: destinationAddress.phone || '',
              },
            },
            ShipFrom: {
              Name: originAddress.name,
              AttentionName: originAddress.name,
              CompanyDisplayableName: originAddress.company || originAddress.name,
              Address: this.formatAddress(originAddress),
              Phone: {
                Number: originAddress.phone || '',
              },
            },
            PaymentInformation: {
              ShipmentCharge: {
                Type: '01',
                BillShipper: {
                  AccountNumber: this.config.accountNumber,
                },
              },
            },
            Service: {
              Code: serviceType,
            },
            Package: packages.map((pkg, index) => ({
              Description: pkg.description || '商品',
              PackagingType: {
                Code: '02',
              },
              Dimensions: {
                UnitOfMeasurement: {
                  Code: 'CM',
                },
                Length: pkg.length.toString(),
                Width: pkg.width.toString(),
                Height: pkg.height.toString(),
              },
              PackageWeight: {
                UnitOfMeasurement: {
                  Code: 'KGS',
                },
                Weight: pkg.weight.toString(),
              },
              ReferenceNumber: reference ? [
                {
                  Code: '01',
                  Value: reference,
                },
              ] : undefined,
              PackageServiceOptions: {
                ...(insuranceValue && {
                  DeclaredValue: {
                    CurrencyCode: 'USD',
                    MonetaryValue: insuranceValue.toString(),
                  },
                }),
                ...(signatureRequired && {
                  DeliveryConfirmation: {
                    DCISType: '1', // Signature Required
                  },
                }),
              },
            })),
            ShipmentServiceOptions: this.isInternational(originAddress.countryCode, destinationAddress.countryCode) ? {
              InternationalForms: {
                FormType: '01', // Invoice
                InvoiceNumber: order.orderNumber,
                InvoiceDate: order.createdAt.toISOString().split('T')[0],
                PurchaseOrderNumber: reference || order.orderNumber,
                TermsOfShipment: 'CFR',
                ReasonForExport: 'SALE',
                CurrencyCode: 'USD',
                Product: packages.map((pkg, index) => ({
                  Description: pkg.description || '商品',
                  CommodityCode: '999999',
                  PartNumber: `ITEM${index + 1}`,
                  OriginCountryCode: originAddress.countryCode,
                  Unit: {
                    UnitOfMeasurement: {
                      Code: 'PCS',
                    },
                    Value: '1',
                  },
                  UnitPrice: {
                    CurrencyCode: 'USD',
                    MonetaryValue: (pkg.value || 0).toString(),
                  },
                })),
              },
            } : undefined,
          },
          LabelSpecification: {
            LabelImageFormat: {
              Code: 'PDF',
            },
            HTTPUserAgent: 'Mozilla/4.5',
          },
        },
      };

      // 调用UPS API创建运单
      const response = await this.apiClient.post('/shipments/v1/ship', shipmentRequestData);
      const shipmentData = response.data;

      const trackingNumber = shipmentData.ShipmentResponse?.ShipmentResults?.PackageResults?.[0]?.TrackingNumber;
      const labelUrl = shipmentData.ShipmentResponse?.ShipmentResults?.PackageResults?.[0]?.ShippingLabel?.GraphicImage;

      // 保存运单记录
      await prisma.shipment.create({
        data: {
          id: `ups_${trackingNumber}`,
          orderId,
          trackingNumber,
          carrier: 'UPS',
          serviceType,
          status: 'CREATED',
          labelUrl: labelUrl ? `data:application/pdf;base64,${labelUrl}` : '',
          estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 默认7天后
          shippingCost: parseFloat(shipmentData.ShipmentResponse?.ShipmentResults?.ShipmentCharges?.TotalCharges?.MonetaryValue || '0'),
          currency: shipmentData.ShipmentResponse?.ShipmentResults?.ShipmentCharges?.TotalCharges?.CurrencyCode || 'USD',
          externalId: trackingNumber,
          externalData: shipmentData,
          createdBy: 'system',
        },
      });

      // 更新订单状态
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'SHIPPED',
          shippedAt: new Date(),
          trackingNumber,
        },
      });

      return {
        success: true,
        data: {
          trackingNumber,
          shipmentId: trackingNumber,
          labelUrl: labelUrl ? `data:application/pdf;base64,${labelUrl}` : '',
          totalCost: parseFloat(shipmentData.ShipmentResponse?.ShipmentResults?.ShipmentCharges?.TotalCharges?.MonetaryValue || '0'),
          currency: shipmentData.ShipmentResponse?.ShipmentResults?.ShipmentCharges?.TotalCharges?.CurrencyCode || 'USD',
          estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
      };
    } catch (error) {
      console.error('UPS创建运单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'UPS创建运单失败',
      };
    }
  }

  /**
   * 跟踪包裹
   */
  async trackShipment(
    trackingNumber: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 调用UPS跟踪API
      const response = await this.apiClient.get(`/track/v1/details/${trackingNumber}`);
      const trackingData = response.data;

      const trackInfo = trackingData.trackResponse?.shipment?.[0];
      if (!trackInfo) {
        return {
          success: false,
          error: '未找到跟踪信息',
        };
      }

      // 解析跟踪信息
      const events = trackInfo.package?.[0]?.activity?.map((activity: any) => ({
        timestamp: new Date(`${activity.date} ${activity.time}`),
        location: `${activity.location?.address?.city || ''}, ${activity.location?.address?.countryCode || ''}`,
        description: activity.status?.description || '',
        statusCode: activity.status?.code || '',
      })) || [];

      const currentStatus = trackInfo.package?.[0]?.currentStatus;
      const trackingInfo = {
        trackingNumber,
        status: this.mapUPSStatus(currentStatus?.code),
        statusDescription: currentStatus?.description || '',
        estimatedDelivery: trackInfo.deliveryDate?.date ? 
          new Date(trackInfo.deliveryDate.date) : undefined,
        actualDelivery: currentStatus?.code === 'D' && events.length > 0 ? 
          events[0].timestamp : undefined,
        events,
      };

      // 更新本地运单状态
      await prisma.shipment.updateMany({
        where: { trackingNumber },
        data: {
          status: trackingInfo.status.toUpperCase(),
          externalData: trackingData,
          ...(trackingInfo.actualDelivery && { deliveredAt: trackingInfo.actualDelivery }),
        },
      });

      return {
        success: true,
        data: trackingInfo,
      };
    } catch (error) {
      console.error('UPS包裹跟踪失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'UPS包裹跟踪失败',
      };
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      // 检查现有令牌是否仍然有效
      if (this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt) {
        return this.accessToken;
      }

      // 获取新的访问令牌
      const tokenRequestData = {
        grant_type: 'client_credentials',
      };

      const response = await axios.post(
        `${this.apiClient.defaults.baseURL}/security/v1/oauth/token`,
        new URLSearchParams(tokenRequestData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'x-merchant-id': this.config.apiKey,
            'Authorization': `Basic ${Buffer.from(`${this.config.username}:${this.config.password}`).toString('base64')}`,
          },
        }
      );

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (tokenData.expires_in - 60) * 1000); // 提前60秒过期

      return this.accessToken;
    } catch (error) {
      console.error('获取UPS访问令牌失败:', error);
      return null;
    }
  }

  /**
   * 格式化地址
   */
  private formatAddress(address: UPSAddress): any {
    return {
      AddressLine: [address.addressLine1, address.addressLine2].filter(Boolean),
      City: address.city,
      StateProvinceCode: address.state || '',
      PostalCode: address.postalCode,
      CountryCode: address.countryCode,
    };
  }

  /**
   * 判断是否为国际运输
   */
  private isInternational(originCountry: string, destinationCountry: string): boolean {
    return originCountry.toUpperCase() !== destinationCountry.toUpperCase();
  }

  /**
   * 映射UPS状态到本地状态
   */
  private mapUPSStatus(statusCode: string): string {
    switch (statusCode?.toUpperCase()) {
      case 'D':
        return 'delivered';
      case 'I':
      case 'P':
      case 'M':
        return 'transit';
      case 'X':
        return 'failure';
      default:
        return 'unknown';
    }
  }

  /**
   * 获取服务名称
   */
  private getServiceName(serviceCode: string): string {
    const serviceNames: Record<string, string> = {
      '01': 'UPS Next Day Air',
      '02': 'UPS 2nd Day Air',
      '03': 'UPS Ground',
      '07': 'UPS Worldwide Express',
      '08': 'UPS Worldwide Expedited',
      '11': 'UPS Standard',
      '12': 'UPS 3 Day Select',
      '13': 'UPS Next Day Air Saver',
      '14': 'UPS Next Day Air Early',
      '59': 'UPS 2nd Day Air A.M.',
      '65': 'UPS Worldwide Saver',
    };

    return serviceNames[serviceCode] || `UPS Service ${serviceCode}`;
  }
}

// 导出单例实例
export const upsService = new UPSService();
