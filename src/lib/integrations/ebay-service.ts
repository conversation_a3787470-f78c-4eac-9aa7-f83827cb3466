/**
 * eBay电商平台集成服务
 * 提供eBay API集成，实现商品信息同步、订单自动导入、库存实时同步等功能
 */

import axios, { AxiosInstance } from 'axios';
import { prisma } from '@/lib/prisma';

// eBay环境枚举
export enum EBayEnvironment {
  SANDBOX = 'sandbox',
  PRODUCTION = 'production',
}

// eBay站点枚举
export enum EBaySite {
  US = 'EBAY_US',
  UK = 'EBAY_GB',
  DE = 'EBAY_DE',
  FR = 'EBAY_FR',
  IT = 'EBAY_IT',
  ES = 'EBAY_ES',
  CA = 'EBAY_CA',
  AU = 'EBAY_AU',
  JP = 'EBAY_JP',
}

// eBay订单状态枚举
export enum EBayOrderStatus {
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
  DISPUTED = 'DISPUTED',
  IN_PROCESS = 'IN_PROCESS',
}

// eBay配置接口
export interface EBayConfig {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  environment: EBayEnvironment;
  siteId: EBaySite;
}

// eBay商品同步请求接口
export interface EBayProductSyncRequest {
  itemId?: string;
  sku?: string;
  categoryId?: string;
  limit?: number;
}

// eBay订单同步请求接口
export interface EBayOrderSyncRequest {
  orderIds?: string[];
  creationDateFrom?: Date;
  creationDateTo?: Date;
  orderStatus?: EBayOrderStatus[];
  limit?: number;
}

// eBay库存更新请求接口
export interface EBayInventoryUpdateRequest {
  sku: string;
  quantity: number;
  price?: number;
}

// eBay商品信息接口
export interface EBayProduct {
  itemId: string;
  sku: string;
  title: string;
  description?: string;
  categoryId: string;
  categoryName: string;
  price: number;
  currency: string;
  quantity: number;
  condition: string;
  images: string[];
  specifications: Record<string, any>;
  shippingOptions: any[];
  returnPolicy: any;
  listingStatus: string;
  listingType: string;
  startTime: Date;
  endTime: Date;
}

// eBay订单信息接口
export interface EBayOrder {
  orderId: string;
  legacyOrderId?: string;
  orderFulfillmentStatus: string;
  orderPaymentStatus: string;
  sellerId: string;
  buyerId: string;
  creationDate: Date;
  lastModifiedDate: Date;
  orderTotal: {
    value: string;
    currency: string;
  };
  salesRecordReference?: string;
  totalFeeBasisAmount?: {
    value: string;
    currency: string;
  };
  totalMarketplaceFee?: {
    value: string;
    currency: string;
  };
  lineItems: EBayOrderLineItem[];
  fulfillmentStartInstructions?: any[];
  fulfillmentHrefs?: string[];
  paymentSummary?: any;
  shippingAddress?: any;
  billingAddress?: any;
  buyer?: any;
  cancelStatus?: any;
  ebayCollectAndRemitTax?: boolean;
  ebayCollectAndRemitTaxes?: any;
  ebayInternationalShipping?: boolean;
  ebayVaultProgram?: boolean;
  extendedOrderId?: string;
  fulfillmentInstructions?: any[];
  monetaryTransactions?: any[];
  pickupOptions?: any[];
  program?: any;
  salesRecordReference2?: string;
}

export interface EBayOrderLineItem {
  itemId: string;
  legacyItemId?: string;
  legacyVariationId?: string;
  lineItemId: string;
  quantity: number;
  lineItemCost: {
    value: string;
    currency: string;
  };
  lineItemFulfillmentStatus: string;
  lineItemFulfillmentInstructions?: any;
  title: string;
  sku?: string;
  soldFormat: string;
  listingMarketplaceId: string;
  purchaseMarketplaceId: string;
  variationAspects?: any[];
  properties?: any;
  ebayCollectAndRemitTax?: boolean;
  ebayCollectAndRemitTaxes?: any;
  giftDetails?: any;
  lineItemFulfillmentInstructions2?: any;
  linkedOrderLineItems?: any[];
  listingType?: string;
  promotionalDiscounts?: any[];
  refunds?: any[];
  returns?: any[];
  shippingDetail?: any;
  taxes?: any[];
  total?: {
    value: string;
    currency: string;
  };
}

/**
 * eBay电商平台服务类
 */
export class EBayService {
  private config: EBayConfig;
  private apiClient: AxiosInstance;
  private accessToken?: string;
  private tokenExpiresAt?: Date;

  constructor() {
    this.config = {
      clientId: process.env.EBAY_CLIENT_ID!,
      clientSecret: process.env.EBAY_CLIENT_SECRET!,
      refreshToken: process.env.EBAY_REFRESH_TOKEN!,
      environment: (process.env.EBAY_ENVIRONMENT as EBayEnvironment) || EBayEnvironment.SANDBOX,
      siteId: (process.env.EBAY_SITE_ID as EBaySite) || EBaySite.US,
    };

    const baseURL = this.config.environment === EBayEnvironment.SANDBOX
      ? 'https://api.sandbox.ebay.com'
      : 'https://api.ebay.com';

    this.apiClient = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // 请求拦截器：自动添加访问令牌
    this.apiClient.interceptors.request.use(async (config) => {
      const token = await this.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  /**
   * 同步eBay商品信息
   */
  async syncProducts(
    request: EBayProductSyncRequest = {}
  ): Promise<{ success: boolean; data?: EBayProduct[]; error?: string }> {
    try {
      const {
        itemId,
        sku,
        categoryId,
        limit = 100,
      } = request;

      let products: EBayProduct[] = [];

      if (itemId) {
        // 通过Item ID获取单个商品
        const productResult = await this.getProductByItemId(itemId);
        if (productResult.success && productResult.data) {
          products.push(productResult.data);
        }
      } else {
        // 获取库存中的所有商品
        const productsResult = await this.getInventoryItems(sku, limit);
        if (productsResult.success && productsResult.data) {
          products = productsResult.data;
        }
      }

      // 同步到本地数据库
      for (const product of products) {
        await this.syncProductToDatabase(product);
      }

      return {
        success: true,
        data: products,
      };
    } catch (error) {
      console.error('eBay商品同步失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'eBay商品同步失败',
      };
    }
  }

  /**
   * 同步eBay订单
   */
  async syncOrders(
    request: EBayOrderSyncRequest = {}
  ): Promise<{ success: boolean; data?: EBayOrder[]; error?: string }> {
    try {
      const {
        orderIds,
        creationDateFrom = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 默认7天前
        creationDateTo = new Date(),
        orderStatus = [EBayOrderStatus.ACTIVE, EBayOrderStatus.IN_PROCESS],
        limit = 100,
      } = request;

      // 构建查询参数
      const params = new URLSearchParams({
        limit: limit.toString(),
      });

      if (orderIds && orderIds.length > 0) {
        params.append('orderIds', orderIds.join(','));
      } else {
        params.append('filter', `creationdate:[${creationDateFrom.toISOString()}..${creationDateTo.toISOString()}]`);
        if (orderStatus.length > 0) {
          params.append('filter', `orderfulfillmentstatus:{${orderStatus.join('|')}}`);
        }
      }

      // 调用eBay API获取订单
      const response = await this.apiClient.get(`/sell/fulfillment/v1/order?${params}`);
      const ordersData = response.data;

      const orders: EBayOrder[] = ordersData.orders || [];

      // 同步到本地数据库
      for (const order of orders) {
        await this.syncOrderToDatabase(order);
      }

      return {
        success: true,
        data: orders,
      };
    } catch (error) {
      console.error('eBay订单同步失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'eBay订单同步失败',
      };
    }
  }

  /**
   * 更新eBay库存
   */
  async updateInventory(
    request: EBayInventoryUpdateRequest
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const { sku, quantity, price } = request;

      // 构建库存更新数据
      const inventoryUpdateData: any = {
        availability: {
          shipToLocationAvailability: {
            quantity: quantity,
          },
        },
      };

      if (price !== undefined) {
        inventoryUpdateData.product = {
          aspects: {},
        };
      }

      // 调用eBay API更新库存
      const response = await this.apiClient.put(
        `/sell/inventory/v1/inventory_item/${sku}`,
        inventoryUpdateData
      );

      // 如果需要更新价格，调用offer API
      if (price !== undefined) {
        await this.updateOfferPrice(sku, price);
      }

      return { success: true };
    } catch (error) {
      console.error('eBay库存更新失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'eBay库存更新失败',
      };
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      // 检查现有令牌是否仍然有效
      if (this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt) {
        return this.accessToken;
      }

      // 使用刷新令牌获取新的访问令牌
      const tokenRequestData = {
        grant_type: 'refresh_token',
        refresh_token: this.config.refreshToken,
        scope: 'https://api.ebay.com/oauth/api_scope https://api.ebay.com/oauth/api_scope/sell.marketing.readonly https://api.ebay.com/oauth/api_scope/sell.marketing https://api.ebay.com/oauth/api_scope/sell.inventory.readonly https://api.ebay.com/oauth/api_scope/sell.inventory https://api.ebay.com/oauth/api_scope/sell.account.readonly https://api.ebay.com/oauth/api_scope/sell.account https://api.ebay.com/oauth/api_scope/sell.fulfillment.readonly https://api.ebay.com/oauth/api_scope/sell.fulfillment https://api.ebay.com/oauth/api_scope/sell.analytics.readonly',
      };

      const authHeader = Buffer.from(`${this.config.clientId}:${this.config.clientSecret}`).toString('base64');

      const response = await axios.post(
        `${this.apiClient.defaults.baseURL}/identity/v1/oauth2/token`,
        new URLSearchParams(tokenRequestData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': `Basic ${authHeader}`,
          },
        }
      );

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (tokenData.expires_in - 60) * 1000); // 提前60秒过期

      return this.accessToken;
    } catch (error) {
      console.error('获取eBay访问令牌失败:', error);
      return null;
    }
  }

  /**
   * 通过Item ID获取商品信息
   */
  private async getProductByItemId(
    itemId: string
  ): Promise<{ success: boolean; data?: EBayProduct; error?: string }> {
    try {
      const response = await this.apiClient.get(`/commerce/catalog/v1_beta/product/${itemId}`);
      const productData = response.data;

      const product = this.transformProductData(productData);

      return {
        success: true,
        data: product,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取商品信息失败',
      };
    }
  }

  /**
   * 获取库存商品列表
   */
  private async getInventoryItems(
    sku?: string,
    limit: number = 100
  ): Promise<{ success: boolean; data?: EBayProduct[]; error?: string }> {
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
      });

      if (sku) {
        params.append('sku', sku);
      }

      const response = await this.apiClient.get(`/sell/inventory/v1/inventory_item?${params}`);
      const inventoryData = response.data;

      const products = (inventoryData.inventoryItems || []).map((item: any) => 
        this.transformInventoryItemData(item)
      );

      return {
        success: true,
        data: products,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取库存商品失败',
      };
    }
  }

  /**
   * 更新Offer价格
   */
  private async updateOfferPrice(sku: string, price: number): Promise<void> {
    try {
      // 首先获取现有的offer
      const offersResponse = await this.apiClient.get(`/sell/inventory/v1/offer?sku=${sku}`);
      const offers = offersResponse.data.offers || [];

      for (const offer of offers) {
        const updateData = {
          pricingSummary: {
            price: {
              value: price.toString(),
              currency: offer.pricingSummary?.price?.currency || 'USD',
            },
          },
        };

        await this.apiClient.put(`/sell/inventory/v1/offer/${offer.offerId}`, updateData);
      }
    } catch (error) {
      console.error('更新eBay Offer价格失败:', error);
    }
  }

  /**
   * 转换商品数据格式
   */
  private transformProductData(productData: any): EBayProduct {
    return {
      itemId: productData.productId || '',
      sku: productData.sku || '',
      title: productData.title || '',
      description: productData.description || '',
      categoryId: productData.primaryCategoryId || '',
      categoryName: productData.primaryCategoryName || '',
      price: parseFloat(productData.price?.value || '0'),
      currency: productData.price?.currency || 'USD',
      quantity: parseInt(productData.quantity || '0'),
      condition: productData.condition || 'NEW',
      images: productData.images?.map((img: any) => img.imageUrl) || [],
      specifications: productData.specifications || {},
      shippingOptions: productData.shippingOptions || [],
      returnPolicy: productData.returnPolicy || {},
      listingStatus: productData.status || 'ACTIVE',
      listingType: productData.listingType || 'FIXED_PRICE',
      startTime: new Date(productData.startTime || Date.now()),
      endTime: new Date(productData.endTime || Date.now() + 30 * 24 * 60 * 60 * 1000),
    };
  }

  /**
   * 转换库存商品数据格式
   */
  private transformInventoryItemData(itemData: any): EBayProduct {
    return {
      itemId: itemData.sku || '',
      sku: itemData.sku || '',
      title: itemData.product?.title || '',
      description: itemData.product?.description || '',
      categoryId: itemData.product?.primaryCategoryId || '',
      categoryName: '',
      price: parseFloat(itemData.product?.price?.value || '0'),
      currency: itemData.product?.price?.currency || 'USD',
      quantity: parseInt(itemData.availability?.shipToLocationAvailability?.quantity || '0'),
      condition: itemData.condition || 'NEW',
      images: itemData.product?.imageUrls || [],
      specifications: itemData.product?.aspects || {},
      shippingOptions: [],
      returnPolicy: {},
      listingStatus: 'ACTIVE',
      listingType: 'FIXED_PRICE',
      startTime: new Date(),
      endTime: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    };
  }

  /**
   * 同步商品到数据库
   */
  private async syncProductToDatabase(product: EBayProduct): Promise<void> {
    try {
      // 检查商品是否已存在
      const existingProduct = await prisma.product.findFirst({
        where: {
          OR: [
            { sku: product.sku },
            { externalId: product.itemId },
          ],
        },
      });

      if (existingProduct) {
        // 更新现有商品
        await prisma.product.update({
          where: { id: existingProduct.id },
          data: {
            name: product.title,
            description: product.description,
            basePrice: product.price,
            externalData: product,
            updatedAt: new Date(),
          },
        });
      } else {
        // 创建新商品
        await prisma.product.create({
          data: {
            name: product.title,
            sku: product.sku,
            description: product.description,
            basePrice: product.price,
            costPrice: product.price * 0.7, // 假设成本价为售价的70%
            status: 'ACTIVE',
            externalId: product.itemId,
            externalSource: 'EBAY',
            externalData: product,
            createdBy: 'system',
          },
        });
      }
    } catch (error) {
      console.error('同步eBay商品到数据库失败:', error);
    }
  }

  /**
   * 同步订单到数据库
   */
  private async syncOrderToDatabase(order: EBayOrder): Promise<void> {
    try {
      // 检查订单是否已存在
      const existingOrder = await prisma.order.findFirst({
        where: { externalId: order.orderId },
      });

      if (existingOrder) {
        // 更新现有订单
        await prisma.order.update({
          where: { id: existingOrder.id },
          data: {
            status: this.mapEBayOrderStatus(order.orderFulfillmentStatus),
            externalData: order,
            updatedAt: new Date(),
          },
        });
      } else {
        // 创建新订单
        const totalAmount = parseFloat(order.orderTotal.value);
        
        await prisma.order.create({
          data: {
            orderNumber: order.orderId,
            customerId: 'ebay-customer', // 需要创建或关联eBay客户
            totalAmount,
            subtotal: totalAmount,
            taxAmount: 0,
            shippingAmount: 0,
            currency: order.orderTotal.currency,
            status: this.mapEBayOrderStatus(order.orderFulfillmentStatus),
            paymentStatus: order.orderPaymentStatus === 'PAID' ? 'PAID' : 'PENDING',
            externalId: order.orderId,
            externalSource: 'EBAY',
            externalData: order,
            createdBy: 'system',
          },
        });
      }
    } catch (error) {
      console.error('同步eBay订单到数据库失败:', error);
    }
  }

  /**
   * 映射eBay订单状态
   */
  private mapEBayOrderStatus(ebayStatus: string): string {
    const statusMap: Record<string, string> = {
      'NOT_STARTED': 'PENDING',
      'IN_PROGRESS': 'PROCESSING',
      'FULFILLED': 'SHIPPED',
      'CANCELLED': 'CANCELLED',
    };

    return statusMap[ebayStatus] || 'PENDING';
  }
}

// 导出单例实例
export const ebayService = new EBayService();
