/**
 * FedEx物流服务集成
 * 提供FedEx运费计算、运单创建、物流跟踪等功能
 */

import axios, { AxiosInstance } from 'axios';
import { prisma } from '@/lib/prisma';

// FedEx环境枚举
export enum FedExEnvironment {
  SANDBOX = 'sandbox',
  PRODUCTION = 'production',
}

// FedEx服务类型枚举
export enum FedExServiceType {
  FEDEX_GROUND = 'FEDEX_GROUND',
  FEDEX_EXPRESS_SAVER = 'FEDEX_EXPRESS_SAVER',
  FEDEX_2_DAY = 'FEDEX_2_DAY',
  FEDEX_2_DAY_AM = 'FEDEX_2_DAY_AM',
  STANDARD_OVERNIGHT = 'STANDARD_OVERNIGHT',
  PRIORITY_OVERNIGHT = 'PRIORITY_OVERNIGHT',
  FIRST_OVERNIGHT = 'FIRST_OVERNIGHT',
  INTERNATIONAL_ECONOMY = 'INTERNATIONAL_ECONOMY',
  INTERNATIONAL_PRIORITY = 'INTERNATIONAL_PRIORITY',
  INTERNATIONAL_FIRST = 'INTERNATIONAL_FIRST',
}

// FedEx配置接口
export interface FedExConfig {
  apiKey: string;
  secretKey: string;
  accountNumber: string;
  meterNumber: string;
  environment: FedExEnvironment;
}

// FedEx地址接口
export interface FedExAddress {
  name: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postalCode: string;
  countryCode: string;
  state?: string;
  phone?: string;
  email?: string;
}

// FedEx包裹接口
export interface FedExPackage {
  weight: number;
  length: number;
  width: number;
  height: number;
  description?: string;
  value?: number;
}

// FedEx运费查询请求
export interface FedExRateRequest {
  originAddress: FedExAddress;
  destinationAddress: FedExAddress;
  packages: FedExPackage[];
  serviceType?: FedExServiceType;
  shipDate?: Date;
  currency?: string;
}

// FedEx运单创建请求
export interface FedExShipmentRequest {
  originAddress: FedExAddress;
  destinationAddress: FedExAddress;
  packages: FedExPackage[];
  serviceType: FedExServiceType;
  shipDate: Date;
  orderId: string;
  reference?: string;
  specialInstructions?: string;
  insuranceValue?: number;
  signatureRequired?: boolean;
}

/**
 * FedEx物流服务类
 */
export class FedExService {
  private config: FedExConfig;
  private apiClient: AxiosInstance;
  private accessToken?: string;
  private tokenExpiresAt?: Date;

  constructor() {
    this.config = {
      apiKey: process.env.FEDEX_API_KEY!,
      secretKey: process.env.FEDEX_SECRET_KEY!,
      accountNumber: process.env.FEDEX_ACCOUNT_NUMBER!,
      meterNumber: process.env.FEDEX_METER_NUMBER!,
      environment: (process.env.FEDEX_ENVIRONMENT as FedExEnvironment) || FedExEnvironment.SANDBOX,
    };

    const baseURL = this.config.environment === FedExEnvironment.SANDBOX
      ? 'https://apis-sandbox.fedex.com'
      : 'https://apis.fedex.com';

    this.apiClient = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // 请求拦截器：自动添加访问令牌
    this.apiClient.interceptors.request.use(async (config) => {
      const token = await this.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  /**
   * 计算运费
   */
  async calculateRates(
    request: FedExRateRequest
  ): Promise<{ success: boolean; data?: any[]; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        serviceType,
        shipDate = new Date(),
        currency = 'USD',
      } = request;

      // 构建FedEx API请求数据
      const rateRequestData = {
        accountNumber: {
          value: this.config.accountNumber,
        },
        requestedShipment: {
          shipper: {
            contact: {
              personName: originAddress.name,
              phoneNumber: originAddress.phone || '',
              companyName: originAddress.company || '',
            },
            address: this.formatAddress(originAddress),
          },
          recipients: [
            {
              contact: {
                personName: destinationAddress.name,
                phoneNumber: destinationAddress.phone || '',
                companyName: destinationAddress.company || '',
              },
              address: this.formatAddress(destinationAddress),
            },
          ],
          shipDatestamp: shipDate.toISOString().split('T')[0],
          serviceType: serviceType || undefined,
          packagingType: 'YOUR_PACKAGING',
          pickupType: 'USE_SCHEDULED_PICKUP',
          requestedPackageLineItems: packages.map((pkg, index) => ({
            sequenceNumber: index + 1,
            groupPackageCount: 1,
            weight: {
              units: 'KG',
              value: pkg.weight,
            },
            dimensions: {
              length: pkg.length,
              width: pkg.width,
              height: pkg.height,
              units: 'CM',
            },
            declaredValue: pkg.value ? {
              amount: pkg.value,
              currency: currency,
            } : undefined,
          })),
          customsClearanceDetail: this.isInternational(originAddress.countryCode, destinationAddress.countryCode) ? {
            dutiesPayment: {
              paymentType: 'SENDER',
            },
            commodities: packages.map((pkg, index) => ({
              numberOfPieces: 1,
              description: pkg.description || '商品',
              countryOfManufacture: originAddress.countryCode,
              weight: {
                units: 'KG',
                value: pkg.weight,
              },
              quantity: 1,
              quantityUnits: 'PCS',
              unitPrice: {
                amount: pkg.value || 0,
                currency: currency,
              },
              customsValue: {
                amount: pkg.value || 0,
                currency: currency,
              },
            })),
          } : undefined,
        },
        carrierCodes: ['FDXE', 'FDXG'],
        rateRequestControlParameters: {
          returnTransitTimes: true,
          servicesNeededOnRateFailure: true,
          variableOptions: 'FREIGHT_GUARANTEE',
          rateSortOrder: 'SERVICENAMETRADITIONAL',
        },
      };

      // 调用FedEx API
      const response = await this.apiClient.post('/rate/v1/rates/quotes', rateRequestData);
      const rateData = response.data;

      // 解析响应数据
      const rates = rateData.output?.rateReplyDetails?.map((detail: any) => ({
        serviceType: detail.serviceType,
        serviceName: detail.serviceName,
        totalPrice: parseFloat(detail.ratedShipmentDetails?.[0]?.totalNetCharge || '0'),
        currency: detail.ratedShipmentDetails?.[0]?.currency || currency,
        transitTime: parseInt(detail.operationalDetail?.transitTime || '0'),
        deliveryDate: new Date(detail.operationalDetail?.deliveryDate || Date.now()),
        breakdown: {
          basePrice: parseFloat(detail.ratedShipmentDetails?.[0]?.shipmentRateDetail?.totalBaseCharge || '0'),
          fuelSurcharge: parseFloat(detail.ratedShipmentDetails?.[0]?.shipmentRateDetail?.totalSurcharges || '0'),
          taxes: parseFloat(detail.ratedShipmentDetails?.[0]?.shipmentRateDetail?.totalTaxes || '0'),
          fees: parseFloat(detail.ratedShipmentDetails?.[0]?.shipmentRateDetail?.totalFreightDiscounts || '0'),
        },
      })) || [];

      return {
        success: true,
        data: rates,
      };
    } catch (error) {
      console.error('FedEx运费计算失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'FedEx运费计算失败',
      };
    }
  }

  /**
   * 创建运单
   */
  async createShipment(
    request: FedExShipmentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        serviceType,
        shipDate,
        orderId,
        reference,
        specialInstructions,
        insuranceValue,
        signatureRequired = false,
      } = request;

      // 验证订单
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { customer: true },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 构建FedEx运单请求数据
      const shipmentRequestData = {
        labelResponseOptions: 'URL_ONLY',
        requestedShipment: {
          shipper: {
            contact: {
              personName: originAddress.name,
              phoneNumber: originAddress.phone || '',
              companyName: originAddress.company || '',
            },
            address: this.formatAddress(originAddress),
          },
          recipients: [
            {
              contact: {
                personName: destinationAddress.name,
                phoneNumber: destinationAddress.phone || '',
                companyName: destinationAddress.company || '',
              },
              address: this.formatAddress(destinationAddress),
            },
          ],
          shipDatestamp: shipDate.toISOString().split('T')[0],
          serviceType: serviceType,
          packagingType: 'YOUR_PACKAGING',
          pickupType: 'USE_SCHEDULED_PICKUP',
          shippingChargesPayment: {
            paymentType: 'SENDER',
            payor: {
              responsibleParty: {
                accountNumber: {
                  value: this.config.accountNumber,
                },
              },
            },
          },
          labelSpecification: {
            imageType: 'PDF',
            labelStockType: 'PAPER_85X11_TOP_HALF_LABEL',
          },
          requestedPackageLineItems: packages.map((pkg, index) => ({
            sequenceNumber: index + 1,
            weight: {
              units: 'KG',
              value: pkg.weight,
            },
            dimensions: {
              length: pkg.length,
              width: pkg.width,
              height: pkg.height,
              units: 'CM',
            },
            customerReferences: reference ? [
              {
                customerReferenceType: 'CUSTOMER_REFERENCE',
                value: reference,
              },
            ] : undefined,
            specialServicesRequested: {
              specialServiceTypes: [
                ...(signatureRequired ? ['SIGNATURE_OPTION'] : []),
                ...(insuranceValue ? ['DECLARED_VALUE'] : []),
              ],
              signatureOptionDetail: signatureRequired ? {
                optionType: 'ADULT',
              } : undefined,
              declaredValueDetail: insuranceValue ? {
                declaredValue: {
                  amount: insuranceValue,
                  currency: 'USD',
                },
              } : undefined,
            },
          })),
          customsClearanceDetail: this.isInternational(originAddress.countryCode, destinationAddress.countryCode) ? {
            dutiesPayment: {
              paymentType: 'SENDER',
              payor: {
                responsibleParty: {
                  accountNumber: {
                    value: this.config.accountNumber,
                  },
                },
              },
            },
            commodities: packages.map((pkg, index) => ({
              numberOfPieces: 1,
              description: pkg.description || '商品',
              countryOfManufacture: originAddress.countryCode,
              weight: {
                units: 'KG',
                value: pkg.weight,
              },
              quantity: 1,
              quantityUnits: 'PCS',
              unitPrice: {
                amount: pkg.value || 0,
                currency: 'USD',
              },
              customsValue: {
                amount: pkg.value || 0,
                currency: 'USD',
              },
            })),
          } : undefined,
        },
        accountNumber: {
          value: this.config.accountNumber,
        },
      };

      // 调用FedEx API创建运单
      const response = await this.apiClient.post('/ship/v1/shipments', shipmentRequestData);
      const shipmentData = response.data;

      const trackingNumber = shipmentData.output?.transactionShipments?.[0]?.masterTrackingNumber;
      const labelUrl = shipmentData.output?.transactionShipments?.[0]?.pieceResponses?.[0]?.packageDocuments?.[0]?.url;

      // 保存运单记录
      await prisma.shipment.create({
        data: {
          id: `fedex_${trackingNumber}`,
          orderId,
          trackingNumber,
          carrier: 'FEDEX',
          serviceType,
          status: 'CREATED',
          labelUrl,
          estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 默认7天后
          shippingCost: parseFloat(shipmentData.output?.transactionShipments?.[0]?.shipmentRating?.totalNetCharge || '0'),
          currency: shipmentData.output?.transactionShipments?.[0]?.shipmentRating?.currency || 'USD',
          externalId: trackingNumber,
          externalData: shipmentData,
          createdBy: 'system',
        },
      });

      // 更新订单状态
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'SHIPPED',
          shippedAt: new Date(),
          trackingNumber,
        },
      });

      return {
        success: true,
        data: {
          trackingNumber,
          shipmentId: trackingNumber,
          labelUrl,
          totalCost: parseFloat(shipmentData.output?.transactionShipments?.[0]?.shipmentRating?.totalNetCharge || '0'),
          currency: shipmentData.output?.transactionShipments?.[0]?.shipmentRating?.currency || 'USD',
          estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
      };
    } catch (error) {
      console.error('FedEx创建运单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'FedEx创建运单失败',
      };
    }
  }

  /**
   * 跟踪包裹
   */
  async trackShipment(
    trackingNumber: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 调用FedEx跟踪API
      const trackingRequestData = {
        includeDetailedScans: true,
        trackingInfo: [
          {
            trackingNumberInfo: {
              trackingNumber: trackingNumber,
            },
          },
        ],
      };

      const response = await this.apiClient.post('/track/v1/trackingnumbers', trackingRequestData);
      const trackingData = response.data;

      const trackInfo = trackingData.output?.completeTrackResults?.[0]?.trackResults?.[0];
      if (!trackInfo) {
        return {
          success: false,
          error: '未找到跟踪信息',
        };
      }

      // 解析跟踪信息
      const events = trackInfo.scanEvents?.map((event: any) => ({
        timestamp: new Date(event.date),
        location: `${event.scanLocation?.city || ''}, ${event.scanLocation?.countryCode || ''}`,
        description: event.eventDescription,
        statusCode: event.eventType,
      })) || [];

      const trackingInfo = {
        trackingNumber,
        status: this.mapFedExStatus(trackInfo.latestStatusDetail?.code),
        statusDescription: trackInfo.latestStatusDetail?.description || '',
        estimatedDelivery: trackInfo.estimatedDeliveryTimeWindow?.window?.begins ? 
          new Date(trackInfo.estimatedDeliveryTimeWindow.window.begins) : undefined,
        actualDelivery: trackInfo.actualDeliveryTimestamp ? 
          new Date(trackInfo.actualDeliveryTimestamp) : undefined,
        events,
      };

      // 更新本地运单状态
      await prisma.shipment.updateMany({
        where: { trackingNumber },
        data: {
          status: trackingInfo.status.toUpperCase(),
          externalData: trackingData,
          ...(trackingInfo.actualDelivery && { deliveredAt: trackingInfo.actualDelivery }),
        },
      });

      return {
        success: true,
        data: trackingInfo,
      };
    } catch (error) {
      console.error('FedEx包裹跟踪失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'FedEx包裹跟踪失败',
      };
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      // 检查现有令牌是否仍然有效
      if (this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt) {
        return this.accessToken;
      }

      // 获取新的访问令牌
      const tokenRequestData = {
        grant_type: 'client_credentials',
        client_id: this.config.apiKey,
        client_secret: this.config.secretKey,
      };

      const response = await axios.post(
        `${this.apiClient.defaults.baseURL}/oauth/token`,
        new URLSearchParams(tokenRequestData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (tokenData.expires_in - 60) * 1000); // 提前60秒过期

      return this.accessToken;
    } catch (error) {
      console.error('获取FedEx访问令牌失败:', error);
      return null;
    }
  }

  /**
   * 格式化地址
   */
  private formatAddress(address: FedExAddress): any {
    return {
      streetLines: [address.addressLine1, address.addressLine2].filter(Boolean),
      city: address.city,
      stateOrProvinceCode: address.state || '',
      postalCode: address.postalCode,
      countryCode: address.countryCode,
    };
  }

  /**
   * 判断是否为国际运输
   */
  private isInternational(originCountry: string, destinationCountry: string): boolean {
    return originCountry.toUpperCase() !== destinationCountry.toUpperCase();
  }

  /**
   * 映射FedEx状态到本地状态
   */
  private mapFedExStatus(statusCode: string): string {
    const code = statusCode?.toLowerCase() || '';
    
    if (code.includes('delivered')) {
      return 'delivered';
    } else if (code.includes('transit') || code.includes('pickup') || code.includes('departed')) {
      return 'transit';
    } else if (code.includes('exception') || code.includes('delay')) {
      return 'failure';
    } else {
      return 'unknown';
    }
  }
}

// 导出单例实例
export const fedexService = new FedExService();
