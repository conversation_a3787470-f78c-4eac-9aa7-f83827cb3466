/**
 * Stripe支付网关集成服务
 * 提供信用卡支付、数字钱包支付、订阅支付、Webhook处理等功能
 */

import Stripe from 'stripe';
import { prisma } from '@/lib/prisma';
import { financialService } from '@/lib/services/financial-service';

// Stripe支付方式枚举
export enum StripePaymentMethod {
  CARD = 'card',
  ALIPAY = 'alipay',
  WECHAT_PAY = 'wechat_pay',
  APPLE_PAY = 'apple_pay',
  GOOGLE_PAY = 'google_pay',
  SEPA_DEBIT = 'sepa_debit',
  BANCONTACT = 'bancontact',
  IDEAL = 'ideal',
}

// 支付意图状态枚举
export enum PaymentIntentStatus {
  REQUIRES_PAYMENT_METHOD = 'requires_payment_method',
  REQUIRES_CONFIRMATION = 'requires_confirmation',
  REQUIRES_ACTION = 'requires_action',
  PROCESSING = 'processing',
  REQUIRES_CAPTURE = 'requires_capture',
  CANCELED = 'canceled',
  SUCCEEDED = 'succeeded',
}

// Stripe配置接口
export interface StripeConfig {
  secretKey: string;
  publishableKey: string;
  webhookSecret: string;
  apiVersion: string;
}

// 支付请求接口
export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  orderId: string;
  customerId?: string;
  paymentMethodTypes?: StripePaymentMethod[];
  captureMethod?: 'automatic' | 'manual';
  confirmationMethod?: 'automatic' | 'manual';
  returnUrl?: string;
  metadata?: Record<string, string>;
  description?: string;
}

// 订阅创建请求接口
export interface CreateSubscriptionRequest {
  customerId: string;
  priceId: string;
  paymentMethodId?: string;
  trialPeriodDays?: number;
  metadata?: Record<string, string>;
  couponId?: string;
}

// 退款请求接口
export interface CreateRefundRequest {
  paymentIntentId: string;
  amount?: number;
  reason?: 'duplicate' | 'fraudulent' | 'requested_by_customer';
  metadata?: Record<string, string>;
}

/**
 * Stripe支付服务类
 */
export class StripeService {
  private stripe: Stripe;
  private config: StripeConfig;

  constructor() {
    this.config = {
      secretKey: process.env.STRIPE_SECRET_KEY!,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY!,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
      apiVersion: '2023-10-16',
    };

    this.stripe = new Stripe(this.config.secretKey, {
      apiVersion: this.config.apiVersion as any,
      typescript: true,
    });
  }

  /**
   * 创建支付意图
   */
  async createPaymentIntent(
    request: CreatePaymentIntentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const {
        amount,
        currency,
        orderId,
        customerId,
        paymentMethodTypes = [StripePaymentMethod.CARD],
        captureMethod = 'automatic',
        confirmationMethod = 'automatic',
        returnUrl,
        metadata = {},
        description,
      } = request;

      // 验证订单
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { customer: true },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 获取或创建Stripe客户
      let stripeCustomerId = customerId;
      if (!stripeCustomerId && order.customer) {
        const stripeCustomer = await this.getOrCreateStripeCustomer(order.customer);
        stripeCustomerId = stripeCustomer.id;
      }

      // 创建支付意图
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Stripe使用分为单位
        currency: currency.toLowerCase(),
        customer: stripeCustomerId,
        payment_method_types: paymentMethodTypes,
        capture_method: captureMethod,
        confirmation_method: confirmationMethod,
        return_url: returnUrl,
        description: description || `订单 ${order.orderNumber} 的支付`,
        metadata: {
          ...metadata,
          orderId,
          orderNumber: order.orderNumber,
        },
      });

      // 保存支付记录
      await prisma.payment.create({
        data: {
          id: `stripe_${paymentIntent.id}`,
          orderId,
          customerId: order.customerId,
          amount,
          currency,
          paymentMethod: 'STRIPE',
          status: 'PENDING',
          externalId: paymentIntent.id,
          externalData: paymentIntent,
          createdBy: 'system',
        },
      });

      return {
        success: true,
        data: {
          paymentIntentId: paymentIntent.id,
          clientSecret: paymentIntent.client_secret,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency,
        },
      };
    } catch (error) {
      console.error('创建Stripe支付意图失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建支付意图失败',
      };
    }
  }

  /**
   * 确认支付意图
   */
  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId?: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const confirmParams: any = {};
      
      if (paymentMethodId) {
        confirmParams.payment_method = paymentMethodId;
      }

      const paymentIntent = await this.stripe.paymentIntents.confirm(
        paymentIntentId,
        confirmParams
      );

      return {
        success: true,
        data: {
          paymentIntentId: paymentIntent.id,
          status: paymentIntent.status,
          clientSecret: paymentIntent.client_secret,
        },
      };
    } catch (error) {
      console.error('确认Stripe支付意图失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '确认支付意图失败',
      };
    }
  }

  /**
   * 创建订阅
   */
  async createSubscription(
    request: CreateSubscriptionRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const {
        customerId,
        priceId,
        paymentMethodId,
        trialPeriodDays,
        metadata = {},
        couponId,
      } = request;

      // 获取或创建Stripe客户
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
      });

      if (!customer) {
        return {
          success: false,
          error: '客户不存在',
        };
      }

      const stripeCustomer = await this.getOrCreateStripeCustomer(customer);

      // 如果提供了支付方式，先附加到客户
      if (paymentMethodId) {
        await this.stripe.paymentMethods.attach(paymentMethodId, {
          customer: stripeCustomer.id,
        });

        // 设置为默认支付方式
        await this.stripe.customers.update(stripeCustomer.id, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
      }

      // 创建订阅
      const subscription = await this.stripe.subscriptions.create({
        customer: stripeCustomer.id,
        items: [{ price: priceId }],
        trial_period_days: trialPeriodDays,
        metadata: {
          ...metadata,
          customerId,
        },
        coupon: couponId,
        expand: ['latest_invoice.payment_intent'],
      });

      // 保存订阅记录
      await prisma.subscription.create({
        data: {
          id: `stripe_${subscription.id}`,
          customerId,
          status: subscription.status,
          priceId,
          currentPeriodStart: new Date(subscription.current_period_start * 1000),
          currentPeriodEnd: new Date(subscription.current_period_end * 1000),
          externalId: subscription.id,
          externalData: subscription,
          createdBy: 'system',
        },
      });

      return {
        success: true,
        data: {
          subscriptionId: subscription.id,
          status: subscription.status,
          clientSecret: (subscription.latest_invoice as any)?.payment_intent?.client_secret,
        },
      };
    } catch (error) {
      console.error('创建Stripe订阅失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建订阅失败',
      };
    }
  }

  /**
   * 创建退款
   */
  async createRefund(
    request: CreateRefundRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const { paymentIntentId, amount, reason, metadata = {} } = request;

      // 获取支付意图
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);

      if (!paymentIntent) {
        return {
          success: false,
          error: '支付意图不存在',
        };
      }

      // 创建退款
      const refund = await this.stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: amount ? Math.round(amount * 100) : undefined,
        reason,
        metadata,
      });

      // 更新本地支付记录
      await prisma.payment.updateMany({
        where: { externalId: paymentIntentId },
        data: {
          status: refund.amount === paymentIntent.amount ? 'REFUNDED' : 'PARTIAL_REFUND',
        },
      });

      // 创建退款记录
      await prisma.refund.create({
        data: {
          id: `stripe_${refund.id}`,
          paymentId: `stripe_${paymentIntentId}`,
          amount: refund.amount / 100,
          currency: refund.currency.toUpperCase(),
          reason: reason || 'requested_by_customer',
          status: 'COMPLETED',
          externalId: refund.id,
          externalData: refund,
          createdBy: 'system',
        },
      });

      return {
        success: true,
        data: {
          refundId: refund.id,
          amount: refund.amount / 100,
          status: refund.status,
        },
      };
    } catch (error) {
      console.error('创建Stripe退款失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建退款失败',
      };
    }
  }

  /**
   * 处理Webhook事件
   */
  async handleWebhook(
    payload: string,
    signature: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // 验证Webhook签名
      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        this.config.webhookSecret
      );

      console.log(`收到Stripe Webhook事件: ${event.type}`);

      // 处理不同类型的事件
      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
          break;

        case 'payment_intent.payment_failed':
          await this.handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
          break;

        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
          break;

        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event.data.object as Stripe.Invoice);
          break;

        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
          await this.handleSubscriptionEvent(event.data.object as Stripe.Subscription);
          break;

        case 'charge.dispute.created':
          await this.handleChargeDispute(event.data.object as Stripe.Dispute);
          break;

        default:
          console.log(`未处理的Webhook事件类型: ${event.type}`);
      }

      return { success: true };
    } catch (error) {
      console.error('处理Stripe Webhook失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '处理Webhook失败',
      };
    }
  }

  /**
   * 获取或创建Stripe客户
   */
  private async getOrCreateStripeCustomer(customer: any): Promise<Stripe.Customer> {
    // 检查是否已有Stripe客户ID
    if (customer.stripeCustomerId) {
      try {
        return await this.stripe.customers.retrieve(customer.stripeCustomerId) as Stripe.Customer;
      } catch (error) {
        // 如果客户不存在，创建新的
      }
    }

    // 创建新的Stripe客户
    const stripeCustomer = await this.stripe.customers.create({
      email: customer.email,
      name: `${customer.firstName} ${customer.lastName}`,
      phone: customer.phone,
      metadata: {
        customerId: customer.id,
      },
    });

    // 更新本地客户记录
    await prisma.customer.update({
      where: { id: customer.id },
      data: { stripeCustomerId: stripeCustomer.id },
    });

    return stripeCustomer;
  }

  /**
   * 处理支付成功事件
   */
  private async handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    const orderId = paymentIntent.metadata.orderId;
    
    if (orderId) {
      // 更新订单状态
      await prisma.order.update({
        where: { id: orderId },
        data: {
          paymentStatus: 'PAID',
          paidAt: new Date(),
        },
      });

      // 更新支付记录
      await prisma.payment.updateMany({
        where: { externalId: paymentIntent.id },
        data: {
          status: 'COMPLETED',
          completedAt: new Date(),
        },
      });

      // 调用财务服务处理支付回调
      await financialService.handlePaymentCallback(
        `stripe_${paymentIntent.id}`,
        'COMPLETED' as any,
        paymentIntent.id,
        { stripePaymentIntent: paymentIntent }
      );
    }
  }

  /**
   * 处理支付失败事件
   */
  private async handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent): Promise<void> {
    const orderId = paymentIntent.metadata.orderId;
    
    if (orderId) {
      // 更新支付记录
      await prisma.payment.updateMany({
        where: { externalId: paymentIntent.id },
        data: {
          status: 'FAILED',
          failureReason: paymentIntent.last_payment_error?.message,
        },
      });
    }
  }

  /**
   * 处理发票支付成功事件
   */
  private async handleInvoicePaymentSucceeded(invoice: Stripe.Invoice): Promise<void> {
    if (invoice.subscription) {
      // 更新订阅状态
      await prisma.subscription.updateMany({
        where: { externalId: invoice.subscription as string },
        data: {
          status: 'active',
          lastPaymentAt: new Date(),
        },
      });
    }
  }

  /**
   * 处理发票支付失败事件
   */
  private async handleInvoicePaymentFailed(invoice: Stripe.Invoice): Promise<void> {
    if (invoice.subscription) {
      // 更新订阅状态
      await prisma.subscription.updateMany({
        where: { externalId: invoice.subscription as string },
        data: {
          status: 'past_due',
        },
      });
    }
  }

  /**
   * 处理订阅事件
   */
  private async handleSubscriptionEvent(subscription: Stripe.Subscription): Promise<void> {
    await prisma.subscription.updateMany({
      where: { externalId: subscription.id },
      data: {
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        externalData: subscription,
      },
    });
  }

  /**
   * 处理争议事件
   */
  private async handleChargeDispute(dispute: Stripe.Dispute): Promise<void> {
    // 创建争议记录
    await prisma.dispute.create({
      data: {
        id: `stripe_${dispute.id}`,
        chargeId: dispute.charge as string,
        amount: dispute.amount / 100,
        currency: dispute.currency.toUpperCase(),
        reason: dispute.reason,
        status: dispute.status,
        externalId: dispute.id,
        externalData: dispute,
        createdBy: 'system',
      },
    });

    // 发送争议通知
    console.log(`收到Stripe争议: ${dispute.id}, 金额: ${dispute.amount / 100} ${dispute.currency}`);
  }

  /**
   * 获取支付方式
   */
  async getPaymentMethods(customerId: string): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
      });

      if (!customer?.stripeCustomerId) {
        return {
          success: false,
          error: '客户没有关联的Stripe账户',
        };
      }

      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customer.stripeCustomerId,
        type: 'card',
      });

      return {
        success: true,
        data: paymentMethods.data,
      };
    } catch (error) {
      console.error('获取支付方式失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取支付方式失败',
      };
    }
  }

  /**
   * 删除支付方式
   */
  async detachPaymentMethod(paymentMethodId: string): Promise<{ success: boolean; error?: string }> {
    try {
      await this.stripe.paymentMethods.detach(paymentMethodId);
      return { success: true };
    } catch (error) {
      console.error('删除支付方式失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '删除支付方式失败',
      };
    }
  }
}

// 导出单例实例
export const stripeService = new StripeService();
