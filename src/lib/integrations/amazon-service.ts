/**
 * Amazon电商平台集成服务
 * 提供Amazon SP-API集成，实现商品信息同步、订单自动导入、库存实时同步等功能
 */

import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';
import { prisma } from '@/lib/prisma';

// Amazon市场枚举
export enum AmazonMarketplace {
  US = 'ATVPDKIKX0DER',
  CA = 'A2EUQ1WTGCTBG2',
  MX = 'A1AM78C64UM0Y8',
  UK = 'A1F83G8C2ARO7P',
  DE = 'A1PA6795UKMFR9',
  FR = 'A13V1IB3VIYZZH',
  IT = 'APJ6JRA9NG5V4',
  ES = 'A1RKKUPIHCS9HS',
  JP = 'A1VC38T7YXB528',
  AU = 'A39IBJ37TRP1C6',
  IN = 'A21TJRUUN4KGV',
  CN = 'AAHKV2X7AFYLW',
}

// Amazon订单状态枚举
export enum AmazonOrderStatus {
  PENDING = 'Pending',
  UNSHIPPED = 'Unshipped',
  PARTIALLY_SHIPPED = 'PartiallyShipped',
  SHIPPED = 'Shipped',
  CANCELLED = 'Canceled',
  UNFULFILLABLE = 'Unfulfillable',
}

// Amazon配置接口
export interface AmazonConfig {
  clientId: string;
  clientSecret: string;
  refreshToken: string;
  sellerId: string;
  marketplaceId: string;
  region: string;
}

// Amazon商品同步请求接口
export interface AmazonProductSyncRequest {
  asin?: string;
  sku?: string;
  marketplaceId?: string;
  includeVariations?: boolean;
}

// Amazon订单同步请求接口
export interface AmazonOrderSyncRequest {
  marketplaceIds: string[];
  createdAfter?: Date;
  createdBefore?: Date;
  orderStatuses?: AmazonOrderStatus[];
  maxResults?: number;
}

// Amazon库存更新请求接口
export interface AmazonInventoryUpdateRequest {
  sku: string;
  quantity: number;
  marketplaceId: string;
  fulfillmentChannel?: 'DEFAULT' | 'AMAZON_NA' | 'AMAZON_EU';
}

// Amazon商品信息接口
export interface AmazonProduct {
  asin: string;
  sku: string;
  title: string;
  description?: string;
  brand?: string;
  price: number;
  currency: string;
  images: string[];
  attributes: Record<string, any>;
  variations?: AmazonProduct[];
}

// Amazon订单信息接口
export interface AmazonOrder {
  amazonOrderId: string;
  sellerOrderId?: string;
  purchaseDate: Date;
  lastUpdateDate: Date;
  orderStatus: AmazonOrderStatus;
  fulfillmentChannel: string;
  salesChannel: string;
  orderChannel: string;
  shipServiceLevel: string;
  orderTotal: {
    currencyCode: string;
    amount: string;
  };
  numberOfItemsShipped: number;
  numberOfItemsUnshipped: number;
  paymentExecutionDetail: any[];
  paymentMethod: string;
  paymentMethodDetails: string[];
  marketplaceId: string;
  shipmentServiceLevelCategory: string;
  orderType: string;
  earliestShipDate?: Date;
  latestShipDate?: Date;
  earliestDeliveryDate?: Date;
  latestDeliveryDate?: Date;
  isBusinessOrder: boolean;
  isPrime: boolean;
  isPremiumOrder: boolean;
  isGlobalExpressEnabled: boolean;
  replacedOrderId?: string;
  isReplacementOrder: boolean;
  promiseResponseDueDate?: Date;
  isEstimatedShipDateSet: boolean;
  isSoldByAB: boolean;
  isIBA: boolean;
  defaultShipFromLocationAddress?: any;
  buyerInvoicePreference?: string;
  buyerTaxInformation?: any;
  fulfillmentInstruction?: any;
  isISPU: boolean;
  isAccessPointOrder: boolean;
  marketplaceTaxInfo?: any;
  sellerDisplayName?: string;
  shippingAddress?: any;
  buyerInfo?: any;
  automatedShippingSettings?: any;
  hasAutomatedShippingSettings: boolean;
  electronicInvoiceStatus?: string;
  items?: AmazonOrderItem[];
}

export interface AmazonOrderItem {
  asin: string;
  sellerSku: string;
  orderItemId: string;
  title: string;
  quantityOrdered: number;
  quantityShipped: number;
  productInfo?: any;
  pointsGranted?: any;
  itemPrice?: {
    currencyCode: string;
    amount: string;
  };
  shippingPrice?: {
    currencyCode: string;
    amount: string;
  };
  itemTax?: {
    currencyCode: string;
    amount: string;
  };
  shippingTax?: {
    currencyCode: string;
    amount: string;
  };
  shippingDiscount?: {
    currencyCode: string;
    amount: string;
  };
  shippingDiscountTax?: {
    currencyCode: string;
    amount: string;
  };
  promotionDiscount?: {
    currencyCode: string;
    amount: string;
  };
  promotionDiscountTax?: {
    currencyCode: string;
    amount: string;
  };
  promotionIds?: string[];
  codFee?: {
    currencyCode: string;
    amount: string;
  };
  codFeeDiscount?: {
    currencyCode: string;
    amount: string;
  };
  isGift: boolean;
  conditionNote?: string;
  conditionId?: string;
  conditionSubtypeId?: string;
  scheduledDeliveryStartDate?: string;
  scheduledDeliveryEndDate?: string;
  priceDesignation?: string;
  taxCollection?: any;
  serialNumberRequired: boolean;
  isTransparency: boolean;
  iossNumber?: string;
  storeChainStoreId?: string;
  deemedResellerCategory?: string;
  buyerInfo?: any;
  buyerRequestedCancel?: any;
}

/**
 * Amazon电商平台服务类
 */
export class AmazonService {
  private config: AmazonConfig;
  private apiClient: AxiosInstance;
  private accessToken?: string;
  private tokenExpiresAt?: Date;

  constructor() {
    this.config = {
      clientId: process.env.AMAZON_CLIENT_ID!,
      clientSecret: process.env.AMAZON_CLIENT_SECRET!,
      refreshToken: process.env.AMAZON_REFRESH_TOKEN!,
      sellerId: process.env.AMAZON_SELLER_ID!,
      marketplaceId: process.env.AMAZON_MARKETPLACE_ID || AmazonMarketplace.US,
      region: process.env.AMAZON_REGION || 'us-east-1',
    };

    const baseURL = this.getApiEndpoint();

    this.apiClient = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // 请求拦截器：自动添加访问令牌和签名
    this.apiClient.interceptors.request.use(async (config) => {
      const token = await this.getAccessToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        config.headers['x-amz-access-token'] = token;
      }

      // 添加AWS签名
      this.addAWSSignature(config);

      return config;
    });
  }

  /**
   * 同步Amazon商品信息
   */
  async syncProducts(
    request: AmazonProductSyncRequest = {}
  ): Promise<{ success: boolean; data?: AmazonProduct[]; error?: string }> {
    try {
      const {
        asin,
        sku,
        marketplaceId = this.config.marketplaceId,
        includeVariations = true,
      } = request;

      let products: AmazonProduct[] = [];

      if (asin) {
        // 通过ASIN获取商品信息
        const productResult = await this.getProductByASIN(asin, marketplaceId);
        if (productResult.success && productResult.data) {
          products.push(productResult.data);
        }
      } else if (sku) {
        // 通过SKU获取商品信息
        const productResult = await this.getProductBySKU(sku, marketplaceId);
        if (productResult.success && productResult.data) {
          products.push(productResult.data);
        }
      } else {
        // 获取所有商品信息
        const productsResult = await this.getAllProducts(marketplaceId);
        if (productsResult.success && productsResult.data) {
          products = productsResult.data;
        }
      }

      // 同步到本地数据库
      for (const product of products) {
        await this.syncProductToDatabase(product);
      }

      return {
        success: true,
        data: products,
      };
    } catch (error) {
      console.error('Amazon商品同步失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Amazon商品同步失败',
      };
    }
  }

  /**
   * 同步Amazon订单
   */
  async syncOrders(
    request: AmazonOrderSyncRequest
  ): Promise<{ success: boolean; data?: AmazonOrder[]; error?: string }> {
    try {
      const {
        marketplaceIds,
        createdAfter = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 默认7天前
        createdBefore = new Date(),
        orderStatuses = [AmazonOrderStatus.UNSHIPPED, AmazonOrderStatus.PARTIALLY_SHIPPED],
        maxResults = 100,
      } = request;

      // 构建查询参数
      const params = new URLSearchParams({
        MarketplaceIds: marketplaceIds.join(','),
        CreatedAfter: createdAfter.toISOString(),
        CreatedBefore: createdBefore.toISOString(),
        OrderStatuses: orderStatuses.join(','),
        MaxResultsPerPage: maxResults.toString(),
      });

      // 调用Amazon API获取订单
      const response = await this.apiClient.get(`/orders/v0/orders?${params}`);
      const ordersData = response.data;

      const orders: AmazonOrder[] = ordersData.payload?.Orders || [];

      // 获取订单项目详情
      for (const order of orders) {
        const itemsResult = await this.getOrderItems(order.amazonOrderId);
        if (itemsResult.success && itemsResult.data) {
          order.items = itemsResult.data;
        }
      }

      // 同步到本地数据库
      for (const order of orders) {
        await this.syncOrderToDatabase(order);
      }

      return {
        success: true,
        data: orders,
      };
    } catch (error) {
      console.error('Amazon订单同步失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Amazon订单同步失败',
      };
    }
  }

  /**
   * 更新Amazon库存
   */
  async updateInventory(
    request: AmazonInventoryUpdateRequest
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const {
        sku,
        quantity,
        marketplaceId,
        fulfillmentChannel = 'DEFAULT',
      } = request;

      // 构建库存更新请求
      const inventoryUpdateData = {
        feeds: [
          {
            feedType: 'POST_INVENTORY_AVAILABILITY_DATA',
            marketplaceIds: [marketplaceId],
            inputFeedDocumentId: await this.createInventoryFeedDocument(sku, quantity),
          },
        ],
      };

      // 调用Amazon API更新库存
      const response = await this.apiClient.post('/feeds/2021-06-30/feeds', inventoryUpdateData);
      const feedResult = response.data;

      // 监控Feed处理状态
      const feedId = feedResult.feedId;
      await this.monitorFeedProcessing(feedId);

      return { success: true };
    } catch (error) {
      console.error('Amazon库存更新失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Amazon库存更新失败',
      };
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string | null> {
    try {
      // 检查现有令牌是否仍然有效
      if (this.accessToken && this.tokenExpiresAt && new Date() < this.tokenExpiresAt) {
        return this.accessToken;
      }

      // 使用刷新令牌获取新的访问令牌
      const tokenRequestData = {
        grant_type: 'refresh_token',
        refresh_token: this.config.refreshToken,
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
      };

      const response = await axios.post(
        'https://api.amazon.com/auth/o2/token',
        new URLSearchParams(tokenRequestData),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const tokenData = response.data;
      this.accessToken = tokenData.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (tokenData.expires_in - 60) * 1000); // 提前60秒过期

      return this.accessToken;
    } catch (error) {
      console.error('获取Amazon访问令牌失败:', error);
      return null;
    }
  }

  /**
   * 获取API端点
   */
  private getApiEndpoint(): string {
    const endpoints: Record<string, string> = {
      'us-east-1': 'https://sellingpartnerapi-na.amazon.com',
      'eu-west-1': 'https://sellingpartnerapi-eu.amazon.com',
      'us-west-2': 'https://sellingpartnerapi-fe.amazon.com',
    };

    return endpoints[this.config.region] || endpoints['us-east-1'];
  }

  /**
   * 添加AWS签名
   */
  private addAWSSignature(config: any): void {
    // 这里应该实现AWS Signature Version 4签名
    // 由于签名逻辑比较复杂，这里简化处理
    const timestamp = new Date().toISOString().replace(/[:\-]|\.\d{3}/g, '');
    config.headers['x-amz-date'] = timestamp;
  }

  /**
   * 通过ASIN获取商品信息
   */
  private async getProductByASIN(
    asin: string,
    marketplaceId: string
  ): Promise<{ success: boolean; data?: AmazonProduct; error?: string }> {
    try {
      const response = await this.apiClient.get(
        `/catalog/v0/items/${asin}?MarketplaceId=${marketplaceId}&includedData=attributes,dimensions,identifiers,images,productTypes,relationships,salesRanks,summaries`
      );

      const productData = response.data.payload;
      const product = this.transformProductData(productData);

      return {
        success: true,
        data: product,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取商品信息失败',
      };
    }
  }

  /**
   * 通过SKU获取商品信息
   */
  private async getProductBySKU(
    sku: string,
    marketplaceId: string
  ): Promise<{ success: boolean; data?: AmazonProduct; error?: string }> {
    try {
      const response = await this.apiClient.get(
        `/catalog/v0/items?MarketplaceId=${marketplaceId}&SellerSKU=${sku}&includedData=attributes,dimensions,identifiers,images,productTypes,relationships,salesRanks,summaries`
      );

      const items = response.data.payload?.Items || [];
      if (items.length === 0) {
        return {
          success: false,
          error: '未找到商品信息',
        };
      }

      const product = this.transformProductData(items[0]);

      return {
        success: true,
        data: product,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取商品信息失败',
      };
    }
  }

  /**
   * 获取所有商品信息
   */
  private async getAllProducts(
    marketplaceId: string
  ): Promise<{ success: boolean; data?: AmazonProduct[]; error?: string }> {
    try {
      // 这里应该实现分页获取所有商品
      // 由于API限制，这里简化处理
      return {
        success: true,
        data: [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取商品列表失败',
      };
    }
  }

  /**
   * 获取订单项目
   */
  private async getOrderItems(
    amazonOrderId: string
  ): Promise<{ success: boolean; data?: AmazonOrderItem[]; error?: string }> {
    try {
      const response = await this.apiClient.get(`/orders/v0/orders/${amazonOrderId}/orderItems`);
      const itemsData = response.data;

      return {
        success: true,
        data: itemsData.payload?.OrderItems || [],
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取订单项目失败',
      };
    }
  }

  /**
   * 转换商品数据格式
   */
  private transformProductData(productData: any): AmazonProduct {
    return {
      asin: productData.asin,
      sku: productData.identifiers?.marketplaceASIN?.marketplaceId || '',
      title: productData.summaries?.[0]?.itemName || '',
      description: productData.summaries?.[0]?.itemDescription || '',
      brand: productData.attributes?.brand?.[0]?.value || '',
      price: parseFloat(productData.summaries?.[0]?.lowestPrice?.amount || '0'),
      currency: productData.summaries?.[0]?.lowestPrice?.currencyCode || 'USD',
      images: productData.images?.map((img: any) => img.link) || [],
      attributes: productData.attributes || {},
    };
  }

  /**
   * 同步商品到数据库
   */
  private async syncProductToDatabase(product: AmazonProduct): Promise<void> {
    try {
      // 检查商品是否已存在
      const existingProduct = await prisma.product.findFirst({
        where: {
          OR: [
            { sku: product.sku },
            { externalId: product.asin },
          ],
        },
      });

      if (existingProduct) {
        // 更新现有商品
        await prisma.product.update({
          where: { id: existingProduct.id },
          data: {
            name: product.title,
            description: product.description,
            basePrice: product.price,
            brand: product.brand,
            externalData: product,
            updatedAt: new Date(),
          },
        });
      } else {
        // 创建新商品
        await prisma.product.create({
          data: {
            name: product.title,
            sku: product.sku,
            description: product.description,
            basePrice: product.price,
            costPrice: product.price * 0.7, // 假设成本价为售价的70%
            brand: product.brand,
            status: 'ACTIVE',
            externalId: product.asin,
            externalSource: 'AMAZON',
            externalData: product,
            createdBy: 'system',
          },
        });
      }
    } catch (error) {
      console.error('同步商品到数据库失败:', error);
    }
  }

  /**
   * 同步订单到数据库
   */
  private async syncOrderToDatabase(order: AmazonOrder): Promise<void> {
    try {
      // 检查订单是否已存在
      const existingOrder = await prisma.order.findFirst({
        where: { externalId: order.amazonOrderId },
      });

      if (existingOrder) {
        // 更新现有订单
        await prisma.order.update({
          where: { id: existingOrder.id },
          data: {
            status: this.mapAmazonOrderStatus(order.orderStatus),
            externalData: order,
            updatedAt: new Date(),
          },
        });
      } else {
        // 创建新订单
        const totalAmount = parseFloat(order.orderTotal.amount);
        
        await prisma.order.create({
          data: {
            orderNumber: order.amazonOrderId,
            customerId: 'amazon-customer', // 需要创建或关联Amazon客户
            totalAmount,
            subtotal: totalAmount,
            taxAmount: 0,
            shippingAmount: 0,
            currency: order.orderTotal.currencyCode,
            status: this.mapAmazonOrderStatus(order.orderStatus),
            paymentStatus: 'PAID', // Amazon订单通常已付款
            externalId: order.amazonOrderId,
            externalSource: 'AMAZON',
            externalData: order,
            createdBy: 'system',
          },
        });
      }
    } catch (error) {
      console.error('同步订单到数据库失败:', error);
    }
  }

  /**
   * 映射Amazon订单状态
   */
  private mapAmazonOrderStatus(amazonStatus: AmazonOrderStatus): string {
    const statusMap: Record<AmazonOrderStatus, string> = {
      [AmazonOrderStatus.PENDING]: 'PENDING',
      [AmazonOrderStatus.UNSHIPPED]: 'CONFIRMED',
      [AmazonOrderStatus.PARTIALLY_SHIPPED]: 'PROCESSING',
      [AmazonOrderStatus.SHIPPED]: 'SHIPPED',
      [AmazonOrderStatus.CANCELLED]: 'CANCELLED',
      [AmazonOrderStatus.UNFULFILLABLE]: 'CANCELLED',
    };

    return statusMap[amazonStatus] || 'PENDING';
  }

  /**
   * 创建库存Feed文档
   */
  private async createInventoryFeedDocument(sku: string, quantity: number): Promise<string> {
    // 这里应该创建库存更新的XML文档并上传到Amazon
    // 简化处理，返回模拟的文档ID
    return `inventory_feed_${Date.now()}`;
  }

  /**
   * 监控Feed处理状态
   */
  private async monitorFeedProcessing(feedId: string): Promise<void> {
    // 这里应该轮询检查Feed处理状态
    // 简化处理
    console.log(`监控Feed处理状态: ${feedId}`);
  }
}

// 导出单例实例
export const amazonService = new AmazonService();
