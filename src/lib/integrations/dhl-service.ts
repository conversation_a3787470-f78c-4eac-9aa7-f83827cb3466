/**
 * DHL物流服务集成
 * 提供运费计算、运单创建、物流跟踪、签收确认等功能
 */

import axios, { AxiosInstance } from 'axios';
import { prisma } from '@/lib/prisma';

// DHL环境枚举
export enum DHLEnvironment {
  SANDBOX = 'sandbox',
  PRODUCTION = 'production',
}

// DHL服务类型枚举
export enum DHLServiceType {
  EXPRESS_WORLDWIDE = 'U',
  EXPRESS_12_00 = 'T',
  EXPRESS_10_30 = 'K',
  EXPRESS_9_00 = 'Y',
  DOMESTIC_EXPRESS = 'D',
  ECONOMY_SELECT = 'W',
}

// 包裹状态枚举
export enum DHLShipmentStatus {
  TRANSIT = 'transit',
  DELIVERED = 'delivered',
  FAILURE = 'failure',
  UNKNOWN = 'unknown',
}

// DHL配置接口
export interface DHLConfig {
  apiKey: string;
  apiSecret: string;
  accountNumber: string;
  environment: DHLEnvironment;
}

// 地址接口
export interface DHLAddress {
  name: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postalCode: string;
  countryCode: string;
  state?: string;
  phone?: string;
  email?: string;
}

// 包裹信息接口
export interface DHLPackage {
  weight: number; // 重量（千克）
  length: number; // 长度（厘米）
  width: number;  // 宽度（厘米）
  height: number; // 高度（厘米）
  description?: string;
  value?: number; // 申报价值
}

// 运费查询请求接口
export interface RateRequest {
  originAddress: DHLAddress;
  destinationAddress: DHLAddress;
  packages: DHLPackage[];
  serviceType?: DHLServiceType;
  shipDate?: Date;
  currency?: string;
}

// 运单创建请求接口
export interface ShipmentRequest {
  originAddress: DHLAddress;
  destinationAddress: DHLAddress;
  packages: DHLPackage[];
  serviceType: DHLServiceType;
  shipDate: Date;
  orderId: string;
  reference?: string;
  specialInstructions?: string;
  insuranceValue?: number;
  signatureRequired?: boolean;
}

// 运费响应接口
export interface RateResponse {
  serviceType: DHLServiceType;
  serviceName: string;
  totalPrice: number;
  currency: string;
  transitTime: number; // 预计运输时间（天）
  deliveryDate: Date;
  breakdown: {
    basePrice: number;
    fuelSurcharge: number;
    taxes: number;
    fees: number;
  };
}

// 运单响应接口
export interface ShipmentResponse {
  trackingNumber: string;
  shipmentId: string;
  labelUrl: string;
  totalCost: number;
  currency: string;
  estimatedDelivery: Date;
}

// 跟踪信息接口
export interface TrackingInfo {
  trackingNumber: string;
  status: DHLShipmentStatus;
  statusDescription: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  events: TrackingEvent[];
}

export interface TrackingEvent {
  timestamp: Date;
  location: string;
  description: string;
  statusCode: string;
}

/**
 * DHL物流服务类
 */
export class DHLService {
  private config: DHLConfig;
  private apiClient: AxiosInstance;

  constructor() {
    this.config = {
      apiKey: process.env.DHL_API_KEY!,
      apiSecret: process.env.DHL_API_SECRET!,
      accountNumber: process.env.DHL_ACCOUNT_NUMBER!,
      environment: (process.env.DHL_ENVIRONMENT as DHLEnvironment) || DHLEnvironment.SANDBOX,
    };

    const baseURL = this.config.environment === DHLEnvironment.SANDBOX
      ? 'https://express.api.dhl.com/mydhlapi/test'
      : 'https://express.api.dhl.com/mydhlapi';

    this.apiClient = axios.create({
      baseURL,
      headers: {
        'DHL-API-Key': this.config.apiKey,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      auth: {
        username: this.config.apiKey,
        password: this.config.apiSecret,
      },
    });
  }

  /**
   * 计算运费
   */
  async calculateRates(
    request: RateRequest
  ): Promise<{ success: boolean; data?: RateResponse[]; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        serviceType,
        shipDate = new Date(),
        currency = 'USD',
      } = request;

      // 构建DHL API请求数据
      const rateRequestData = {
        customerDetails: {
          shipperDetails: {
            postalAddress: this.formatAddress(originAddress),
            contactInformation: {
              phone: originAddress.phone || '',
              companyName: originAddress.company || originAddress.name,
              fullName: originAddress.name,
              email: originAddress.email || '',
            },
          },
          receiverDetails: {
            postalAddress: this.formatAddress(destinationAddress),
            contactInformation: {
              phone: destinationAddress.phone || '',
              companyName: destinationAddress.company || destinationAddress.name,
              fullName: destinationAddress.name,
              email: destinationAddress.email || '',
            },
          },
        },
        accounts: [
          {
            typeCode: 'shipper',
            number: this.config.accountNumber,
          },
        ],
        plannedShippingDateAndTime: shipDate.toISOString(),
        unitOfMeasurement: 'metric',
        isCustomsDeclarable: this.isInternational(originAddress.countryCode, destinationAddress.countryCode),
        monetaryAmount: packages.reduce((sum, pkg) => sum + (pkg.value || 0), 0),
        requestedCurrency: currency,
        packages: packages.map((pkg, index) => ({
          typeCode: '2BP', // Box
          weight: pkg.weight,
          dimensions: {
            length: pkg.length,
            width: pkg.width,
            height: pkg.height,
          },
        })),
        ...(serviceType && { productCode: serviceType }),
      };

      // 调用DHL API
      const response = await this.apiClient.post('/rates', rateRequestData);
      const rateData = response.data;

      // 解析响应数据
      const rates: RateResponse[] = rateData.products?.map((product: any) => ({
        serviceType: product.productCode as DHLServiceType,
        serviceName: product.productName,
        totalPrice: parseFloat(product.totalPrice?.[0]?.price || '0'),
        currency: product.totalPrice?.[0]?.currencyType || currency,
        transitTime: parseInt(product.deliveryCapabilities?.deliveryTypeCode || '0'),
        deliveryDate: new Date(product.deliveryCapabilities?.estimatedDeliveryDateAndTime || Date.now()),
        breakdown: {
          basePrice: parseFloat(product.detailedPriceBreakdown?.[0]?.breakdown?.find((b: any) => b.name === 'TRANSPORTATION')?.price || '0'),
          fuelSurcharge: parseFloat(product.detailedPriceBreakdown?.[0]?.breakdown?.find((b: any) => b.name === 'FUEL_SURCHARGE')?.price || '0'),
          taxes: parseFloat(product.detailedPriceBreakdown?.[0]?.breakdown?.find((b: any) => b.name === 'TAX')?.price || '0'),
          fees: parseFloat(product.detailedPriceBreakdown?.[0]?.breakdown?.find((b: any) => b.name === 'FEE')?.price || '0'),
        },
      })) || [];

      return {
        success: true,
        data: rates,
      };
    } catch (error) {
      console.error('DHL运费计算失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'DHL运费计算失败',
      };
    }
  }

  /**
   * 创建运单
   */
  async createShipment(
    request: ShipmentRequest
  ): Promise<{ success: boolean; data?: ShipmentResponse; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        serviceType,
        shipDate,
        orderId,
        reference,
        specialInstructions,
        insuranceValue,
        signatureRequired = false,
      } = request;

      // 验证订单
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { customer: true },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 构建DHL运单请求数据
      const shipmentRequestData = {
        plannedShippingDateAndTime: shipDate.toISOString(),
        pickup: {
          isRequested: false,
        },
        productCode: serviceType,
        accounts: [
          {
            typeCode: 'shipper',
            number: this.config.accountNumber,
          },
        ],
        customerDetails: {
          shipperDetails: {
            postalAddress: this.formatAddress(originAddress),
            contactInformation: {
              phone: originAddress.phone || '',
              companyName: originAddress.company || originAddress.name,
              fullName: originAddress.name,
              email: originAddress.email || '',
            },
          },
          receiverDetails: {
            postalAddress: this.formatAddress(destinationAddress),
            contactInformation: {
              phone: destinationAddress.phone || '',
              companyName: destinationAddress.company || destinationAddress.name,
              fullName: destinationAddress.name,
              email: destinationAddress.email || '',
            },
          },
        },
        content: {
          packages: packages.map((pkg, index) => ({
            typeCode: '2BP',
            weight: pkg.weight,
            dimensions: {
              length: pkg.length,
              width: pkg.width,
              height: pkg.height,
            },
            customerReferences: [
              {
                value: reference || order.orderNumber,
                typeCode: 'CU',
              },
            ],
          })),
          isCustomsDeclarable: this.isInternational(originAddress.countryCode, destinationAddress.countryCode),
          declaredValue: packages.reduce((sum, pkg) => sum + (pkg.value || 0), 0),
          declaredValueCurrency: 'USD',
          description: packages.map(pkg => pkg.description).join(', ') || '商品',
          ...(this.isInternational(originAddress.countryCode, destinationAddress.countryCode) && {
            exportDeclaration: {
              lineItems: packages.map((pkg, index) => ({
                number: index + 1,
                description: pkg.description || '商品',
                price: pkg.value || 0,
                quantity: {
                  value: 1,
                  unitOfMeasurement: 'PCS',
                },
                commodityCodes: [
                  {
                    typeCode: 'outbound',
                    value: '999999', // 默认商品编码
                  },
                ],
                weight: {
                  netValue: pkg.weight,
                  grossValue: pkg.weight,
                },
                preCalculatedLineItemTotalValue: pkg.value || 0,
              })),
              invoice: {
                number: order.orderNumber,
                date: order.createdAt.toISOString().split('T')[0],
                function: 'commercial',
              },
            },
          }),
        },
        valueAddedServices: [
          ...(signatureRequired ? [{ serviceCode: 'DD' }] : []),
          ...(insuranceValue ? [{ serviceCode: 'II', value: insuranceValue }] : []),
        ],
        outputImageProperties: {
          printerDPI: 300,
          encodingFormat: 'pdf',
          imageOptions: [
            {
              typeCode: 'label',
              templateName: 'ECOM26_84_001',
              isRequested: true,
            },
          ],
        },
      };

      // 调用DHL API创建运单
      const response = await this.apiClient.post('/shipments', shipmentRequestData);
      const shipmentData = response.data;

      const trackingNumber = shipmentData.shipmentTrackingNumber;
      const labelUrl = shipmentData.documents?.[0]?.content || '';

      // 保存运单记录
      await prisma.shipment.create({
        data: {
          id: `dhl_${trackingNumber}`,
          orderId,
          trackingNumber,
          carrier: 'DHL',
          serviceType,
          status: 'CREATED',
          labelUrl,
          estimatedDelivery: new Date(shipmentData.estimatedDeliveryDate?.estimatedDeliveryDateAndTime || Date.now()),
          shippingCost: parseFloat(shipmentData.shipmentCharges?.[0]?.price || '0'),
          currency: shipmentData.shipmentCharges?.[0]?.currencyType || 'USD',
          externalId: shipmentData.shipmentTrackingNumber,
          externalData: shipmentData,
          createdBy: 'system',
        },
      });

      // 更新订单状态
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'SHIPPED',
          shippedAt: new Date(),
          trackingNumber,
        },
      });

      const shipmentResponse: ShipmentResponse = {
        trackingNumber,
        shipmentId: shipmentData.shipmentTrackingNumber,
        labelUrl,
        totalCost: parseFloat(shipmentData.shipmentCharges?.[0]?.price || '0'),
        currency: shipmentData.shipmentCharges?.[0]?.currencyType || 'USD',
        estimatedDelivery: new Date(shipmentData.estimatedDeliveryDate?.estimatedDeliveryDateAndTime || Date.now()),
      };

      return {
        success: true,
        data: shipmentResponse,
      };
    } catch (error) {
      console.error('DHL创建运单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'DHL创建运单失败',
      };
    }
  }

  /**
   * 跟踪包裹
   */
  async trackShipment(
    trackingNumber: string
  ): Promise<{ success: boolean; data?: TrackingInfo; error?: string }> {
    try {
      // 调用DHL跟踪API
      const response = await this.apiClient.get(`/track/shipments?trackingNumber=${trackingNumber}`);
      const trackingData = response.data;

      const shipment = trackingData.shipments?.[0];
      if (!shipment) {
        return {
          success: false,
          error: '未找到跟踪信息',
        };
      }

      // 解析跟踪信息
      const events: TrackingEvent[] = shipment.events?.map((event: any) => ({
        timestamp: new Date(event.timestamp),
        location: `${event.location?.address?.addressLocality || ''}, ${event.location?.address?.countryCode || ''}`,
        description: event.description,
        statusCode: event.statusCode,
      })) || [];

      const trackingInfo: TrackingInfo = {
        trackingNumber,
        status: this.mapDHLStatus(shipment.status),
        statusDescription: shipment.status?.description || '',
        estimatedDelivery: shipment.estimatedTimeOfDelivery ? new Date(shipment.estimatedTimeOfDelivery) : undefined,
        actualDelivery: shipment.events?.find((e: any) => e.statusCode === 'delivered')?.timestamp ? 
          new Date(shipment.events.find((e: any) => e.statusCode === 'delivered').timestamp) : undefined,
        events,
      };

      // 更新本地运单状态
      await prisma.shipment.updateMany({
        where: { trackingNumber },
        data: {
          status: trackingInfo.status.toUpperCase(),
          externalData: trackingData,
          ...(trackingInfo.actualDelivery && { deliveredAt: trackingInfo.actualDelivery }),
        },
      });

      // 如果已签收，更新订单状态
      if (trackingInfo.status === DHLShipmentStatus.DELIVERED) {
        await prisma.order.updateMany({
          where: { trackingNumber },
          data: {
            status: 'DELIVERED',
            deliveredAt: trackingInfo.actualDelivery,
          },
        });
      }

      return {
        success: true,
        data: trackingInfo,
      };
    } catch (error) {
      console.error('DHL包裹跟踪失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'DHL包裹跟踪失败',
      };
    }
  }

  /**
   * 格式化地址
   */
  private formatAddress(address: DHLAddress): any {
    return {
      addressLine1: address.addressLine1,
      addressLine2: address.addressLine2 || '',
      cityName: address.city,
      countryCode: address.countryCode,
      postalCode: address.postalCode,
      provinceCode: address.state || '',
    };
  }

  /**
   * 判断是否为国际运输
   */
  private isInternational(originCountry: string, destinationCountry: string): boolean {
    return originCountry.toUpperCase() !== destinationCountry.toUpperCase();
  }

  /**
   * 映射DHL状态到本地状态
   */
  private mapDHLStatus(status: any): DHLShipmentStatus {
    const statusCode = status?.statusCode?.toLowerCase() || '';
    
    if (statusCode.includes('delivered')) {
      return DHLShipmentStatus.DELIVERED;
    } else if (statusCode.includes('transit') || statusCode.includes('pickup') || statusCode.includes('processed')) {
      return DHLShipmentStatus.TRANSIT;
    } else if (statusCode.includes('exception') || statusCode.includes('failure')) {
      return DHLShipmentStatus.FAILURE;
    } else {
      return DHLShipmentStatus.UNKNOWN;
    }
  }

  /**
   * 取消运单
   */
  async cancelShipment(
    trackingNumber: string,
    reason?: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      // DHL Express API通常不支持直接取消，需要联系客服
      // 这里更新本地状态
      await prisma.shipment.updateMany({
        where: { trackingNumber },
        data: {
          status: 'CANCELLED',
          cancelledAt: new Date(),
          cancelReason: reason || '用户取消',
        },
      });

      return { success: true };
    } catch (error) {
      console.error('DHL取消运单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'DHL取消运单失败',
      };
    }
  }

  /**
   * 获取服务可用性
   */
  async getServiceAvailability(
    originCountry: string,
    destinationCountry: string
  ): Promise<{ success: boolean; data?: DHLServiceType[]; error?: string }> {
    try {
      // 调用DHL服务可用性API
      const response = await this.apiClient.get(
        `/products?accountNumber=${this.config.accountNumber}&originCountryCode=${originCountry}&destinationCountryCode=${destinationCountry}&plannedShippingDate=${new Date().toISOString().split('T')[0]}`
      );

      const services = response.data.products?.map((product: any) => product.productCode as DHLServiceType) || [];

      return {
        success: true,
        data: services,
      };
    } catch (error) {
      console.error('获取DHL服务可用性失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取DHL服务可用性失败',
      };
    }
  }
}

// 导出单例实例
export const dhlService = new DHLService();
