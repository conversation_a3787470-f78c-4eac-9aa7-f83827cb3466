/**
 * 统一物流服务管理器
 * 提供多物流商统一接口管理，包括DHL、FedEx、UPS等
 */

import { dhlService } from './dhl-service';
import { fedexService } from './fedex-service';
import { upsService } from './ups-service';
import { prisma } from '@/lib/prisma';

// 物流商枚举
export enum ShippingCarrier {
  DHL = 'DHL',
  FEDEX = 'FEDEX',
  UPS = 'UPS',
}

// 统一地址接口
export interface UnifiedAddress {
  name: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postalCode: string;
  countryCode: string;
  state?: string;
  phone?: string;
  email?: string;
}

// 统一包裹接口
export interface UnifiedPackage {
  weight: number;
  length: number;
  width: number;
  height: number;
  description?: string;
  value?: number;
}

// 统一运费查询请求
export interface UnifiedRateRequest {
  originAddress: UnifiedAddress;
  destinationAddress: UnifiedAddress;
  packages: UnifiedPackage[];
  shipDate?: Date;
  currency?: string;
  carriers?: ShippingCarrier[];
}

// 统一运费响应
export interface UnifiedRateResponse {
  carrier: ShippingCarrier;
  serviceType: string;
  serviceName: string;
  totalPrice: number;
  currency: string;
  transitTime: number;
  deliveryDate: Date;
  breakdown?: {
    basePrice: number;
    fuelSurcharge: number;
    taxes: number;
    fees: number;
  };
}

// 统一运单创建请求
export interface UnifiedShipmentRequest {
  carrier: ShippingCarrier;
  originAddress: UnifiedAddress;
  destinationAddress: UnifiedAddress;
  packages: UnifiedPackage[];
  serviceType: string;
  shipDate: Date;
  orderId: string;
  reference?: string;
  specialInstructions?: string;
  insuranceValue?: number;
  signatureRequired?: boolean;
}

// 统一运单响应
export interface UnifiedShipmentResponse {
  carrier: ShippingCarrier;
  trackingNumber: string;
  shipmentId: string;
  labelUrl: string;
  totalCost: number;
  currency: string;
  estimatedDelivery: Date;
}

// 统一跟踪信息
export interface UnifiedTrackingInfo {
  carrier: ShippingCarrier;
  trackingNumber: string;
  status: string;
  statusDescription: string;
  estimatedDelivery?: Date;
  actualDelivery?: Date;
  events: UnifiedTrackingEvent[];
}

export interface UnifiedTrackingEvent {
  timestamp: Date;
  location: string;
  description: string;
  statusCode: string;
}

// 物流商配置接口
export interface CarrierConfig {
  carrier: ShippingCarrier;
  isEnabled: boolean;
  priority: number;
  supportedCountries: string[];
  supportedServices: string[];
  credentials: Record<string, string>;
}

/**
 * 统一物流服务管理器类
 */
export class ShippingServiceManager {
  private carrierConfigs: Map<ShippingCarrier, CarrierConfig> = new Map();

  constructor() {
    this.initializeCarrierConfigs();
  }

  /**
   * 初始化物流商配置
   */
  private initializeCarrierConfigs(): void {
    // DHL配置
    this.carrierConfigs.set(ShippingCarrier.DHL, {
      carrier: ShippingCarrier.DHL,
      isEnabled: !!process.env.DHL_API_KEY,
      priority: 1,
      supportedCountries: ['*'], // 支持全球
      supportedServices: ['U', 'T', 'K', 'Y', 'D', 'W'],
      credentials: {
        apiKey: process.env.DHL_API_KEY || '',
        apiSecret: process.env.DHL_API_SECRET || '',
        accountNumber: process.env.DHL_ACCOUNT_NUMBER || '',
      },
    });

    // FedEx配置
    this.carrierConfigs.set(ShippingCarrier.FEDEX, {
      carrier: ShippingCarrier.FEDEX,
      isEnabled: !!process.env.FEDEX_API_KEY,
      priority: 2,
      supportedCountries: ['*'], // 支持全球
      supportedServices: ['FEDEX_GROUND', 'FEDEX_EXPRESS_SAVER', 'FEDEX_2_DAY', 'STANDARD_OVERNIGHT'],
      credentials: {
        apiKey: process.env.FEDEX_API_KEY || '',
        secretKey: process.env.FEDEX_SECRET_KEY || '',
        accountNumber: process.env.FEDEX_ACCOUNT_NUMBER || '',
      },
    });

    // UPS配置
    this.carrierConfigs.set(ShippingCarrier.UPS, {
      carrier: ShippingCarrier.UPS,
      isEnabled: !!process.env.UPS_API_KEY,
      priority: 3,
      supportedCountries: ['*'], // 支持全球
      supportedServices: ['UPS_GROUND', 'UPS_3_DAY_SELECT', 'UPS_2ND_DAY_AIR', 'UPS_NEXT_DAY_AIR'],
      credentials: {
        apiKey: process.env.UPS_API_KEY || '',
        username: process.env.UPS_USERNAME || '',
        password: process.env.UPS_PASSWORD || '',
        accountNumber: process.env.UPS_ACCOUNT_NUMBER || '',
      },
    });
  }

  /**
   * 获取多物流商运费报价
   */
  async getMultiCarrierRates(
    request: UnifiedRateRequest
  ): Promise<{ success: boolean; data?: UnifiedRateResponse[]; error?: string }> {
    try {
      const {
        originAddress,
        destinationAddress,
        packages,
        shipDate = new Date(),
        currency = 'USD',
        carriers = [ShippingCarrier.DHL, ShippingCarrier.FEDEX, ShippingCarrier.UPS],
      } = request;

      const allRates: UnifiedRateResponse[] = [];
      const errors: string[] = [];

      // 并行查询所有物流商的运费
      const ratePromises = carriers
        .filter(carrier => this.isCarrierEnabled(carrier))
        .filter(carrier => this.isRouteSupported(carrier, originAddress.countryCode, destinationAddress.countryCode))
        .map(async (carrier) => {
          try {
            const rates = await this.getCarrierRates(carrier, {
              originAddress,
              destinationAddress,
              packages,
              shipDate,
              currency,
            });

            if (rates.success && rates.data) {
              return rates.data.map(rate => ({
                ...rate,
                carrier,
              }));
            } else {
              errors.push(`${carrier}: ${rates.error}`);
              return [];
            }
          } catch (error) {
            errors.push(`${carrier}: ${error instanceof Error ? error.message : '未知错误'}`);
            return [];
          }
        });

      const rateResults = await Promise.all(ratePromises);
      
      // 合并所有运费结果
      for (const rates of rateResults) {
        allRates.push(...rates);
      }

      // 按价格排序
      allRates.sort((a, b) => a.totalPrice - b.totalPrice);

      return {
        success: true,
        data: allRates,
      };
    } catch (error) {
      console.error('获取多物流商运费失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取多物流商运费失败',
      };
    }
  }

  /**
   * 创建运单
   */
  async createShipment(
    request: UnifiedShipmentRequest
  ): Promise<{ success: boolean; data?: UnifiedShipmentResponse; error?: string }> {
    try {
      const { carrier } = request;

      if (!this.isCarrierEnabled(carrier)) {
        return {
          success: false,
          error: `物流商 ${carrier} 未启用`,
        };
      }

      let result;

      switch (carrier) {
        case ShippingCarrier.DHL:
          result = await dhlService.createShipment({
            originAddress: request.originAddress,
            destinationAddress: request.destinationAddress,
            packages: request.packages,
            serviceType: request.serviceType as any,
            shipDate: request.shipDate,
            orderId: request.orderId,
            reference: request.reference,
            specialInstructions: request.specialInstructions,
            insuranceValue: request.insuranceValue,
            signatureRequired: request.signatureRequired,
          });
          break;

        case ShippingCarrier.FEDEX:
          result = await fedexService.createShipment({
            originAddress: request.originAddress,
            destinationAddress: request.destinationAddress,
            packages: request.packages,
            serviceType: request.serviceType as any,
            shipDate: request.shipDate,
            orderId: request.orderId,
            reference: request.reference,
            specialInstructions: request.specialInstructions,
            insuranceValue: request.insuranceValue,
            signatureRequired: request.signatureRequired,
          });
          break;

        case ShippingCarrier.UPS:
          result = await upsService.createShipment({
            originAddress: request.originAddress,
            destinationAddress: request.destinationAddress,
            packages: request.packages,
            serviceType: request.serviceType as any,
            shipDate: request.shipDate,
            orderId: request.orderId,
            reference: request.reference,
            specialInstructions: request.specialInstructions,
            insuranceValue: request.insuranceValue,
            signatureRequired: request.signatureRequired,
          });
          break;

        default:
          return {
            success: false,
            error: `不支持的物流商: ${carrier}`,
          };
      }

      if (result.success && result.data) {
        const unifiedResponse: UnifiedShipmentResponse = {
          carrier,
          trackingNumber: result.data.trackingNumber,
          shipmentId: result.data.shipmentId,
          labelUrl: result.data.labelUrl,
          totalCost: result.data.totalCost,
          currency: result.data.currency,
          estimatedDelivery: result.data.estimatedDelivery,
        };

        return {
          success: true,
          data: unifiedResponse,
        };
      } else {
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error('创建运单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建运单失败',
      };
    }
  }

  /**
   * 跟踪包裹
   */
  async trackShipment(
    trackingNumber: string,
    carrier?: ShippingCarrier
  ): Promise<{ success: boolean; data?: UnifiedTrackingInfo; error?: string }> {
    try {
      // 如果没有指定物流商，尝试从跟踪号识别
      if (!carrier) {
        carrier = this.identifyCarrierFromTrackingNumber(trackingNumber);
      }

      if (!carrier || !this.isCarrierEnabled(carrier)) {
        return {
          success: false,
          error: '无法识别物流商或物流商未启用',
        };
      }

      let result;

      switch (carrier) {
        case ShippingCarrier.DHL:
          result = await dhlService.trackShipment(trackingNumber);
          break;

        case ShippingCarrier.FEDEX:
          result = await fedexService.trackShipment(trackingNumber);
          break;

        case ShippingCarrier.UPS:
          result = await upsService.trackShipment(trackingNumber);
          break;

        default:
          return {
            success: false,
            error: `不支持的物流商: ${carrier}`,
          };
      }

      if (result.success && result.data) {
        const unifiedTracking: UnifiedTrackingInfo = {
          carrier,
          trackingNumber: result.data.trackingNumber,
          status: result.data.status,
          statusDescription: result.data.statusDescription,
          estimatedDelivery: result.data.estimatedDelivery,
          actualDelivery: result.data.actualDelivery,
          events: result.data.events.map(event => ({
            timestamp: event.timestamp,
            location: event.location,
            description: event.description,
            statusCode: event.statusCode,
          })),
        };

        return {
          success: true,
          data: unifiedTracking,
        };
      } else {
        return {
          success: false,
          error: result.error,
        };
      }
    } catch (error) {
      console.error('跟踪包裹失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '跟踪包裹失败',
      };
    }
  }

  /**
   * 获取最佳物流商推荐
   */
  async getBestCarrierRecommendation(
    request: UnifiedRateRequest,
    criteria: {
      prioritizePrice?: boolean;
      prioritizeSpeed?: boolean;
      prioritizeReliability?: boolean;
    } = {}
  ): Promise<{ success: boolean; data?: { carrier: ShippingCarrier; serviceType: string; reason: string }; error?: string }> {
    try {
      const ratesResult = await this.getMultiCarrierRates(request);
      
      if (!ratesResult.success || !ratesResult.data || ratesResult.data.length === 0) {
        return {
          success: false,
          error: '无法获取运费信息',
        };
      }

      const rates = ratesResult.data;
      const { prioritizePrice = true, prioritizeSpeed = false, prioritizeReliability = false } = criteria;

      let bestRate: UnifiedRateResponse;
      let reason: string;

      if (prioritizePrice) {
        bestRate = rates.reduce((min, rate) => rate.totalPrice < min.totalPrice ? rate : min);
        reason = `最低价格: ${bestRate.currency} ${bestRate.totalPrice}`;
      } else if (prioritizeSpeed) {
        bestRate = rates.reduce((fastest, rate) => rate.transitTime < fastest.transitTime ? rate : fastest);
        reason = `最快送达: ${bestRate.transitTime} 天`;
      } else if (prioritizeReliability) {
        // 基于物流商优先级选择
        bestRate = rates.reduce((best, rate) => {
          const bestPriority = this.carrierConfigs.get(best.carrier)?.priority || 999;
          const ratePriority = this.carrierConfigs.get(rate.carrier)?.priority || 999;
          return ratePriority < bestPriority ? rate : best;
        });
        reason = `最可靠物流商`;
      } else {
        // 综合评分
        bestRate = rates.reduce((best, rate) => {
          const bestScore = this.calculateCarrierScore(best);
          const rateScore = this.calculateCarrierScore(rate);
          return rateScore > bestScore ? rate : best;
        });
        reason = `综合评分最高`;
      }

      return {
        success: true,
        data: {
          carrier: bestRate.carrier,
          serviceType: bestRate.serviceType,
          reason,
        },
      };
    } catch (error) {
      console.error('获取最佳物流商推荐失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取最佳物流商推荐失败',
      };
    }
  }

  /**
   * 获取单个物流商运费
   */
  private async getCarrierRates(
    carrier: ShippingCarrier,
    request: any
  ): Promise<{ success: boolean; data?: any[]; error?: string }> {
    switch (carrier) {
      case ShippingCarrier.DHL:
        return dhlService.calculateRates(request);
      case ShippingCarrier.FEDEX:
        return fedexService.calculateRates(request);
      case ShippingCarrier.UPS:
        return upsService.calculateRates(request);
      default:
        return {
          success: false,
          error: `不支持的物流商: ${carrier}`,
        };
    }
  }

  /**
   * 检查物流商是否启用
   */
  private isCarrierEnabled(carrier: ShippingCarrier): boolean {
    const config = this.carrierConfigs.get(carrier);
    return config?.isEnabled || false;
  }

  /**
   * 检查路线是否支持
   */
  private isRouteSupported(
    carrier: ShippingCarrier,
    originCountry: string,
    destinationCountry: string
  ): boolean {
    const config = this.carrierConfigs.get(carrier);
    if (!config) return false;

    const supportedCountries = config.supportedCountries;
    
    // 如果支持所有国家
    if (supportedCountries.includes('*')) return true;
    
    // 检查具体国家支持
    return supportedCountries.includes(originCountry) && supportedCountries.includes(destinationCountry);
  }

  /**
   * 从跟踪号识别物流商
   */
  private identifyCarrierFromTrackingNumber(trackingNumber: string): ShippingCarrier | null {
    // DHL跟踪号通常是10位数字
    if (/^\d{10}$/.test(trackingNumber)) {
      return ShippingCarrier.DHL;
    }
    
    // FedEx跟踪号通常是12位数字
    if (/^\d{12}$/.test(trackingNumber)) {
      return ShippingCarrier.FEDEX;
    }
    
    // UPS跟踪号通常是1Z开头
    if (/^1Z/.test(trackingNumber)) {
      return ShippingCarrier.UPS;
    }

    return null;
  }

  /**
   * 计算物流商评分
   */
  private calculateCarrierScore(rate: UnifiedRateResponse): number {
    const config = this.carrierConfigs.get(rate.carrier);
    const priorityScore = config ? (10 - config.priority) * 10 : 0;
    const priceScore = Math.max(0, 100 - rate.totalPrice);
    const speedScore = Math.max(0, 100 - rate.transitTime * 10);
    
    return (priorityScore + priceScore + speedScore) / 3;
  }

  /**
   * 获取物流商配置
   */
  getCarrierConfig(carrier: ShippingCarrier): CarrierConfig | undefined {
    return this.carrierConfigs.get(carrier);
  }

  /**
   * 更新物流商配置
   */
  updateCarrierConfig(carrier: ShippingCarrier, config: Partial<CarrierConfig>): void {
    const existingConfig = this.carrierConfigs.get(carrier);
    if (existingConfig) {
      this.carrierConfigs.set(carrier, { ...existingConfig, ...config });
    }
  }

  /**
   * 获取所有启用的物流商
   */
  getEnabledCarriers(): ShippingCarrier[] {
    return Array.from(this.carrierConfigs.entries())
      .filter(([_, config]) => config.isEnabled)
      .map(([carrier, _]) => carrier);
  }
}

// 导出单例实例
export const shippingServiceManager = new ShippingServiceManager();
