/**
 * 库存优化服务
 * 提供库存分配优化、ABC分析、季节性调整等高级库存管理功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';

// ABC分析分类枚举
export enum ABCCategory {
  A = 'A', // 高价值商品
  B = 'B', // 中价值商品
  C = 'C', // 低价值商品
}

// 库存分配策略枚举
export enum AllocationStrategy {
  FIFO = 'FIFO',                         // 先进先出
  LIFO = 'LIFO',                         // 后进先出
  FEFO = 'FEFO',                         // 先到期先出
  PRIORITY_BASED = 'PRIORITY_BASED',     // 基于优先级
  COST_OPTIMIZED = 'COST_OPTIMIZED',     // 成本优化
}

// 库存优化建议接口
export interface OptimizationRecommendation {
  type: 'REBALANCE' | 'REDUCE' | 'INCREASE' | 'TRANSFER' | 'DISCONTINUE';
  productId: string;
  warehouseId?: string;
  currentValue: number;
  recommendedValue: number;
  reason: string;
  impact: {
    costSaving?: number;
    serviceImprovement?: number;
    riskReduction?: number;
  };
  priority: number;
}

// ABC分析结果接口
export interface ABCAnalysisResult {
  productId: string;
  category: ABCCategory;
  revenue: number;
  revenuePercentage: number;
  cumulativePercentage: number;
  quantity: number;
  unitValue: number;
  recommendations: string[];
}

// 季节性分析结果接口
export interface SeasonalityAnalysis {
  productId: string;
  seasonalityIndex: number;
  peakMonths: number[];
  lowMonths: number[];
  adjustmentFactor: number;
  recommendations: string[];
}

/**
 * 库存优化服务类
 */
export class InventoryOptimizationService {
  /**
   * 执行ABC分析
   */
  async performABCAnalysis(
    options: {
      warehouseId?: string;
      categoryId?: string;
      period?: number; // 分析期间（天数）
    } = {},
    userId: string
  ): Promise<{ success: boolean; data?: ABCAnalysisResult[]; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'inventory',
        action: 'analyze',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法执行ABC分析',
        };
      }

      const { warehouseId, categoryId, period = 365 } = options;

      // 计算分析期间的开始日期
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      // 获取商品销售数据
      const salesData = await this.getSalesDataForABC(startDate, warehouseId, categoryId);

      if (salesData.length === 0) {
        return {
          success: true,
          data: [],
        };
      }

      // 计算总收入
      const totalRevenue = salesData.reduce((sum, item) => sum + item.revenue, 0);

      // 按收入排序
      salesData.sort((a, b) => b.revenue - a.revenue);

      // 计算累计百分比并分类
      let cumulativeRevenue = 0;
      const abcResults: ABCAnalysisResult[] = [];

      for (const item of salesData) {
        cumulativeRevenue += item.revenue;
        const revenuePercentage = (item.revenue / totalRevenue) * 100;
        const cumulativePercentage = (cumulativeRevenue / totalRevenue) * 100;

        // ABC分类规则：A类80%，B类15%，C类5%
        let category: ABCCategory;
        if (cumulativePercentage <= 80) {
          category = ABCCategory.A;
        } else if (cumulativePercentage <= 95) {
          category = ABCCategory.B;
        } else {
          category = ABCCategory.C;
        }

        const recommendations = this.generateABCRecommendations(category, item);

        abcResults.push({
          productId: item.productId,
          category,
          revenue: item.revenue,
          revenuePercentage,
          cumulativePercentage,
          quantity: item.quantity,
          unitValue: item.revenue / item.quantity,
          recommendations,
        });
      }

      // 保存ABC分析结果
      await this.saveABCAnalysisResults(abcResults, userId);

      return {
        success: true,
        data: abcResults,
      };
    } catch (error) {
      console.error('执行ABC分析失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行ABC分析失败',
      };
    }
  }

  /**
   * 生成库存优化建议
   */
  async generateOptimizationRecommendations(
    options: {
      warehouseId?: string;
      maxRecommendations?: number;
    } = {},
    userId: string
  ): Promise<{ success: boolean; data?: OptimizationRecommendation[]; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'inventory',
        action: 'optimize',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法生成库存优化建议',
        };
      }

      const { warehouseId, maxRecommendations = 50 } = options;

      const recommendations: OptimizationRecommendation[] = [];

      // 1. 分析超库存商品
      const overstockRecommendations = await this.analyzeOverstock(warehouseId);
      recommendations.push(...overstockRecommendations);

      // 2. 分析滞销商品
      const slowMovingRecommendations = await this.analyzeSlowMovingItems(warehouseId);
      recommendations.push(...slowMovingRecommendations);

      // 3. 分析库存不平衡
      const rebalanceRecommendations = await this.analyzeInventoryImbalance(warehouseId);
      recommendations.push(...rebalanceRecommendations);

      // 4. 分析安全库存优化
      const safetyStockRecommendations = await this.analyzeSafetyStockOptimization(warehouseId);
      recommendations.push(...safetyStockRecommendations);

      // 按优先级排序并限制数量
      recommendations.sort((a, b) => b.priority - a.priority);
      const limitedRecommendations = recommendations.slice(0, maxRecommendations);

      return {
        success: true,
        data: limitedRecommendations,
      };
    } catch (error) {
      console.error('生成库存优化建议失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成库存优化建议失败',
      };
    }
  }

  /**
   * 执行季节性分析
   */
  async performSeasonalityAnalysis(
    productIds?: string[],
    userId?: string
  ): Promise<{ success: boolean; data?: SeasonalityAnalysis[]; error?: string }> {
    try {
      // 权限检查
      if (userId) {
        const permissionCheck = await permissionService.checkPermission({
          userId,
          resource: 'inventory',
          action: 'analyze',
        });

        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: '权限不足：无法执行季节性分析',
          };
        }
      }

      // 获取过去24个月的销售数据
      const twoYearsAgo = new Date();
      twoYearsAgo.setMonth(twoYearsAgo.getMonth() - 24);

      const whereConditions: any = {
        order: {
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
          createdAt: {
            gte: twoYearsAgo,
          },
        },
      };

      if (productIds && productIds.length > 0) {
        whereConditions.productId = { in: productIds };
      }

      const salesData = await prisma.orderItem.findMany({
        where: whereConditions,
        select: {
          productId: true,
          quantity: true,
          order: {
            select: {
              createdAt: true,
            },
          },
        },
      });

      // 按商品分组并分析季节性
      const productSalesMap = new Map<string, any[]>();
      
      for (const item of salesData) {
        if (!productSalesMap.has(item.productId)) {
          productSalesMap.set(item.productId, []);
        }
        productSalesMap.get(item.productId)!.push({
          quantity: item.quantity,
          month: item.order.createdAt.getMonth() + 1,
          year: item.order.createdAt.getFullYear(),
        });
      }

      const seasonalityResults: SeasonalityAnalysis[] = [];

      for (const [productId, sales] of productSalesMap) {
        const analysis = this.analyzeProductSeasonality(productId, sales);
        seasonalityResults.push(analysis);
      }

      return {
        success: true,
        data: seasonalityResults,
      };
    } catch (error) {
      console.error('执行季节性分析失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行季节性分析失败',
      };
    }
  }

  /**
   * 优化库存分配
   */
  async optimizeInventoryAllocation(
    orderId: string,
    strategy: AllocationStrategy = AllocationStrategy.FIFO,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'inventory',
        action: 'allocate',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法优化库存分配',
        };
      }

      // 获取订单信息
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          addresses: true,
        },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      const allocations = [];

      for (const orderItem of order.items) {
        const allocation = await this.allocateInventoryForItem(
          orderItem,
          order.addresses.find(addr => addr.type === 'SHIPPING'),
          strategy
        );
        
        if (allocation) {
          allocations.push(allocation);
        }
      }

      return {
        success: true,
        data: {
          orderId,
          strategy,
          allocations,
          totalAllocated: allocations.reduce((sum, alloc) => sum + alloc.quantity, 0),
        },
      };
    } catch (error) {
      console.error('优化库存分配失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '优化库存分配失败',
      };
    }
  }

  /**
   * 获取ABC分析的销售数据
   */
  private async getSalesDataForABC(
    startDate: Date,
    warehouseId?: string,
    categoryId?: string
  ): Promise<any[]> {
    const whereConditions: any = {
      order: {
        status: {
          in: ['DELIVERED', 'COMPLETED'],
        },
        createdAt: {
          gte: startDate,
        },
      },
    };

    if (categoryId) {
      whereConditions.product = {
        categoryId,
      };
    }

    const salesData = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: whereConditions,
      _sum: {
        quantity: true,
        totalPrice: true,
      },
    });

    return salesData.map(item => ({
      productId: item.productId,
      quantity: item._sum.quantity || 0,
      revenue: item._sum.totalPrice || 0,
    }));
  }

  /**
   * 生成ABC分类建议
   */
  private generateABCRecommendations(category: ABCCategory, item: any): string[] {
    const recommendations = [];

    switch (category) {
      case ABCCategory.A:
        recommendations.push('高价值商品，建议严格控制库存水平');
        recommendations.push('优先保证库存充足，避免缺货');
        recommendations.push('考虑实施更频繁的库存监控');
        break;

      case ABCCategory.B:
        recommendations.push('中等价值商品，采用标准库存管理策略');
        recommendations.push('定期审查库存水平和补货策略');
        break;

      case ABCCategory.C:
        recommendations.push('低价值商品，可采用较宽松的库存控制');
        recommendations.push('考虑批量采购以降低成本');
        recommendations.push('评估是否需要继续销售此商品');
        break;
    }

    return recommendations;
  }

  /**
   * 保存ABC分析结果
   */
  private async saveABCAnalysisResults(results: ABCAnalysisResult[], userId: string): Promise<void> {
    // 删除旧的分析结果
    await prisma.abcAnalysisResult.deleteMany({
      where: {
        createdAt: {
          lt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
        },
      },
    });

    // 保存新的分析结果
    for (const result of results) {
      await prisma.abcAnalysisResult.create({
        data: {
          productId: result.productId,
          category: result.category,
          revenue: result.revenue,
          revenuePercentage: result.revenuePercentage,
          cumulativePercentage: result.cumulativePercentage,
          quantity: result.quantity,
          unitValue: result.unitValue,
          recommendations: result.recommendations,
          createdBy: userId,
        },
      });
    }
  }

  /**
   * 分析超库存商品
   */
  private async analyzeOverstock(warehouseId?: string): Promise<OptimizationRecommendation[]> {
    const whereConditions: any = {
      totalStock: {
        gt: prisma.inventory.fields.maxStock,
      },
    };

    if (warehouseId) {
      whereConditions.warehouseId = warehouseId;
    }

    const overstockItems = await prisma.inventory.findMany({
      where: whereConditions,
      include: {
        product: true,
      },
    });

    return overstockItems.map(item => ({
      type: 'REDUCE' as const,
      productId: item.productId,
      warehouseId: item.warehouseId,
      currentValue: item.totalStock,
      recommendedValue: item.maxStock,
      reason: `库存超过最大库存量 ${item.maxStock - item.totalStock} 件`,
      impact: {
        costSaving: (item.totalStock - item.maxStock) * item.product.costPrice,
      },
      priority: Math.min(100, (item.totalStock - item.maxStock) / item.maxStock * 100),
    }));
  }

  /**
   * 分析滞销商品
   */
  private async analyzeSlowMovingItems(warehouseId?: string): Promise<OptimizationRecommendation[]> {
    // 获取过去90天没有销售的商品
    const ninetyDaysAgo = new Date();
    ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

    const slowMovingItems = await prisma.inventory.findMany({
      where: {
        ...(warehouseId && { warehouseId }),
        totalStock: {
          gt: 0,
        },
        product: {
          orderItems: {
            none: {
              order: {
                createdAt: {
                  gte: ninetyDaysAgo,
                },
              },
            },
          },
        },
      },
      include: {
        product: true,
      },
    });

    return slowMovingItems.map(item => ({
      type: 'REDUCE' as const,
      productId: item.productId,
      warehouseId: item.warehouseId,
      currentValue: item.totalStock,
      recommendedValue: Math.floor(item.totalStock * 0.5), // 建议减少50%
      reason: '过去90天无销售，建议减少库存或促销处理',
      impact: {
        costSaving: item.totalStock * 0.5 * item.product.costPrice,
      },
      priority: 70,
    }));
  }

  /**
   * 分析库存不平衡
   */
  private async analyzeInventoryImbalance(warehouseId?: string): Promise<OptimizationRecommendation[]> {
    // 这里可以实现多仓库间的库存平衡分析
    // 简化实现，返回空数组
    return [];
  }

  /**
   * 分析安全库存优化
   */
  private async analyzeSafetyStockOptimization(warehouseId?: string): Promise<OptimizationRecommendation[]> {
    // 分析安全库存设置是否合理
    // 简化实现，返回空数组
    return [];
  }

  /**
   * 分析商品季节性
   */
  private analyzeProductSeasonality(productId: string, sales: any[]): SeasonalityAnalysis {
    // 按月份统计销量
    const monthlySales = new Array(12).fill(0);
    
    for (const sale of sales) {
      monthlySales[sale.month - 1] += sale.quantity;
    }

    // 计算平均月销量
    const averageMonthlySales = monthlySales.reduce((sum, sales) => sum + sales, 0) / 12;

    // 计算季节性指数
    const seasonalityIndex = Math.max(...monthlySales) / Math.min(...monthlySales);

    // 找出峰值月份和低谷月份
    const peakMonths = [];
    const lowMonths = [];

    for (let i = 0; i < 12; i++) {
      if (monthlySales[i] > averageMonthlySales * 1.2) {
        peakMonths.push(i + 1);
      } else if (monthlySales[i] < averageMonthlySales * 0.8) {
        lowMonths.push(i + 1);
      }
    }

    // 计算调整因子
    const adjustmentFactor = seasonalityIndex > 2 ? 1.5 : 1.0;

    // 生成建议
    const recommendations = [];
    if (seasonalityIndex > 2) {
      recommendations.push('商品具有明显季节性，建议根据季节调整库存策略');
      recommendations.push(`在 ${peakMonths.join(', ')} 月增加库存`);
      recommendations.push(`在 ${lowMonths.join(', ')} 月减少库存`);
    } else {
      recommendations.push('商品销售相对稳定，可采用标准库存策略');
    }

    return {
      productId,
      seasonalityIndex,
      peakMonths,
      lowMonths,
      adjustmentFactor,
      recommendations,
    };
  }

  /**
   * 为订单项分配库存
   */
  private async allocateInventoryForItem(
    orderItem: any,
    shippingAddress: any,
    strategy: AllocationStrategy
  ): Promise<any | null> {
    // 获取可用库存
    const availableInventory = await prisma.inventory.findMany({
      where: {
        productId: orderItem.productId,
        availableStock: {
          gt: 0,
        },
      },
      include: {
        warehouse: true,
      },
    });

    if (availableInventory.length === 0) {
      return null;
    }

    // 根据策略排序库存
    let sortedInventory;
    
    switch (strategy) {
      case AllocationStrategy.FIFO:
        sortedInventory = availableInventory.sort((a, b) => 
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );
        break;

      case AllocationStrategy.LIFO:
        sortedInventory = availableInventory.sort((a, b) => 
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
        break;

      case AllocationStrategy.COST_OPTIMIZED:
        sortedInventory = availableInventory.sort((a, b) => 
          a.unitCost - b.unitCost
        );
        break;

      default:
        sortedInventory = availableInventory;
    }

    // 分配库存
    let remainingQuantity = orderItem.quantity;
    const allocations = [];

    for (const inventory of sortedInventory) {
      if (remainingQuantity <= 0) break;

      const allocatedQuantity = Math.min(remainingQuantity, inventory.availableStock);
      
      allocations.push({
        inventoryId: inventory.id,
        warehouseId: inventory.warehouseId,
        quantity: allocatedQuantity,
        warehouse: inventory.warehouse,
      });

      remainingQuantity -= allocatedQuantity;
    }

    return {
      orderItemId: orderItem.id,
      productId: orderItem.productId,
      requestedQuantity: orderItem.quantity,
      allocatedQuantity: orderItem.quantity - remainingQuantity,
      allocations,
      fullyAllocated: remainingQuantity === 0,
    };
  }
}

// 导出单例实例
export const inventoryOptimizationService = new InventoryOptimizationService();
