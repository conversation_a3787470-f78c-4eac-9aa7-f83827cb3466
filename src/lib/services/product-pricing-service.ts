/**
 * 商品价格管理服务
 * 提供批量价格调整、价格历史记录、动态定价等功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';

// 价格调整类型枚举
export enum PriceAdjustmentType {
  PERCENTAGE = 'PERCENTAGE',     // 百分比调整
  FIXED_AMOUNT = 'FIXED_AMOUNT', // 固定金额调整
  SET_PRICE = 'SET_PRICE',       // 设置价格
}

// 价格调整原因枚举
export enum PriceAdjustmentReason {
  PROMOTION = 'PROMOTION',           // 促销活动
  COST_CHANGE = 'COST_CHANGE',       // 成本变化
  MARKET_ADJUSTMENT = 'MARKET_ADJUSTMENT', // 市场调整
  INVENTORY_CLEARANCE = 'INVENTORY_CLEARANCE', // 库存清理
  SEASONAL = 'SEASONAL',             // 季节性调整
  COMPETITOR_MATCH = 'COMPETITOR_MATCH', // 竞争对手匹配
  MANUAL = 'MANUAL',                 // 手动调整
}

// 批量价格调整请求接口
export interface BatchPriceAdjustmentRequest {
  productIds?: string[];
  categoryIds?: string[];
  filters?: {
    priceRange?: {
      min?: number;
      max?: number;
    };
    tags?: string[];
    status?: string[];
  };
  adjustment: {
    type: PriceAdjustmentType;
    value: number;
    applyTo: 'basePrice' | 'costPrice' | 'both';
  };
  reason: PriceAdjustmentReason;
  description?: string;
  effectiveDate?: Date;
  expiryDate?: Date;
}

// 价格历史查询选项
export interface PriceHistoryOptions {
  productId?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  includeVariants?: boolean;
}

// 动态定价规则接口
export interface DynamicPricingRule {
  name: string;
  description?: string;
  conditions: {
    inventoryLevel?: {
      operator: 'lt' | 'lte' | 'gt' | 'gte';
      value: number;
    };
    salesVelocity?: {
      operator: 'lt' | 'lte' | 'gt' | 'gte';
      value: number;
      period: number; // 天数
    };
    seasonality?: {
      months: number[];
    };
    competitorPrice?: {
      operator: 'lt' | 'lte' | 'gt' | 'gte';
      percentage: number;
    };
  };
  action: {
    type: PriceAdjustmentType;
    value: number;
    maxAdjustment?: number;
    minPrice?: number;
    maxPrice?: number;
  };
  isActive: boolean;
  priority: number;
}

/**
 * 商品价格管理服务类
 */
export class ProductPricingService {
  /**
   * 批量调整价格
   */
  async batchAdjustPrices(
    request: BatchPriceAdjustmentRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'products',
        action: 'update',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法调整商品价格',
        };
      }

      // 构建查询条件
      const whereConditions: any = {
        status: 'ACTIVE',
      };

      if (request.productIds && request.productIds.length > 0) {
        whereConditions.id = { in: request.productIds };
      }

      if (request.categoryIds && request.categoryIds.length > 0) {
        whereConditions.categoryId = { in: request.categoryIds };
      }

      if (request.filters) {
        const { priceRange, tags, status } = request.filters;

        if (priceRange) {
          whereConditions.basePrice = {};
          if (priceRange.min !== undefined) {
            whereConditions.basePrice.gte = priceRange.min;
          }
          if (priceRange.max !== undefined) {
            whereConditions.basePrice.lte = priceRange.max;
          }
        }

        if (tags && tags.length > 0) {
          whereConditions.tags = {
            some: {
              tag: {
                name: { in: tags },
              },
            },
          };
        }

        if (status && status.length > 0) {
          whereConditions.status = { in: status };
        }
      }

      // 获取符合条件的商品
      const products = await prisma.product.findMany({
        where: whereConditions,
        include: {
          variants: true,
        },
      });

      if (products.length === 0) {
        return {
          success: false,
          error: '没有找到符合条件的商品',
        };
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        const updatedProducts = [];
        const priceHistories = [];

        for (const product of products) {
          const oldBasePrice = product.basePrice;
          const oldCostPrice = product.costPrice;

          let newBasePrice = oldBasePrice;
          let newCostPrice = oldCostPrice;

          // 计算新价格
          if (request.adjustment.applyTo === 'basePrice' || request.adjustment.applyTo === 'both') {
            newBasePrice = this.calculateNewPrice(
              oldBasePrice,
              request.adjustment.type,
              request.adjustment.value
            );
          }

          if (request.adjustment.applyTo === 'costPrice' || request.adjustment.applyTo === 'both') {
            newCostPrice = this.calculateNewPrice(
              oldCostPrice,
              request.adjustment.type,
              request.adjustment.value
            );
          }

          // 更新商品价格
          const updatedProduct = await tx.product.update({
            where: { id: product.id },
            data: {
              basePrice: newBasePrice,
              costPrice: newCostPrice,
              updatedBy: userId,
              updatedAt: new Date(),
            },
          });

          updatedProducts.push(updatedProduct);

          // 记录价格历史
          if (newBasePrice !== oldBasePrice) {
            priceHistories.push({
              productId: product.id,
              priceType: 'BASE_PRICE',
              oldPrice: oldBasePrice,
              newPrice: newBasePrice,
              adjustmentType: request.adjustment.type,
              adjustmentValue: request.adjustment.value,
              reason: request.reason,
              description: request.description,
              effectiveDate: request.effectiveDate || new Date(),
              expiryDate: request.expiryDate,
              createdBy: userId,
            });
          }

          if (newCostPrice !== oldCostPrice) {
            priceHistories.push({
              productId: product.id,
              priceType: 'COST_PRICE',
              oldPrice: oldCostPrice,
              newPrice: newCostPrice,
              adjustmentType: request.adjustment.type,
              adjustmentValue: request.adjustment.value,
              reason: request.reason,
              description: request.description,
              effectiveDate: request.effectiveDate || new Date(),
              expiryDate: request.expiryDate,
              createdBy: userId,
            });
          }

          // 更新变体价格（如果有）
          if (product.variants.length > 0) {
            for (const variant of product.variants) {
              const oldVariantPrice = variant.price;
              const newVariantPrice = this.calculateNewPrice(
                oldVariantPrice,
                request.adjustment.type,
                request.adjustment.value
              );

              if (newVariantPrice !== oldVariantPrice) {
                await tx.productVariant.update({
                  where: { id: variant.id },
                  data: {
                    price: newVariantPrice,
                    updatedBy: userId,
                    updatedAt: new Date(),
                  },
                });

                priceHistories.push({
                  productId: product.id,
                  variantId: variant.id,
                  priceType: 'VARIANT_PRICE',
                  oldPrice: oldVariantPrice,
                  newPrice: newVariantPrice,
                  adjustmentType: request.adjustment.type,
                  adjustmentValue: request.adjustment.value,
                  reason: request.reason,
                  description: request.description,
                  effectiveDate: request.effectiveDate || new Date(),
                  expiryDate: request.expiryDate,
                  createdBy: userId,
                });
              }
            }
          }
        }

        // 批量创建价格历史记录
        if (priceHistories.length > 0) {
          await tx.priceHistory.createMany({
            data: priceHistories,
          });
        }

        return {
          updatedCount: updatedProducts.length,
          updatedProducts,
          priceHistoryCount: priceHistories.length,
        };
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('批量调整价格失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '批量调整价格失败',
      };
    }
  }

  /**
   * 获取价格历史
   */
  async getPriceHistory(
    options: PriceHistoryOptions,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'products',
        action: 'read',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法查看价格历史',
        };
      }

      const {
        productId,
        startDate,
        endDate,
        limit = 100,
        includeVariants = true,
      } = options;

      const whereConditions: any = {};

      if (productId) {
        whereConditions.productId = productId;
      }

      if (startDate || endDate) {
        whereConditions.effectiveDate = {};
        if (startDate) {
          whereConditions.effectiveDate.gte = startDate;
        }
        if (endDate) {
          whereConditions.effectiveDate.lte = endDate;
        }
      }

      if (!includeVariants) {
        whereConditions.variantId = null;
      }

      const priceHistory = await prisma.priceHistory.findMany({
        where: whereConditions,
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
          variant: {
            select: {
              id: true,
              name: true,
              sku: true,
            },
          },
          createdByUser: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            },
          },
        },
        orderBy: {
          effectiveDate: 'desc',
        },
        take: limit,
      });

      return {
        success: true,
        data: priceHistory,
      };
    } catch (error) {
      console.error('获取价格历史失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取价格历史失败',
      };
    }
  }

  /**
   * 应用动态定价规则
   */
  async applyDynamicPricingRules(
    productIds?: string[],
    userId?: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取活跃的动态定价规则
      const rules = await prisma.dynamicPricingRule.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          priority: 'asc',
        },
      });

      if (rules.length === 0) {
        return {
          success: true,
          data: { message: '没有活跃的动态定价规则' },
        };
      }

      // 获取需要应用规则的商品
      const whereConditions: any = {
        status: 'ACTIVE',
      };

      if (productIds && productIds.length > 0) {
        whereConditions.id = { in: productIds };
      }

      const products = await prisma.product.findMany({
        where: whereConditions,
        include: {
          inventory: true,
          category: true,
        },
      });

      const adjustments = [];

      for (const product of products) {
        for (const rule of rules) {
          const shouldApply = await this.evaluatePricingRule(product, rule);
          
          if (shouldApply) {
            const currentPrice = product.basePrice;
            const newPrice = this.calculateNewPrice(
              currentPrice,
              rule.action.type,
              rule.action.value
            );

            // 应用最小/最大价格限制
            const finalPrice = Math.max(
              rule.action.minPrice || 0,
              Math.min(rule.action.maxPrice || Infinity, newPrice)
            );

            // 应用最大调整限制
            if (rule.action.maxAdjustment) {
              const maxChange = currentPrice * (rule.action.maxAdjustment / 100);
              const actualChange = Math.abs(finalPrice - currentPrice);
              
              if (actualChange > maxChange) {
                continue; // 跳过这个规则
              }
            }

            if (finalPrice !== currentPrice) {
              adjustments.push({
                productId: product.id,
                ruleName: rule.name,
                oldPrice: currentPrice,
                newPrice: finalPrice,
                reason: `动态定价规则: ${rule.name}`,
              });

              // 更新商品价格
              await prisma.product.update({
                where: { id: product.id },
                data: {
                  basePrice: finalPrice,
                  updatedBy: userId || 'system',
                  updatedAt: new Date(),
                },
              });

              // 记录价格历史
              await prisma.priceHistory.create({
                data: {
                  productId: product.id,
                  priceType: 'BASE_PRICE',
                  oldPrice: currentPrice,
                  newPrice: finalPrice,
                  adjustmentType: rule.action.type,
                  adjustmentValue: rule.action.value,
                  reason: 'DYNAMIC_PRICING',
                  description: `动态定价规则: ${rule.name}`,
                  effectiveDate: new Date(),
                  createdBy: userId || 'system',
                },
              });

              break; // 只应用第一个匹配的规则
            }
          }
        }
      }

      return {
        success: true,
        data: {
          adjustmentsCount: adjustments.length,
          adjustments,
        },
      };
    } catch (error) {
      console.error('应用动态定价规则失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '应用动态定价规则失败',
      };
    }
  }

  /**
   * 计算新价格
   */
  private calculateNewPrice(
    currentPrice: number,
    adjustmentType: PriceAdjustmentType,
    adjustmentValue: number
  ): number {
    switch (adjustmentType) {
      case PriceAdjustmentType.PERCENTAGE:
        return currentPrice * (1 + adjustmentValue / 100);
      
      case PriceAdjustmentType.FIXED_AMOUNT:
        return currentPrice + adjustmentValue;
      
      case PriceAdjustmentType.SET_PRICE:
        return adjustmentValue;
      
      default:
        return currentPrice;
    }
  }

  /**
   * 评估定价规则是否应该应用
   */
  private async evaluatePricingRule(
    product: any,
    rule: DynamicPricingRule
  ): Promise<boolean> {
    const { conditions } = rule;

    // 检查库存水平条件
    if (conditions.inventoryLevel && product.inventory.length > 0) {
      const totalStock = product.inventory.reduce(
        (sum: number, inv: any) => sum + inv.availableStock,
        0
      );
      
      const { operator, value } = conditions.inventoryLevel;
      
      switch (operator) {
        case 'lt':
          if (!(totalStock < value)) return false;
          break;
        case 'lte':
          if (!(totalStock <= value)) return false;
          break;
        case 'gt':
          if (!(totalStock > value)) return false;
          break;
        case 'gte':
          if (!(totalStock >= value)) return false;
          break;
      }
    }

    // 检查销售速度条件
    if (conditions.salesVelocity) {
      const { operator, value, period } = conditions.salesVelocity;
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - period);

      const salesData = await prisma.orderItem.aggregate({
        where: {
          productId: product.id,
          order: {
            status: {
              in: ['DELIVERED', 'COMPLETED'],
            },
            createdAt: {
              gte: startDate,
            },
          },
        },
        _sum: {
          quantity: true,
        },
      });

      const salesVelocity = (salesData._sum.quantity || 0) / period;

      switch (operator) {
        case 'lt':
          if (!(salesVelocity < value)) return false;
          break;
        case 'lte':
          if (!(salesVelocity <= value)) return false;
          break;
        case 'gt':
          if (!(salesVelocity > value)) return false;
          break;
        case 'gte':
          if (!(salesVelocity >= value)) return false;
          break;
      }
    }

    // 检查季节性条件
    if (conditions.seasonality) {
      const currentMonth = new Date().getMonth() + 1;
      if (!conditions.seasonality.months.includes(currentMonth)) {
        return false;
      }
    }

    // 检查竞争对手价格条件（这里需要外部数据源）
    if (conditions.competitorPrice) {
      // 实际实现中需要集成竞争对手价格数据
      // 这里只是示例逻辑
      const competitorPrice = await this.getCompetitorPrice(product.id);
      if (competitorPrice) {
        const { operator, percentage } = conditions.competitorPrice;
        const threshold = competitorPrice * (1 + percentage / 100);

        switch (operator) {
          case 'lt':
            if (!(product.basePrice < threshold)) return false;
            break;
          case 'lte':
            if (!(product.basePrice <= threshold)) return false;
            break;
          case 'gt':
            if (!(product.basePrice > threshold)) return false;
            break;
          case 'gte':
            if (!(product.basePrice >= threshold)) return false;
            break;
        }
      }
    }

    return true;
  }

  /**
   * 获取竞争对手价格（示例方法）
   */
  private async getCompetitorPrice(productId: string): Promise<number | null> {
    // 这里应该集成外部价格监控服务
    // 例如：爬虫数据、第三方API等
    return null;
  }

  /**
   * 创建动态定价规则
   */
  async createDynamicPricingRule(
    rule: Omit<DynamicPricingRule, 'id'>,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'products',
        action: 'manage_pricing',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法创建动态定价规则',
        };
      }

      const createdRule = await prisma.dynamicPricingRule.create({
        data: {
          ...rule,
          conditions: rule.conditions as any,
          action: rule.action as any,
          createdBy: userId,
        },
      });

      return {
        success: true,
        data: createdRule,
      };
    } catch (error) {
      console.error('创建动态定价规则失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建动态定价规则失败',
      };
    }
  }
}

// 导出单例实例
export const productPricingService = new ProductPricingService();
