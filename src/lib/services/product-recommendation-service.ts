/**
 * 商品关联推荐服务
 * 提供基于用户行为、商品属性、销售数据的智能推荐功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';

// 推荐类型枚举
export enum RecommendationType {
  FREQUENTLY_BOUGHT_TOGETHER = 'FREQUENTLY_BOUGHT_TOGETHER', // 经常一起购买
  SIMILAR_PRODUCTS = 'SIMILAR_PRODUCTS',                     // 相似商品
  CROSS_SELL = 'CROSS_SELL',                                 // 交叉销售
  UP_SELL = 'UP_SELL',                                       // 向上销售
  TRENDING = 'TRENDING',                                     // 热门商品
  PERSONALIZED = 'PERSONALIZED',                             // 个性化推荐
}

// 推荐算法枚举
export enum RecommendationAlgorithm {
  COLLABORATIVE_FILTERING = 'COLLABORATIVE_FILTERING',       // 协同过滤
  CONTENT_BASED = 'CONTENT_BASED',                          // 基于内容
  HYBRID = 'HYBRID',                                        // 混合推荐
  ASSOCIATION_RULES = 'ASSOCIATION_RULES',                  // 关联规则
  POPULARITY_BASED = 'POPULARITY_BASED',                    // 基于热度
}

// 推荐请求接口
export interface RecommendationRequest {
  productId?: string;
  customerId?: string;
  categoryId?: string;
  type: RecommendationType;
  algorithm?: RecommendationAlgorithm;
  limit?: number;
  excludeProductIds?: string[];
  minScore?: number;
}

// 推荐结果接口
export interface RecommendationResult {
  productId: string;
  score: number;
  reason: string;
  algorithm: RecommendationAlgorithm;
  product: {
    id: string;
    name: string;
    sku: string;
    basePrice: number;
    images: any[];
    category: any;
  };
}

/**
 * 商品推荐服务类
 */
export class ProductRecommendationService {
  /**
   * 获取商品推荐
   */
  async getRecommendations(
    request: RecommendationRequest,
    userId: string
  ): Promise<{ success: boolean; data?: RecommendationResult[]; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'products',
        action: 'read',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法获取商品推荐',
        };
      }

      const {
        productId,
        customerId,
        categoryId,
        type,
        algorithm = RecommendationAlgorithm.HYBRID,
        limit = 10,
        excludeProductIds = [],
        minScore = 0.1,
      } = request;

      let recommendations: RecommendationResult[] = [];

      switch (type) {
        case RecommendationType.FREQUENTLY_BOUGHT_TOGETHER:
          recommendations = await this.getFrequentlyBoughtTogether(
            productId!,
            limit,
            excludeProductIds,
            minScore
          );
          break;

        case RecommendationType.SIMILAR_PRODUCTS:
          recommendations = await this.getSimilarProducts(
            productId!,
            algorithm,
            limit,
            excludeProductIds,
            minScore
          );
          break;

        case RecommendationType.CROSS_SELL:
          recommendations = await this.getCrossSellProducts(
            productId!,
            limit,
            excludeProductIds,
            minScore
          );
          break;

        case RecommendationType.UP_SELL:
          recommendations = await this.getUpSellProducts(
            productId!,
            limit,
            excludeProductIds,
            minScore
          );
          break;

        case RecommendationType.TRENDING:
          recommendations = await this.getTrendingProducts(
            categoryId,
            limit,
            excludeProductIds
          );
          break;

        case RecommendationType.PERSONALIZED:
          recommendations = await this.getPersonalizedRecommendations(
            customerId!,
            algorithm,
            limit,
            excludeProductIds,
            minScore
          );
          break;

        default:
          return {
            success: false,
            error: '不支持的推荐类型',
          };
      }

      return {
        success: true,
        data: recommendations,
      };
    } catch (error) {
      console.error('获取商品推荐失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取商品推荐失败',
      };
    }
  }

  /**
   * 获取经常一起购买的商品
   */
  private async getFrequentlyBoughtTogether(
    productId: string,
    limit: number,
    excludeProductIds: string[],
    minScore: number
  ): Promise<RecommendationResult[]> {
    // 查找包含目标商品的订单
    const ordersWithProduct = await prisma.orderItem.findMany({
      where: {
        productId,
        order: {
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
        },
      },
      select: {
        orderId: true,
      },
    });

    const orderIds = ordersWithProduct.map(item => item.orderId);

    if (orderIds.length === 0) {
      return [];
    }

    // 查找这些订单中的其他商品
    const coOccurrenceData = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        orderId: {
          in: orderIds,
        },
        productId: {
          not: productId,
          notIn: excludeProductIds,
        },
      },
      _count: {
        orderId: true,
      },
      orderBy: {
        _count: {
          orderId: 'desc',
        },
      },
      take: limit * 2, // 获取更多数据用于计算置信度
    });

    // 计算关联度分数
    const totalOrders = orderIds.length;
    const recommendations: RecommendationResult[] = [];

    for (const item of coOccurrenceData) {
      const coOccurrenceCount = item._count.orderId;
      const confidence = coOccurrenceCount / totalOrders;

      if (confidence >= minScore) {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          include: {
            images: true,
            category: true,
          },
        });

        if (product && product.status === 'ACTIVE') {
          recommendations.push({
            productId: item.productId,
            score: confidence,
            reason: `${Math.round(confidence * 100)}% 的客户同时购买了这个商品`,
            algorithm: RecommendationAlgorithm.ASSOCIATION_RULES,
            product: {
              id: product.id,
              name: product.name,
              sku: product.sku,
              basePrice: product.basePrice,
              images: product.images,
              category: product.category,
            },
          });
        }
      }

      if (recommendations.length >= limit) {
        break;
      }
    }

    return recommendations;
  }

  /**
   * 获取相似商品
   */
  private async getSimilarProducts(
    productId: string,
    algorithm: RecommendationAlgorithm,
    limit: number,
    excludeProductIds: string[],
    minScore: number
  ): Promise<RecommendationResult[]> {
    const targetProduct = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: true,
        tags: true,
      },
    });

    if (!targetProduct) {
      return [];
    }

    // 基于分类和标签的相似度计算
    const similarProducts = await prisma.product.findMany({
      where: {
        id: {
          not: productId,
          notIn: excludeProductIds,
        },
        status: 'ACTIVE',
        OR: [
          {
            categoryId: targetProduct.categoryId,
          },
          {
            tags: {
              some: {
                tag: {
                  name: {
                    in: targetProduct.tags.map(t => t.tag.name),
                  },
                },
              },
            },
          },
        ],
      },
      include: {
        images: true,
        category: true,
        tags: {
          include: {
            tag: true,
          },
        },
      },
      take: limit * 3,
    });

    // 计算相似度分数
    const recommendations: RecommendationResult[] = [];

    for (const product of similarProducts) {
      let score = 0;

      // 分类相似度
      if (product.categoryId === targetProduct.categoryId) {
        score += 0.5;
      }

      // 标签相似度
      const commonTags = product.tags.filter(pt =>
        targetProduct.tags.some(tt => tt.tag.name === pt.tag.name)
      );
      const tagSimilarity = commonTags.length / Math.max(product.tags.length, targetProduct.tags.length, 1);
      score += tagSimilarity * 0.3;

      // 价格相似度
      const priceDiff = Math.abs(product.basePrice - targetProduct.basePrice);
      const maxPrice = Math.max(product.basePrice, targetProduct.basePrice);
      const priceSimilarity = 1 - (priceDiff / maxPrice);
      score += priceSimilarity * 0.2;

      if (score >= minScore) {
        recommendations.push({
          productId: product.id,
          score,
          reason: `基于分类、标签和价格的相似度: ${Math.round(score * 100)}%`,
          algorithm,
          product: {
            id: product.id,
            name: product.name,
            sku: product.sku,
            basePrice: product.basePrice,
            images: product.images,
            category: product.category,
          },
        });
      }
    }

    // 按分数排序并限制数量
    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }

  /**
   * 获取交叉销售商品
   */
  private async getCrossSellProducts(
    productId: string,
    limit: number,
    excludeProductIds: string[],
    minScore: number
  ): Promise<RecommendationResult[]> {
    // 获取不同分类但经常一起购买的商品
    const targetProduct = await prisma.product.findUnique({
      where: { id: productId },
      select: { categoryId: true },
    });

    if (!targetProduct) {
      return [];
    }

    // 查找包含目标商品的订单
    const ordersWithProduct = await prisma.orderItem.findMany({
      where: {
        productId,
        order: {
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
        },
      },
      select: {
        orderId: true,
      },
    });

    const orderIds = ordersWithProduct.map(item => item.orderId);

    if (orderIds.length === 0) {
      return [];
    }

    // 查找这些订单中不同分类的商品
    const crossSellData = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        orderId: {
          in: orderIds,
        },
        productId: {
          not: productId,
          notIn: excludeProductIds,
        },
        product: {
          categoryId: {
            not: targetProduct.categoryId,
          },
          status: 'ACTIVE',
        },
      },
      _count: {
        orderId: true,
      },
      orderBy: {
        _count: {
          orderId: 'desc',
        },
      },
      take: limit,
    });

    const recommendations: RecommendationResult[] = [];
    const totalOrders = orderIds.length;

    for (const item of crossSellData) {
      const coOccurrenceCount = item._count.orderId;
      const confidence = coOccurrenceCount / totalOrders;

      if (confidence >= minScore) {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          include: {
            images: true,
            category: true,
          },
        });

        if (product) {
          recommendations.push({
            productId: item.productId,
            score: confidence,
            reason: `交叉销售推荐 - ${Math.round(confidence * 100)}% 的客户也购买了此商品`,
            algorithm: RecommendationAlgorithm.ASSOCIATION_RULES,
            product: {
              id: product.id,
              name: product.name,
              sku: product.sku,
              basePrice: product.basePrice,
              images: product.images,
              category: product.category,
            },
          });
        }
      }
    }

    return recommendations;
  }

  /**
   * 获取向上销售商品
   */
  private async getUpSellProducts(
    productId: string,
    limit: number,
    excludeProductIds: string[],
    minScore: number
  ): Promise<RecommendationResult[]> {
    const targetProduct = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        category: true,
      },
    });

    if (!targetProduct) {
      return [];
    }

    // 查找同分类但价格更高的商品
    const upSellProducts = await prisma.product.findMany({
      where: {
        id: {
          not: productId,
          notIn: excludeProductIds,
        },
        categoryId: targetProduct.categoryId,
        basePrice: {
          gt: targetProduct.basePrice,
        },
        status: 'ACTIVE',
      },
      include: {
        images: true,
        category: true,
      },
      orderBy: {
        basePrice: 'asc',
      },
      take: limit,
    });

    const recommendations: RecommendationResult[] = [];

    for (const product of upSellProducts) {
      const priceRatio = product.basePrice / targetProduct.basePrice;
      const score = Math.max(0, 1 - (priceRatio - 1) / 2); // 价格越接近，分数越高

      if (score >= minScore) {
        recommendations.push({
          productId: product.id,
          score,
          reason: `升级推荐 - 更高品质的选择`,
          algorithm: RecommendationAlgorithm.CONTENT_BASED,
          product: {
            id: product.id,
            name: product.name,
            sku: product.sku,
            basePrice: product.basePrice,
            images: product.images,
            category: product.category,
          },
        });
      }
    }

    return recommendations;
  }

  /**
   * 获取热门商品
   */
  private async getTrendingProducts(
    categoryId?: string,
    limit: number = 10,
    excludeProductIds: string[] = []
  ): Promise<RecommendationResult[]> {
    // 计算最近30天的销售数据
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const trendingData = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        productId: {
          notIn: excludeProductIds,
        },
        order: {
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
          createdAt: {
            gte: thirtyDaysAgo,
          },
        },
        product: {
          status: 'ACTIVE',
          ...(categoryId && { categoryId }),
        },
      },
      _sum: {
        quantity: true,
      },
      _count: {
        orderId: true,
      },
      orderBy: {
        _sum: {
          quantity: 'desc',
        },
      },
      take: limit,
    });

    const recommendations: RecommendationResult[] = [];

    for (const item of trendingData) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        include: {
          images: true,
          category: true,
        },
      });

      if (product) {
        const totalSold = item._sum.quantity || 0;
        const orderCount = item._count.orderId;
        const score = Math.min(1, totalSold / 100); // 标准化分数

        recommendations.push({
          productId: item.productId,
          score,
          reason: `热门商品 - 最近30天售出 ${totalSold} 件`,
          algorithm: RecommendationAlgorithm.POPULARITY_BASED,
          product: {
            id: product.id,
            name: product.name,
            sku: product.sku,
            basePrice: product.basePrice,
            images: product.images,
            category: product.category,
          },
        });
      }
    }

    return recommendations;
  }

  /**
   * 获取个性化推荐
   */
  private async getPersonalizedRecommendations(
    customerId: string,
    algorithm: RecommendationAlgorithm,
    limit: number,
    excludeProductIds: string[],
    minScore: number
  ): Promise<RecommendationResult[]> {
    // 获取客户的购买历史
    const customerOrders = await prisma.orderItem.findMany({
      where: {
        order: {
          customerId,
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
        },
      },
      include: {
        product: {
          include: {
            category: true,
            tags: true,
          },
        },
      },
    });

    if (customerOrders.length === 0) {
      // 如果没有购买历史，返回热门商品
      return this.getTrendingProducts(undefined, limit, excludeProductIds);
    }

    // 分析客户偏好
    const categoryPreferences = new Map<string, number>();
    const tagPreferences = new Map<string, number>();

    for (const orderItem of customerOrders) {
      const product = orderItem.product;
      
      // 分类偏好
      if (product.categoryId) {
        categoryPreferences.set(
          product.categoryId,
          (categoryPreferences.get(product.categoryId) || 0) + orderItem.quantity
        );
      }

      // 标签偏好
      for (const productTag of product.tags) {
        tagPreferences.set(
          productTag.tag.name,
          (tagPreferences.get(productTag.tag.name) || 0) + orderItem.quantity
        );
      }
    }

    // 基于偏好推荐商品
    const preferredCategories = Array.from(categoryPreferences.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([categoryId]) => categoryId);

    const preferredTags = Array.from(tagPreferences.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .map(([tagName]) => tagName);

    const recommendedProducts = await prisma.product.findMany({
      where: {
        id: {
          notIn: [...excludeProductIds, ...customerOrders.map(o => o.productId)],
        },
        status: 'ACTIVE',
        OR: [
          {
            categoryId: {
              in: preferredCategories,
            },
          },
          {
            tags: {
              some: {
                tag: {
                  name: {
                    in: preferredTags,
                  },
                },
              },
            },
          },
        ],
      },
      include: {
        images: true,
        category: true,
        tags: {
          include: {
            tag: true,
          },
        },
      },
      take: limit * 2,
    });

    const recommendations: RecommendationResult[] = [];

    for (const product of recommendedProducts) {
      let score = 0;

      // 分类匹配分数
      if (product.categoryId && categoryPreferences.has(product.categoryId)) {
        const categoryScore = categoryPreferences.get(product.categoryId)! / 
          Math.max(...categoryPreferences.values());
        score += categoryScore * 0.6;
      }

      // 标签匹配分数
      const matchingTags = product.tags.filter(pt =>
        tagPreferences.has(pt.tag.name)
      );
      if (matchingTags.length > 0) {
        const tagScore = matchingTags.reduce((sum, tag) =>
          sum + (tagPreferences.get(tag.tag.name) || 0), 0
        ) / Math.max(...tagPreferences.values());
        score += tagScore * 0.4;
      }

      if (score >= minScore) {
        recommendations.push({
          productId: product.id,
          score,
          reason: `基于您的购买偏好推荐`,
          algorithm,
          product: {
            id: product.id,
            name: product.name,
            sku: product.sku,
            basePrice: product.basePrice,
            images: product.images,
            category: product.category,
          },
        });
      }
    }

    return recommendations
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  }
}

// 导出单例实例
export const productRecommendationService = new ProductRecommendationService();
