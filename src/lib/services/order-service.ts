/**
 * 订单管理业务逻辑服务
 * 提供订单创建、处理、履行、状态管理等核心功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';
import { rowLevelSecurity } from '@/lib/rbac/row-level-security';

// 订单状态枚举
export enum OrderStatus {
  PENDING = 'PENDING',           // 待处理
  CONFIRMED = 'CONFIRMED',       // 已确认
  PROCESSING = 'PROCESSING',     // 处理中
  SHIPPED = 'SHIPPED',           // 已发货
  DELIVERED = 'DELIVERED',       // 已送达
  CANCELLED = 'CANCELLED',       // 已取消
  REFUNDED = 'REFUNDED',         // 已退款
}

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'PENDING',           // 待支付
  PAID = 'PAID',                 // 已支付
  FAILED = 'FAILED',             // 支付失败
  REFUNDED = 'REFUNDED',         // 已退款
  PARTIAL_REFUND = 'PARTIAL_REFUND', // 部分退款
}

// 订单创建接口
export interface CreateOrderRequest {
  customerId?: string;
  customerEmail?: string;
  customerInfo?: {
    firstName?: string;
    lastName?: string;
    phone?: string;
    email: string;
  };
  items: OrderItemRequest[];
  shippingAddress: AddressRequest;
  billingAddress?: AddressRequest;
  notes?: string;
  couponCode?: string;
  paymentMethod?: string;
}

export interface OrderItemRequest {
  productId: string;
  variantId?: string;
  quantity: number;
  unitPrice?: number;
}

export interface AddressRequest {
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  postalCode: string;
  countryCode: string;
  phone?: string;
}

// 订单更新接口
export interface UpdateOrderRequest {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  trackingNumber?: string;
  notes?: string;
  internalNotes?: string;
}

// 订单查询接口
export interface OrderQueryOptions {
  page?: number;
  limit?: number;
  status?: OrderStatus[];
  paymentStatus?: PaymentStatus[];
  customerId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
  sortBy?: 'createdAt' | 'updatedAt' | 'totalAmount';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 订单管理服务类
 */
export class OrderService {
  /**
   * 创建新订单
   */
  async createOrder(
    request: CreateOrderRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'orders',
        action: 'create',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法创建订单',
        };
      }

      // 验证商品库存
      const stockValidation = await this.validateStock(request.items);
      if (!stockValidation.valid) {
        return {
          success: false,
          error: `库存不足：${stockValidation.message}`,
        };
      }

      // 计算订单金额
      const orderCalculation = await this.calculateOrderTotal(request.items, request.couponCode);

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 创建或获取客户
        let customer;
        if (request.customerId) {
          customer = await tx.customer.findUnique({
            where: { id: request.customerId },
          });
        } else if (request.customerEmail) {
          customer = await tx.customer.findUnique({
            where: { email: request.customerEmail },
          });
        }

        if (!customer && request.customerInfo) {
          customer = await tx.customer.create({
            data: {
              email: request.customerInfo.email,
              firstName: request.customerInfo.firstName,
              lastName: request.customerInfo.lastName,
              phone: request.customerInfo.phone,
              languageCode: 'zh-CN',
              currencyCode: 'CNY',
              customerGroup: 'REGULAR',
              marketingConsent: false,
            },
          });
        }

        // 生成订单号
        const orderNumber = await this.generateOrderNumber();

        // 创建订单
        const order = await tx.order.create({
          data: {
            orderNumber,
            customerId: customer?.id,
            status: OrderStatus.PENDING,
            paymentStatus: PaymentStatus.PENDING,
            currency: orderCalculation.currency,
            subtotal: orderCalculation.subtotal,
            taxAmount: orderCalculation.taxAmount,
            shippingAmount: orderCalculation.shippingAmount,
            discountAmount: orderCalculation.discountAmount,
            totalAmount: orderCalculation.totalAmount,
            notes: request.notes,
            createdBy: userId,
            updatedBy: userId,
          },
        });

        // 创建订单项
        for (const item of request.items) {
          const product = await tx.product.findUnique({
            where: { id: item.productId },
            include: { variants: true },
          });

          if (!product) {
            throw new Error(`商品不存在：${item.productId}`);
          }

          const variant = item.variantId 
            ? product.variants.find(v => v.id === item.variantId)
            : null;

          const unitPrice = item.unitPrice || variant?.price || product.basePrice;

          await tx.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId,
              variantId: item.variantId,
              sku: variant?.sku || product.sku,
              name: variant?.name || product.name,
              quantity: item.quantity,
              unitPrice,
              totalPrice: unitPrice * item.quantity,
            },
          });

          // 预留库存
          await this.reserveInventory(tx, item.productId, item.variantId, item.quantity);
        }

        // 创建收货地址
        await tx.orderAddress.create({
          data: {
            orderId: order.id,
            type: 'SHIPPING',
            firstName: request.shippingAddress.firstName,
            lastName: request.shippingAddress.lastName,
            company: request.shippingAddress.company,
            addressLine1: request.shippingAddress.addressLine1,
            addressLine2: request.shippingAddress.addressLine2,
            city: request.shippingAddress.city,
            state: request.shippingAddress.state,
            postalCode: request.shippingAddress.postalCode,
            countryCode: request.shippingAddress.countryCode,
            phone: request.shippingAddress.phone,
          },
        });

        // 创建账单地址（如果提供）
        if (request.billingAddress) {
          await tx.orderAddress.create({
            data: {
              orderId: order.id,
              type: 'BILLING',
              firstName: request.billingAddress.firstName,
              lastName: request.billingAddress.lastName,
              company: request.billingAddress.company,
              addressLine1: request.billingAddress.addressLine1,
              addressLine2: request.billingAddress.addressLine2,
              city: request.billingAddress.city,
              state: request.billingAddress.state,
              postalCode: request.billingAddress.postalCode,
              countryCode: request.billingAddress.countryCode,
              phone: request.billingAddress.phone,
            },
          });
        }

        // 记录订单历史
        await tx.orderHistory.create({
          data: {
            orderId: order.id,
            status: OrderStatus.PENDING,
            notes: '订单已创建',
            createdBy: userId,
          },
        });

        return order;
      });

      // 发送订单确认邮件
      await this.sendOrderConfirmationEmail(result.id);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('创建订单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建订单失败',
      };
    }
  }

  /**
   * 更新订单状态
   */
  async updateOrderStatus(
    orderId: string,
    updates: UpdateOrderRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'orders',
        action: 'update',
        resourceId: orderId,
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法更新订单',
        };
      }

      // 获取当前订单
      const currentOrder = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          items: true,
          addresses: true,
          history: true,
        },
      });

      if (!currentOrder) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 行级权限检查
      const rowPermissionCheck = await rowLevelSecurity.checkRowPermission(
        userId,
        'orders',
        currentOrder
      );

      if (!rowPermissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法访问此订单',
        };
      }

      // 验证状态转换
      const statusValidation = this.validateStatusTransition(
        currentOrder.status as OrderStatus,
        updates.status
      );

      if (!statusValidation.valid) {
        return {
          success: false,
          error: statusValidation.message,
        };
      }

      // 更新订单
      const result = await prisma.$transaction(async (tx) => {
        const updatedOrder = await tx.order.update({
          where: { id: orderId },
          data: {
            ...(updates.status && { status: updates.status }),
            ...(updates.paymentStatus && { paymentStatus: updates.paymentStatus }),
            ...(updates.trackingNumber && { trackingNumber: updates.trackingNumber }),
            ...(updates.notes && { notes: updates.notes }),
            ...(updates.internalNotes && { internalNotes: updates.internalNotes }),
            updatedBy: userId,
            updatedAt: new Date(),
          },
          include: {
            items: true,
            addresses: true,
            customer: true,
          },
        });

        // 记录状态变更历史
        if (updates.status && updates.status !== currentOrder.status) {
          await tx.orderHistory.create({
            data: {
              orderId,
              status: updates.status,
              notes: `订单状态从 ${currentOrder.status} 更新为 ${updates.status}`,
              createdBy: userId,
            },
          });

          // 处理状态变更的业务逻辑
          await this.handleStatusChange(tx, updatedOrder, currentOrder.status as OrderStatus, updates.status);
        }

        return updatedOrder;
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('更新订单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新订单失败',
      };
    }
  }

  /**
   * 验证库存
   */
  private async validateStock(items: OrderItemRequest[]): Promise<{ valid: boolean; message?: string }> {
    for (const item of items) {
      const inventory = await prisma.inventory.findFirst({
        where: {
          productId: item.productId,
          ...(item.variantId && { variantId: item.variantId }),
        },
      });

      if (!inventory || inventory.availableStock < item.quantity) {
        return {
          valid: false,
          message: `商品库存不足，需要 ${item.quantity} 件，可用 ${inventory?.availableStock || 0} 件`,
        };
      }
    }

    return { valid: true };
  }

  /**
   * 计算订单总额
   */
  private async calculateOrderTotal(
    items: OrderItemRequest[],
    couponCode?: string
  ): Promise<{
    subtotal: number;
    taxAmount: number;
    shippingAmount: number;
    discountAmount: number;
    totalAmount: number;
    currency: string;
  }> {
    let subtotal = 0;

    // 计算商品小计
    for (const item of items) {
      const product = await prisma.product.findUnique({
        where: { id: item.productId },
        include: { variants: true },
      });

      if (product) {
        const variant = item.variantId 
          ? product.variants.find(v => v.id === item.variantId)
          : null;
        
        const unitPrice = item.unitPrice || variant?.price || product.basePrice;
        subtotal += unitPrice * item.quantity;
      }
    }

    // 计算税费（简化版本，实际应该根据地区和商品类型计算）
    const taxRate = 0.1; // 10% 税率
    const taxAmount = subtotal * taxRate;

    // 计算运费（简化版本，实际应该根据重量、距离等计算）
    const shippingAmount = subtotal > 500 ? 0 : 50; // 满500免运费

    // 计算折扣（如果有优惠券）
    let discountAmount = 0;
    if (couponCode) {
      const coupon = await prisma.coupon.findUnique({
        where: { code: couponCode, isActive: true },
      });

      if (coupon && coupon.expiresAt > new Date()) {
        if (coupon.type === 'PERCENTAGE') {
          discountAmount = subtotal * (coupon.value / 100);
        } else if (coupon.type === 'FIXED') {
          discountAmount = coupon.value;
        }
        
        // 确保折扣不超过小计
        discountAmount = Math.min(discountAmount, subtotal);
      }
    }

    const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount;

    return {
      subtotal,
      taxAmount,
      shippingAmount,
      discountAmount,
      totalAmount,
      currency: 'CNY',
    };
  }

  /**
   * 生成订单号
   */
  private async generateOrderNumber(): Promise<string> {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // 获取当天的订单数量
    const startOfDay = new Date(year, date.getMonth(), date.getDate());
    const endOfDay = new Date(year, date.getMonth(), date.getDate() + 1);
    
    const orderCount = await prisma.order.count({
      where: {
        createdAt: {
          gte: startOfDay,
          lt: endOfDay,
        },
      },
    });

    const sequence = String(orderCount + 1).padStart(4, '0');
    return `ORD${year}${month}${day}${sequence}`;
  }

  /**
   * 预留库存
   */
  private async reserveInventory(
    tx: any,
    productId: string,
    variantId: string | undefined,
    quantity: number
  ): Promise<void> {
    await tx.inventory.updateMany({
      where: {
        productId,
        ...(variantId && { variantId }),
      },
      data: {
        reservedStock: {
          increment: quantity,
        },
        availableStock: {
          decrement: quantity,
        },
      },
    });
  }

  /**
   * 验证状态转换
   */
  private validateStatusTransition(
    currentStatus: OrderStatus,
    newStatus?: OrderStatus
  ): { valid: boolean; message?: string } {
    if (!newStatus) {
      return { valid: true };
    }

    const validTransitions: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
      [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED],
      [OrderStatus.DELIVERED]: [OrderStatus.REFUNDED],
      [OrderStatus.CANCELLED]: [],
      [OrderStatus.REFUNDED]: [],
    };

    const allowedTransitions = validTransitions[currentStatus] || [];
    
    if (!allowedTransitions.includes(newStatus)) {
      return {
        valid: false,
        message: `无法从状态 ${currentStatus} 转换到 ${newStatus}`,
      };
    }

    return { valid: true };
  }

  /**
   * 处理状态变更
   */
  private async handleStatusChange(
    tx: any,
    order: any,
    oldStatus: OrderStatus,
    newStatus: OrderStatus
  ): Promise<void> {
    switch (newStatus) {
      case OrderStatus.CONFIRMED:
        // 确认订单时的处理逻辑
        await this.handleOrderConfirmed(tx, order);
        break;
      
      case OrderStatus.SHIPPED:
        // 发货时的处理逻辑
        await this.handleOrderShipped(tx, order);
        break;
      
      case OrderStatus.DELIVERED:
        // 送达时的处理逻辑
        await this.handleOrderDelivered(tx, order);
        break;
      
      case OrderStatus.CANCELLED:
        // 取消订单时的处理逻辑
        await this.handleOrderCancelled(tx, order);
        break;
      
      case OrderStatus.REFUNDED:
        // 退款时的处理逻辑
        await this.handleOrderRefunded(tx, order);
        break;
    }
  }

  /**
   * 处理订单确认
   */
  private async handleOrderConfirmed(tx: any, order: any): Promise<void> {
    // 发送订单确认邮件
    // 通知仓库准备发货
    // 更新库存状态
  }

  /**
   * 处理订单发货
   */
  private async handleOrderShipped(tx: any, order: any): Promise<void> {
    // 发送发货通知邮件
    // 创建物流跟踪记录
    // 更新库存状态
  }

  /**
   * 处理订单送达
   */
  private async handleOrderDelivered(tx: any, order: any): Promise<void> {
    // 发送送达确认邮件
    // 更新客户统计信息
    // 触发评价邀请
  }

  /**
   * 处理订单取消
   */
  private async handleOrderCancelled(tx: any, order: any): Promise<void> {
    // 释放预留库存
    for (const item of order.items) {
      await tx.inventory.updateMany({
        where: {
          productId: item.productId,
          ...(item.variantId && { variantId: item.variantId }),
        },
        data: {
          reservedStock: {
            decrement: item.quantity,
          },
          availableStock: {
            increment: item.quantity,
          },
        },
      });
    }

    // 处理退款（如果已支付）
    if (order.paymentStatus === PaymentStatus.PAID) {
      // 触发退款流程
    }

    // 发送取消通知邮件
  }

  /**
   * 处理订单退款
   */
  private async handleOrderRefunded(tx: any, order: any): Promise<void> {
    // 处理库存退回
    // 更新财务记录
    // 发送退款确认邮件
  }

  /**
   * 发送订单确认邮件
   */
  private async sendOrderConfirmationEmail(orderId: string): Promise<void> {
    // 实现邮件发送逻辑
    console.log(`发送订单确认邮件: ${orderId}`);
  }
}

// 导出单例实例
export const orderService = new OrderService();
