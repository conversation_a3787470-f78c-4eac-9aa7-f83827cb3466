/**
 * 财务管理业务逻辑服务
 * 提供支付管理、收入管理、成本管理、发票管理、退款管理等核心功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';
import { rowLevelSecurity } from '@/lib/rbac/row-level-security';

// 支付状态枚举
export enum PaymentStatus {
  PENDING = 'PENDING',           // 待支付
  PROCESSING = 'PROCESSING',     // 处理中
  COMPLETED = 'COMPLETED',       // 已完成
  FAILED = 'FAILED',             // 失败
  CANCELLED = 'CANCELLED',       // 已取消
  REFUNDED = 'REFUNDED',         // 已退款
  PARTIAL_REFUND = 'PARTIAL_REFUND', // 部分退款
}

// 支付方式枚举
export enum PaymentMethod {
  CREDIT_CARD = 'CREDIT_CARD',   // 信用卡
  DEBIT_CARD = 'DEBIT_CARD',     // 借记卡
  BANK_TRANSFER = 'BANK_TRANSFER', // 银行转账
  ALIPAY = 'ALIPAY',             // 支付宝
  WECHAT_PAY = 'WECHAT_PAY',     // 微信支付
  PAYPAL = 'PAYPAL',             // PayPal
  CASH = 'CASH',                 // 现金
  CHECK = 'CHECK',               // 支票
}

// 交易类型枚举
export enum TransactionType {
  PAYMENT = 'PAYMENT',           // 收款
  REFUND = 'REFUND',             // 退款
  ADJUSTMENT = 'ADJUSTMENT',     // 调整
  FEE = 'FEE',                   // 手续费
  CHARGEBACK = 'CHARGEBACK',     // 拒付
}

// 发票状态枚举
export enum InvoiceStatus {
  DRAFT = 'DRAFT',               // 草稿
  SENT = 'SENT',                 // 已发送
  PAID = 'PAID',                 // 已支付
  OVERDUE = 'OVERDUE',           // 逾期
  CANCELLED = 'CANCELLED',       // 已取消
}

// 支付请求接口
export interface CreatePaymentRequest {
  orderId: string;
  amount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  description?: string;
  metadata?: Record<string, any>;
  returnUrl?: string;
  cancelUrl?: string;
}

// 退款请求接口
export interface CreateRefundRequest {
  paymentId: string;
  amount?: number; // 如果不提供则全额退款
  reason: string;
  description?: string;
  metadata?: Record<string, any>;
}

// 发票创建请求接口
export interface CreateInvoiceRequest {
  orderId?: string;
  customerId: string;
  items: InvoiceItem[];
  dueDate: Date;
  notes?: string;
  taxRate?: number;
  discountAmount?: number;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate?: number;
}

// 财务报表查询选项
export interface FinancialReportOptions {
  startDate: Date;
  endDate: Date;
  groupBy?: 'day' | 'week' | 'month' | 'year';
  currency?: string;
  paymentMethod?: PaymentMethod[];
  includeRefunds?: boolean;
}

/**
 * 财务管理服务类
 */
export class FinancialService {
  /**
   * 创建支付
   */
  async createPayment(
    request: CreatePaymentRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'payments',
        action: 'create',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法创建支付',
        };
      }

      // 验证订单
      const order = await prisma.order.findUnique({
        where: { id: request.orderId },
        include: { customer: true },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      // 检查支付金额
      if (request.amount <= 0) {
        return {
          success: false,
          error: '支付金额必须大于0',
        };
      }

      if (request.amount > order.totalAmount) {
        return {
          success: false,
          error: '支付金额不能超过订单总额',
        };
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 生成支付ID
        const paymentId = await this.generatePaymentId();

        // 创建支付记录
        const payment = await tx.payment.create({
          data: {
            id: paymentId,
            orderId: request.orderId,
            customerId: order.customerId,
            amount: request.amount,
            currency: request.currency,
            paymentMethod: request.paymentMethod,
            status: PaymentStatus.PENDING,
            description: request.description,
            metadata: request.metadata,
            returnUrl: request.returnUrl,
            cancelUrl: request.cancelUrl,
            createdBy: userId,
          },
        });

        // 创建交易记录
        await tx.transaction.create({
          data: {
            paymentId: payment.id,
            type: TransactionType.PAYMENT,
            amount: request.amount,
            currency: request.currency,
            status: PaymentStatus.PENDING,
            description: `订单 ${order.orderNumber} 的支付`,
            createdBy: userId,
          },
        });

        // 根据支付方式处理支付
        const paymentResult = await this.processPayment(payment, request);

        if (!paymentResult.success) {
          throw new Error(paymentResult.error);
        }

        return {
          payment,
          paymentResult: paymentResult.data,
        };
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('创建支付失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建支付失败',
      };
    }
  }

  /**
   * 处理支付回调
   */
  async handlePaymentCallback(
    paymentId: string,
    status: PaymentStatus,
    transactionId?: string,
    metadata?: Record<string, any>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const result = await prisma.$transaction(async (tx) => {
        // 获取支付记录
        const payment = await tx.payment.findUnique({
          where: { id: paymentId },
          include: { order: true },
        });

        if (!payment) {
          throw new Error('支付记录不存在');
        }

        // 更新支付状态
        const updatedPayment = await tx.payment.update({
          where: { id: paymentId },
          data: {
            status,
            transactionId,
            metadata: {
              ...payment.metadata,
              ...metadata,
            },
            completedAt: status === PaymentStatus.COMPLETED ? new Date() : null,
          },
        });

        // 更新交易记录
        await tx.transaction.updateMany({
          where: {
            paymentId,
            type: TransactionType.PAYMENT,
          },
          data: {
            status,
            transactionId,
            completedAt: status === PaymentStatus.COMPLETED ? new Date() : null,
          },
        });

        // 如果支付成功，更新订单状态
        if (status === PaymentStatus.COMPLETED) {
          await tx.order.update({
            where: { id: payment.orderId },
            data: {
              paymentStatus: 'PAID',
              paidAt: new Date(),
            },
          });

          // 创建收入记录
          await this.createRevenueRecord(tx, payment);
        }

        return updatedPayment;
      });

      return { success: true };
    } catch (error) {
      console.error('处理支付回调失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '处理支付回调失败',
      };
    }
  }

  /**
   * 创建退款
   */
  async createRefund(
    request: CreateRefundRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'payments',
        action: 'refund',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法创建退款',
        };
      }

      // 获取原支付记录
      const payment = await prisma.payment.findUnique({
        where: { id: request.paymentId },
        include: { order: true, refunds: true },
      });

      if (!payment) {
        return {
          success: false,
          error: '支付记录不存在',
        };
      }

      if (payment.status !== PaymentStatus.COMPLETED) {
        return {
          success: false,
          error: '只能对已完成的支付进行退款',
        };
      }

      // 计算退款金额
      const refundAmount = request.amount || payment.amount;
      const totalRefunded = payment.refunds.reduce((sum, refund) => sum + refund.amount, 0);
      const availableAmount = payment.amount - totalRefunded;

      if (refundAmount > availableAmount) {
        return {
          success: false,
          error: '退款金额超过可退款金额',
        };
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 生成退款ID
        const refundId = await this.generateRefundId();

        // 创建退款记录
        const refund = await tx.refund.create({
          data: {
            id: refundId,
            paymentId: request.paymentId,
            amount: refundAmount,
            currency: payment.currency,
            reason: request.reason,
            description: request.description,
            status: PaymentStatus.PENDING,
            metadata: request.metadata,
            createdBy: userId,
          },
        });

        // 创建交易记录
        await tx.transaction.create({
          data: {
            refundId: refund.id,
            type: TransactionType.REFUND,
            amount: -refundAmount, // 负数表示退款
            currency: payment.currency,
            status: PaymentStatus.PENDING,
            description: `退款: ${request.reason}`,
            createdBy: userId,
          },
        });

        // 处理退款
        const refundResult = await this.processRefund(refund, payment);

        if (!refundResult.success) {
          throw new Error(refundResult.error);
        }

        // 更新支付状态
        const newTotalRefunded = totalRefunded + refundAmount;
        const newPaymentStatus = newTotalRefunded >= payment.amount 
          ? PaymentStatus.REFUNDED 
          : PaymentStatus.PARTIAL_REFUND;

        await tx.payment.update({
          where: { id: request.paymentId },
          data: { status: newPaymentStatus },
        });

        return refund;
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('创建退款失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建退款失败',
      };
    }
  }

  /**
   * 创建发票
   */
  async createInvoice(
    request: CreateInvoiceRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'invoices',
        action: 'create',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法创建发票',
        };
      }

      // 验证客户
      const customer = await prisma.customer.findUnique({
        where: { id: request.customerId },
      });

      if (!customer) {
        return {
          success: false,
          error: '客户不存在',
        };
      }

      // 计算发票金额
      const subtotal = request.items.reduce(
        (sum, item) => sum + (item.quantity * item.unitPrice),
        0
      );
      const taxAmount = request.items.reduce(
        (sum, item) => sum + (item.quantity * item.unitPrice * (item.taxRate || request.taxRate || 0)),
        0
      );
      const discountAmount = request.discountAmount || 0;
      const totalAmount = subtotal + taxAmount - discountAmount;

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 生成发票号
        const invoiceNumber = await this.generateInvoiceNumber();

        // 创建发票
        const invoice = await tx.invoice.create({
          data: {
            invoiceNumber,
            orderId: request.orderId,
            customerId: request.customerId,
            subtotal,
            taxAmount,
            discountAmount,
            totalAmount,
            currency: 'CNY',
            status: InvoiceStatus.DRAFT,
            dueDate: request.dueDate,
            notes: request.notes,
            createdBy: userId,
          },
        });

        // 创建发票项目
        for (const item of request.items) {
          await tx.invoiceItem.create({
            data: {
              invoiceId: invoice.id,
              description: item.description,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              taxRate: item.taxRate || request.taxRate || 0,
              totalPrice: item.quantity * item.unitPrice,
            },
          });
        }

        return invoice;
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('创建发票失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建发票失败',
      };
    }
  }

  /**
   * 生成财务报表
   */
  async generateFinancialReport(
    options: FinancialReportOptions,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'reports',
        action: 'read',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法查看财务报表',
        };
      }

      const { startDate, endDate, groupBy = 'day', currency = 'CNY' } = options;

      // 收入统计
      const revenueStats = await this.getRevenueStats(startDate, endDate, groupBy, currency);
      
      // 支出统计
      const expenseStats = await this.getExpenseStats(startDate, endDate, groupBy, currency);
      
      // 支付方式统计
      const paymentMethodStats = await this.getPaymentMethodStats(startDate, endDate, currency);
      
      // 退款统计
      const refundStats = await this.getRefundStats(startDate, endDate, currency);

      const report = {
        period: {
          startDate,
          endDate,
          groupBy,
        },
        summary: {
          totalRevenue: revenueStats.total,
          totalExpenses: expenseStats.total,
          netIncome: revenueStats.total - expenseStats.total,
          totalRefunds: refundStats.total,
          currency,
        },
        revenue: revenueStats.data,
        expenses: expenseStats.data,
        paymentMethods: paymentMethodStats,
        refunds: refundStats.data,
        generatedAt: new Date(),
        generatedBy: userId,
      };

      return {
        success: true,
        data: report,
      };
    } catch (error) {
      console.error('生成财务报表失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成财务报表失败',
      };
    }
  }

  /**
   * 处理支付
   */
  private async processPayment(
    payment: any,
    request: CreatePaymentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // 这里应该集成具体的支付网关
    // 例如：Stripe, PayPal, 支付宝, 微信支付等
    
    switch (request.paymentMethod) {
      case PaymentMethod.CREDIT_CARD:
      case PaymentMethod.DEBIT_CARD:
        return this.processCreditCardPayment(payment, request);
      
      case PaymentMethod.ALIPAY:
        return this.processAlipayPayment(payment, request);
      
      case PaymentMethod.WECHAT_PAY:
        return this.processWechatPayment(payment, request);
      
      case PaymentMethod.BANK_TRANSFER:
        return this.processBankTransferPayment(payment, request);
      
      default:
        return {
          success: false,
          error: '不支持的支付方式',
        };
    }
  }

  /**
   * 处理信用卡支付
   */
  private async processCreditCardPayment(
    payment: any,
    request: CreatePaymentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // 模拟信用卡支付处理
    // 实际实现应该调用支付网关API
    
    return {
      success: true,
      data: {
        paymentUrl: `https://payment.gateway.com/pay/${payment.id}`,
        transactionId: `txn_${Date.now()}`,
      },
    };
  }

  /**
   * 处理支付宝支付
   */
  private async processAlipayPayment(
    payment: any,
    request: CreatePaymentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // 模拟支付宝支付处理
    return {
      success: true,
      data: {
        paymentUrl: `https://openapi.alipay.com/gateway.do?${payment.id}`,
        qrCode: `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==`,
      },
    };
  }

  /**
   * 处理微信支付
   */
  private async processWechatPayment(
    payment: any,
    request: CreatePaymentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // 模拟微信支付处理
    return {
      success: true,
      data: {
        prepayId: `prepay_id_${Date.now()}`,
        qrCode: `weixin://wxpay/bizpayurl?pr=${payment.id}`,
      },
    };
  }

  /**
   * 处理银行转账支付
   */
  private async processBankTransferPayment(
    payment: any,
    request: CreatePaymentRequest
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    // 银行转账通常需要手动确认
    return {
      success: true,
      data: {
        bankAccount: {
          accountName: 'CBEC ERP系统',
          accountNumber: '****************',
          bankName: '中国银行',
          routingNumber: '************',
        },
        reference: payment.id,
      },
    };
  }

  /**
   * 处理退款
   */
  private async processRefund(
    refund: any,
    payment: any
  ): Promise<{ success: boolean; error?: string }> {
    // 这里应该调用相应支付网关的退款API
    // 模拟退款处理
    
    return { success: true };
  }

  /**
   * 创建收入记录
   */
  private async createRevenueRecord(tx: any, payment: any): Promise<void> {
    await tx.revenue.create({
      data: {
        paymentId: payment.id,
        orderId: payment.orderId,
        amount: payment.amount,
        currency: payment.currency,
        recordDate: new Date(),
        category: 'SALES',
        description: `订单收入`,
      },
    });
  }

  /**
   * 获取收入统计
   */
  private async getRevenueStats(
    startDate: Date,
    endDate: Date,
    groupBy: string,
    currency: string
  ): Promise<{ total: number; data: any[] }> {
    // 实现收入统计逻辑
    return { total: 0, data: [] };
  }

  /**
   * 获取支出统计
   */
  private async getExpenseStats(
    startDate: Date,
    endDate: Date,
    groupBy: string,
    currency: string
  ): Promise<{ total: number; data: any[] }> {
    // 实现支出统计逻辑
    return { total: 0, data: [] };
  }

  /**
   * 获取支付方式统计
   */
  private async getPaymentMethodStats(
    startDate: Date,
    endDate: Date,
    currency: string
  ): Promise<any[]> {
    // 实现支付方式统计逻辑
    return [];
  }

  /**
   * 获取退款统计
   */
  private async getRefundStats(
    startDate: Date,
    endDate: Date,
    currency: string
  ): Promise<{ total: number; data: any[] }> {
    // 实现退款统计逻辑
    return { total: 0, data: [] };
  }

  /**
   * 生成支付ID
   */
  private async generatePaymentId(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `pay_${timestamp}_${random}`;
  }

  /**
   * 生成退款ID
   */
  private async generateRefundId(): Promise<string> {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `ref_${timestamp}_${random}`;
  }

  /**
   * 生成发票号
   */
  private async generateInvoiceNumber(): Promise<string> {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    
    const count = await prisma.invoice.count({
      where: {
        createdAt: {
          gte: new Date(year, date.getMonth(), 1),
          lt: new Date(year, date.getMonth() + 1, 1),
        },
      },
    });

    const sequence = String(count + 1).padStart(4, '0');
    return `INV${year}${month}${sequence}`;
  }
}

// 导出单例实例
export const financialService = new FinancialService();
