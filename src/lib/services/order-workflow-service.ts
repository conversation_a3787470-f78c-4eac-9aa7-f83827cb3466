/**
 * 订单工作流服务
 * 提供订单状态自动化、批量处理、订单分配、SLA管理等功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';
import { orderService, OrderStatus, PaymentStatus } from './order-service';

// 工作流状态枚举
export enum WorkflowStatus {
  PENDING = 'PENDING',           // 待处理
  IN_PROGRESS = 'IN_PROGRESS',   // 进行中
  COMPLETED = 'COMPLETED',       // 已完成
  FAILED = 'FAILED',             // 失败
  CANCELLED = 'CANCELLED',       // 已取消
}

// 工作流动作类型枚举
export enum WorkflowActionType {
  AUTO_CONFIRM = 'AUTO_CONFIRM',         // 自动确认
  AUTO_ALLOCATE = 'AUTO_ALLOCATE',       // 自动分配
  AUTO_SHIP = 'AUTO_SHIP',               // 自动发货
  SEND_NOTIFICATION = 'SEND_NOTIFICATION', // 发送通知
  UPDATE_INVENTORY = 'UPDATE_INVENTORY',   // 更新库存
  GENERATE_INVOICE = 'GENERATE_INVOICE',   // 生成发票
  PAYMENT_REMINDER = 'PAYMENT_REMINDER',   // 支付提醒
  ESCALATE = 'ESCALATE',                   // 升级处理
}

// 订单分配策略枚举
export enum OrderAllocationStrategy {
  ROUND_ROBIN = 'ROUND_ROBIN',           // 轮询分配
  WORKLOAD_BASED = 'WORKLOAD_BASED',     // 基于工作量
  SKILL_BASED = 'SKILL_BASED',           // 基于技能
  GEOGRAPHIC = 'GEOGRAPHIC',             // 基于地理位置
  RANDOM = 'RANDOM',                     // 随机分配
}

// SLA规则接口
export interface SLARule {
  name: string;
  description?: string;
  conditions: {
    orderValue?: {
      min?: number;
      max?: number;
    };
    customerTier?: string[];
    paymentMethod?: string[];
    shippingMethod?: string[];
    productCategory?: string[];
  };
  targets: {
    confirmationTime?: number;    // 确认时间（分钟）
    processingTime?: number;      // 处理时间（小时）
    shippingTime?: number;        // 发货时间（小时）
    deliveryTime?: number;        // 配送时间（天）
  };
  escalationRules: {
    warningThreshold: number;     // 警告阈值（百分比）
    escalationThreshold: number;  // 升级阈值（百分比）
    escalationTo: string[];       // 升级到的用户ID
  };
  isActive: boolean;
  priority: number;
}

// 工作流规则接口
export interface WorkflowRule {
  name: string;
  description?: string;
  trigger: {
    event: 'ORDER_CREATED' | 'ORDER_UPDATED' | 'PAYMENT_RECEIVED' | 'INVENTORY_UPDATED';
    conditions?: {
      orderStatus?: OrderStatus[];
      paymentStatus?: PaymentStatus[];
      orderValue?: {
        min?: number;
        max?: number;
      };
      customerTier?: string[];
    };
  };
  actions: {
    type: WorkflowActionType;
    parameters?: Record<string, any>;
    delay?: number; // 延迟执行（分钟）
  }[];
  isActive: boolean;
  priority: number;
}

// 批量操作请求接口
export interface BatchOrderOperationRequest {
  orderIds: string[];
  operation: {
    type: 'UPDATE_STATUS' | 'ASSIGN_USER' | 'ADD_TAGS' | 'SEND_NOTIFICATION' | 'GENERATE_INVOICE';
    parameters: Record<string, any>;
  };
  reason?: string;
  scheduleAt?: Date;
}

/**
 * 订单工作流服务类
 */
export class OrderWorkflowService {
  /**
   * 执行批量订单操作
   */
  async executeBatchOperation(
    request: BatchOrderOperationRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'orders',
        action: 'batch_update',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法执行批量订单操作',
        };
      }

      const { orderIds, operation, reason, scheduleAt } = request;

      // 验证订单存在性和权限
      const orders = await prisma.order.findMany({
        where: {
          id: { in: orderIds },
        },
        include: {
          customer: true,
          items: true,
        },
      });

      if (orders.length !== orderIds.length) {
        return {
          success: false,
          error: '部分订单不存在或无权限访问',
        };
      }

      // 如果设置了调度时间，创建调度任务
      if (scheduleAt && scheduleAt > new Date()) {
        const scheduledTask = await prisma.scheduledTask.create({
          data: {
            type: 'BATCH_ORDER_OPERATION',
            parameters: {
              orderIds,
              operation,
              reason,
              userId,
            },
            scheduledAt: scheduleAt,
            status: 'PENDING',
            createdBy: userId,
          },
        });

        return {
          success: true,
          data: {
            message: '批量操作已调度',
            taskId: scheduledTask.id,
            scheduledAt,
          },
        };
      }

      // 立即执行操作
      const result = await this.performBatchOperation(orders, operation, userId, reason);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('执行批量订单操作失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行批量订单操作失败',
      };
    }
  }

  /**
   * 自动分配订单
   */
  async autoAllocateOrders(
    orderIds?: string[],
    strategy: OrderAllocationStrategy = OrderAllocationStrategy.WORKLOAD_BASED,
    userId?: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取待分配的订单
      const whereConditions: any = {
        status: OrderStatus.PENDING,
        assignedTo: null,
      };

      if (orderIds && orderIds.length > 0) {
        whereConditions.id = { in: orderIds };
      }

      const orders = await prisma.order.findMany({
        where: whereConditions,
        include: {
          customer: true,
          addresses: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      if (orders.length === 0) {
        return {
          success: true,
          data: { message: '没有需要分配的订单' },
        };
      }

      // 获取可分配的用户
      const availableUsers = await this.getAvailableUsers(strategy);

      if (availableUsers.length === 0) {
        return {
          success: false,
          error: '没有可用的用户进行订单分配',
        };
      }

      const allocations = [];

      for (const order of orders) {
        const assignedUser = await this.selectUserForOrder(order, availableUsers, strategy);
        
        if (assignedUser) {
          // 分配订单
          await prisma.order.update({
            where: { id: order.id },
            data: {
              assignedTo: assignedUser.id,
              assignedAt: new Date(),
              updatedBy: userId || 'system',
            },
          });

          // 记录分配历史
          await prisma.orderHistory.create({
            data: {
              orderId: order.id,
              status: order.status,
              notes: `订单已分配给 ${assignedUser.firstName} ${assignedUser.lastName}`,
              createdBy: userId || 'system',
            },
          });

          allocations.push({
            orderId: order.id,
            orderNumber: order.orderNumber,
            assignedTo: {
              id: assignedUser.id,
              name: `${assignedUser.firstName} ${assignedUser.lastName}`,
              email: assignedUser.email,
            },
          });

          // 发送分配通知
          await this.sendOrderAssignmentNotification(order, assignedUser);
        }
      }

      return {
        success: true,
        data: {
          allocatedCount: allocations.length,
          allocations,
        },
      };
    } catch (error) {
      console.error('自动分配订单失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '自动分配订单失败',
      };
    }
  }

  /**
   * 检查SLA合规性
   */
  async checkSLACompliance(
    orderIds?: string[]
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取活跃的SLA规则
      const slaRules = await prisma.slaRule.findMany({
        where: { isActive: true },
        orderBy: { priority: 'asc' },
      });

      if (slaRules.length === 0) {
        return {
          success: true,
          data: { message: '没有配置SLA规则' },
        };
      }

      // 获取需要检查的订单
      const whereConditions: any = {
        status: {
          notIn: [OrderStatus.CANCELLED, OrderStatus.DELIVERED],
        },
      };

      if (orderIds && orderIds.length > 0) {
        whereConditions.id = { in: orderIds };
      }

      const orders = await prisma.order.findMany({
        where: whereConditions,
        include: {
          customer: true,
          items: {
            include: {
              product: {
                include: {
                  category: true,
                },
              },
            },
          },
          addresses: true,
          history: true,
        },
      });

      const complianceResults = [];

      for (const order of orders) {
        // 找到适用的SLA规则
        const applicableRule = this.findApplicableSLARule(order, slaRules);
        
        if (applicableRule) {
          const compliance = await this.evaluateOrderSLA(order, applicableRule);
          complianceResults.push({
            orderId: order.id,
            orderNumber: order.orderNumber,
            slaRule: applicableRule.name,
            compliance,
          });

          // 如果违反SLA，触发升级
          if (compliance.isViolated || compliance.isAtRisk) {
            await this.handleSLAViolation(order, applicableRule, compliance);
          }
        }
      }

      return {
        success: true,
        data: {
          checkedCount: complianceResults.length,
          results: complianceResults,
        },
      };
    } catch (error) {
      console.error('检查SLA合规性失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '检查SLA合规性失败',
      };
    }
  }

  /**
   * 执行工作流规则
   */
  async executeWorkflowRules(
    event: string,
    orderId: string,
    context?: Record<string, any>
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 获取适用的工作流规则
      const workflowRules = await prisma.workflowRule.findMany({
        where: {
          isActive: true,
          'trigger.event': event,
        },
        orderBy: { priority: 'asc' },
      });

      if (workflowRules.length === 0) {
        return {
          success: true,
          data: { message: '没有适用的工作流规则' },
        };
      }

      // 获取订单信息
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: {
          customer: true,
          items: {
            include: {
              product: true,
            },
          },
        },
      });

      if (!order) {
        return {
          success: false,
          error: '订单不存在',
        };
      }

      const executedActions = [];

      for (const rule of workflowRules) {
        // 检查规则条件
        if (this.evaluateWorkflowConditions(order, rule.trigger.conditions, context)) {
          // 执行规则动作
          for (const action of rule.actions) {
            try {
              const result = await this.executeWorkflowAction(order, action);
              executedActions.push({
                rule: rule.name,
                action: action.type,
                result,
              });
            } catch (actionError) {
              console.error(`执行工作流动作失败: ${action.type}`, actionError);
              executedActions.push({
                rule: rule.name,
                action: action.type,
                error: actionError instanceof Error ? actionError.message : '执行失败',
              });
            }
          }
        }
      }

      return {
        success: true,
        data: {
          executedActionsCount: executedActions.length,
          actions: executedActions,
        },
      };
    } catch (error) {
      console.error('执行工作流规则失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '执行工作流规则失败',
      };
    }
  }

  /**
   * 执行批量操作
   */
  private async performBatchOperation(
    orders: any[],
    operation: any,
    userId: string,
    reason?: string
  ): Promise<any> {
    const results = [];

    for (const order of orders) {
      try {
        let result;

        switch (operation.type) {
          case 'UPDATE_STATUS':
            result = await this.updateOrderStatus(order.id, operation.parameters.status, userId);
            break;

          case 'ASSIGN_USER':
            result = await this.assignOrderToUser(order.id, operation.parameters.userId, userId);
            break;

          case 'ADD_TAGS':
            result = await this.addOrderTags(order.id, operation.parameters.tags, userId);
            break;

          case 'SEND_NOTIFICATION':
            result = await this.sendOrderNotification(order, operation.parameters.template, operation.parameters.recipients);
            break;

          case 'GENERATE_INVOICE':
            result = await this.generateOrderInvoice(order.id, userId);
            break;

          default:
            throw new Error(`不支持的操作类型: ${operation.type}`);
        }

        results.push({
          orderId: order.id,
          orderNumber: order.orderNumber,
          success: true,
          result,
        });
      } catch (error) {
        results.push({
          orderId: order.id,
          orderNumber: order.orderNumber,
          success: false,
          error: error instanceof Error ? error.message : '操作失败',
        });
      }
    }

    return {
      totalCount: orders.length,
      successCount: results.filter(r => r.success).length,
      failureCount: results.filter(r => !r.success).length,
      results,
    };
  }

  /**
   * 获取可用用户
   */
  private async getAvailableUsers(strategy: OrderAllocationStrategy): Promise<any[]> {
    const baseQuery = {
      where: {
        isActive: true,
        userRoles: {
          some: {
            role: {
              name: {
                in: ['order_manager', 'sales_rep', 'customer_service'],
              },
            },
          },
        },
      },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    };

    switch (strategy) {
      case OrderAllocationStrategy.WORKLOAD_BASED:
        // 获取当前工作量较少的用户
        return prisma.user.findMany({
          ...baseQuery,
          orderBy: {
            assignedOrders: {
              _count: 'asc',
            },
          },
        });

      case OrderAllocationStrategy.SKILL_BASED:
        // 基于技能匹配（这里简化为角色匹配）
        return prisma.user.findMany(baseQuery);

      case OrderAllocationStrategy.ROUND_ROBIN:
      case OrderAllocationStrategy.RANDOM:
      default:
        return prisma.user.findMany(baseQuery);
    }
  }

  /**
   * 为订单选择用户
   */
  private async selectUserForOrder(
    order: any,
    availableUsers: any[],
    strategy: OrderAllocationStrategy
  ): Promise<any | null> {
    if (availableUsers.length === 0) {
      return null;
    }

    switch (strategy) {
      case OrderAllocationStrategy.ROUND_ROBIN:
        // 轮询分配（简化实现）
        const lastAssignedIndex = await this.getLastAssignedUserIndex();
        const nextIndex = (lastAssignedIndex + 1) % availableUsers.length;
        await this.updateLastAssignedUserIndex(nextIndex);
        return availableUsers[nextIndex];

      case OrderAllocationStrategy.WORKLOAD_BASED:
        // 选择工作量最少的用户
        return availableUsers[0]; // 已经按工作量排序

      case OrderAllocationStrategy.SKILL_BASED:
        // 基于技能匹配（简化实现）
        return this.findBestSkillMatch(order, availableUsers);

      case OrderAllocationStrategy.GEOGRAPHIC:
        // 基于地理位置（简化实现）
        return this.findClosestUser(order, availableUsers);

      case OrderAllocationStrategy.RANDOM:
        // 随机分配
        const randomIndex = Math.floor(Math.random() * availableUsers.length);
        return availableUsers[randomIndex];

      default:
        return availableUsers[0];
    }
  }

  /**
   * 查找适用的SLA规则
   */
  private findApplicableSLARule(order: any, slaRules: any[]): any | null {
    for (const rule of slaRules) {
      if (this.evaluateSLAConditions(order, rule.conditions)) {
        return rule;
      }
    }
    return null;
  }

  /**
   * 评估SLA条件
   */
  private evaluateSLAConditions(order: any, conditions: any): boolean {
    // 检查订单金额
    if (conditions.orderValue) {
      if (conditions.orderValue.min && order.totalAmount < conditions.orderValue.min) {
        return false;
      }
      if (conditions.orderValue.max && order.totalAmount > conditions.orderValue.max) {
        return false;
      }
    }

    // 检查客户等级
    if (conditions.customerTier && conditions.customerTier.length > 0) {
      if (!conditions.customerTier.includes(order.customer.customerGroup)) {
        return false;
      }
    }

    // 其他条件检查...

    return true;
  }

  /**
   * 评估订单SLA
   */
  private async evaluateOrderSLA(order: any, slaRule: any): Promise<any> {
    const now = new Date();
    const orderCreatedAt = new Date(order.createdAt);
    const elapsedMinutes = (now.getTime() - orderCreatedAt.getTime()) / (1000 * 60);

    const compliance = {
      isCompliant: true,
      isAtRisk: false,
      isViolated: false,
      violations: [],
      warnings: [],
    };

    // 检查确认时间
    if (slaRule.targets.confirmationTime && order.status === OrderStatus.PENDING) {
      const targetMinutes = slaRule.targets.confirmationTime;
      const warningThreshold = targetMinutes * (slaRule.escalationRules.warningThreshold / 100);
      const violationThreshold = targetMinutes;

      if (elapsedMinutes > violationThreshold) {
        compliance.isViolated = true;
        compliance.isCompliant = false;
        compliance.violations.push({
          type: 'CONFIRMATION_TIME',
          target: targetMinutes,
          actual: elapsedMinutes,
          message: `订单确认超时 ${Math.round(elapsedMinutes - targetMinutes)} 分钟`,
        });
      } else if (elapsedMinutes > warningThreshold) {
        compliance.isAtRisk = true;
        compliance.warnings.push({
          type: 'CONFIRMATION_TIME',
          target: targetMinutes,
          actual: elapsedMinutes,
          message: `订单确认即将超时`,
        });
      }
    }

    // 其他SLA目标检查...

    return compliance;
  }

  /**
   * 处理SLA违规
   */
  private async handleSLAViolation(order: any, slaRule: any, compliance: any): Promise<void> {
    // 创建SLA违规记录
    await prisma.slaViolation.create({
      data: {
        orderId: order.id,
        slaRuleId: slaRule.id,
        violationType: compliance.isViolated ? 'VIOLATION' : 'WARNING',
        details: compliance,
        createdAt: new Date(),
      },
    });

    // 发送升级通知
    if (compliance.isViolated && slaRule.escalationRules.escalationTo.length > 0) {
      await this.sendSLAEscalationNotification(order, slaRule, compliance);
    }
  }

  /**
   * 评估工作流条件
   */
  private evaluateWorkflowConditions(order: any, conditions: any, context?: any): boolean {
    if (!conditions) return true;

    // 检查订单状态
    if (conditions.orderStatus && conditions.orderStatus.length > 0) {
      if (!conditions.orderStatus.includes(order.status)) {
        return false;
      }
    }

    // 检查支付状态
    if (conditions.paymentStatus && conditions.paymentStatus.length > 0) {
      if (!conditions.paymentStatus.includes(order.paymentStatus)) {
        return false;
      }
    }

    // 其他条件检查...

    return true;
  }

  /**
   * 执行工作流动作
   */
  private async executeWorkflowAction(order: any, action: any): Promise<any> {
    switch (action.type) {
      case WorkflowActionType.AUTO_CONFIRM:
        return this.autoConfirmOrder(order.id);

      case WorkflowActionType.AUTO_ALLOCATE:
        return this.autoAllocateOrders([order.id]);

      case WorkflowActionType.SEND_NOTIFICATION:
        return this.sendWorkflowNotification(order, action.parameters);

      case WorkflowActionType.UPDATE_INVENTORY:
        return this.updateInventoryForOrder(order);

      case WorkflowActionType.GENERATE_INVOICE:
        return this.generateOrderInvoice(order.id, 'system');

      default:
        throw new Error(`不支持的工作流动作: ${action.type}`);
    }
  }

  // 辅助方法实现...
  private async getLastAssignedUserIndex(): Promise<number> {
    // 实现轮询分配的索引管理
    return 0;
  }

  private async updateLastAssignedUserIndex(index: number): Promise<void> {
    // 更新轮询分配的索引
  }

  private findBestSkillMatch(order: any, users: any[]): any {
    // 实现基于技能的匹配逻辑
    return users[0];
  }

  private findClosestUser(order: any, users: any[]): any {
    // 实现基于地理位置的匹配逻辑
    return users[0];
  }

  private async updateOrderStatus(orderId: string, status: string, userId: string): Promise<any> {
    return orderService.updateOrderStatus(orderId, { status: status as OrderStatus }, userId);
  }

  private async assignOrderToUser(orderId: string, assignedUserId: string, userId: string): Promise<any> {
    return prisma.order.update({
      where: { id: orderId },
      data: {
        assignedTo: assignedUserId,
        assignedAt: new Date(),
        updatedBy: userId,
      },
    });
  }

  private async addOrderTags(orderId: string, tags: string[], userId: string): Promise<any> {
    // 实现订单标签添加逻辑
    return { message: '标签已添加' };
  }

  private async sendOrderNotification(order: any, template: string, recipients: string[]): Promise<any> {
    // 实现订单通知发送逻辑
    return { message: '通知已发送' };
  }

  private async generateOrderInvoice(orderId: string, userId: string): Promise<any> {
    // 实现发票生成逻辑
    return { message: '发票已生成' };
  }

  private async sendOrderAssignmentNotification(order: any, user: any): Promise<void> {
    // 实现订单分配通知逻辑
  }

  private async sendSLAEscalationNotification(order: any, slaRule: any, compliance: any): Promise<void> {
    // 实现SLA升级通知逻辑
  }

  private async autoConfirmOrder(orderId: string): Promise<any> {
    return this.updateOrderStatus(orderId, OrderStatus.CONFIRMED, 'system');
  }

  private async sendWorkflowNotification(order: any, parameters: any): Promise<any> {
    // 实现工作流通知逻辑
    return { message: '工作流通知已发送' };
  }

  private async updateInventoryForOrder(order: any): Promise<any> {
    // 实现订单库存更新逻辑
    return { message: '库存已更新' };
  }
}

// 导出单例实例
export const orderWorkflowService = new OrderWorkflowService();
