/**
 * 库存预警和补货建议服务
 * 提供智能库存预警、自动补货建议、库存优化等功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';

// 预警类型枚举
export enum AlertType {
  LOW_STOCK = 'LOW_STOCK',               // 低库存
  OUT_OF_STOCK = 'OUT_OF_STOCK',         // 缺货
  OVERSTOCK = 'OVERSTOCK',               // 超库存
  SLOW_MOVING = 'SLOW_MOVING',           // 滞销
  FAST_MOVING = 'FAST_MOVING',           // 快销
  EXPIRY_WARNING = 'EXPIRY_WARNING',     // 过期预警
  DAMAGE_ALERT = 'DAMAGE_ALERT',         // 损坏预警
  REORDER_POINT = 'REORDER_POINT',       // 补货点
}

// 预警级别枚举
export enum AlertLevel {
  INFO = 'INFO',                         // 信息
  WARNING = 'WARNING',                   // 警告
  CRITICAL = 'CRITICAL',                 // 严重
  URGENT = 'URGENT',                     // 紧急
}

// 补货策略枚举
export enum ReplenishmentStrategy {
  FIXED_QUANTITY = 'FIXED_QUANTITY',     // 固定数量
  ECONOMIC_ORDER_QUANTITY = 'EOQ',       // 经济订货量
  MIN_MAX = 'MIN_MAX',                   // 最小最大库存
  DEMAND_FORECAST = 'DEMAND_FORECAST',   // 需求预测
  SEASONAL_ADJUSTMENT = 'SEASONAL_ADJUSTMENT', // 季节性调整
}

// 库存预警配置接口
export interface InventoryAlertConfig {
  productId?: string;
  categoryId?: string;
  warehouseId?: string;
  alertType: AlertType;
  alertLevel: AlertLevel;
  threshold: number;
  thresholdType: 'PERCENTAGE' | 'ABSOLUTE';
  isActive: boolean;
  notificationChannels: string[];
  recipients: string[];
}

// 补货建议接口
export interface ReplenishmentSuggestion {
  productId: string;
  variantId?: string;
  warehouseId: string;
  currentStock: number;
  reorderPoint: number;
  suggestedQuantity: number;
  strategy: ReplenishmentStrategy;
  priority: number;
  estimatedCost: number;
  leadTime: number;
  reason: string;
  product: {
    id: string;
    name: string;
    sku: string;
    basePrice: number;
    costPrice: number;
  };
  supplier?: {
    id: string;
    name: string;
    contactInfo: any;
  };
}

// 库存分析结果接口
export interface InventoryAnalysis {
  totalValue: number;
  turnoverRate: number;
  daysOfInventory: number;
  stockoutRisk: number;
  overstockRisk: number;
  recommendations: string[];
}

/**
 * 库存预警服务类
 */
export class InventoryAlertService {
  /**
   * 检查库存预警
   */
  async checkInventoryAlerts(
    warehouseIds?: string[],
    productIds?: string[],
    userId?: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      if (userId) {
        const permissionCheck = await permissionService.checkPermission({
          userId,
          resource: 'inventory',
          action: 'read',
        });

        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: '权限不足：无法检查库存预警',
          };
        }
      }

      // 获取活跃的预警配置
      const alertConfigs = await prisma.inventoryAlertConfig.findMany({
        where: {
          isActive: true,
          ...(warehouseIds && { warehouseId: { in: warehouseIds } }),
          ...(productIds && { productId: { in: productIds } }),
        },
      });

      if (alertConfigs.length === 0) {
        return {
          success: true,
          data: { message: '没有配置库存预警规则' },
        };
      }

      const alerts = [];

      for (const config of alertConfigs) {
        const configAlerts = await this.evaluateAlertConfig(config);
        alerts.push(...configAlerts);
      }

      // 按优先级排序
      alerts.sort((a, b) => {
        const levelPriority = { URGENT: 4, CRITICAL: 3, WARNING: 2, INFO: 1 };
        return levelPriority[b.level] - levelPriority[a.level];
      });

      // 发送通知
      for (const alert of alerts) {
        await this.sendAlertNotification(alert);
      }

      return {
        success: true,
        data: {
          alertCount: alerts.length,
          alerts,
          summary: this.generateAlertSummary(alerts),
        },
      };
    } catch (error) {
      console.error('检查库存预警失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '检查库存预警失败',
      };
    }
  }

  /**
   * 生成补货建议
   */
  async generateReplenishmentSuggestions(
    options: {
      warehouseIds?: string[];
      productIds?: string[];
      strategy?: ReplenishmentStrategy;
      maxSuggestions?: number;
    } = {},
    userId: string
  ): Promise<{ success: boolean; data?: ReplenishmentSuggestion[]; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'inventory',
        action: 'read',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法生成补货建议',
        };
      }

      const {
        warehouseIds,
        productIds,
        strategy = ReplenishmentStrategy.DEMAND_FORECAST,
        maxSuggestions = 50,
      } = options;

      // 获取需要补货的库存
      const inventoryItems = await prisma.inventory.findMany({
        where: {
          ...(warehouseIds && { warehouseId: { in: warehouseIds } }),
          ...(productIds && { productId: { in: productIds } }),
          OR: [
            { availableStock: { lte: prisma.inventory.fields.reorderPoint } },
            { totalStock: { lte: 0 } },
          ],
        },
        include: {
          product: {
            include: {
              category: true,
              suppliers: {
                include: {
                  supplier: true,
                },
              },
            },
          },
          warehouse: true,
        },
        take: maxSuggestions,
      });

      const suggestions: ReplenishmentSuggestion[] = [];

      for (const inventory of inventoryItems) {
        const suggestion = await this.calculateReplenishmentSuggestion(inventory, strategy);
        if (suggestion) {
          suggestions.push(suggestion);
        }
      }

      // 按优先级排序
      suggestions.sort((a, b) => b.priority - a.priority);

      return {
        success: true,
        data: suggestions,
      };
    } catch (error) {
      console.error('生成补货建议失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '生成补货建议失败',
      };
    }
  }

  /**
   * 分析库存健康度
   */
  async analyzeInventoryHealth(
    warehouseId?: string,
    userId?: string
  ): Promise<{ success: boolean; data?: InventoryAnalysis; error?: string }> {
    try {
      // 权限检查
      if (userId) {
        const permissionCheck = await permissionService.checkPermission({
          userId,
          resource: 'inventory',
          action: 'read',
        });

        if (!permissionCheck.allowed) {
          return {
            success: false,
            error: '权限不足：无法分析库存健康度',
          };
        }
      }

      const whereConditions: any = {};
      if (warehouseId) {
        whereConditions.warehouseId = warehouseId;
      }

      // 计算总库存价值
      const inventoryValue = await prisma.inventory.aggregate({
        where: whereConditions,
        _sum: {
          totalValue: true,
        },
      });

      // 计算库存周转率
      const turnoverRate = await this.calculateInventoryTurnover(warehouseId);

      // 计算库存天数
      const daysOfInventory = await this.calculateDaysOfInventory(warehouseId);

      // 评估缺货风险
      const stockoutRisk = await this.assessStockoutRisk(warehouseId);

      // 评估超库存风险
      const overstockRisk = await this.assessOverstockRisk(warehouseId);

      // 生成优化建议
      const recommendations = await this.generateOptimizationRecommendations(
        warehouseId,
        turnoverRate,
        daysOfInventory,
        stockoutRisk,
        overstockRisk
      );

      const analysis: InventoryAnalysis = {
        totalValue: inventoryValue._sum.totalValue || 0,
        turnoverRate,
        daysOfInventory,
        stockoutRisk,
        overstockRisk,
        recommendations,
      };

      return {
        success: true,
        data: analysis,
      };
    } catch (error) {
      console.error('分析库存健康度失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '分析库存健康度失败',
      };
    }
  }

  /**
   * 评估预警配置
   */
  private async evaluateAlertConfig(config: any): Promise<any[]> {
    const alerts = [];

    // 构建查询条件
    const whereConditions: any = {};
    
    if (config.productId) {
      whereConditions.productId = config.productId;
    }
    
    if (config.warehouseId) {
      whereConditions.warehouseId = config.warehouseId;
    }

    if (config.categoryId) {
      whereConditions.product = {
        categoryId: config.categoryId,
      };
    }

    // 获取库存数据
    const inventoryItems = await prisma.inventory.findMany({
      where: whereConditions,
      include: {
        product: {
          include: {
            category: true,
          },
        },
        warehouse: true,
      },
    });

    for (const inventory of inventoryItems) {
      const alertCondition = this.checkAlertCondition(inventory, config);
      
      if (alertCondition.triggered) {
        alerts.push({
          id: `${config.alertType}_${inventory.id}_${Date.now()}`,
          type: config.alertType,
          level: config.alertLevel,
          productId: inventory.productId,
          warehouseId: inventory.warehouseId,
          currentValue: alertCondition.currentValue,
          threshold: config.threshold,
          message: alertCondition.message,
          product: inventory.product,
          warehouse: inventory.warehouse,
          createdAt: new Date(),
          config,
        });
      }
    }

    return alerts;
  }

  /**
   * 检查预警条件
   */
  private checkAlertCondition(inventory: any, config: any): { triggered: boolean; currentValue: number; message: string } {
    let currentValue = 0;
    let triggered = false;
    let message = '';

    switch (config.alertType) {
      case AlertType.LOW_STOCK:
        currentValue = inventory.availableStock;
        if (config.thresholdType === 'PERCENTAGE') {
          const threshold = inventory.maxStock * (config.threshold / 100);
          triggered = currentValue <= threshold;
          message = `库存低于阈值 ${config.threshold}% (${threshold} 件)，当前库存 ${currentValue} 件`;
        } else {
          triggered = currentValue <= config.threshold;
          message = `库存低于阈值 ${config.threshold} 件，当前库存 ${currentValue} 件`;
        }
        break;

      case AlertType.OUT_OF_STOCK:
        currentValue = inventory.availableStock;
        triggered = currentValue <= 0;
        message = `商品已缺货，当前库存 ${currentValue} 件`;
        break;

      case AlertType.OVERSTOCK:
        currentValue = inventory.totalStock;
        if (config.thresholdType === 'PERCENTAGE') {
          const threshold = inventory.maxStock * (config.threshold / 100);
          triggered = currentValue >= threshold;
          message = `库存超过阈值 ${config.threshold}% (${threshold} 件)，当前库存 ${currentValue} 件`;
        } else {
          triggered = currentValue >= config.threshold;
          message = `库存超过阈值 ${config.threshold} 件，当前库存 ${currentValue} 件`;
        }
        break;

      case AlertType.REORDER_POINT:
        currentValue = inventory.availableStock;
        triggered = currentValue <= inventory.reorderPoint;
        message = `库存达到补货点 ${inventory.reorderPoint} 件，当前库存 ${currentValue} 件`;
        break;

      // 其他预警类型...
    }

    return { triggered, currentValue, message };
  }

  /**
   * 计算补货建议
   */
  private async calculateReplenishmentSuggestion(
    inventory: any,
    strategy: ReplenishmentStrategy
  ): Promise<ReplenishmentSuggestion | null> {
    const product = inventory.product;
    
    // 获取历史销售数据
    const salesHistory = await this.getSalesHistory(inventory.productId, inventory.warehouseId, 90);
    
    // 计算平均日销量
    const averageDailySales = this.calculateAverageDailySales(salesHistory);
    
    // 获取供应商信息
    const primarySupplier = product.suppliers?.[0]?.supplier;
    
    let suggestedQuantity = 0;
    let reason = '';

    switch (strategy) {
      case ReplenishmentStrategy.FIXED_QUANTITY:
        suggestedQuantity = inventory.maxStock - inventory.totalStock;
        reason = '补充到最大库存量';
        break;

      case ReplenishmentStrategy.ECONOMIC_ORDER_QUANTITY:
        suggestedQuantity = this.calculateEOQ(averageDailySales, product.costPrice, 10); // 假设持有成本10%
        reason = '基于经济订货量计算';
        break;

      case ReplenishmentStrategy.MIN_MAX:
        suggestedQuantity = inventory.maxStock - inventory.totalStock;
        reason = '最小最大库存策略';
        break;

      case ReplenishmentStrategy.DEMAND_FORECAST:
        const forecastDemand = this.forecastDemand(salesHistory, 30); // 预测30天需求
        const safetyStock = averageDailySales * 7; // 7天安全库存
        suggestedQuantity = Math.max(0, forecastDemand + safetyStock - inventory.totalStock);
        reason = '基于需求预测和安全库存';
        break;

      default:
        suggestedQuantity = inventory.reorderPoint - inventory.totalStock;
        reason = '基于补货点计算';
    }

    if (suggestedQuantity <= 0) {
      return null;
    }

    // 计算优先级
    const priority = this.calculateReplenishmentPriority(inventory, averageDailySales);

    return {
      productId: inventory.productId,
      variantId: inventory.variantId,
      warehouseId: inventory.warehouseId,
      currentStock: inventory.totalStock,
      reorderPoint: inventory.reorderPoint,
      suggestedQuantity,
      strategy,
      priority,
      estimatedCost: suggestedQuantity * product.costPrice,
      leadTime: primarySupplier?.leadTime || 7,
      reason,
      product: {
        id: product.id,
        name: product.name,
        sku: product.sku,
        basePrice: product.basePrice,
        costPrice: product.costPrice,
      },
      supplier: primarySupplier ? {
        id: primarySupplier.id,
        name: primarySupplier.name,
        contactInfo: primarySupplier.contactInfo,
      } : undefined,
    };
  }

  /**
   * 计算库存周转率
   */
  private async calculateInventoryTurnover(warehouseId?: string): Promise<number> {
    // 获取过去12个月的销售成本
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const salesData = await prisma.orderItem.aggregate({
      where: {
        order: {
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
          createdAt: {
            gte: twelveMonthsAgo,
          },
        },
        ...(warehouseId && {
          product: {
            inventory: {
              some: {
                warehouseId,
              },
            },
          },
        }),
      },
      _sum: {
        totalPrice: true,
      },
    });

    // 获取平均库存价值
    const averageInventoryValue = await this.calculateAverageInventoryValue(warehouseId);

    const cogs = salesData._sum.totalPrice || 0;
    
    return averageInventoryValue > 0 ? cogs / averageInventoryValue : 0;
  }

  /**
   * 计算库存天数
   */
  private async calculateDaysOfInventory(warehouseId?: string): Promise<number> {
    const turnoverRate = await this.calculateInventoryTurnover(warehouseId);
    return turnoverRate > 0 ? 365 / turnoverRate : 0;
  }

  /**
   * 评估缺货风险
   */
  private async assessStockoutRisk(warehouseId?: string): Promise<number> {
    const whereConditions: any = {};
    if (warehouseId) {
      whereConditions.warehouseId = warehouseId;
    }

    const totalItems = await prisma.inventory.count({ where: whereConditions });
    const lowStockItems = await prisma.inventory.count({
      where: {
        ...whereConditions,
        availableStock: {
          lte: prisma.inventory.fields.reorderPoint,
        },
      },
    });

    return totalItems > 0 ? (lowStockItems / totalItems) * 100 : 0;
  }

  /**
   * 评估超库存风险
   */
  private async assessOverstockRisk(warehouseId?: string): Promise<number> {
    const whereConditions: any = {};
    if (warehouseId) {
      whereConditions.warehouseId = warehouseId;
    }

    const totalItems = await prisma.inventory.count({ where: whereConditions });
    const overstockItems = await prisma.inventory.count({
      where: {
        ...whereConditions,
        totalStock: {
          gte: prisma.inventory.fields.maxStock,
        },
      },
    });

    return totalItems > 0 ? (overstockItems / totalItems) * 100 : 0;
  }

  // 辅助方法实现...
  private generateAlertSummary(alerts: any[]): any {
    const summary = {
      total: alerts.length,
      byLevel: {},
      byType: {},
    };

    alerts.forEach(alert => {
      summary.byLevel[alert.level] = (summary.byLevel[alert.level] || 0) + 1;
      summary.byType[alert.type] = (summary.byType[alert.type] || 0) + 1;
    });

    return summary;
  }

  private async sendAlertNotification(alert: any): Promise<void> {
    // 实现预警通知发送逻辑
    console.log(`发送库存预警通知: ${alert.message}`);
  }

  private async getSalesHistory(productId: string, warehouseId: string, days: number): Promise<any[]> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    return prisma.orderItem.findMany({
      where: {
        productId,
        order: {
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
          createdAt: {
            gte: startDate,
          },
        },
      },
      select: {
        quantity: true,
        order: {
          select: {
            createdAt: true,
          },
        },
      },
    });
  }

  private calculateAverageDailySales(salesHistory: any[]): number {
    if (salesHistory.length === 0) return 0;
    
    const totalQuantity = salesHistory.reduce((sum, item) => sum + item.quantity, 0);
    const days = Math.max(1, salesHistory.length);
    
    return totalQuantity / days;
  }

  private calculateEOQ(demandRate: number, orderCost: number, holdingCostRate: number): number {
    // EOQ = sqrt((2 * D * S) / H)
    // D = 年需求量, S = 订货成本, H = 持有成本
    const annualDemand = demandRate * 365;
    const holdingCost = orderCost * holdingCostRate;
    
    return Math.sqrt((2 * annualDemand * orderCost) / holdingCost);
  }

  private forecastDemand(salesHistory: any[], forecastDays: number): number {
    // 简单的移动平均预测
    const averageDailySales = this.calculateAverageDailySales(salesHistory);
    return averageDailySales * forecastDays;
  }

  private calculateReplenishmentPriority(inventory: any, averageDailySales: number): number {
    // 基于库存水平和销售速度计算优先级
    const stockoutRisk = inventory.availableStock / Math.max(1, averageDailySales);
    const priority = Math.max(0, 100 - stockoutRisk * 10);
    
    return Math.min(100, priority);
  }

  private async calculateAverageInventoryValue(warehouseId?: string): Promise<number> {
    // 计算平均库存价值（简化实现）
    const whereConditions: any = {};
    if (warehouseId) {
      whereConditions.warehouseId = warehouseId;
    }

    const inventoryValue = await prisma.inventory.aggregate({
      where: whereConditions,
      _avg: {
        totalValue: true,
      },
    });

    return inventoryValue._avg.totalValue || 0;
  }

  private async generateOptimizationRecommendations(
    warehouseId: string | undefined,
    turnoverRate: number,
    daysOfInventory: number,
    stockoutRisk: number,
    overstockRisk: number
  ): Promise<string[]> {
    const recommendations = [];

    if (turnoverRate < 4) {
      recommendations.push('库存周转率偏低，建议优化商品组合或促销滞销商品');
    }

    if (daysOfInventory > 90) {
      recommendations.push('库存天数过长，建议减少采购量或加强销售推广');
    }

    if (stockoutRisk > 20) {
      recommendations.push('缺货风险较高，建议增加安全库存或优化补货策略');
    }

    if (overstockRisk > 15) {
      recommendations.push('超库存风险较高，建议调整最大库存设置或促销处理');
    }

    return recommendations;
  }
}

// 导出单例实例
export const inventoryAlertService = new InventoryAlertService();
