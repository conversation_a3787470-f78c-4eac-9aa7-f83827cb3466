/**
 * 客户管理业务逻辑服务
 * 提供客户信息管理、客户分组、客户统计等核心功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';
import { rowLevelSecurity } from '@/lib/rbac/row-level-security';

// 客户状态枚举
export enum CustomerStatus {
  ACTIVE = 'ACTIVE',           // 活跃
  INACTIVE = 'INACTIVE',       // 非活跃
  SUSPENDED = 'SUSPENDED',     // 暂停
  BLOCKED = 'BLOCKED',         // 封禁
}

// 客户分组枚举
export enum CustomerGroup {
  REGULAR = 'REGULAR',         // 普通客户
  VIP = 'VIP',                 // VIP客户
  WHOLESALE = 'WHOLESALE',     // 批发客户
  CORPORATE = 'CORPORATE',     // 企业客户
  PREMIUM = 'PREMIUM',         // 高级客户
}

// 客户创建请求接口
export interface CreateCustomerRequest {
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  languageCode?: string;
  currencyCode?: string;
  customerGroup?: CustomerGroup;
  marketingConsent?: boolean;
  addresses?: CustomerAddressRequest[];
  tags?: string[];
  notes?: string;
}

export interface CustomerAddressRequest {
  type: 'BILLING' | 'SHIPPING';
  firstName: string;
  lastName: string;
  company?: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  postalCode: string;
  countryCode: string;
  phone?: string;
  isDefault?: boolean;
}

// 客户更新请求接口
export interface UpdateCustomerRequest {
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: Date;
  gender?: 'MALE' | 'FEMALE' | 'OTHER';
  languageCode?: string;
  currencyCode?: string;
  customerGroup?: CustomerGroup;
  status?: CustomerStatus;
  marketingConsent?: boolean;
  tags?: string[];
  notes?: string;
}

// 客户查询选项接口
export interface CustomerQueryOptions {
  page?: number;
  limit?: number;
  status?: CustomerStatus[];
  customerGroup?: CustomerGroup[];
  search?: string;
  tags?: string[];
  registrationDateFrom?: Date;
  registrationDateTo?: Date;
  lastOrderDateFrom?: Date;
  lastOrderDateTo?: Date;
  sortBy?: 'createdAt' | 'lastOrderAt' | 'totalSpent' | 'orderCount';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 客户管理服务类
 */
export class CustomerService {
  /**
   * 创建新客户
   */
  async createCustomer(
    request: CreateCustomerRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'customers',
        action: 'create',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法创建客户',
        };
      }

      // 检查邮箱是否已存在
      const existingCustomer = await prisma.customer.findUnique({
        where: { email: request.email },
      });

      if (existingCustomer) {
        return {
          success: false,
          error: '邮箱已被使用',
        };
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 创建客户
        const customer = await tx.customer.create({
          data: {
            email: request.email,
            firstName: request.firstName,
            lastName: request.lastName,
            phone: request.phone,
            dateOfBirth: request.dateOfBirth,
            gender: request.gender,
            languageCode: request.languageCode || 'zh-CN',
            currencyCode: request.currencyCode || 'CNY',
            customerGroup: request.customerGroup || CustomerGroup.REGULAR,
            status: CustomerStatus.ACTIVE,
            marketingConsent: request.marketingConsent || false,
            notes: request.notes,
            createdBy: userId,
          },
        });

        // 创建客户地址
        if (request.addresses && request.addresses.length > 0) {
          for (const address of request.addresses) {
            await tx.customerAddress.create({
              data: {
                customerId: customer.id,
                type: address.type,
                firstName: address.firstName,
                lastName: address.lastName,
                company: address.company,
                addressLine1: address.addressLine1,
                addressLine2: address.addressLine2,
                city: address.city,
                state: address.state,
                postalCode: address.postalCode,
                countryCode: address.countryCode,
                phone: address.phone,
                isDefault: address.isDefault || false,
              },
            });
          }
        }

        // 创建客户标签
        if (request.tags && request.tags.length > 0) {
          for (const tagName of request.tags) {
            // 获取或创建标签
            const tag = await tx.customerTag.upsert({
              where: { name: tagName },
              update: {},
              create: {
                name: tagName,
                color: this.generateTagColor(tagName),
              },
            });

            // 关联客户和标签
            await tx.customerTagRelation.create({
              data: {
                customerId: customer.id,
                tagId: tag.id,
              },
            });
          }
        }

        // 初始化客户统计
        await tx.customerStats.create({
          data: {
            customerId: customer.id,
            totalOrders: 0,
            totalSpent: 0,
            averageOrderValue: 0,
            lastOrderAt: null,
            firstOrderAt: null,
          },
        });

        return customer;
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('创建客户失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '创建客户失败',
      };
    }
  }

  /**
   * 更新客户信息
   */
  async updateCustomer(
    customerId: string,
    request: UpdateCustomerRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'customers',
        action: 'update',
        resourceId: customerId,
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法更新客户',
        };
      }

      // 获取当前客户信息
      const currentCustomer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          tags: {
            include: {
              tag: true,
            },
          },
        },
      });

      if (!currentCustomer) {
        return {
          success: false,
          error: '客户不存在',
        };
      }

      // 行级权限检查
      const rowPermissionCheck = await rowLevelSecurity.checkRowPermission(
        userId,
        'customers',
        currentCustomer
      );

      if (!rowPermissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法访问此客户',
        };
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 更新客户基本信息
        const updatedCustomer = await tx.customer.update({
          where: { id: customerId },
          data: {
            ...(request.firstName !== undefined && { firstName: request.firstName }),
            ...(request.lastName !== undefined && { lastName: request.lastName }),
            ...(request.phone !== undefined && { phone: request.phone }),
            ...(request.dateOfBirth !== undefined && { dateOfBirth: request.dateOfBirth }),
            ...(request.gender !== undefined && { gender: request.gender }),
            ...(request.languageCode !== undefined && { languageCode: request.languageCode }),
            ...(request.currencyCode !== undefined && { currencyCode: request.currencyCode }),
            ...(request.customerGroup !== undefined && { customerGroup: request.customerGroup }),
            ...(request.status !== undefined && { status: request.status }),
            ...(request.marketingConsent !== undefined && { marketingConsent: request.marketingConsent }),
            ...(request.notes !== undefined && { notes: request.notes }),
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });

        // 更新客户标签
        if (request.tags !== undefined) {
          // 删除现有标签关联
          await tx.customerTagRelation.deleteMany({
            where: { customerId },
          });

          // 创建新的标签关联
          if (request.tags.length > 0) {
            for (const tagName of request.tags) {
              const tag = await tx.customerTag.upsert({
                where: { name: tagName },
                update: {},
                create: {
                  name: tagName,
                  color: this.generateTagColor(tagName),
                },
              });

              await tx.customerTagRelation.create({
                data: {
                  customerId,
                  tagId: tag.id,
                },
              });
            }
          }
        }

        // 记录客户变更历史
        await tx.customerHistory.create({
          data: {
            customerId,
            action: 'UPDATE',
            changes: JSON.stringify(request),
            notes: '客户信息更新',
            createdBy: userId,
          },
        });

        return updatedCustomer;
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('更新客户失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '更新客户失败',
      };
    }
  }

  /**
   * 获取客户详情
   */
  async getCustomerById(
    customerId: string,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'customers',
        action: 'read',
        resourceId: customerId,
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法查看客户',
        };
      }

      // 获取客户详细信息
      const customer = await prisma.customer.findUnique({
        where: { id: customerId },
        include: {
          addresses: true,
          tags: {
            include: {
              tag: true,
            },
          },
          stats: true,
          orders: {
            select: {
              id: true,
              orderNumber: true,
              status: true,
              totalAmount: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 10,
          },
        },
      });

      if (!customer) {
        return {
          success: false,
          error: '客户不存在',
        };
      }

      // 行级权限检查
      const rowPermissionCheck = await rowLevelSecurity.checkRowPermission(
        userId,
        'customers',
        customer
      );

      if (!rowPermissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法访问此客户',
        };
      }

      return {
        success: true,
        data: customer,
      };
    } catch (error) {
      console.error('获取客户详情失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '获取客户详情失败',
      };
    }
  }

  /**
   * 更新客户统计信息
   */
  async updateCustomerStats(customerId: string): Promise<void> {
    try {
      // 计算客户统计信息
      const orderStats = await prisma.order.aggregate({
        where: {
          customerId,
          status: {
            in: ['DELIVERED', 'COMPLETED'],
          },
        },
        _count: {
          id: true,
        },
        _sum: {
          totalAmount: true,
        },
        _min: {
          createdAt: true,
        },
        _max: {
          createdAt: true,
        },
      });

      const totalOrders = orderStats._count.id || 0;
      const totalSpent = orderStats._sum.totalAmount || 0;
      const averageOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0;
      const firstOrderAt = orderStats._min.createdAt;
      const lastOrderAt = orderStats._max.createdAt;

      // 更新客户统计
      await prisma.customerStats.upsert({
        where: { customerId },
        update: {
          totalOrders,
          totalSpent,
          averageOrderValue,
          firstOrderAt,
          lastOrderAt,
        },
        create: {
          customerId,
          totalOrders,
          totalSpent,
          averageOrderValue,
          firstOrderAt,
          lastOrderAt,
        },
      });
    } catch (error) {
      console.error('更新客户统计失败:', error);
    }
  }

  /**
   * 生成标签颜色
   */
  private generateTagColor(tagName: string): string {
    const colors = [
      '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
      '#8B5CF6', '#EC4899', '#06B6D4', '#84CC16',
      '#F97316', '#6366F1', '#14B8A6', '#F43F5E',
    ];
    
    let hash = 0;
    for (let i = 0; i < tagName.length; i++) {
      hash = tagName.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  }
}

// 导出单例实例
export const customerService = new CustomerService();
