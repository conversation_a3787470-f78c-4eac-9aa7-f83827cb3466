/**
 * 库存管理业务逻辑服务
 * 提供库存调整、库存转移、库存盘点等核心功能
 */

import { prisma } from '@/lib/prisma';
import { permissionService } from '@/lib/rbac/permission-service';
import { rowLevelSecurity } from '@/lib/rbac/row-level-security';

// 库存调整类型枚举
export enum InventoryAdjustmentType {
  INCREASE = 'INCREASE',         // 增加库存
  DECREASE = 'DECREASE',         // 减少库存
  SET = 'SET',                   // 设置库存
  DAMAGE = 'DAMAGE',             // 损坏
  EXPIRED = 'EXPIRED',           // 过期
  LOST = 'LOST',                 // 丢失
  FOUND = 'FOUND',               // 找到
  RETURN = 'RETURN',             // 退货
  SALE = 'SALE',                 // 销售
  PURCHASE = 'PURCHASE',         // 采购
}

// 库存调整原因枚举
export enum InventoryAdjustmentReason {
  MANUAL_ADJUSTMENT = 'MANUAL_ADJUSTMENT',     // 手动调整
  STOCK_COUNT = 'STOCK_COUNT',                 // 库存盘点
  DAMAGE_REPORT = 'DAMAGE_REPORT',             // 损坏报告
  EXPIRY_CHECK = 'EXPIRY_CHECK',               // 过期检查
  CUSTOMER_RETURN = 'CUSTOMER_RETURN',         // 客户退货
  SUPPLIER_RETURN = 'SUPPLIER_RETURN',         // 供应商退货
  TRANSFER_IN = 'TRANSFER_IN',                 // 转入
  TRANSFER_OUT = 'TRANSFER_OUT',               // 转出
  SALE_ORDER = 'SALE_ORDER',                   // 销售订单
  PURCHASE_ORDER = 'PURCHASE_ORDER',           // 采购订单
  PRODUCTION = 'PRODUCTION',                   // 生产
  CONSUMPTION = 'CONSUMPTION',                 // 消耗
}

// 库存调整请求接口
export interface InventoryAdjustmentRequest {
  productId: string;
  variantId?: string;
  warehouseId: string;
  type: InventoryAdjustmentType;
  reason: InventoryAdjustmentReason;
  quantity: number;
  unitCost?: number;
  notes?: string;
  referenceType?: string;
  referenceId?: string;
}

// 库存转移请求接口
export interface InventoryTransferRequest {
  productId: string;
  variantId?: string;
  fromWarehouseId: string;
  toWarehouseId: string;
  quantity: number;
  notes?: string;
  expectedDate?: Date;
}

// 库存盘点请求接口
export interface InventoryCountRequest {
  warehouseId: string;
  productIds?: string[];
  countDate: Date;
  notes?: string;
  items: InventoryCountItem[];
}

export interface InventoryCountItem {
  productId: string;
  variantId?: string;
  expectedQuantity: number;
  actualQuantity: number;
  notes?: string;
}

// 库存查询选项接口
export interface InventoryQueryOptions {
  page?: number;
  limit?: number;
  warehouseId?: string;
  productId?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  search?: string;
  sortBy?: 'productName' | 'sku' | 'quantity' | 'value';
  sortOrder?: 'asc' | 'desc';
}

/**
 * 库存管理服务类
 */
export class InventoryService {
  /**
   * 调整库存
   */
  async adjustInventory(
    request: InventoryAdjustmentRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'inventory',
        action: 'adjust',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法调整库存',
        };
      }

      // 验证商品和仓库
      const [product, warehouse] = await Promise.all([
        prisma.product.findUnique({
          where: { id: request.productId },
          include: { variants: true },
        }),
        prisma.warehouse.findUnique({
          where: { id: request.warehouseId },
        }),
      ]);

      if (!product) {
        return {
          success: false,
          error: '商品不存在',
        };
      }

      if (!warehouse) {
        return {
          success: false,
          error: '仓库不存在',
        };
      }

      // 验证变体（如果提供）
      if (request.variantId) {
        const variant = product.variants.find(v => v.id === request.variantId);
        if (!variant) {
          return {
            success: false,
            error: '商品变体不存在',
          };
        }
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 获取当前库存记录
        let inventory = await tx.inventory.findFirst({
          where: {
            productId: request.productId,
            variantId: request.variantId,
            warehouseId: request.warehouseId,
          },
        });

        // 如果库存记录不存在，创建新记录
        if (!inventory) {
          inventory = await tx.inventory.create({
            data: {
              productId: request.productId,
              variantId: request.variantId,
              warehouseId: request.warehouseId,
              totalStock: 0,
              availableStock: 0,
              reservedStock: 0,
              damagedStock: 0,
              unitCost: request.unitCost || 0,
              totalValue: 0,
              reorderPoint: 0,
              maxStock: 0,
              createdBy: userId,
            },
          });
        }

        // 计算新的库存数量
        let newTotalStock = inventory.totalStock;
        let newAvailableStock = inventory.availableStock;
        let newDamagedStock = inventory.damagedStock;

        switch (request.type) {
          case InventoryAdjustmentType.INCREASE:
            newTotalStock += request.quantity;
            newAvailableStock += request.quantity;
            break;
          
          case InventoryAdjustmentType.DECREASE:
            if (newAvailableStock < request.quantity) {
              throw new Error('可用库存不足');
            }
            newTotalStock -= request.quantity;
            newAvailableStock -= request.quantity;
            break;
          
          case InventoryAdjustmentType.SET:
            const difference = request.quantity - newTotalStock;
            newTotalStock = request.quantity;
            newAvailableStock = Math.max(0, newAvailableStock + difference);
            break;
          
          case InventoryAdjustmentType.DAMAGE:
            if (newAvailableStock < request.quantity) {
              throw new Error('可用库存不足');
            }
            newAvailableStock -= request.quantity;
            newDamagedStock += request.quantity;
            break;
          
          case InventoryAdjustmentType.FOUND:
            newTotalStock += request.quantity;
            newAvailableStock += request.quantity;
            break;
          
          case InventoryAdjustmentType.LOST:
            if (newAvailableStock < request.quantity) {
              throw new Error('可用库存不足');
            }
            newTotalStock -= request.quantity;
            newAvailableStock -= request.quantity;
            break;
        }

        // 确保库存数量不为负数
        if (newTotalStock < 0 || newAvailableStock < 0 || newDamagedStock < 0) {
          throw new Error('库存数量不能为负数');
        }

        // 更新库存记录
        const updatedInventory = await tx.inventory.update({
          where: { id: inventory.id },
          data: {
            totalStock: newTotalStock,
            availableStock: newAvailableStock,
            damagedStock: newDamagedStock,
            unitCost: request.unitCost || inventory.unitCost,
            totalValue: newTotalStock * (request.unitCost || inventory.unitCost),
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });

        // 创建库存调整记录
        const adjustment = await tx.inventoryAdjustment.create({
          data: {
            inventoryId: inventory.id,
            type: request.type,
            reason: request.reason,
            quantity: request.quantity,
            previousQuantity: inventory.totalStock,
            newQuantity: newTotalStock,
            unitCost: request.unitCost || inventory.unitCost,
            totalCost: request.quantity * (request.unitCost || inventory.unitCost),
            notes: request.notes,
            referenceType: request.referenceType,
            referenceId: request.referenceId,
            createdBy: userId,
          },
        });

        // 检查库存预警
        await this.checkStockAlerts(tx, updatedInventory);

        return {
          inventory: updatedInventory,
          adjustment,
        };
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('调整库存失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '调整库存失败',
      };
    }
  }

  /**
   * 库存转移
   */
  async transferInventory(
    request: InventoryTransferRequest,
    userId: string
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    try {
      // 权限检查
      const permissionCheck = await permissionService.checkPermission({
        userId,
        resource: 'inventory',
        action: 'transfer',
      });

      if (!permissionCheck.allowed) {
        return {
          success: false,
          error: '权限不足：无法转移库存',
        };
      }

      // 验证仓库
      const [fromWarehouse, toWarehouse] = await Promise.all([
        prisma.warehouse.findUnique({
          where: { id: request.fromWarehouseId },
        }),
        prisma.warehouse.findUnique({
          where: { id: request.toWarehouseId },
        }),
      ]);

      if (!fromWarehouse || !toWarehouse) {
        return {
          success: false,
          error: '源仓库或目标仓库不存在',
        };
      }

      if (request.fromWarehouseId === request.toWarehouseId) {
        return {
          success: false,
          error: '源仓库和目标仓库不能相同',
        };
      }

      // 开始数据库事务
      const result = await prisma.$transaction(async (tx) => {
        // 获取源仓库库存
        const fromInventory = await tx.inventory.findFirst({
          where: {
            productId: request.productId,
            variantId: request.variantId,
            warehouseId: request.fromWarehouseId,
          },
        });

        if (!fromInventory || fromInventory.availableStock < request.quantity) {
          throw new Error('源仓库库存不足');
        }

        // 获取或创建目标仓库库存
        let toInventory = await tx.inventory.findFirst({
          where: {
            productId: request.productId,
            variantId: request.variantId,
            warehouseId: request.toWarehouseId,
          },
        });

        if (!toInventory) {
          toInventory = await tx.inventory.create({
            data: {
              productId: request.productId,
              variantId: request.variantId,
              warehouseId: request.toWarehouseId,
              totalStock: 0,
              availableStock: 0,
              reservedStock: 0,
              damagedStock: 0,
              unitCost: fromInventory.unitCost,
              totalValue: 0,
              reorderPoint: 0,
              maxStock: 0,
              createdBy: userId,
            },
          });
        }

        // 更新源仓库库存（减少）
        const updatedFromInventory = await tx.inventory.update({
          where: { id: fromInventory.id },
          data: {
            totalStock: fromInventory.totalStock - request.quantity,
            availableStock: fromInventory.availableStock - request.quantity,
            totalValue: (fromInventory.totalStock - request.quantity) * fromInventory.unitCost,
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });

        // 更新目标仓库库存（增加）
        const updatedToInventory = await tx.inventory.update({
          where: { id: toInventory.id },
          data: {
            totalStock: toInventory.totalStock + request.quantity,
            availableStock: toInventory.availableStock + request.quantity,
            totalValue: (toInventory.totalStock + request.quantity) * toInventory.unitCost,
            updatedBy: userId,
            updatedAt: new Date(),
          },
        });

        // 创建转移记录
        const transfer = await tx.inventoryTransfer.create({
          data: {
            productId: request.productId,
            variantId: request.variantId,
            fromWarehouseId: request.fromWarehouseId,
            toWarehouseId: request.toWarehouseId,
            quantity: request.quantity,
            unitCost: fromInventory.unitCost,
            totalCost: request.quantity * fromInventory.unitCost,
            status: 'COMPLETED',
            notes: request.notes,
            expectedDate: request.expectedDate,
            completedDate: new Date(),
            createdBy: userId,
          },
        });

        // 创建库存调整记录
        await Promise.all([
          // 源仓库调整记录
          tx.inventoryAdjustment.create({
            data: {
              inventoryId: fromInventory.id,
              type: InventoryAdjustmentType.DECREASE,
              reason: InventoryAdjustmentReason.TRANSFER_OUT,
              quantity: request.quantity,
              previousQuantity: fromInventory.totalStock,
              newQuantity: fromInventory.totalStock - request.quantity,
              unitCost: fromInventory.unitCost,
              totalCost: request.quantity * fromInventory.unitCost,
              notes: `转移到仓库: ${toWarehouse.name}`,
              referenceType: 'TRANSFER',
              referenceId: transfer.id,
              createdBy: userId,
            },
          }),
          // 目标仓库调整记录
          tx.inventoryAdjustment.create({
            data: {
              inventoryId: toInventory.id,
              type: InventoryAdjustmentType.INCREASE,
              reason: InventoryAdjustmentReason.TRANSFER_IN,
              quantity: request.quantity,
              previousQuantity: toInventory.totalStock,
              newQuantity: toInventory.totalStock + request.quantity,
              unitCost: toInventory.unitCost,
              totalCost: request.quantity * toInventory.unitCost,
              notes: `从仓库转入: ${fromWarehouse.name}`,
              referenceType: 'TRANSFER',
              referenceId: transfer.id,
              createdBy: userId,
            },
          }),
        ]);

        return {
          transfer,
          fromInventory: updatedFromInventory,
          toInventory: updatedToInventory,
        };
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error('转移库存失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '转移库存失败',
      };
    }
  }

  /**
   * 检查库存预警
   */
  private async checkStockAlerts(tx: any, inventory: any): Promise<void> {
    // 检查低库存预警
    if (inventory.availableStock <= inventory.reorderPoint && inventory.reorderPoint > 0) {
      await tx.stockAlert.upsert({
        where: {
          inventoryId_type: {
            inventoryId: inventory.id,
            type: 'LOW_STOCK',
          },
        },
        update: {
          isActive: true,
          updatedAt: new Date(),
        },
        create: {
          inventoryId: inventory.id,
          type: 'LOW_STOCK',
          message: `库存低于补货点 (${inventory.reorderPoint})`,
          isActive: true,
        },
      });
    }

    // 检查缺货预警
    if (inventory.availableStock === 0) {
      await tx.stockAlert.upsert({
        where: {
          inventoryId_type: {
            inventoryId: inventory.id,
            type: 'OUT_OF_STOCK',
          },
        },
        update: {
          isActive: true,
          updatedAt: new Date(),
        },
        create: {
          inventoryId: inventory.id,
          type: 'OUT_OF_STOCK',
          message: '商品已缺货',
          isActive: true,
        },
      });
    } else {
      // 如果库存恢复，取消缺货预警
      await tx.stockAlert.updateMany({
        where: {
          inventoryId: inventory.id,
          type: 'OUT_OF_STOCK',
          isActive: true,
        },
        data: {
          isActive: false,
          resolvedAt: new Date(),
        },
      });
    }

    // 检查超库存预警
    if (inventory.totalStock > inventory.maxStock && inventory.maxStock > 0) {
      await tx.stockAlert.upsert({
        where: {
          inventoryId_type: {
            inventoryId: inventory.id,
            type: 'OVERSTOCK',
          },
        },
        update: {
          isActive: true,
          updatedAt: new Date(),
        },
        create: {
          inventoryId: inventory.id,
          type: 'OVERSTOCK',
          message: `库存超过最大库存 (${inventory.maxStock})`,
          isActive: true,
        },
      });
    }
  }
}

// 导出单例实例
export const inventoryService = new InventoryService();
