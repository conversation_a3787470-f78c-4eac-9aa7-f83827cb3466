/**
 * 权限验证React Hook
 * 提供前端权限检查和管理功能
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAuth } from '@/hooks/useAuth';

// 权限检查结果接口
export interface PermissionCheckResult {
  allowed: boolean;
  reason?: string;
  conditions?: Record<string, any>;
}

// 权限上下文接口
export interface PermissionContext {
  resource: string;
  action: string;
  resourceId?: string;
  metadata?: Record<string, any>;
}

// 权限Hook返回值接口
export interface UsePermissionsReturn {
  // 权限检查函数
  checkPermission: (context: PermissionContext) => Promise<PermissionCheckResult>;
  checkPermissionSync: (context: PermissionContext) => PermissionCheckResult | null;
  
  // 批量权限检查
  checkMultiplePermissions: (permissions: PermissionContext[]) => Promise<Record<string, PermissionCheckResult>>;
  
  // 权限状态
  permissions: string[];
  roles: string[];
  isLoading: boolean;
  error: string | null;
  
  // 便捷方法
  hasPermission: (resource: string, action: string, resourceId?: string) => boolean;
  hasRole: (roleName: string) => boolean;
  hasAnyRole: (roleNames: string[]) => boolean;
  hasAllRoles: (roleNames: string[]) => boolean;
  
  // 缓存管理
  clearCache: () => void;
  refreshPermissions: () => Promise<void>;
}

// 权限缓存
interface PermissionCache {
  [key: string]: {
    result: PermissionCheckResult;
    timestamp: number;
    ttl: number;
  };
}

// Hook配置
interface UsePermissionsConfig {
  cacheTTL?: number; // 缓存生存时间（毫秒）
  autoRefresh?: boolean; // 是否自动刷新权限
  refreshInterval?: number; // 自动刷新间隔（毫秒）
}

const DEFAULT_CONFIG: UsePermissionsConfig = {
  cacheTTL: 5 * 60 * 1000, // 5分钟
  autoRefresh: false,
  refreshInterval: 30 * 60 * 1000, // 30分钟
};

/**
 * 权限验证Hook
 */
export function usePermissions(config: UsePermissionsConfig = {}): UsePermissionsReturn {
  const { user, isAuthenticated } = useAuth();
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // 状态管理
  const [permissions, setPermissions] = useState<string[]>([]);
  const [roles, setRoles] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permissionCache, setPermissionCache] = useState<PermissionCache>({});

  // 生成缓存键
  const getCacheKey = useCallback((context: PermissionContext): string => {
    return `${context.resource}.${context.action}${context.resourceId ? `.${context.resourceId}` : ''}`;
  }, []);

  // 检查缓存是否有效
  const isCacheValid = useCallback((cacheKey: string): boolean => {
    const cached = permissionCache[cacheKey];
    if (!cached) return false;
    
    const now = Date.now();
    return now - cached.timestamp < cached.ttl;
  }, [permissionCache]);

  // 从缓存获取权限检查结果
  const getFromCache = useCallback((cacheKey: string): PermissionCheckResult | null => {
    if (!isCacheValid(cacheKey)) return null;
    return permissionCache[cacheKey].result;
  }, [permissionCache, isCacheValid]);

  // 设置缓存
  const setCache = useCallback((cacheKey: string, result: PermissionCheckResult): void => {
    setPermissionCache(prev => ({
      ...prev,
      [cacheKey]: {
        result,
        timestamp: Date.now(),
        ttl: finalConfig.cacheTTL!,
      },
    }));
  }, [finalConfig.cacheTTL]);

  // 清除过期缓存
  const clearExpiredCache = useCallback((): void => {
    const now = Date.now();
    setPermissionCache(prev => {
      const newCache: PermissionCache = {};
      Object.entries(prev).forEach(([key, value]) => {
        if (now - value.timestamp < value.ttl) {
          newCache[key] = value;
        }
      });
      return newCache;
    });
  }, []);

  // API调用：检查单个权限
  const checkPermission = useCallback(async (context: PermissionContext): Promise<PermissionCheckResult> => {
    if (!isAuthenticated || !user) {
      return { allowed: false, reason: '用户未认证' };
    }

    const cacheKey = getCacheKey(context);
    const cached = getFromCache(cacheKey);
    if (cached) {
      return cached;
    }

    try {
      setError(null);
      const response = await fetch('/api/auth/check-permission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(context),
      });

      if (!response.ok) {
        throw new Error(`权限检查失败: ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error?.message || '权限检查失败');
      }

      const result = data.data as PermissionCheckResult;
      setCache(cacheKey, result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '权限检查失败';
      setError(errorMessage);
      return { allowed: false, reason: errorMessage };
    }
  }, [isAuthenticated, user, getCacheKey, getFromCache, setCache]);

  // 同步权限检查（仅从缓存获取）
  const checkPermissionSync = useCallback((context: PermissionContext): PermissionCheckResult | null => {
    if (!isAuthenticated || !user) {
      return { allowed: false, reason: '用户未认证' };
    }

    const cacheKey = getCacheKey(context);
    return getFromCache(cacheKey);
  }, [isAuthenticated, user, getCacheKey, getFromCache]);

  // API调用：批量检查权限
  const checkMultiplePermissions = useCallback(async (
    contexts: PermissionContext[]
  ): Promise<Record<string, PermissionCheckResult>> => {
    if (!isAuthenticated || !user) {
      const result: Record<string, PermissionCheckResult> = {};
      contexts.forEach(context => {
        const key = getCacheKey(context);
        result[key] = { allowed: false, reason: '用户未认证' };
      });
      return result;
    }

    // 检查哪些权限需要从API获取
    const uncachedContexts: PermissionContext[] = [];
    const result: Record<string, PermissionCheckResult> = {};

    contexts.forEach(context => {
      const cacheKey = getCacheKey(context);
      const cached = getFromCache(cacheKey);
      if (cached) {
        result[cacheKey] = cached;
      } else {
        uncachedContexts.push(context);
      }
    });

    // 如果所有权限都在缓存中，直接返回
    if (uncachedContexts.length === 0) {
      return result;
    }

    try {
      setError(null);
      const response = await fetch('/api/auth/check-permission', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ permissions: uncachedContexts }),
      });

      if (!response.ok) {
        throw new Error(`批量权限检查失败: ${response.statusText}`);
      }

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error?.message || '批量权限检查失败');
      }

      const apiResults = data.data as Record<string, PermissionCheckResult>;
      
      // 更新缓存和结果
      Object.entries(apiResults).forEach(([key, permResult]) => {
        setCache(key, permResult);
        result[key] = permResult;
      });

      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '批量权限检查失败';
      setError(errorMessage);
      
      // 为未缓存的权限返回错误结果
      uncachedContexts.forEach(context => {
        const key = getCacheKey(context);
        result[key] = { allowed: false, reason: errorMessage };
      });
      
      return result;
    }
  }, [isAuthenticated, user, getCacheKey, getFromCache, setCache]);

  // 便捷方法：检查是否有特定权限
  const hasPermission = useCallback((resource: string, action: string, resourceId?: string): boolean => {
    if (!isAuthenticated || !user) return false;
    
    // 超级管理员拥有所有权限
    if (roles.includes('super_admin')) return true;
    
    // 检查具体权限
    const permissionName = `${resource}.${action}`;
    return permissions.includes(permissionName);
  }, [isAuthenticated, user, roles, permissions]);

  // 便捷方法：检查是否有特定角色
  const hasRole = useCallback((roleName: string): boolean => {
    return roles.includes(roleName);
  }, [roles]);

  // 便捷方法：检查是否有任意一个角色
  const hasAnyRole = useCallback((roleNames: string[]): boolean => {
    return roleNames.some(roleName => roles.includes(roleName));
  }, [roles]);

  // 便捷方法：检查是否有所有角色
  const hasAllRoles = useCallback((roleNames: string[]): boolean => {
    return roleNames.every(roleName => roles.includes(roleName));
  }, [roles]);

  // 清除缓存
  const clearCache = useCallback((): void => {
    setPermissionCache({});
  }, []);

  // 刷新用户权限
  const refreshPermissions = useCallback(async (): Promise<void> => {
    if (!isAuthenticated || !user) return;

    try {
      setIsLoading(true);
      setError(null);

      // 清除权限缓存
      await fetch('/api/auth/check-permission', {
        method: 'DELETE',
      });

      // 更新用户信息（这里假设用户信息包含权限和角色）
      if (user.permissions) {
        setPermissions(user.permissions);
      }
      if (user.roles) {
        setRoles(user.roles);
      }

      // 清除本地缓存
      clearCache();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刷新权限失败';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, clearCache]);

  // 初始化权限数据
  useEffect(() => {
    if (isAuthenticated && user) {
      setPermissions(user.permissions || []);
      setRoles(user.roles || []);
    } else {
      setPermissions([]);
      setRoles([]);
      clearCache();
    }
  }, [isAuthenticated, user, clearCache]);

  // 定期清除过期缓存
  useEffect(() => {
    const interval = setInterval(clearExpiredCache, 60000); // 每分钟清理一次
    return () => clearInterval(interval);
  }, [clearExpiredCache]);

  // 自动刷新权限
  useEffect(() => {
    if (!finalConfig.autoRefresh || !finalConfig.refreshInterval) return;

    const interval = setInterval(refreshPermissions, finalConfig.refreshInterval);
    return () => clearInterval(interval);
  }, [finalConfig.autoRefresh, finalConfig.refreshInterval, refreshPermissions]);

  return {
    checkPermission,
    checkPermissionSync,
    checkMultiplePermissions,
    permissions,
    roles,
    isLoading,
    error,
    hasPermission,
    hasRole,
    hasAnyRole,
    hasAllRoles,
    clearCache,
    refreshPermissions,
  };
}
