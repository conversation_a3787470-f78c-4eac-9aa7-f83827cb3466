/**
 * 翻译Hook
 * 提供翻译文本获取、语言切换、翻译状态管理等功能
 */

'use client';

import { useState, useEffect, useCallback, useContext, createContext, ReactNode } from 'react';
import { 
  SupportedLanguage, 
  TranslationNamespace, 
  TranslationKey, 
  TranslationParams, 
  TranslationOptions,
  LanguageSwitchOptions,
  DEFAULT_LANGUAGE,
  LANGUAGE_COOKIE_CONFIG,
  LANGUAGE_STORAGE_CONFIG,
  getBrowserLanguage,
  isLanguageSupported,
  getLanguageInfo,
} from '@/lib/i18n/config';
import { translationService } from '@/lib/i18n/translation-service';

// 翻译上下文接口
interface TranslationContextType {
  currentLanguage: SupportedLanguage;
  isLoading: boolean;
  error: string | null;
  changeLanguage: (language: SupportedLanguage, options?: LanguageSwitchOptions) => Promise<void>;
  translate: (key: TranslationKey, options?: TranslationOptions) => Promise<string>;
  translateSync: (key: TranslationKey, options?: TranslationOptions) => string;
  isLanguageLoaded: (language: SupportedLanguage, namespace?: TranslationNamespace) => boolean;
  preloadLanguage: (language: SupportedLanguage, namespaces?: TranslationNamespace[]) => Promise<void>;
  getAvailableLanguages: () => SupportedLanguage[];
  getCurrentLanguageInfo: () => any;
}

// 翻译上下文
const TranslationContext = createContext<TranslationContextType | null>(null);

// 翻译提供者组件属性
interface TranslationProviderProps {
  children: ReactNode;
  defaultLanguage?: SupportedLanguage;
  fallbackLanguage?: SupportedLanguage;
  detectLanguage?: boolean;
}

// 翻译缓存
const translationCache = new Map<string, string>();

/**
 * 翻译提供者组件
 */
export function TranslationProvider({
  children,
  defaultLanguage = DEFAULT_LANGUAGE,
  fallbackLanguage = DEFAULT_LANGUAGE,
  detectLanguage = true,
}: TranslationProviderProps) {
  const [currentLanguage, setCurrentLanguage] = useState<SupportedLanguage>(defaultLanguage);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loadedLanguages, setLoadedLanguages] = useState<Set<string>>(new Set());

  // 初始化语言
  useEffect(() => {
    const initializeLanguage = async () => {
      if (!detectLanguage) return;

      try {
        setIsLoading(true);
        
        // 检测语言的优先级：用户设置 > Cookie > 本地存储 > 浏览器语言 > 默认语言
        let detectedLanguage = defaultLanguage;

        // 从Cookie检测
        if (typeof document !== 'undefined') {
          const cookieLanguage = getCookieLanguage();
          if (cookieLanguage && isLanguageSupported(cookieLanguage)) {
            detectedLanguage = cookieLanguage as SupportedLanguage;
          }
        }

        // 从本地存储检测
        if (typeof window !== 'undefined') {
          const storageLanguage = getStorageLanguage();
          if (storageLanguage && isLanguageSupported(storageLanguage)) {
            detectedLanguage = storageLanguage as SupportedLanguage;
          }
        }

        // 从浏览器语言检测
        const browserLanguage = getBrowserLanguage();
        if (browserLanguage && detectedLanguage === defaultLanguage) {
          detectedLanguage = browserLanguage;
        }

        // 设置检测到的语言
        if (detectedLanguage !== currentLanguage) {
          await changeLanguage(detectedLanguage, { saveToStorage: false, saveToCookie: false });
        }
      } catch (err) {
        console.error('语言初始化失败:', err);
        setError(err instanceof Error ? err.message : '语言初始化失败');
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);

  // 切换语言
  const changeLanguage = useCallback(async (
    language: SupportedLanguage,
    options: LanguageSwitchOptions = {}
  ) => {
    const {
      saveToStorage = true,
      saveToCookie = true,
      saveToUserSettings = false,
      reloadPage = false,
      updateUrl = false,
    } = options;

    try {
      setIsLoading(true);
      setError(null);

      // 验证语言是否支持
      if (!isLanguageSupported(language)) {
        throw new Error(`不支持的语言: ${language}`);
      }

      // 预加载常用命名空间的翻译
      await translationService.preloadTranslations(
        [language],
        [
          TranslationNamespace.COMMON,
          TranslationNamespace.AUTH,
          TranslationNamespace.DASHBOARD,
        ]
      );

      // 更新当前语言
      setCurrentLanguage(language);
      setLoadedLanguages(prev => new Set([...prev, language]));

      // 保存到Cookie
      if (saveToCookie && typeof document !== 'undefined') {
        setCookieLanguage(language);
      }

      // 保存到本地存储
      if (saveToStorage && typeof window !== 'undefined') {
        setStorageLanguage(language);
      }

      // 保存到用户设置
      if (saveToUserSettings) {
        await saveUserLanguageSettings(language);
      }

      // 更新URL
      if (updateUrl && typeof window !== 'undefined') {
        updateUrlLanguage(language);
      }

      // 更新HTML语言属性
      if (typeof document !== 'undefined') {
        document.documentElement.lang = language;
        document.documentElement.dir = getLanguageInfo(language)?.direction || 'ltr';
      }

      // 重新加载页面
      if (reloadPage && typeof window !== 'undefined') {
        window.location.reload();
      }
    } catch (err) {
      console.error('语言切换失败:', err);
      setError(err instanceof Error ? err.message : '语言切换失败');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 翻译函数（异步）
  const translate = useCallback(async (
    key: TranslationKey,
    options: TranslationOptions = {}
  ): Promise<string> => {
    const cacheKey = `${currentLanguage}:${options.namespace || TranslationNamespace.COMMON}:${key}:${JSON.stringify(options.params || {})}`;
    
    // 检查缓存
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    try {
      const translation = await translationService.translate(key, currentLanguage, options);
      
      // 缓存翻译结果
      translationCache.set(cacheKey, translation);
      
      return translation;
    } catch (err) {
      console.error('翻译失败:', err);
      return options.fallback || key;
    }
  }, [currentLanguage]);

  // 翻译函数（同步）
  const translateSync = useCallback((
    key: TranslationKey,
    options: TranslationOptions = {}
  ): string => {
    const cacheKey = `${currentLanguage}:${options.namespace || TranslationNamespace.COMMON}:${key}:${JSON.stringify(options.params || {})}`;
    
    // 检查缓存
    if (translationCache.has(cacheKey)) {
      return translationCache.get(cacheKey)!;
    }

    // 同步翻译只能返回回退文本或键名
    return options.fallback || key;
  }, [currentLanguage]);

  // 检查语言是否已加载
  const isLanguageLoaded = useCallback((
    language: SupportedLanguage,
    namespace?: TranslationNamespace
  ): boolean => {
    if (namespace) {
      return loadedLanguages.has(`${language}:${namespace}`);
    }
    return loadedLanguages.has(language);
  }, [loadedLanguages]);

  // 预加载语言
  const preloadLanguage = useCallback(async (
    language: SupportedLanguage,
    namespaces: TranslationNamespace[] = [TranslationNamespace.COMMON]
  ) => {
    try {
      await translationService.preloadTranslations([language], namespaces);
      
      setLoadedLanguages(prev => {
        const newSet = new Set(prev);
        newSet.add(language);
        namespaces.forEach(ns => newSet.add(`${language}:${ns}`));
        return newSet;
      });
    } catch (err) {
      console.error('预加载语言失败:', err);
    }
  }, []);

  // 获取可用语言
  const getAvailableLanguages = useCallback((): SupportedLanguage[] => {
    return Object.values(SupportedLanguage);
  }, []);

  // 获取当前语言信息
  const getCurrentLanguageInfo = useCallback(() => {
    return getLanguageInfo(currentLanguage);
  }, [currentLanguage]);

  const contextValue: TranslationContextType = {
    currentLanguage,
    isLoading,
    error,
    changeLanguage,
    translate,
    translateSync,
    isLanguageLoaded,
    preloadLanguage,
    getAvailableLanguages,
    getCurrentLanguageInfo,
  };

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  );
}

/**
 * 使用翻译Hook
 */
export function useTranslation(namespace?: TranslationNamespace) {
  const context = useContext(TranslationContext);
  
  if (!context) {
    throw new Error('useTranslation必须在TranslationProvider内使用');
  }

  const { translate, translateSync, ...rest } = context;

  // 包装翻译函数以自动使用指定的命名空间
  const t = useCallback(async (
    key: TranslationKey,
    options: Omit<TranslationOptions, 'namespace'> & { namespace?: TranslationNamespace } = {}
  ) => {
    return translate(key, {
      ...options,
      namespace: options.namespace || namespace || TranslationNamespace.COMMON,
    });
  }, [translate, namespace]);

  const tSync = useCallback((
    key: TranslationKey,
    options: Omit<TranslationOptions, 'namespace'> & { namespace?: TranslationNamespace } = {}
  ) => {
    return translateSync(key, {
      ...options,
      namespace: options.namespace || namespace || TranslationNamespace.COMMON,
    });
  }, [translateSync, namespace]);

  return {
    ...rest,
    t,
    tSync,
    translate: t,
    translateSync: tSync,
  };
}

/**
 * 使用语言切换Hook
 */
export function useLanguageSwitch() {
  const { currentLanguage, changeLanguage, getAvailableLanguages, getCurrentLanguageInfo } = useTranslation();

  return {
    currentLanguage,
    changeLanguage,
    availableLanguages: getAvailableLanguages(),
    currentLanguageInfo: getCurrentLanguageInfo(),
  };
}

// 辅助函数

/**
 * 从Cookie获取语言
 */
function getCookieLanguage(): string | null {
  if (typeof document === 'undefined') return null;
  
  const cookies = document.cookie.split(';');
  const languageCookie = cookies.find(cookie => 
    cookie.trim().startsWith(`${LANGUAGE_COOKIE_CONFIG.name}=`)
  );
  
  if (languageCookie) {
    return languageCookie.split('=')[1];
  }
  
  return null;
}

/**
 * 设置Cookie语言
 */
function setCookieLanguage(language: SupportedLanguage): void {
  if (typeof document === 'undefined') return;
  
  const expires = new Date();
  expires.setTime(expires.getTime() + LANGUAGE_COOKIE_CONFIG.maxAge * 1000);
  
  document.cookie = `${LANGUAGE_COOKIE_CONFIG.name}=${language}; expires=${expires.toUTCString()}; path=/; SameSite=${LANGUAGE_COOKIE_CONFIG.sameSite}${LANGUAGE_COOKIE_CONFIG.secure ? '; Secure' : ''}`;
}

/**
 * 从本地存储获取语言
 */
function getStorageLanguage(): string | null {
  if (typeof window === 'undefined') return null;
  
  const storage = LANGUAGE_STORAGE_CONFIG.storageType === 'localStorage' 
    ? window.localStorage 
    : window.sessionStorage;
    
  return storage.getItem(LANGUAGE_STORAGE_CONFIG.key);
}

/**
 * 设置本地存储语言
 */
function setStorageLanguage(language: SupportedLanguage): void {
  if (typeof window === 'undefined') return;
  
  const storage = LANGUAGE_STORAGE_CONFIG.storageType === 'localStorage' 
    ? window.localStorage 
    : window.sessionStorage;
    
  storage.setItem(LANGUAGE_STORAGE_CONFIG.key, language);
}

/**
 * 保存用户语言设置
 */
async function saveUserLanguageSettings(language: SupportedLanguage): Promise<void> {
  try {
    await fetch('/api/user/settings', {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        language,
      }),
    });
  } catch (error) {
    console.error('保存用户语言设置失败:', error);
  }
}

/**
 * 更新URL语言参数
 */
function updateUrlLanguage(language: SupportedLanguage): void {
  if (typeof window === 'undefined') return;
  
  const url = new URL(window.location.href);
  url.searchParams.set('lang', language);
  
  window.history.replaceState({}, '', url.toString());
}
