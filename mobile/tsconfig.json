{"extends": "@tsconfig/react-native/tsconfig.json", "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@screens/*": ["screens/*"], "@navigation/*": ["navigation/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@hooks/*": ["hooks/*"], "@store/*": ["store/*"], "@types/*": ["types/*"], "@assets/*": ["assets/*"], "@config/*": ["config/*"], "@constants/*": ["constants/*"], "@styles/*": ["styles/*"], "@themes/*": ["themes/*"]}}, "include": ["src/**/*", "index.js", "App.tsx"], "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js", "android", "ios"]}