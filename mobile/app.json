{"name": "CBECERPMobile", "displayName": "CBEC ERP", "description": "跨境电商ERP系统移动端应用", "version": "1.0.0", "versionCode": 1, "bundleId": "com.cbecerp.mobile", "packageName": "com.cbecerp.mobile", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "splash": {"image": "./src/assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "permissions": ["CAMERA", "WRITE_EXTERNAL_STORAGE", "READ_EXTERNAL_STORAGE", "ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "RECORD_AUDIO", "VIBRATE", "RECEIVE_BOOT_COMPLETED", "SYSTEM_ALERT_WINDOW", "INTERNET", "ACCESS_NETWORK_STATE", "ACCESS_WIFI_STATE", "CHANGE_WIFI_STATE", "BLUETOOTH", "BLUETOOTH_ADMIN", "NFC", "USE_FINGERPRINT", "USE_BIOMETRIC", "CALL_PHONE", "READ_PHONE_STATE", "SEND_SMS", "READ_SMS", "RECEIVE_SMS", "READ_CONTACTS", "WRITE_CONTACTS", "READ_CALENDAR", "WRITE_CALENDAR"], "android": {"compileSdkVersion": 34, "targetSdkVersion": 34, "minSdkVersion": 21, "buildToolsVersion": "34.0.0", "gradle": {"version": "8.0.2"}, "proguard": {"enabled": true}, "signing": {"release": {"storeFile": "release.keystore", "keyAlias": "cbec-erp-mobile", "storePassword": "cbec123456", "keyPassword": "cbec123456"}}}, "ios": {"deploymentTarget": "12.0", "bundleIdentifier": "com.cbecerp.mobile", "buildNumber": "1", "infoPlist": {"NSCameraUsageDescription": "应用需要访问相机来扫描二维码和拍照上传", "NSPhotoLibraryUsageDescription": "应用需要访问相册来选择和上传图片", "NSLocationWhenInUseUsageDescription": "应用需要访问位置信息来提供基于位置的服务", "NSLocationAlwaysAndWhenInUseUsageDescription": "应用需要访问位置信息来提供基于位置的服务", "NSMicrophoneUsageDescription": "应用需要访问麦克风来进行语音输入", "NSContactsUsageDescription": "应用需要访问通讯录来管理客户联系信息", "NSCalendarsUsageDescription": "应用需要访问日历来管理日程安排", "NSFaceIDUsageDescription": "应用使用Face ID来进行生物识别验证", "NSBluetoothAlwaysUsageDescription": "应用需要蓝牙权限来连接外部设备", "NSBluetoothPeripheralUsageDescription": "应用需要蓝牙权限来连接外部设备"}}}