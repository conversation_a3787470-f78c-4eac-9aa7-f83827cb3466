{"name": "cbec-erp-mobile", "version": "1.0.0", "description": "跨境电商ERP系统移动端应用", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "build:android": "cd android && ./gradlew assembleRelease", "build:ios": "cd ios && xcodebuild -workspace CBECERPMobile.xcworkspace -scheme CBECERPMobile -configuration Release -destination generic/platform=iOS -archivePath CBECERPMobile.xcarchive archive", "clean": "react-native clean-project-auto"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-vector-icons": "^10.0.2", "react-native-paper": "^5.11.1", "react-native-elements": "^3.4.3", "react-native-svg": "^13.14.0", "react-native-chart-kit": "^6.12.0", "react-native-camera": "^4.2.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-image-picker": "^7.0.3", "react-native-geolocation-service": "^5.3.1", "react-native-voice": "^3.2.4", "react-native-push-notification": "^8.1.1", "@react-native-async-storage/async-storage": "^1.19.5", "react-native-keychain": "^8.1.3", "react-native-device-info": "^10.11.0", "react-native-network-info": "^5.2.1", "react-native-permissions": "^3.10.1", "react-native-splash-screen": "^3.3.0", "react-native-orientation-locker": "^1.5.0", "react-native-share": "^9.4.1", "react-native-print": "^0.8.0", "react-native-pdf": "^6.7.3", "react-native-fs": "^2.20.0", "react-native-zip-archive": "^6.0.8", "react-native-date-picker": "^4.3.3", "react-native-modal": "^13.0.1", "react-native-super-grid": "^4.9.6", "react-native-swipe-list-view": "^3.2.9", "react-native-pull-to-refresh": "^2.1.3", "react-native-skeleton-placeholder": "^5.2.4", "react-native-shimmer": "^0.6.0", "react-native-linear-gradient": "^2.8.3", "react-native-blur": "^4.3.2", "react-native-haptic-feedback": "^2.2.0", "react-native-sound": "^0.11.2", "react-native-video": "^5.2.1", "react-native-webview": "^13.6.3", "react-native-maps": "^1.8.0", "react-native-contacts": "^7.0.8", "react-native-calendar-picker": "^7.1.4", "react-native-progress": "^5.0.1", "react-native-animatable": "^1.3.3", "react-native-lottie": "^6.4.1", "react-native-fast-image": "^8.6.3", "react-native-image-crop-picker": "^0.40.2", "react-native-document-picker": "^9.1.1", "react-native-file-viewer": "^2.1.5", "react-native-mail": "^6.1.1", "react-native-sms": "^2.1.1", "react-native-call-log": "^4.0.1", "react-native-biometrics": "^3.0.1", "react-native-touch-id": "^4.4.1", "react-native-face-id": "^2.0.0", "react-native-app-state": "^2.0.0", "react-native-background-task": "^0.2.1", "react-native-background-fetch": "^4.1.9", "react-native-code-push": "^8.2.1", "react-native-version-check": "^3.4.7", "react-native-app-intro-slider": "^4.0.4", "react-native-onboarding-swiper": "^1.2.0", "react-native-tutorial-coach-mark": "^4.0.0", "react-native-walkthrough-tooltip": "^1.5.0", "react-native-highlight-words": "^2.0.0", "react-native-search-filter": "^0.1.5", "react-native-sortable-list": "^0.0.24", "react-native-draggable-flatlist": "^4.0.1", "react-native-swipe-gestures": "^1.0.5", "react-native-pinch": "^2.0.0", "react-native-zoom-us": "^5.17.5", "react-native-agora": "^4.2.6", "react-native-jitsi-meet": "^5.1.0", "react-native-twilio-video-webrtc": "^2.8.2", "react-native-socket.io-client": "^2.0.2", "react-native-mqtt": "^1.3.0", "react-native-ble-plx": "^3.1.2", "react-native-nfc-manager": "^3.14.12", "react-native-wifi-reborn": "^4.12.0", "react-native-network-logger": "^1.14.0", "react-native-flipper": "^0.212.0", "react-native-debugger-open": "^0.3.25", "react-native-performance": "^5.1.0", "react-native-memory-info": "^1.1.1", "react-native-battery-status": "^1.0.4", "react-native-system-setting": "^1.7.6", "react-native-brightness": "^0.3.1", "react-native-volume-manager": "^1.10.0", "react-native-keep-awake": "^4.0.0", "react-native-screen-brightness": "^2.0.0-alpha", "react-native-auto-brightness": "^1.0.0", "react-native-system-navigation-bar": "^2.6.1", "react-native-status-bar-height": "^2.6.0", "react-native-immersive": "^2.0.0", "react-native-full-screen": "^1.1.0", "react-native-hide-show-password-input": "^1.2.0", "react-native-password-strength-meter": "^0.0.2", "react-native-pin-view": "^2.8.1", "react-native-otp-inputs": "^7.4.0", "react-native-countdown-component": "^2.7.1", "react-native-timer-countdown": "^1.1.3", "react-native-stopwatch-timer": "^0.0.21", "react-native-alarm-clock": "^1.0.8", "react-native-local-notifications": "^0.1.1", "react-native-push-notifications": "^1.0.7", "react-native-firebase": "^5.6.0", "react-native-google-analytics": "^1.3.2", "react-native-mixpanel": "^2.3.1", "react-native-amplitude-analytics": "^1.4.10", "react-native-appsflyer": "^6.12.2", "react-native-adjust": "^4.35.2", "react-native-branch": "^5.8.1", "react-native-facebook-login": "^1.4.0", "react-native-google-signin": "^10.1.1", "react-native-apple-authentication": "^2.2.2", "react-native-wechat-lib": "^1.1.29", "react-native-qq": "^2.0.0-alpha.5", "react-native-weibo": "^2.0.1", "react-native-alipay": "^2.0.3", "react-native-wechat-pay": "^1.0.8", "react-native-stripe-api": "^1.0.19", "react-native-paypal": "^2.1.3", "react-native-square-in-app-payments": "^1.7.4", "react-native-razorpay": "^2.3.0", "react-native-paytm": "^2.0.1", "react-native-upi-payment": "^2.0.1", "react-native-gpay": "^1.2.7", "react-native-amazon-pay": "^1.0.2", "react-native-samsung-pay": "^1.0.1", "react-native-huawei-pay": "^1.0.0", "react-native-xiaomi-pay": "^1.0.0", "react-native-oppo-pay": "^1.0.0", "react-native-vivo-pay": "^1.0.0", "react-native-crypto-js": "^1.0.0", "react-native-rsa": "^1.0.4", "react-native-aes-crypto": "^2.1.0", "react-native-sha256": "^1.4.10", "react-native-md5": "^1.0.0", "react-native-base64": "^0.2.1", "react-native-uuid": "^2.0.1", "react-native-jwt-decode": "^1.0.1", "react-native-oauth": "^2.1.17", "react-native-keychain-touchid": "^3.0.1", "react-native-secure-storage": "^1.0.10", "react-native-encrypted-storage": "^4.0.3", "react-native-mmkv": "^2.10.2", "react-native-realm": "^12.3.1", "react-native-sqlite-storage": "^6.0.1", "react-native-sqlite-2": "^3.7.1", "react-native-watermelondb": "^0.27.1", "react-native-pouchdb": "^0.1.0", "react-native-couchbase-lite": "^2.1.3", "react-native-mongodb-mobile": "^0.1.4", "react-native-firebase-firestore": "^3.2.8", "react-native-aws-amplify": "^1.1.3", "react-native-supabase": "^1.0.3", "react-native-hasura": "^1.0.1", "react-native-graphql": "^2.0.1", "react-native-apollo": "^1.0.1", "react-native-relay": "^0.2.1", "react-native-urql": "^1.0.1", "react-native-axios": "^0.17.1", "react-native-fetch": "^1.0.1", "react-native-superagent": "^1.0.1", "react-native-ky": "^1.0.1", "react-native-got": "^1.0.1", "react-native-node-fetch": "^1.0.1", "react-native-isomorphic-fetch": "^1.0.1", "react-native-whatwg-fetch": "^1.0.1", "react-native-cross-fetch": "^1.0.1", "react-native-unfetch": "^1.0.1", "react-native-reduxjs-toolkit": "^1.9.7", "react-native-redux": "^4.2.1", "react-native-redux-persist": "^6.0.0", "react-native-redux-saga": "^1.2.3", "react-native-redux-thunk": "^2.4.2", "react-native-redux-observable": "^2.0.0", "react-native-mobx": "^6.10.2", "react-native-mobx-state-tree": "^5.2.0", "react-native-zustand": "^4.4.7", "react-native-jotai": "^2.5.1", "react-native-valtio": "^1.11.2", "react-native-recoil": "^0.7.7", "react-native-context": "^1.0.1", "react-native-unstated": "^2.1.1", "react-native-easy-peasy": "^5.2.0", "react-native-hookstate": "^4.0.1", "react-native-react-query": "^3.39.3", "react-native-swr": "^2.2.4", "react-native-apollo-client": "^3.8.7", "react-native-relay-modern": "^1.0.1", "react-native-urql-client": "^1.0.1", "react-native-formik": "^2.4.5", "react-native-react-hook-form": "^7.47.0", "react-native-final-form": "^4.20.10", "react-native-unform": "^2.1.6", "react-native-tcomb-form": "^0.6.20", "react-native-yup": "^1.4.0", "react-native-joi": "^17.11.0", "react-native-zod": "^3.22.4", "react-native-ajv": "^8.12.0", "react-native-superstruct": "^1.0.3", "react-native-io-ts": "^2.2.21", "react-native-runtypes": "^6.7.0", "react-native-class-validator": "^0.14.0", "react-native-vest": "^5.0.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}, "keywords": ["react-native", "mobile", "erp", "ecommerce", "cross-border", "跨境电商", "移动端", "企业资源规划"], "author": "CBEC ERP Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cbec-erp/mobile.git"}, "bugs": {"url": "https://github.com/cbec-erp/mobile/issues"}, "homepage": "https://github.com/cbec-erp/mobile#readme"}