/**
 * 主应用导航组件
 * 管理已认证用户的应用内导航
 */

import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { createDrawerNavigator } from '@react-navigation/drawer';
import Icon from 'react-native-vector-icons/MaterialIcons';

// 导入屏幕组件
import DashboardScreen from '@screens/Dashboard/DashboardScreen';
import OrdersScreen from '@screens/Orders/OrdersScreen';
import OrderDetailScreen from '@screens/Orders/OrderDetailScreen';
import ProductsScreen from '@screens/Products/ProductsScreen';
import ProductDetailScreen from '@screens/Products/ProductDetailScreen';
import InventoryScreen from '@screens/Inventory/InventoryScreen';
import CustomersScreen from '@screens/Customers/CustomersScreen';
import CustomerDetailScreen from '@screens/Customers/CustomerDetailScreen';
import ReportsScreen from '@screens/Reports/ReportsScreen';
import SettingsScreen from '@screens/Settings/SettingsScreen';
import ProfileScreen from '@screens/Profile/ProfileScreen';
import NotificationsScreen from '@screens/Notifications/NotificationsScreen';
import ScannerScreen from '@screens/Scanner/ScannerScreen';
import CameraScreen from '@screens/Camera/CameraScreen';

// 导入AI功能屏幕
import AIScreen from '@screens/AI/AIScreen';
import RecommendationsScreen from '@screens/AI/RecommendationsScreen';
import PricingScreen from '@screens/AI/PricingScreen';
import InventoryPredictionScreen from '@screens/AI/InventoryPredictionScreen';
import CustomerServiceScreen from '@screens/AI/CustomerServiceScreen';

// 导入自定义组件
import { CustomDrawerContent } from '@components/CustomDrawerContent';
import { TabBarIcon } from '@components/TabBarIcon';

// 导入主题
import { useTheme } from 'react-native-paper';

// 创建导航器
const Tab = createBottomTabNavigator();
const Stack = createStackNavigator();
const Drawer = createDrawerNavigator();

/**
 * 订单相关页面堆栈导航
 */
const OrdersStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="OrdersList" component={OrdersScreen} />
      <Stack.Screen name="OrderDetail" component={OrderDetailScreen} />
    </Stack.Navigator>
  );
};

/**
 * 商品相关页面堆栈导航
 */
const ProductsStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="ProductsList" component={ProductsScreen} />
      <Stack.Screen name="ProductDetail" component={ProductDetailScreen} />
    </Stack.Navigator>
  );
};

/**
 * 客户相关页面堆栈导航
 */
const CustomersStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="CustomersList" component={CustomersScreen} />
      <Stack.Screen name="CustomerDetail" component={CustomerDetailScreen} />
    </Stack.Navigator>
  );
};

/**
 * AI功能页面堆栈导航
 */
const AIStack = () => {
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="AIHome" component={AIScreen} />
      <Stack.Screen name="Recommendations" component={RecommendationsScreen} />
      <Stack.Screen name="Pricing" component={PricingScreen} />
      <Stack.Screen name="InventoryPrediction" component={InventoryPredictionScreen} />
      <Stack.Screen name="CustomerService" component={CustomerServiceScreen} />
    </Stack.Navigator>
  );
};

/**
 * 底部标签导航
 */
const TabNavigator = () => {
  const theme = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = 'dashboard';
              break;
            case 'Orders':
              iconName = 'shopping-cart';
              break;
            case 'Products':
              iconName = 'inventory';
              break;
            case 'Customers':
              iconName = 'people';
              break;
            case 'AI':
              iconName = 'psychology';
              break;
            default:
              iconName = 'help';
          }

          return <TabBarIcon name={iconName} size={size} color={color} focused={focused} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
        },
        headerShown: false,
      })}
    >
      <Tab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{
          tabBarLabel: '仪表板',
        }}
      />
      <Tab.Screen 
        name="Orders" 
        component={OrdersStack}
        options={{
          tabBarLabel: '订单',
        }}
      />
      <Tab.Screen 
        name="Products" 
        component={ProductsStack}
        options={{
          tabBarLabel: '商品',
        }}
      />
      <Tab.Screen 
        name="Customers" 
        component={CustomersStack}
        options={{
          tabBarLabel: '客户',
        }}
      />
      <Tab.Screen 
        name="AI" 
        component={AIStack}
        options={{
          tabBarLabel: 'AI助手',
        }}
      />
    </Tab.Navigator>
  );
};

/**
 * 主应用导航器
 */
interface AppNavigatorProps {
  toggleTheme: () => void;
}

const AppNavigator: React.FC<AppNavigatorProps> = ({ toggleTheme }) => {
  const theme = useTheme();

  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} toggleTheme={toggleTheme} />}
      screenOptions={{
        headerShown: true,
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        drawerStyle: {
          backgroundColor: theme.colors.surface,
        },
        drawerActiveTintColor: theme.colors.primary,
        drawerInactiveTintColor: theme.colors.onSurfaceVariant,
      }}
    >
      <Drawer.Screen 
        name="Main" 
        component={TabNavigator}
        options={{
          title: 'CBEC ERP',
          drawerLabel: '主页',
          drawerIcon: ({ color, size }) => (
            <Icon name="home" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Inventory" 
        component={InventoryScreen}
        options={{
          title: '库存管理',
          drawerLabel: '库存管理',
          drawerIcon: ({ color, size }) => (
            <Icon name="warehouse" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Reports" 
        component={ReportsScreen}
        options={{
          title: '报表分析',
          drawerLabel: '报表分析',
          drawerIcon: ({ color, size }) => (
            <Icon name="analytics" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Scanner" 
        component={ScannerScreen}
        options={{
          title: '扫码功能',
          drawerLabel: '扫码功能',
          drawerIcon: ({ color, size }) => (
            <Icon name="qr-code-scanner" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Camera" 
        component={CameraScreen}
        options={{
          title: '拍照上传',
          drawerLabel: '拍照上传',
          drawerIcon: ({ color, size }) => (
            <Icon name="camera-alt" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{
          title: '通知中心',
          drawerLabel: '通知中心',
          drawerIcon: ({ color, size }) => (
            <Icon name="notifications" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{
          title: '个人资料',
          drawerLabel: '个人资料',
          drawerIcon: ({ color, size }) => (
            <Icon name="person" size={size} color={color} />
          ),
        }}
      />
      <Drawer.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{
          title: '设置',
          drawerLabel: '设置',
          drawerIcon: ({ color, size }) => (
            <Icon name="settings" size={size} color={color} />
          ),
        }}
      />
    </Drawer.Navigator>
  );
};

export default AppNavigator;
