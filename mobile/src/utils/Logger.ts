/**
 * 日志工具类
 * 提供统一的日志记录功能，支持不同级别的日志输出
 */

import { Platform } from 'react-native';

// 日志级别枚举
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

// 日志配置接口
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  maxFileSize: number;
  maxFiles: number;
}

// 默认配置
const defaultConfig: LoggerConfig = {
  level: __DEV__ ? LogLevel.DEBUG : LogLevel.INFO,
  enableConsole: true,
  enableFile: !__DEV__,
  maxFileSize: 5 * 1024 * 1024, // 5MB
  maxFiles: 5,
};

class LoggerClass {
  private config: LoggerConfig;
  private logs: LogEntry[] = [];

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
  }

  /**
   * 调试日志
   */
  debug(message: string, ...args: any[]) {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  /**
   * 信息日志
   */
  info(message: string, ...args: any[]) {
    this.log(LogLevel.INFO, message, ...args);
  }

  /**
   * 警告日志
   */
  warn(message: string, ...args: any[]) {
    this.log(LogLevel.WARN, message, ...args);
  }

  /**
   * 错误日志
   */
  error(message: string, ...args: any[]) {
    this.log(LogLevel.ERROR, message, ...args);
  }

  /**
   * 记录日志
   */
  private log(level: LogLevel, message: string, ...args: any[]) {
    if (level < this.config.level) {
      return;
    }

    const timestamp = new Date();
    const logEntry: LogEntry = {
      level,
      message,
      args,
      timestamp,
      platform: Platform.OS,
    };

    // 添加到内存日志
    this.logs.push(logEntry);
    this.trimLogs();

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(logEntry);
    }

    // 文件输出
    if (this.config.enableFile) {
      this.logToFile(logEntry);
    }
  }

  /**
   * 控制台输出
   */
  private logToConsole(entry: LogEntry) {
    const { level, message, args, timestamp } = entry;
    const timeStr = timestamp.toISOString();
    const levelStr = LogLevel[level];
    const fullMessage = `[${timeStr}] [${levelStr}] ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(fullMessage, ...args);
        break;
      case LogLevel.INFO:
        console.info(fullMessage, ...args);
        break;
      case LogLevel.WARN:
        console.warn(fullMessage, ...args);
        break;
      case LogLevel.ERROR:
        console.error(fullMessage, ...args);
        break;
    }
  }

  /**
   * 文件输出
   */
  private async logToFile(entry: LogEntry) {
    try {
      // 这里可以集成文件系统写入逻辑
      // 例如使用 react-native-fs
      const logLine = this.formatLogEntry(entry);
      // await RNFS.appendFile(logFilePath, logLine + '\n');
    } catch (error) {
      console.error('写入日志文件失败:', error);
    }
  }

  /**
   * 格式化日志条目
   */
  private formatLogEntry(entry: LogEntry): string {
    const { level, message, args, timestamp, platform } = entry;
    const timeStr = timestamp.toISOString();
    const levelStr = LogLevel[level];
    const argsStr = args.length > 0 ? ` | ${JSON.stringify(args)}` : '';
    return `[${timeStr}] [${platform}] [${levelStr}] ${message}${argsStr}`;
  }

  /**
   * 清理旧日志
   */
  private trimLogs() {
    const maxLogs = 1000;
    if (this.logs.length > maxLogs) {
      this.logs = this.logs.slice(-maxLogs);
    }
  }

  /**
   * 获取日志
   */
  getLogs(level?: LogLevel): LogEntry[] {
    if (level !== undefined) {
      return this.logs.filter(log => log.level >= level);
    }
    return [...this.logs];
  }

  /**
   * 清除日志
   */
  clearLogs() {
    this.logs = [];
  }

  /**
   * 导出日志
   */
  exportLogs(): string {
    return this.logs.map(entry => this.formatLogEntry(entry)).join('\n');
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<LoggerConfig>) {
    this.config = { ...this.config, ...config };
  }
}

// 日志条目接口
interface LogEntry {
  level: LogLevel;
  message: string;
  args: any[];
  timestamp: Date;
  platform: string;
}

// 导出单例实例
export const Logger = new LoggerClass();

// 导出类型
export type { LoggerConfig, LogEntry };
