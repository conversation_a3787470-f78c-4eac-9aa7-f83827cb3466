/**
 * CBEC ERP移动端主应用组件
 * 跨境电商ERP系统React Native应用的根组件
 */

import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
  Alert,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import SplashScreen from 'react-native-splash-screen';
import { enableScreens } from 'react-native-screens';
import AsyncStorage from '@react-native-async-storage/async-storage';

// 导入导航组件
import AppNavigator from '@navigation/AppNavigator';
import AuthNavigator from '@navigation/AuthNavigator';

// 导入状态管理
import { AuthProvider, useAuth } from '@store/AuthContext';
import { AppProvider } from '@store/AppContext';

// 导入主题
import { lightTheme, darkTheme } from '@themes/index';

// 导入服务
import { initializeServices } from '@services/index';
import { NetworkService } from '@services/NetworkService';
import { NotificationService } from '@services/NotificationService';
import { BiometricService } from '@services/BiometricService';

// 导入工具
import { Logger } from '@utils/Logger';
import { ErrorBoundary } from '@components/ErrorBoundary';
import { LoadingScreen } from '@components/LoadingScreen';

// 启用原生屏幕优化
enableScreens();

/**
 * 主应用组件
 */
const App: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  /**
   * 应用初始化
   */
  useEffect(() => {
    initializeApp();
  }, []);

  /**
   * 初始化应用
   */
  const initializeApp = async () => {
    try {
      Logger.info('应用开始初始化...');

      // 请求必要权限
      await requestPermissions();

      // 初始化服务
      await initializeServices();

      // 初始化网络服务
      await NetworkService.initialize();

      // 初始化通知服务
      await NotificationService.initialize();

      // 初始化生物识别服务
      await BiometricService.initialize();

      // 加载用户偏好设置
      await loadUserPreferences();

      // 检查应用更新
      await checkAppUpdate();

      setIsInitialized(true);
      Logger.info('应用初始化完成');

    } catch (error) {
      Logger.error('应用初始化失败:', error);
      Alert.alert(
        '初始化失败',
        '应用初始化过程中发生错误，请重启应用重试。',
        [{ text: '确定', onPress: () => {} }]
      );
    } finally {
      setIsLoading(false);
      // 隐藏启动屏幕
      SplashScreen.hide();
    }
  };

  /**
   * 请求必要权限
   */
  const requestPermissions = async () => {
    if (Platform.OS === 'android') {
      try {
        const permissions = [
          PermissionsAndroid.PERMISSIONS.CAMERA,
          PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          PermissionsAndroid.PERMISSIONS.ACCESS_COARSE_LOCATION,
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        ];

        const granted = await PermissionsAndroid.requestMultiple(permissions);
        
        // 检查权限授予情况
        const deniedPermissions = Object.keys(granted).filter(
          permission => granted[permission] !== PermissionsAndroid.RESULTS.GRANTED
        );

        if (deniedPermissions.length > 0) {
          Logger.warn('部分权限未授予:', deniedPermissions);
        }

      } catch (error) {
        Logger.error('权限请求失败:', error);
      }
    }
  };

  /**
   * 加载用户偏好设置
   */
  const loadUserPreferences = async () => {
    try {
      const darkModePreference = await AsyncStorage.getItem('darkMode');
      if (darkModePreference !== null) {
        setIsDarkMode(JSON.parse(darkModePreference));
      }
    } catch (error) {
      Logger.error('加载用户偏好设置失败:', error);
    }
  };

  /**
   * 检查应用更新
   */
  const checkAppUpdate = async () => {
    try {
      // 这里可以集成CodePush或其他更新服务
      Logger.info('检查应用更新...');
    } catch (error) {
      Logger.error('检查更新失败:', error);
    }
  };

  /**
   * 切换主题模式
   */
  const toggleTheme = async () => {
    const newTheme = !isDarkMode;
    setIsDarkMode(newTheme);
    try {
      await AsyncStorage.setItem('darkMode', JSON.stringify(newTheme));
    } catch (error) {
      Logger.error('保存主题偏好失败:', error);
    }
  };

  // 显示加载屏幕
  if (isLoading || !isInitialized) {
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <PaperProvider theme={isDarkMode ? darkTheme : lightTheme}>
          <AuthProvider>
            <AppProvider>
              <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={isDarkMode ? '#000000' : '#ffffff'}
                translucent={false}
              />
              <NavigationContainer theme={isDarkMode ? darkTheme : lightTheme}>
                <AppContent toggleTheme={toggleTheme} />
              </NavigationContainer>
            </AppProvider>
          </AuthProvider>
        </PaperProvider>
      </SafeAreaProvider>
    </ErrorBoundary>
  );
};

/**
 * 应用内容组件
 */
interface AppContentProps {
  toggleTheme: () => void;
}

const AppContent: React.FC<AppContentProps> = ({ toggleTheme }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <View style={styles.container}>
      {isAuthenticated ? (
        <AppNavigator toggleTheme={toggleTheme} />
      ) : (
        <AuthNavigator />
      )}
    </View>
  );
};

/**
 * 样式定义
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
