/**
 * 存储服务
 * 管理本地数据存储、缓存和数据同步
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { MMKV } from 'react-native-mmkv';
import EncryptedStorage from 'react-native-encrypted-storage';
import { Logger } from '@utils/Logger';

// 存储类型枚举
export enum StorageType {
  ASYNC = 'async',      // AsyncStorage - 异步存储
  MMKV = 'mmkv',        // MMKV - 高性能存储
  ENCRYPTED = 'encrypted' // EncryptedStorage - 加密存储
}

// 存储配置
interface StorageConfig {
  defaultType: StorageType;
  encryptSensitiveData: boolean;
  cacheExpiry: number; // 缓存过期时间（毫秒）
  maxCacheSize: number; // 最大缓存大小（字节）
}

// 缓存条目
interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  expiry: number;
  size: number;
}

class StorageServiceClass {
  private isInitialized = false;
  private mmkvInstance: MMKV | null = null;
  private config: StorageConfig = {
    defaultType: StorageType.ASYNC,
    encryptSensitiveData: true,
    cacheExpiry: 24 * 60 * 60 * 1000, // 24小时
    maxCacheSize: 50 * 1024 * 1024, // 50MB
  };
  private cacheSize = 0;

  /**
   * 初始化存储服务
   */
  async initialize(): Promise<void> {
    try {
      // 初始化MMKV
      this.mmkvInstance = new MMKV({
        id: 'cbec-erp-storage',
        encryptionKey: 'cbec-erp-encryption-key-2024',
      });

      // 加载配置
      await this.loadConfig();

      // 计算当前缓存大小
      await this.calculateCacheSize();

      // 清理过期缓存
      await this.cleanExpiredCache();

      this.isInitialized = true;
      Logger.info('存储服务初始化成功');
    } catch (error) {
      Logger.error('存储服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 存储数据
   */
  async setItem<T>(key: string, value: T, options: {
    type?: StorageType;
    encrypt?: boolean;
    expiry?: number;
  } = {}): Promise<void> {
    try {
      const {
        type = this.config.defaultType,
        encrypt = this.config.encryptSensitiveData,
        expiry = this.config.cacheExpiry,
      } = options;

      const serializedValue = JSON.stringify(value);
      const dataSize = new Blob([serializedValue]).size;

      // 检查缓存大小限制
      if (this.cacheSize + dataSize > this.config.maxCacheSize) {
        await this.cleanOldCache();
      }

      // 创建缓存条目
      const cacheEntry: CacheEntry<T> = {
        data: value,
        timestamp: Date.now(),
        expiry: Date.now() + expiry,
        size: dataSize,
      };

      const entryData = JSON.stringify(cacheEntry);

      switch (type) {
        case StorageType.ASYNC:
          await AsyncStorage.setItem(key, entryData);
          break;
        case StorageType.MMKV:
          if (this.mmkvInstance) {
            this.mmkvInstance.set(key, entryData);
          } else {
            throw new Error('MMKV未初始化');
          }
          break;
        case StorageType.ENCRYPTED:
          if (encrypt) {
            await EncryptedStorage.setItem(key, entryData);
          } else {
            await AsyncStorage.setItem(key, entryData);
          }
          break;
      }

      this.cacheSize += dataSize;
      Logger.debug(`数据已存储: ${key} (${dataSize} bytes)`);
    } catch (error) {
      Logger.error(`存储数据失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 获取数据
   */
  async getItem<T>(key: string, options: {
    type?: StorageType;
    defaultValue?: T;
    allowExpired?: boolean;
  } = {}): Promise<T | null> {
    try {
      const {
        type = this.config.defaultType,
        defaultValue = null,
        allowExpired = false,
      } = options;

      let entryData: string | null = null;

      switch (type) {
        case StorageType.ASYNC:
          entryData = await AsyncStorage.getItem(key);
          break;
        case StorageType.MMKV:
          if (this.mmkvInstance) {
            entryData = this.mmkvInstance.getString(key) || null;
          }
          break;
        case StorageType.ENCRYPTED:
          entryData = await EncryptedStorage.getItem(key);
          break;
      }

      if (!entryData) {
        return defaultValue;
      }

      const cacheEntry: CacheEntry<T> = JSON.parse(entryData);
      const now = Date.now();

      // 检查是否过期
      if (!allowExpired && now > cacheEntry.expiry) {
        await this.removeItem(key, { type });
        Logger.debug(`缓存已过期: ${key}`);
        return defaultValue;
      }

      Logger.debug(`数据已获取: ${key}`);
      return cacheEntry.data;
    } catch (error) {
      Logger.error(`获取数据失败 [${key}]:`, error);
      return options.defaultValue || null;
    }
  }

  /**
   * 删除数据
   */
  async removeItem(key: string, options: {
    type?: StorageType;
  } = {}): Promise<void> {
    try {
      const { type = this.config.defaultType } = options;

      // 先获取数据大小以更新缓存大小
      const existingData = await this.getItem(key, { type, allowExpired: true });
      if (existingData) {
        const dataSize = new Blob([JSON.stringify(existingData)]).size;
        this.cacheSize = Math.max(0, this.cacheSize - dataSize);
      }

      switch (type) {
        case StorageType.ASYNC:
          await AsyncStorage.removeItem(key);
          break;
        case StorageType.MMKV:
          if (this.mmkvInstance) {
            this.mmkvInstance.delete(key);
          }
          break;
        case StorageType.ENCRYPTED:
          await EncryptedStorage.removeItem(key);
          break;
      }

      Logger.debug(`数据已删除: ${key}`);
    } catch (error) {
      Logger.error(`删除数据失败 [${key}]:`, error);
      throw error;
    }
  }

  /**
   * 清空所有数据
   */
  async clear(type?: StorageType): Promise<void> {
    try {
      const targetType = type || this.config.defaultType;

      switch (targetType) {
        case StorageType.ASYNC:
          await AsyncStorage.clear();
          break;
        case StorageType.MMKV:
          if (this.mmkvInstance) {
            this.mmkvInstance.clearAll();
          }
          break;
        case StorageType.ENCRYPTED:
          await EncryptedStorage.clear();
          break;
      }

      if (!type) {
        this.cacheSize = 0;
      }

      Logger.info(`存储已清空: ${targetType}`);
    } catch (error) {
      Logger.error('清空存储失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有键
   */
  async getAllKeys(type?: StorageType): Promise<string[]> {
    try {
      const targetType = type || this.config.defaultType;

      switch (targetType) {
        case StorageType.ASYNC:
          return await AsyncStorage.getAllKeys();
        case StorageType.MMKV:
          if (this.mmkvInstance) {
            return this.mmkvInstance.getAllKeys();
          }
          return [];
        case StorageType.ENCRYPTED:
          // EncryptedStorage没有getAllKeys方法
          return [];
        default:
          return [];
      }
    } catch (error) {
      Logger.error('获取所有键失败:', error);
      return [];
    }
  }

  /**
   * 批量获取数据
   */
  async multiGet<T>(keys: string[], options: {
    type?: StorageType;
  } = {}): Promise<Array<[string, T | null]>> {
    try {
      const { type = this.config.defaultType } = options;
      const results: Array<[string, T | null]> = [];

      for (const key of keys) {
        const value = await this.getItem<T>(key, { type });
        results.push([key, value]);
      }

      return results;
    } catch (error) {
      Logger.error('批量获取数据失败:', error);
      throw error;
    }
  }

  /**
   * 批量设置数据
   */
  async multiSet<T>(keyValuePairs: Array<[string, T]>, options: {
    type?: StorageType;
    encrypt?: boolean;
    expiry?: number;
  } = {}): Promise<void> {
    try {
      for (const [key, value] of keyValuePairs) {
        await this.setItem(key, value, options);
      }
    } catch (error) {
      Logger.error('批量设置数据失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除数据
   */
  async multiRemove(keys: string[], options: {
    type?: StorageType;
  } = {}): Promise<void> {
    try {
      for (const key of keys) {
        await this.removeItem(key, options);
      }
    } catch (error) {
      Logger.error('批量删除数据失败:', error);
      throw error;
    }
  }

  /**
   * 检查键是否存在
   */
  async hasItem(key: string, options: {
    type?: StorageType;
  } = {}): Promise<boolean> {
    try {
      const value = await this.getItem(key, options);
      return value !== null;
    } catch (error) {
      Logger.error(`检查键存在失败 [${key}]:`, error);
      return false;
    }
  }

  /**
   * 获取存储大小信息
   */
  async getStorageInfo(): Promise<{
    totalSize: number;
    usedSize: number;
    availableSize: number;
    itemCount: number;
  }> {
    try {
      const keys = await this.getAllKeys();
      const itemCount = keys.length;
      const usedSize = this.cacheSize;
      const totalSize = this.config.maxCacheSize;
      const availableSize = totalSize - usedSize;

      return {
        totalSize,
        usedSize,
        availableSize,
        itemCount,
      };
    } catch (error) {
      Logger.error('获取存储信息失败:', error);
      return {
        totalSize: 0,
        usedSize: 0,
        availableSize: 0,
        itemCount: 0,
      };
    }
  }

  /**
   * 计算缓存大小
   */
  private async calculateCacheSize(): Promise<void> {
    try {
      const keys = await this.getAllKeys();
      let totalSize = 0;

      for (const key of keys) {
        try {
          const data = await this.getItem(key, { allowExpired: true });
          if (data) {
            const size = new Blob([JSON.stringify(data)]).size;
            totalSize += size;
          }
        } catch (error) {
          // 忽略单个项目的错误
        }
      }

      this.cacheSize = totalSize;
      Logger.debug(`缓存大小计算完成: ${totalSize} bytes`);
    } catch (error) {
      Logger.error('计算缓存大小失败:', error);
    }
  }

  /**
   * 清理过期缓存
   */
  private async cleanExpiredCache(): Promise<void> {
    try {
      const keys = await this.getAllKeys();
      const expiredKeys: string[] = [];

      for (const key of keys) {
        try {
          const data = await this.getItem(key, { allowExpired: true });
          if (!data) {
            expiredKeys.push(key);
          }
        } catch (error) {
          // 如果无法读取，也认为是过期的
          expiredKeys.push(key);
        }
      }

      if (expiredKeys.length > 0) {
        await this.multiRemove(expiredKeys);
        Logger.info(`清理过期缓存: ${expiredKeys.length} 个项目`);
      }
    } catch (error) {
      Logger.error('清理过期缓存失败:', error);
    }
  }

  /**
   * 清理旧缓存（LRU策略）
   */
  private async cleanOldCache(): Promise<void> {
    try {
      const keys = await this.getAllKeys();
      const cacheEntries: Array<{ key: string; timestamp: number; size: number }> = [];

      // 获取所有缓存条目的时间戳
      for (const key of keys) {
        try {
          const entryData = await AsyncStorage.getItem(key);
          if (entryData) {
            const entry: CacheEntry = JSON.parse(entryData);
            cacheEntries.push({
              key,
              timestamp: entry.timestamp,
              size: entry.size,
            });
          }
        } catch (error) {
          // 忽略错误
        }
      }

      // 按时间戳排序（最旧的在前）
      cacheEntries.sort((a, b) => a.timestamp - b.timestamp);

      // 删除最旧的缓存直到释放足够空间
      const targetSize = this.config.maxCacheSize * 0.8; // 释放到80%
      let currentSize = this.cacheSize;

      for (const entry of cacheEntries) {
        if (currentSize <= targetSize) break;

        await this.removeItem(entry.key);
        currentSize -= entry.size;
      }

      Logger.info('旧缓存清理完成');
    } catch (error) {
      Logger.error('清理旧缓存失败:', error);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<StorageConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    Logger.info('存储配置已更新:', newConfig);
  }

  /**
   * 获取配置
   */
  getConfig(): StorageConfig {
    return { ...this.config };
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const configJson = await AsyncStorage.getItem('storageConfig');
      if (configJson) {
        const savedConfig = JSON.parse(configJson);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      Logger.error('加载存储配置失败:', error);
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<void> {
    try {
      await AsyncStorage.setItem('storageConfig', JSON.stringify(this.config));
    } catch (error) {
      Logger.error('保存存储配置失败:', error);
    }
  }
}

// 导出单例实例
export const StorageService = new StorageServiceClass();
