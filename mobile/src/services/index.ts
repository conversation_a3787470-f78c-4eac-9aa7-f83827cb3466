/**
 * 服务初始化模块
 * 统一管理和初始化所有应用服务
 */

import { Logger } from '@utils/Logger';
import { NetworkService } from './NetworkService';
import { NotificationService } from './NotificationService';
import { BiometricService } from './BiometricService';
import { StorageService } from './StorageService';
import { AnalyticsService } from './AnalyticsService';

// 服务状态类型
interface ServiceStatus {
  name: string;
  initialized: boolean;
  error?: string;
}

// 服务管理器类
class ServiceManager {
  private services: Map<string, any> = new Map();
  private initializationPromise: Promise<void> | null = null;

  /**
   * 初始化所有服务
   */
  async initializeServices(): Promise<void> {
    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._initializeServices();
    return this.initializationPromise;
  }

  /**
   * 内部初始化方法
   */
  private async _initializeServices(): Promise<void> {
    Logger.info('开始初始化应用服务...');

    const serviceInitializers = [
      { name: 'Storage', service: StorageService, init: () => StorageService.initialize() },
      { name: 'Network', service: NetworkService, init: () => NetworkService.initialize() },
      { name: 'Notification', service: NotificationService, init: () => NotificationService.initialize() },
      { name: 'Biometric', service: BiometricService, init: () => BiometricService.initialize() },
      { name: 'Analytics', service: AnalyticsService, init: () => AnalyticsService.initialize() },
    ];

    const results: ServiceStatus[] = [];

    for (const { name, service, init } of serviceInitializers) {
      try {
        Logger.info(`初始化${name}服务...`);
        await init();
        this.services.set(name, service);
        results.push({ name, initialized: true });
        Logger.info(`${name}服务初始化成功`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '未知错误';
        results.push({ name, initialized: false, error: errorMessage });
        Logger.error(`${name}服务初始化失败:`, error);
      }
    }

    // 输出初始化结果
    const successCount = results.filter(r => r.initialized).length;
    const totalCount = results.length;
    
    Logger.info(`服务初始化完成: ${successCount}/${totalCount} 个服务成功初始化`);

    if (successCount < totalCount) {
      const failedServices = results.filter(r => !r.initialized);
      Logger.warn('部分服务初始化失败:', failedServices);
    }
  }

  /**
   * 获取服务实例
   */
  getService<T>(name: string): T | null {
    return this.services.get(name) || null;
  }

  /**
   * 检查服务是否已初始化
   */
  isServiceInitialized(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * 获取所有服务状态
   */
  getServicesStatus(): ServiceStatus[] {
    const serviceNames = ['Storage', 'Network', 'Notification', 'Biometric', 'Analytics'];
    return serviceNames.map(name => ({
      name,
      initialized: this.isServiceInitialized(name),
    }));
  }

  /**
   * 重新初始化特定服务
   */
  async reinitializeService(name: string): Promise<void> {
    try {
      Logger.info(`重新初始化${name}服务...`);
      
      switch (name) {
        case 'Storage':
          await StorageService.initialize();
          break;
        case 'Network':
          await NetworkService.initialize();
          break;
        case 'Notification':
          await NotificationService.initialize();
          break;
        case 'Biometric':
          await BiometricService.initialize();
          break;
        case 'Analytics':
          await AnalyticsService.initialize();
          break;
        default:
          throw new Error(`未知服务: ${name}`);
      }

      Logger.info(`${name}服务重新初始化成功`);
    } catch (error) {
      Logger.error(`${name}服务重新初始化失败:`, error);
      throw error;
    }
  }

  /**
   * 清理所有服务
   */
  async cleanup(): Promise<void> {
    Logger.info('开始清理应用服务...');

    const cleanupPromises = [];

    if (this.isServiceInitialized('Analytics')) {
      cleanupPromises.push(AnalyticsService.cleanup?.());
    }

    if (this.isServiceInitialized('Notification')) {
      cleanupPromises.push(NotificationService.cleanup?.());
    }

    if (this.isServiceInitialized('Network')) {
      cleanupPromises.push(NetworkService.cleanup?.());
    }

    try {
      await Promise.all(cleanupPromises);
      this.services.clear();
      this.initializationPromise = null;
      Logger.info('应用服务清理完成');
    } catch (error) {
      Logger.error('服务清理过程中发生错误:', error);
    }
  }
}

// 创建服务管理器实例
const serviceManager = new ServiceManager();

// 导出初始化函数
export const initializeServices = () => serviceManager.initializeServices();

// 导出服务管理器
export { serviceManager as ServiceManager };

// 导出服务类型
export type { ServiceStatus };
