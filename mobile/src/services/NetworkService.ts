/**
 * 网络服务
 * 管理网络请求、连接状态监控和离线缓存
 */

import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Logger } from '@utils/Logger';

// 网络状态类型
export interface NetworkState {
  isConnected: boolean;
  connectionType: string;
  isSlowConnection: boolean;
  isInternetReachable: boolean | null;
}

// 请求配置类型
interface RequestConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  cache?: boolean;
  retry?: number;
}

// 缓存条目类型
interface CacheEntry {
  data: any;
  timestamp: number;
  expiry: number;
}

class NetworkServiceClass {
  private baseURL: string = '';
  private defaultHeaders: Record<string, string> = {};
  private networkState: NetworkState | null = null;
  private listeners: ((state: NetworkState) => void)[] = [];
  private requestQueue: QueuedRequest[] = [];
  private isInitialized = false;

  /**
   * 初始化网络服务
   */
  async initialize(): Promise<void> {
    try {
      // 设置基础URL
      this.baseURL = __DEV__ 
        ? 'http://localhost:3000/api'
        : 'https://api.cbec-erp.com/api';

      // 设置默认请求头
      this.defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': 'CBEC-ERP-Mobile/1.0.0',
      };

      // 监听网络状态变化
      NetInfo.addEventListener(this.handleNetworkStateChange.bind(this));

      // 获取初始网络状态
      const netInfoState = await NetInfo.fetch();
      this.handleNetworkStateChange(netInfoState);

      // 处理离线队列
      this.processOfflineQueue();

      this.isInitialized = true;
      Logger.info('网络服务初始化成功');
    } catch (error) {
      Logger.error('网络服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 处理网络状态变化
   */
  private handleNetworkStateChange(state: any) {
    const networkState: NetworkState = {
      isConnected: state.isConnected ?? false,
      connectionType: state.type || 'unknown',
      isSlowConnection: state.details?.isConnectionExpensive ?? false,
      isInternetReachable: state.isInternetReachable,
    };

    const previousState = this.networkState;
    this.networkState = networkState;

    // 通知监听器
    this.listeners.forEach(listener => listener(networkState));

    // 如果从离线变为在线，处理队列
    if (previousState && !previousState.isConnected && networkState.isConnected) {
      Logger.info('网络连接恢复，处理离线队列');
      this.processOfflineQueue();
    }

    Logger.info('网络状态变化:', networkState);
  }

  /**
   * 添加网络状态监听器
   */
  addNetworkListener(listener: (state: NetworkState) => void): () => void {
    this.listeners.push(listener);
    
    // 如果已有状态，立即调用
    if (this.networkState) {
      listener(this.networkState);
    }

    // 返回取消监听的函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * 获取当前网络状态
   */
  getNetworkState(): NetworkState | null {
    return this.networkState;
  }

  /**
   * 发送HTTP请求
   */
  async request<T = any>(url: string, config: RequestConfig = { method: 'GET' }): Promise<T> {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    // 检查缓存
    if (config.method === 'GET' && config.cache !== false) {
      const cached = await this.getFromCache(fullUrl);
      if (cached) {
        Logger.debug('从缓存返回数据:', fullUrl);
        return cached;
      }
    }

    // 检查网络连接
    if (!this.networkState?.isConnected) {
      // 如果是GET请求，尝试从缓存获取
      if (config.method === 'GET') {
        const cached = await this.getFromCache(fullUrl, true); // 允许过期缓存
        if (cached) {
          Logger.warn('网络离线，返回过期缓存:', fullUrl);
          return cached;
        }
      }

      // 添加到离线队列
      if (config.method !== 'GET') {
        await this.addToOfflineQueue(fullUrl, config);
        throw new Error('网络连接不可用，请求已加入离线队列');
      }

      throw new Error('网络连接不可用');
    }

    try {
      const response = await this.executeRequest<T>(fullUrl, config);
      
      // 缓存GET请求结果
      if (config.method === 'GET' && config.cache !== false) {
        await this.saveToCache(fullUrl, response);
      }

      return response;
    } catch (error) {
      // 重试逻辑
      if (config.retry && config.retry > 0) {
        Logger.warn(`请求失败，重试中... 剩余重试次数: ${config.retry}`);
        return this.request(url, { ...config, retry: config.retry - 1 });
      }

      Logger.error('网络请求失败:', error);
      throw error;
    }
  }

  /**
   * 执行HTTP请求
   */
  private async executeRequest<T>(url: string, config: RequestConfig): Promise<T> {
    const { method, headers = {}, body, timeout = 10000 } = config;

    const requestHeaders = {
      ...this.defaultHeaders,
      ...headers,
    };

    // 添加认证头
    const token = await AsyncStorage.getItem('authToken');
    if (token) {
      requestHeaders.Authorization = `Bearer ${token}`;
    }

    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
    };

    if (body && method !== 'GET') {
      requestConfig.body = typeof body === 'string' ? body : JSON.stringify(body);
    }

    // 创建超时Promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), timeout);
    });

    const fetchPromise = fetch(url, requestConfig);

    const response = await Promise.race([fetchPromise, timeoutPromise]);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    }

    return response.text() as any;
  }

  /**
   * 缓存相关方法
   */
  private async getFromCache(key: string, allowExpired = false): Promise<any> {
    try {
      const cached = await AsyncStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const entry: CacheEntry = JSON.parse(cached);
      const now = Date.now();

      if (!allowExpired && now > entry.expiry) {
        await AsyncStorage.removeItem(`cache_${key}`);
        return null;
      }

      return entry.data;
    } catch (error) {
      Logger.error('读取缓存失败:', error);
      return null;
    }
  }

  private async saveToCache(key: string, data: any, ttl = 300000): Promise<void> {
    try {
      const entry: CacheEntry = {
        data,
        timestamp: Date.now(),
        expiry: Date.now() + ttl,
      };

      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(entry));
    } catch (error) {
      Logger.error('保存缓存失败:', error);
    }
  }

  /**
   * 离线队列相关方法
   */
  private async addToOfflineQueue(url: string, config: RequestConfig): Promise<void> {
    const queuedRequest: QueuedRequest = {
      id: `${Date.now()}_${Math.random()}`,
      url,
      config,
      timestamp: Date.now(),
    };

    this.requestQueue.push(queuedRequest);

    // 持久化队列
    await AsyncStorage.setItem('offlineQueue', JSON.stringify(this.requestQueue));
    Logger.info('请求已添加到离线队列:', url);
  }

  private async processOfflineQueue(): Promise<void> {
    try {
      // 加载持久化的队列
      const queueData = await AsyncStorage.getItem('offlineQueue');
      if (queueData) {
        this.requestQueue = JSON.parse(queueData);
      }

      if (this.requestQueue.length === 0) return;

      Logger.info(`处理离线队列，共 ${this.requestQueue.length} 个请求`);

      const processedRequests: string[] = [];

      for (const queuedRequest of this.requestQueue) {
        try {
          await this.executeRequest(queuedRequest.url, queuedRequest.config);
          processedRequests.push(queuedRequest.id);
          Logger.info('离线请求处理成功:', queuedRequest.url);
        } catch (error) {
          Logger.error('离线请求处理失败:', queuedRequest.url, error);
        }
      }

      // 移除已处理的请求
      this.requestQueue = this.requestQueue.filter(
        req => !processedRequests.includes(req.id)
      );

      // 更新持久化队列
      await AsyncStorage.setItem('offlineQueue', JSON.stringify(this.requestQueue));

      Logger.info(`离线队列处理完成，成功: ${processedRequests.length} 个`);
    } catch (error) {
      Logger.error('处理离线队列失败:', error);
    }
  }

  /**
   * 清理服务
   */
  async cleanup(): Promise<void> {
    this.listeners = [];
    this.requestQueue = [];
    await AsyncStorage.removeItem('offlineQueue');
    Logger.info('网络服务已清理');
  }
}

// 队列请求类型
interface QueuedRequest {
  id: string;
  url: string;
  config: RequestConfig;
  timestamp: number;
}

// 导出单例实例
export const NetworkService = new NetworkServiceClass();
