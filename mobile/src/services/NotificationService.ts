/**
 * 通知服务
 * 管理推送通知、本地通知和通知权限
 */

import PushNotification from 'react-native-push-notification';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import { Platform, PermissionsAndroid } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Logger } from '@utils/Logger';

// 通知类型
export interface NotificationData {
  id?: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  sound?: string;
  vibrate?: boolean;
  priority?: 'high' | 'normal' | 'low';
  category?: string;
  actions?: NotificationAction[];
}

export interface NotificationAction {
  id: string;
  title: string;
  icon?: string;
}

// 通知配置
interface NotificationConfig {
  enablePush: boolean;
  enableLocal: boolean;
  enableSound: boolean;
  enableVibration: boolean;
  quietHours: {
    enabled: boolean;
    start: string; // HH:mm
    end: string;   // HH:mm
  };
}

class NotificationServiceClass {
  private isInitialized = false;
  private config: NotificationConfig = {
    enablePush: true,
    enableLocal: true,
    enableSound: true,
    enableVibration: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00',
    },
  };
  private deviceToken: string | null = null;
  private notificationHandlers: Map<string, (notification: any) => void> = new Map();

  /**
   * 初始化通知服务
   */
  async initialize(): Promise<void> {
    try {
      // 加载配置
      await this.loadConfig();

      // 请求权限
      await this.requestPermissions();

      // 配置推送通知
      this.configurePushNotifications();

      // 配置本地通知
      this.configureLocalNotifications();

      this.isInitialized = true;
      Logger.info('通知服务初始化成功');
    } catch (error) {
      Logger.error('通知服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 请求通知权限
   */
  private async requestPermissions(): Promise<boolean> {
    try {
      if (Platform.OS === 'ios') {
        const permissions = await PushNotificationIOS.requestPermissions({
          alert: true,
          badge: true,
          sound: true,
        });
        return permissions.alert || permissions.badge || permissions.sound;
      } else {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      }
    } catch (error) {
      Logger.error('请求通知权限失败:', error);
      return false;
    }
  }

  /**
   * 配置推送通知
   */
  private configurePushNotifications(): void {
    PushNotification.configure({
      // 接收到推送通知时的回调
      onNotification: (notification) => {
        Logger.info('收到推送通知:', notification);
        this.handleNotification(notification);

        // iOS需要调用finish
        if (Platform.OS === 'ios') {
          notification.finish(PushNotificationIOS.FetchResult.NoData);
        }
      },

      // 注册推送通知时的回调
      onRegister: (token) => {
        Logger.info('推送通知注册成功:', token);
        this.deviceToken = token.token;
        this.saveDeviceToken(token.token);
      },

      // 注册失败时的回调
      onRegistrationError: (error) => {
        Logger.error('推送通知注册失败:', error);
      },

      // 权限设置
      permissions: {
        alert: true,
        badge: true,
        sound: true,
      },

      // 是否弹出权限请求对话框
      requestPermissions: true,
    });
  }

  /**
   * 配置本地通知
   */
  private configureLocalNotifications(): void {
    // 创建默认通知频道（Android）
    if (Platform.OS === 'android') {
      PushNotification.createChannel(
        {
          channelId: 'default',
          channelName: '默认通知',
          channelDescription: '应用默认通知频道',
          playSound: this.config.enableSound,
          soundName: 'default',
          importance: 4,
          vibrate: this.config.enableVibration,
        },
        (created) => {
          Logger.info('默认通知频道创建:', created);
        }
      );

      // 创建重要通知频道
      PushNotification.createChannel(
        {
          channelId: 'important',
          channelName: '重要通知',
          channelDescription: '重要业务通知频道',
          playSound: true,
          soundName: 'default',
          importance: 5,
          vibrate: true,
        },
        (created) => {
          Logger.info('重要通知频道创建:', created);
        }
      );
    }
  }

  /**
   * 发送本地通知
   */
  async sendLocalNotification(notification: NotificationData): Promise<void> {
    if (!this.config.enableLocal) {
      Logger.info('本地通知已禁用');
      return;
    }

    // 检查静音时间
    if (this.isQuietHours()) {
      Logger.info('当前为静音时间，跳过通知');
      return;
    }

    try {
      const notificationId = notification.id || Date.now().toString();

      PushNotification.localNotification({
        id: notificationId,
        title: notification.title,
        message: notification.message,
        playSound: this.config.enableSound,
        soundName: notification.sound || 'default',
        vibrate: this.config.enableVibration,
        vibration: 300,
        priority: notification.priority || 'normal',
        channelId: notification.priority === 'high' ? 'important' : 'default',
        userInfo: notification.data || {},
        actions: notification.actions?.map(action => action.title) || [],
      });

      Logger.info('本地通知已发送:', notification.title);
    } catch (error) {
      Logger.error('发送本地通知失败:', error);
    }
  }

  /**
   * 发送推送通知（通过服务器）
   */
  async sendPushNotification(notification: NotificationData): Promise<void> {
    if (!this.config.enablePush || !this.deviceToken) {
      Logger.warn('推送通知未启用或设备令牌不可用');
      return;
    }

    try {
      // 发送到服务器
      const response = await fetch('/api/notifications/push', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          deviceToken: this.deviceToken,
          notification,
        }),
      });

      if (response.ok) {
        Logger.info('推送通知发送成功');
      } else {
        throw new Error('推送通知发送失败');
      }
    } catch (error) {
      Logger.error('发送推送通知失败:', error);
    }
  }

  /**
   * 处理通知点击
   */
  private handleNotification(notification: any): void {
    const { userInfo, data, action } = notification;
    const notificationData = userInfo || data || {};

    // 调用注册的处理器
    const category = notificationData.category || 'default';
    const handler = this.notificationHandlers.get(category);

    if (handler) {
      handler(notification);
    } else {
      // 默认处理逻辑
      this.handleDefaultNotification(notification);
    }
  }

  /**
   * 默认通知处理
   */
  private handleDefaultNotification(notification: any): void {
    Logger.info('处理默认通知:', notification);
    
    // 这里可以添加默认的通知处理逻辑
    // 例如导航到特定页面、更新应用状态等
  }

  /**
   * 注册通知处理器
   */
  registerNotificationHandler(category: string, handler: (notification: any) => void): void {
    this.notificationHandlers.set(category, handler);
    Logger.info(`注册通知处理器: ${category}`);
  }

  /**
   * 取消通知处理器
   */
  unregisterNotificationHandler(category: string): void {
    this.notificationHandlers.delete(category);
    Logger.info(`取消通知处理器: ${category}`);
  }

  /**
   * 取消本地通知
   */
  cancelLocalNotification(notificationId: string): void {
    PushNotification.cancelLocalNotifications({ id: notificationId });
    Logger.info('取消本地通知:', notificationId);
  }

  /**
   * 取消所有本地通知
   */
  cancelAllLocalNotifications(): void {
    PushNotification.cancelAllLocalNotifications();
    Logger.info('取消所有本地通知');
  }

  /**
   * 获取应用角标数量
   */
  getBadgeCount(): Promise<number> {
    return new Promise((resolve) => {
      if (Platform.OS === 'ios') {
        PushNotificationIOS.getApplicationIconBadgeNumber(resolve);
      } else {
        resolve(0); // Android不支持角标
      }
    });
  }

  /**
   * 设置应用角标数量
   */
  setBadgeCount(count: number): void {
    if (Platform.OS === 'ios') {
      PushNotificationIOS.setApplicationIconBadgeNumber(count);
    }
    Logger.info('设置应用角标:', count);
  }

  /**
   * 检查是否为静音时间
   */
  private isQuietHours(): boolean {
    if (!this.config.quietHours.enabled) {
      return false;
    }

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { start, end } = this.config.quietHours;
    
    if (start <= end) {
      return currentTime >= start && currentTime <= end;
    } else {
      return currentTime >= start || currentTime <= end;
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<NotificationConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    Logger.info('通知配置已更新:', newConfig);
  }

  /**
   * 获取配置
   */
  getConfig(): NotificationConfig {
    return { ...this.config };
  }

  /**
   * 获取设备令牌
   */
  getDeviceToken(): string | null {
    return this.deviceToken;
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const configJson = await AsyncStorage.getItem('notificationConfig');
      if (configJson) {
        const savedConfig = JSON.parse(configJson);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      Logger.error('加载通知配置失败:', error);
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<void> {
    try {
      await AsyncStorage.setItem('notificationConfig', JSON.stringify(this.config));
    } catch (error) {
      Logger.error('保存通知配置失败:', error);
    }
  }

  /**
   * 保存设备令牌
   */
  private async saveDeviceToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('deviceToken', token);
    } catch (error) {
      Logger.error('保存设备令牌失败:', error);
    }
  }

  /**
   * 清理服务
   */
  async cleanup(): Promise<void> {
    this.notificationHandlers.clear();
    this.cancelAllLocalNotifications();
    Logger.info('通知服务已清理');
  }
}

// 导出单例实例
export const NotificationService = new NotificationServiceClass();
