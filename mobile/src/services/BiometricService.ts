/**
 * 生物识别服务
 * 管理指纹识别、面部识别等生物识别功能
 */

import TouchID from 'react-native-touch-id';
import ReactNativeBiometrics from 'react-native-biometrics';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Logger } from '@utils/Logger';

// 生物识别类型
export type BiometricType = 'TouchID' | 'FaceID' | 'Fingerprint' | 'Face' | 'Iris';

// 生物识别配置
interface BiometricConfig {
  enabled: boolean;
  autoLogin: boolean;
  fallbackToPassword: boolean;
  maxAttempts: number;
}

// 认证选项
interface AuthenticationOptions {
  title?: string;
  subtitle?: string;
  description?: string;
  fallbackLabel?: string;
  cancelLabel?: string;
  disableDeviceFallback?: boolean;
}

// 认证结果
interface AuthenticationResult {
  success: boolean;
  error?: string;
  biometryType?: BiometricType;
}

class BiometricServiceClass {
  private isInitialized = false;
  private isSupported = false;
  private availableBiometrics: BiometricType[] = [];
  private config: BiometricConfig = {
    enabled: false,
    autoLogin: false,
    fallbackToPassword: true,
    maxAttempts: 3,
  };

  /**
   * 初始化生物识别服务
   */
  async initialize(): Promise<void> {
    try {
      // 检查设备支持
      await this.checkBiometricSupport();

      // 加载配置
      await this.loadConfig();

      this.isInitialized = true;
      Logger.info('生物识别服务初始化成功', {
        supported: this.isSupported,
        biometrics: this.availableBiometrics,
      });
    } catch (error) {
      Logger.error('生物识别服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 检查生物识别支持
   */
  private async checkBiometricSupport(): Promise<void> {
    try {
      // 使用TouchID库检查支持
      const touchIdSupported = await TouchID.isSupported();
      if (touchIdSupported) {
        this.isSupported = true;
        this.availableBiometrics.push(touchIdSupported as BiometricType);
      }

      // 使用ReactNativeBiometrics库检查更多类型
      const rnBiometrics = new ReactNativeBiometrics();
      const { available, biometryType } = await rnBiometrics.isSensorAvailable();
      
      if (available && biometryType) {
        this.isSupported = true;
        if (!this.availableBiometrics.includes(biometryType as BiometricType)) {
          this.availableBiometrics.push(biometryType as BiometricType);
        }
      }

      Logger.info('生物识别支持检查完成:', {
        supported: this.isSupported,
        types: this.availableBiometrics,
      });
    } catch (error) {
      Logger.warn('检查生物识别支持时出错:', error);
      this.isSupported = false;
      this.availableBiometrics = [];
    }
  }

  /**
   * 检查是否支持生物识别
   */
  isSupported(): boolean {
    return this.isSupported;
  }

  /**
   * 获取可用的生物识别类型
   */
  getAvailableBiometrics(): BiometricType[] {
    return [...this.availableBiometrics];
  }

  /**
   * 进行生物识别认证
   */
  async authenticate(options: AuthenticationOptions = {}): Promise<AuthenticationResult> {
    if (!this.isSupported) {
      return {
        success: false,
        error: '设备不支持生物识别',
      };
    }

    if (!this.config.enabled) {
      return {
        success: false,
        error: '生物识别未启用',
      };
    }

    try {
      const defaultOptions = {
        title: '生物识别验证',
        subtitle: '请使用您的生物识别信息进行验证',
        description: '将手指放在传感器上或看向摄像头',
        fallbackLabel: '使用密码',
        cancelLabel: '取消',
        disableDeviceFallback: !this.config.fallbackToPassword,
      };

      const authOptions = { ...defaultOptions, ...options };

      // 尝试使用TouchID库
      if (this.availableBiometrics.includes('TouchID') || this.availableBiometrics.includes('FaceID')) {
        try {
          const result = await TouchID.authenticate(
            authOptions.description,
            {
              title: authOptions.title,
              fallbackLabel: authOptions.fallbackLabel,
              cancelLabel: authOptions.cancelLabel,
              passcodeFallback: !authOptions.disableDeviceFallback,
            }
          );

          Logger.info('TouchID认证成功');
          return {
            success: true,
            biometryType: this.availableBiometrics[0],
          };
        } catch (error) {
          Logger.warn('TouchID认证失败:', error);
        }
      }

      // 尝试使用ReactNativeBiometrics库
      const rnBiometrics = new ReactNativeBiometrics();
      const { success, error } = await rnBiometrics.simplePrompt({
        promptMessage: authOptions.description,
        cancelButtonText: authOptions.cancelLabel,
      });

      if (success) {
        Logger.info('生物识别认证成功');
        return {
          success: true,
          biometryType: this.availableBiometrics[0],
        };
      } else {
        Logger.warn('生物识别认证失败:', error);
        return {
          success: false,
          error: error || '认证失败',
        };
      }
    } catch (error) {
      Logger.error('生物识别认证出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '认证出错',
      };
    }
  }

  /**
   * 创建生物识别密钥
   */
  async createBiometricKey(keyAlias: string): Promise<boolean> {
    try {
      const rnBiometrics = new ReactNativeBiometrics();
      const { available } = await rnBiometrics.isSensorAvailable();

      if (!available) {
        Logger.warn('生物识别不可用，无法创建密钥');
        return false;
      }

      const { success, error } = await rnBiometrics.createKeys();

      if (success) {
        Logger.info('生物识别密钥创建成功');
        await AsyncStorage.setItem(`biometric_key_${keyAlias}`, 'true');
        return true;
      } else {
        Logger.error('生物识别密钥创建失败:', error);
        return false;
      }
    } catch (error) {
      Logger.error('创建生物识别密钥时出错:', error);
      return false;
    }
  }

  /**
   * 删除生物识别密钥
   */
  async deleteBiometricKey(keyAlias: string): Promise<boolean> {
    try {
      const rnBiometrics = new ReactNativeBiometrics();
      const { success, error } = await rnBiometrics.deleteKeys();

      if (success) {
        Logger.info('生物识别密钥删除成功');
        await AsyncStorage.removeItem(`biometric_key_${keyAlias}`);
        return true;
      } else {
        Logger.error('生物识别密钥删除失败:', error);
        return false;
      }
    } catch (error) {
      Logger.error('删除生物识别密钥时出错:', error);
      return false;
    }
  }

  /**
   * 检查是否存在生物识别密钥
   */
  async hasBiometricKey(keyAlias: string): Promise<boolean> {
    try {
      const keyExists = await AsyncStorage.getItem(`biometric_key_${keyAlias}`);
      return keyExists === 'true';
    } catch (error) {
      Logger.error('检查生物识别密钥时出错:', error);
      return false;
    }
  }

  /**
   * 使用生物识别签名数据
   */
  async signWithBiometric(payload: string, options: AuthenticationOptions = {}): Promise<{
    success: boolean;
    signature?: string;
    error?: string;
  }> {
    try {
      const rnBiometrics = new ReactNativeBiometrics();
      
      const defaultOptions = {
        title: '生物识别签名',
        subtitle: '请验证您的身份以签名数据',
        description: '使用生物识别验证身份',
        cancelLabel: '取消',
      };

      const authOptions = { ...defaultOptions, ...options };

      const { success, signature, error } = await rnBiometrics.createSignature({
        promptMessage: authOptions.description,
        payload,
        cancelButtonText: authOptions.cancelLabel,
      });

      if (success && signature) {
        Logger.info('生物识别签名成功');
        return { success: true, signature };
      } else {
        Logger.warn('生物识别签名失败:', error);
        return { success: false, error: error || '签名失败' };
      }
    } catch (error) {
      Logger.error('生物识别签名出错:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '签名出错',
      };
    }
  }

  /**
   * 启用生物识别
   */
  async enableBiometric(): Promise<boolean> {
    if (!this.isSupported) {
      Logger.warn('设备不支持生物识别');
      return false;
    }

    try {
      // 测试认证
      const result = await this.authenticate({
        title: '启用生物识别',
        description: '请验证您的身份以启用生物识别功能',
      });

      if (result.success) {
        this.config.enabled = true;
        await this.saveConfig();
        Logger.info('生物识别已启用');
        return true;
      } else {
        Logger.warn('启用生物识别失败:', result.error);
        return false;
      }
    } catch (error) {
      Logger.error('启用生物识别时出错:', error);
      return false;
    }
  }

  /**
   * 禁用生物识别
   */
  async disableBiometric(): Promise<void> {
    this.config.enabled = false;
    await this.saveConfig();
    Logger.info('生物识别已禁用');
  }

  /**
   * 检查生物识别是否已启用
   */
  isEnabled(): boolean {
    return this.config.enabled;
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<BiometricConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    Logger.info('生物识别配置已更新:', newConfig);
  }

  /**
   * 获取配置
   */
  getConfig(): BiometricConfig {
    return { ...this.config };
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const configJson = await AsyncStorage.getItem('biometricConfig');
      if (configJson) {
        const savedConfig = JSON.parse(configJson);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      Logger.error('加载生物识别配置失败:', error);
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<void> {
    try {
      await AsyncStorage.setItem('biometricConfig', JSON.stringify(this.config));
    } catch (error) {
      Logger.error('保存生物识别配置失败:', error);
    }
  }

  /**
   * 获取生物识别类型的显示名称
   */
  getBiometricTypeName(type: BiometricType): string {
    switch (type) {
      case 'TouchID':
        return 'Touch ID';
      case 'FaceID':
        return 'Face ID';
      case 'Fingerprint':
        return '指纹识别';
      case 'Face':
        return '面部识别';
      case 'Iris':
        return '虹膜识别';
      default:
        return '生物识别';
    }
  }
}

// 导出单例实例
export const BiometricService = new BiometricServiceClass();
