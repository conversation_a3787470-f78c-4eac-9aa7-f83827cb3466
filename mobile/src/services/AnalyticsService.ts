/**
 * 分析服务
 * 管理用户行为分析、性能监控和数据统计
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Dimensions } from 'react-native';
import DeviceInfo from 'react-native-device-info';
import { Logger } from '@utils/Logger';

// 事件类型
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  timestamp?: number;
  userId?: string;
  sessionId?: string;
}

// 用户属性
export interface UserProperties {
  userId?: string;
  email?: string;
  name?: string;
  role?: string;
  plan?: string;
  registrationDate?: string;
  lastActiveDate?: string;
  customProperties?: Record<string, any>;
}

// 设备信息
interface DeviceInfo {
  platform: string;
  version: string;
  model: string;
  brand: string;
  screenWidth: number;
  screenHeight: number;
  appVersion: string;
  buildNumber: string;
  bundleId: string;
  deviceId: string;
  isTablet: boolean;
  hasNotch: boolean;
  timezone: string;
  locale: string;
}

// 会话信息
interface SessionInfo {
  sessionId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  screenViews: number;
  events: number;
}

// 分析配置
interface AnalyticsConfig {
  enabled: boolean;
  trackScreenViews: boolean;
  trackUserInteractions: boolean;
  trackPerformance: boolean;
  trackCrashes: boolean;
  batchSize: number;
  flushInterval: number; // 毫秒
  maxQueueSize: number;
  enableDebugLogging: boolean;
}

class AnalyticsServiceClass {
  private isInitialized = false;
  private config: AnalyticsConfig = {
    enabled: true,
    trackScreenViews: true,
    trackUserInteractions: true,
    trackPerformance: true,
    trackCrashes: true,
    batchSize: 20,
    flushInterval: 30000, // 30秒
    maxQueueSize: 100,
    enableDebugLogging: __DEV__,
  };
  
  private eventQueue: AnalyticsEvent[] = [];
  private currentSession: SessionInfo | null = null;
  private userProperties: UserProperties = {};
  private deviceInfo: DeviceInfo | null = null;
  private flushTimer: NodeJS.Timeout | null = null;

  /**
   * 初始化分析服务
   */
  async initialize(): Promise<void> {
    try {
      // 加载配置
      await this.loadConfig();

      // 收集设备信息
      await this.collectDeviceInfo();

      // 加载用户属性
      await this.loadUserProperties();

      // 开始新会话
      await this.startSession();

      // 启动定时刷新
      this.startFlushTimer();

      // 加载未发送的事件
      await this.loadQueuedEvents();

      this.isInitialized = true;
      Logger.info('分析服务初始化成功');
    } catch (error) {
      Logger.error('分析服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 跟踪事件
   */
  async trackEvent(name: string, properties?: Record<string, any>): Promise<void> {
    if (!this.config.enabled) return;

    try {
      const event: AnalyticsEvent = {
        name,
        properties: {
          ...properties,
          platform: Platform.OS,
          appVersion: this.deviceInfo?.appVersion,
          sessionId: this.currentSession?.sessionId,
        },
        timestamp: Date.now(),
        userId: this.userProperties.userId,
        sessionId: this.currentSession?.sessionId,
      };

      this.eventQueue.push(event);

      // 更新会话统计
      if (this.currentSession) {
        this.currentSession.events++;
      }

      // 检查是否需要立即发送
      if (this.eventQueue.length >= this.config.batchSize) {
        await this.flushEvents();
      }

      if (this.config.enableDebugLogging) {
        Logger.debug('事件已跟踪:', { name, properties });
      }
    } catch (error) {
      Logger.error('跟踪事件失败:', error);
    }
  }

  /**
   * 跟踪屏幕浏览
   */
  async trackScreenView(screenName: string, properties?: Record<string, any>): Promise<void> {
    if (!this.config.trackScreenViews) return;

    await this.trackEvent('screen_view', {
      screen_name: screenName,
      ...properties,
    });

    // 更新会话统计
    if (this.currentSession) {
      this.currentSession.screenViews++;
    }
  }

  /**
   * 跟踪用户交互
   */
  async trackUserInteraction(action: string, target: string, properties?: Record<string, any>): Promise<void> {
    if (!this.config.trackUserInteractions) return;

    await this.trackEvent('user_interaction', {
      action,
      target,
      ...properties,
    });
  }

  /**
   * 跟踪性能指标
   */
  async trackPerformance(metric: string, value: number, properties?: Record<string, any>): Promise<void> {
    if (!this.config.trackPerformance) return;

    await this.trackEvent('performance_metric', {
      metric,
      value,
      ...properties,
    });
  }

  /**
   * 跟踪错误
   */
  async trackError(error: Error, context?: Record<string, any>): Promise<void> {
    if (!this.config.trackCrashes) return;

    await this.trackEvent('error', {
      error_name: error.name,
      error_message: error.message,
      error_stack: error.stack,
      ...context,
    });
  }

  /**
   * 设置用户属性
   */
  async setUserProperties(properties: UserProperties): Promise<void> {
    try {
      this.userProperties = { ...this.userProperties, ...properties };
      await this.saveUserProperties();

      if (this.config.enableDebugLogging) {
        Logger.debug('用户属性已设置:', properties);
      }
    } catch (error) {
      Logger.error('设置用户属性失败:', error);
    }
  }

  /**
   * 设置用户ID
   */
  async setUserId(userId: string): Promise<void> {
    await this.setUserProperties({ userId });
  }

  /**
   * 开始新会话
   */
  private async startSession(): Promise<void> {
    try {
      // 结束当前会话
      if (this.currentSession) {
        await this.endSession();
      }

      // 创建新会话
      this.currentSession = {
        sessionId: `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        startTime: Date.now(),
        screenViews: 0,
        events: 0,
      };

      // 跟踪会话开始事件
      await this.trackEvent('session_start', {
        session_id: this.currentSession.sessionId,
      });

      Logger.info('新会话已开始:', this.currentSession.sessionId);
    } catch (error) {
      Logger.error('开始会话失败:', error);
    }
  }

  /**
   * 结束会话
   */
  private async endSession(): Promise<void> {
    if (!this.currentSession) return;

    try {
      const endTime = Date.now();
      const duration = endTime - this.currentSession.startTime;

      this.currentSession.endTime = endTime;
      this.currentSession.duration = duration;

      // 跟踪会话结束事件
      await this.trackEvent('session_end', {
        session_id: this.currentSession.sessionId,
        duration,
        screen_views: this.currentSession.screenViews,
        events: this.currentSession.events,
      });

      Logger.info('会话已结束:', {
        sessionId: this.currentSession.sessionId,
        duration,
      });

      this.currentSession = null;
    } catch (error) {
      Logger.error('结束会话失败:', error);
    }
  }

  /**
   * 收集设备信息
   */
  private async collectDeviceInfo(): Promise<void> {
    try {
      const { width, height } = Dimensions.get('screen');
      
      this.deviceInfo = {
        platform: Platform.OS,
        version: Platform.Version.toString(),
        model: await DeviceInfo.getModel(),
        brand: await DeviceInfo.getBrand(),
        screenWidth: width,
        screenHeight: height,
        appVersion: DeviceInfo.getVersion(),
        buildNumber: DeviceInfo.getBuildNumber(),
        bundleId: DeviceInfo.getBundleId(),
        deviceId: await DeviceInfo.getUniqueId(),
        isTablet: DeviceInfo.isTablet(),
        hasNotch: DeviceInfo.hasNotch(),
        timezone: DeviceInfo.getTimezone(),
        locale: await DeviceInfo.getDeviceLocale(),
      };

      Logger.debug('设备信息已收集:', this.deviceInfo);
    } catch (error) {
      Logger.error('收集设备信息失败:', error);
    }
  }

  /**
   * 刷新事件队列
   */
  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    try {
      const events = [...this.eventQueue];
      this.eventQueue = [];

      // 发送到服务器
      await this.sendEventsToServer(events);

      Logger.debug(`已发送 ${events.length} 个事件到服务器`);
    } catch (error) {
      Logger.error('刷新事件失败:', error);
      
      // 如果发送失败，将事件重新加入队列
      this.eventQueue.unshift(...this.eventQueue);
      
      // 保存到本地存储
      await this.saveQueuedEvents();
    }
  }

  /**
   * 发送事件到服务器
   */
  private async sendEventsToServer(events: AnalyticsEvent[]): Promise<void> {
    try {
      const payload = {
        events,
        user: this.userProperties,
        device: this.deviceInfo,
        session: this.currentSession,
        timestamp: Date.now(),
      };

      const response = await fetch('/api/analytics/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      Logger.debug('事件发送成功');
    } catch (error) {
      Logger.error('发送事件到服务器失败:', error);
      throw error;
    }
  }

  /**
   * 启动定时刷新
   */
  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(async () => {
      await this.flushEvents();
    }, this.config.flushInterval);
  }

  /**
   * 停止定时刷新
   */
  private stopFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
  }

  /**
   * 保存队列中的事件
   */
  private async saveQueuedEvents(): Promise<void> {
    try {
      await AsyncStorage.setItem('analyticsQueue', JSON.stringify(this.eventQueue));
    } catch (error) {
      Logger.error('保存队列事件失败:', error);
    }
  }

  /**
   * 加载队列中的事件
   */
  private async loadQueuedEvents(): Promise<void> {
    try {
      const queueData = await AsyncStorage.getItem('analyticsQueue');
      if (queueData) {
        const events = JSON.parse(queueData);
        this.eventQueue.push(...events);
        await AsyncStorage.removeItem('analyticsQueue');
        Logger.info(`加载了 ${events.length} 个队列事件`);
      }
    } catch (error) {
      Logger.error('加载队列事件失败:', error);
    }
  }

  /**
   * 保存用户属性
   */
  private async saveUserProperties(): Promise<void> {
    try {
      await AsyncStorage.setItem('analyticsUserProperties', JSON.stringify(this.userProperties));
    } catch (error) {
      Logger.error('保存用户属性失败:', error);
    }
  }

  /**
   * 加载用户属性
   */
  private async loadUserProperties(): Promise<void> {
    try {
      const propertiesData = await AsyncStorage.getItem('analyticsUserProperties');
      if (propertiesData) {
        this.userProperties = JSON.parse(propertiesData);
      }
    } catch (error) {
      Logger.error('加载用户属性失败:', error);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(newConfig: Partial<AnalyticsConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();

    // 重启定时器
    if (this.config.enabled) {
      this.startFlushTimer();
    } else {
      this.stopFlushTimer();
    }

    Logger.info('分析配置已更新:', newConfig);
  }

  /**
   * 获取配置
   */
  getConfig(): AnalyticsConfig {
    return { ...this.config };
  }

  /**
   * 加载配置
   */
  private async loadConfig(): Promise<void> {
    try {
      const configJson = await AsyncStorage.getItem('analyticsConfig');
      if (configJson) {
        const savedConfig = JSON.parse(configJson);
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      Logger.error('加载分析配置失败:', error);
    }
  }

  /**
   * 保存配置
   */
  private async saveConfig(): Promise<void> {
    try {
      await AsyncStorage.setItem('analyticsConfig', JSON.stringify(this.config));
    } catch (error) {
      Logger.error('保存分析配置失败:', error);
    }
  }

  /**
   * 获取分析统计
   */
  getAnalyticsStats(): {
    queueSize: number;
    currentSession: SessionInfo | null;
    userProperties: UserProperties;
    deviceInfo: DeviceInfo | null;
  } {
    return {
      queueSize: this.eventQueue.length,
      currentSession: this.currentSession,
      userProperties: this.userProperties,
      deviceInfo: this.deviceInfo,
    };
  }

  /**
   * 清理服务
   */
  async cleanup(): Promise<void> {
    try {
      // 结束当前会话
      await this.endSession();

      // 刷新剩余事件
      await this.flushEvents();

      // 停止定时器
      this.stopFlushTimer();

      // 清空队列
      this.eventQueue = [];

      Logger.info('分析服务已清理');
    } catch (error) {
      Logger.error('清理分析服务失败:', error);
    }
  }
}

// 导出单例实例
export const AnalyticsService = new AnalyticsServiceClass();
