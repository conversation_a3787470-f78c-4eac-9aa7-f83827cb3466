/**
 * 订单列表屏幕
 * 显示订单列表，支持筛选、搜索和批量操作
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  IconButton,
  FAB,
  Searchbar,
  Menu,
  Badge,
  useTheme,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NetworkService } from '@services/NetworkService';
import { AnalyticsService } from '@services/AnalyticsService';
import { Logger } from '@utils/Logger';

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  customerEmail: string;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  totalAmount: number;
  currency: string;
  createdAt: string;
  updatedAt: string;
  itemCount: number;
  shippingAddress: string;
}

const OrderListScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [menuVisible, setMenuVisible] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);

  const statusOptions = [
    { value: 'all', label: '全部', color: theme.colors.outline },
    { value: 'pending', label: '待付款', color: '#ff9800' },
    { value: 'paid', label: '已付款', color: '#2196f3' },
    { value: 'shipped', label: '已发货', color: '#9c27b0' },
    { value: 'delivered', label: '已送达', color: '#4caf50' },
    { value: 'cancelled', label: '已取消', color: '#f44336' },
  ];

  useFocusEffect(
    useCallback(() => {
      loadOrders();
      trackScreenView();
    }, [selectedStatus, searchQuery])
  );

  /**
   * 跟踪屏幕浏览
   */
  const trackScreenView = () => {
    AnalyticsService.trackScreenView('order_list_screen', {
      status_filter: selectedStatus,
      has_search: searchQuery.length > 0,
    });
  };

  /**
   * 加载订单列表
   */
  const loadOrders = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (selectedStatus !== 'all') {
        params.append('status', selectedStatus);
      }
      if (searchQuery) {
        params.append('search', searchQuery);
      }
      params.append('limit', '50');

      const response = await NetworkService.request<{ orders: Order[] }>(
        `/orders?${params.toString()}`
      );

      setOrders(response.orders);
      Logger.info('订单列表加载成功:', response.orders.length);
    } catch (error) {
      Logger.error('加载订单列表失败:', error);
      Alert.alert('加载失败', '无法加载订单列表，请重试');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 下拉刷新
   */
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadOrders();
    setRefreshing(false);
  };

  /**
   * 搜索订单
   */
  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  /**
   * 筛选状态
   */
  const handleStatusFilter = (status: string) => {
    setSelectedStatus(status);
    setMenuVisible(false);
  };

  /**
   * 选择订单
   */
  const toggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev => {
      if (prev.includes(orderId)) {
        return prev.filter(id => id !== orderId);
      } else {
        return [...prev, orderId];
      }
    });
  };

  /**
   * 全选/取消全选
   */
  const toggleSelectAll = () => {
    if (selectedOrders.length === orders.length) {
      setSelectedOrders([]);
    } else {
      setSelectedOrders(orders.map(order => order.id));
    }
  };

  /**
   * 批量操作
   */
  const handleBatchAction = (action: string) => {
    if (selectedOrders.length === 0) {
      Alert.alert('提示', '请先选择要操作的订单');
      return;
    }

    switch (action) {
      case 'export':
        handleBatchExport();
        break;
      case 'print':
        handleBatchPrint();
        break;
      case 'delete':
        handleBatchDelete();
        break;
    }
  };

  /**
   * 批量导出
   */
  const handleBatchExport = () => {
    Alert.alert(
      '导出订单',
      `确定要导出选中的 ${selectedOrders.length} 个订单吗？`,
      [
        { text: '取消', style: 'cancel' },
        { text: '确定', onPress: () => {
          // 实现导出逻辑
          Logger.info('批量导出订单:', selectedOrders);
          setSelectionMode(false);
          setSelectedOrders([]);
        }}
      ]
    );
  };

  /**
   * 批量打印
   */
  const handleBatchPrint = () => {
    Alert.alert(
      '打印订单',
      `确定要打印选中的 ${selectedOrders.length} 个订单吗？`,
      [
        { text: '取消', style: 'cancel' },
        { text: '确定', onPress: () => {
          // 实现打印逻辑
          Logger.info('批量打印订单:', selectedOrders);
          setSelectionMode(false);
          setSelectedOrders([]);
        }}
      ]
    );
  };

  /**
   * 批量删除
   */
  const handleBatchDelete = () => {
    Alert.alert(
      '删除订单',
      `确定要删除选中的 ${selectedOrders.length} 个订单吗？此操作不可撤销。`,
      [
        { text: '取消', style: 'cancel' },
        { text: '删除', style: 'destructive', onPress: () => {
          // 实现删除逻辑
          Logger.info('批量删除订单:', selectedOrders);
          setSelectionMode(false);
          setSelectedOrders([]);
        }}
      ]
    );
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option?.color || theme.colors.outline;
  };

  /**
   * 获取状态标签
   */
  const getStatusLabel = (status: string) => {
    const option = statusOptions.find(opt => opt.value === status);
    return option?.label || status;
  };

  /**
   * 渲染订单项
   */
  const renderOrderItem = ({ item }: { item: Order }) => {
    const isSelected = selectedOrders.includes(item.id);

    return (
      <Card
        style={[
          styles.orderCard,
          isSelected && { borderColor: theme.colors.primary, borderWidth: 2 }
        ]}
        onPress={() => {
          if (selectionMode) {
            toggleOrderSelection(item.id);
          } else {
            navigation.navigate('OrderDetail', { orderId: item.id });
          }
        }}
        onLongPress={() => {
          if (!selectionMode) {
            setSelectionMode(true);
            toggleOrderSelection(item.id);
          }
        }}
      >
        <Card.Content style={styles.cardContent}>
          <View style={styles.orderHeader}>
            <View style={styles.orderInfo}>
              <Text style={styles.orderNumber}>#{item.orderNumber}</Text>
              <Chip
                mode="outlined"
                style={[styles.statusChip, { borderColor: getStatusColor(item.status) }]}
                textStyle={{ color: getStatusColor(item.status) }}
              >
                {getStatusLabel(item.status)}
              </Chip>
            </View>
            {selectionMode && (
              <IconButton
                icon={isSelected ? 'checkbox-marked' : 'checkbox-blank-outline'}
                iconColor={isSelected ? theme.colors.primary : theme.colors.outline}
                onPress={() => toggleOrderSelection(item.id)}
              />
            )}
          </View>

          <View style={styles.customerInfo}>
            <Text style={styles.customerName}>{item.customerName}</Text>
            <Text style={styles.customerEmail}>{item.customerEmail}</Text>
          </View>

          <View style={styles.orderDetails}>
            <View style={styles.amountContainer}>
              <Text style={styles.amount}>
                {item.currency} {item.totalAmount.toFixed(2)}
              </Text>
              <Text style={styles.itemCount}>
                {item.itemCount} 件商品
              </Text>
            </View>
            <Text style={styles.date}>
              {new Date(item.createdAt).toLocaleDateString('zh-CN')}
            </Text>
          </View>
        </Card.Content>
      </Card>
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      padding: 16,
      paddingTop: insets.top + 16,
      backgroundColor: theme.colors.surface,
      elevation: 2,
    },
    searchContainer: {
      marginBottom: 12,
    },
    filterContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    },
    filterChips: {
      flex: 1,
    },
    menuAnchor: {
      marginLeft: 8,
    },
    selectionHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
    },
    selectionText: {
      color: theme.colors.onPrimaryContainer,
      fontWeight: 'bold',
    },
    selectionActions: {
      flexDirection: 'row',
    },
    list: {
      flex: 1,
      padding: 16,
    },
    orderCard: {
      marginBottom: 12,
      elevation: 2,
    },
    cardContent: {
      padding: 16,
    },
    orderHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    orderInfo: {
      flexDirection: 'row',
      alignItems: 'center',
      flex: 1,
    },
    orderNumber: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      marginRight: 12,
    },
    statusChip: {
      height: 28,
    },
    customerInfo: {
      marginBottom: 12,
    },
    customerName: {
      fontSize: 14,
      fontWeight: '500',
      color: theme.colors.onSurface,
    },
    customerEmail: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    orderDetails: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    amountContainer: {
      flex: 1,
    },
    amount: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    itemCount: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      marginTop: 2,
    },
    date: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    fab: {
      position: 'absolute',
      margin: 16,
      right: 0,
      bottom: insets.bottom,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 32,
    },
    emptyText: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 16,
    },
  });

  return (
    <View style={styles.container}>
      {/* 搜索和筛选 */}
      <View style={styles.header}>
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="搜索订单号、客户名称..."
            onChangeText={handleSearch}
            value={searchQuery}
          />
        </View>
        <View style={styles.filterContainer}>
          <View style={styles.filterChips}>
            <Chip
              mode={selectedStatus === 'all' ? 'flat' : 'outlined'}
              selected={selectedStatus === 'all'}
              onPress={() => handleStatusFilter('all')}
            >
              全部订单
            </Chip>
          </View>
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <IconButton
                icon="filter-variant"
                onPress={() => setMenuVisible(true)}
                style={styles.menuAnchor}
              />
            }
          >
            {statusOptions.slice(1).map((option) => (
              <Menu.Item
                key={option.value}
                onPress={() => handleStatusFilter(option.value)}
                title={option.label}
                leadingIcon="circle"
                titleStyle={{ color: option.color }}
              />
            ))}
          </Menu>
        </View>
      </View>

      {/* 选择模式头部 */}
      {selectionMode && (
        <View style={styles.selectionHeader}>
          <Text style={styles.selectionText}>
            已选择 {selectedOrders.length} 个订单
          </Text>
          <View style={styles.selectionActions}>
            <IconButton
              icon="select-all"
              iconColor={theme.colors.onPrimaryContainer}
              onPress={toggleSelectAll}
            />
            <IconButton
              icon="export"
              iconColor={theme.colors.onPrimaryContainer}
              onPress={() => handleBatchAction('export')}
            />
            <IconButton
              icon="printer"
              iconColor={theme.colors.onPrimaryContainer}
              onPress={() => handleBatchAction('print')}
            />
            <IconButton
              icon="close"
              iconColor={theme.colors.onPrimaryContainer}
              onPress={() => {
                setSelectionMode(false);
                setSelectedOrders([]);
              }}
            />
          </View>
        </View>
      )}

      {/* 订单列表 */}
      <FlatList
        style={styles.list}
        data={orders}
        renderItem={renderOrderItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <IconButton icon="package-variant" size={64} />
            <Text style={styles.emptyText}>
              {searchQuery ? '未找到匹配的订单' : '暂无订单数据'}
            </Text>
          </View>
        }
      />

      {/* 添加订单按钮 */}
      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => navigation.navigate('CreateOrder')}
      />
    </View>
  );
};

export default OrderListScreen;
