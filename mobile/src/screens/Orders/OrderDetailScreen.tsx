/**
 * 订单详情屏幕
 * 显示订单详细信息，支持状态更新和操作
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  Linking,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  Button,
  IconButton,
  Divider,
  List,
  useTheme,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NetworkService } from '@services/NetworkService';
import { AnalyticsService } from '@services/AnalyticsService';
import { Logger } from '@utils/Logger';

interface OrderItem {
  id: string;
  productId: string;
  productName: string;
  sku: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  imageUrl?: string;
}

interface OrderDetail {
  id: string;
  orderNumber: string;
  status: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  shippingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  billingAddress: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  currency: string;
  paymentMethod: string;
  paymentStatus: string;
  shippingMethod: string;
  trackingNumber?: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

const OrderDetailScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const route = useRoute();
  const { orderId } = route.params as { orderId: string };

  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadOrderDetail();
    trackScreenView();
  }, [orderId]);

  /**
   * 跟踪屏幕浏览
   */
  const trackScreenView = () => {
    AnalyticsService.trackScreenView('order_detail_screen', {
      order_id: orderId,
    });
  };

  /**
   * 加载订单详情
   */
  const loadOrderDetail = async () => {
    try {
      const response = await NetworkService.request<OrderDetail>(`/orders/${orderId}`);
      setOrder(response);
      Logger.info('订单详情加载成功:', response.orderNumber);
    } catch (error) {
      Logger.error('加载订单详情失败:', error);
      Alert.alert('加载失败', '无法加载订单详情，请重试');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  /**
   * 更新订单状态
   */
  const updateOrderStatus = async (newStatus: string) => {
    if (!order) return;

    try {
      await NetworkService.request(`/orders/${orderId}/status`, {
        method: 'PUT',
        body: { status: newStatus },
      });

      setOrder(prev => prev ? { ...prev, status: newStatus } : null);
      
      AnalyticsService.trackEvent('order_status_updated', {
        order_id: orderId,
        old_status: order.status,
        new_status: newStatus,
      });

      Logger.info('订单状态更新成功:', newStatus);
    } catch (error) {
      Logger.error('更新订单状态失败:', error);
      Alert.alert('更新失败', '无法更新订单状态，请重试');
    }
  };

  /**
   * 联系客户
   */
  const contactCustomer = (method: 'email' | 'phone') => {
    if (!order) return;

    if (method === 'email') {
      Linking.openURL(`mailto:${order.customerEmail}`);
    } else if (method === 'phone' && order.customerPhone) {
      Linking.openURL(`tel:${order.customerPhone}`);
    }

    AnalyticsService.trackEvent('customer_contacted', {
      order_id: orderId,
      method,
    });
  };

  /**
   * 跟踪包裹
   */
  const trackPackage = () => {
    if (!order?.trackingNumber) return;

    // 这里可以集成物流跟踪API或跳转到物流公司网站
    Alert.alert(
      '包裹跟踪',
      `跟踪号: ${order.trackingNumber}`,
      [
        { text: '取消', style: 'cancel' },
        { text: '复制跟踪号', onPress: () => {
          // 复制到剪贴板
          Logger.info('复制跟踪号:', order.trackingNumber);
        }},
        { text: '查看详情', onPress: () => {
          // 跳转到跟踪页面
          Logger.info('查看包裹跟踪详情');
        }},
      ]
    );
  };

  /**
   * 获取状态颜色
   */
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#ff9800';
      case 'paid': return '#2196f3';
      case 'shipped': return '#9c27b0';
      case 'delivered': return '#4caf50';
      case 'cancelled': return '#f44336';
      default: return theme.colors.outline;
    }
  };

  /**
   * 获取状态标签
   */
  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return '待付款';
      case 'paid': return '已付款';
      case 'shipped': return '已发货';
      case 'delivered': return '已送达';
      case 'cancelled': return '已取消';
      default: return status;
    }
  };

  /**
   * 渲染订单项
   */
  const renderOrderItem = (item: OrderItem) => (
    <List.Item
      key={item.id}
      title={item.productName}
      description={`SKU: ${item.sku} | 数量: ${item.quantity}`}
      left={(props) => (
        <List.Icon {...props} icon="package-variant" />
      )}
      right={() => (
        <View style={styles.itemPrice}>
          <Text style={styles.unitPrice}>
            {order?.currency} {item.unitPrice.toFixed(2)}
          </Text>
          <Text style={styles.totalPrice}>
            {order?.currency} {item.totalPrice.toFixed(2)}
          </Text>
        </View>
      )}
    />
  );

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      padding: 16,
      paddingBottom: insets.bottom + 16,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 16,
    },
    orderNumber: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.onBackground,
    },
    statusChip: {
      height: 32,
    },
    section: {
      marginBottom: 16,
    },
    sectionTitle: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      marginBottom: 8,
    },
    customerInfo: {
      padding: 16,
    },
    customerName: {
      fontSize: 16,
      fontWeight: '500',
      color: theme.colors.onSurface,
      marginBottom: 4,
    },
    customerContact: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      marginBottom: 2,
    },
    addressContainer: {
      padding: 16,
    },
    addressText: {
      fontSize: 14,
      color: theme.colors.onSurface,
      lineHeight: 20,
    },
    itemPrice: {
      alignItems: 'flex-end',
    },
    unitPrice: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    totalPrice: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    summaryContainer: {
      padding: 16,
    },
    summaryRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 8,
    },
    summaryLabel: {
      fontSize: 14,
      color: theme.colors.onSurface,
    },
    summaryValue: {
      fontSize: 14,
      color: theme.colors.onSurface,
    },
    totalRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      paddingTop: 8,
      borderTopWidth: 1,
      borderTopColor: theme.colors.outline,
    },
    totalLabel: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
    },
    totalValue: {
      fontSize: 16,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    actionButtons: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginTop: 16,
    },
    actionButton: {
      flex: 1,
      marginHorizontal: 4,
    },
    trackingContainer: {
      padding: 16,
      backgroundColor: theme.colors.primaryContainer,
      borderRadius: 8,
      marginBottom: 16,
    },
    trackingTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: theme.colors.onPrimaryContainer,
      marginBottom: 4,
    },
    trackingNumber: {
      fontSize: 16,
      color: theme.colors.onPrimaryContainer,
      fontFamily: 'monospace',
    },
  });

  if (loading || !order) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text>加载中...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.scrollContainer}>
      {/* 订单头部 */}
      <View style={styles.header}>
        <Text style={styles.orderNumber}>#{order.orderNumber}</Text>
        <Chip
          mode="outlined"
          style={[styles.statusChip, { borderColor: getStatusColor(order.status) }]}
          textStyle={{ color: getStatusColor(order.status) }}
        >
          {getStatusLabel(order.status)}
        </Chip>
      </View>

      {/* 跟踪信息 */}
      {order.trackingNumber && (
        <Card style={styles.trackingContainer} onPress={trackPackage}>
          <Text style={styles.trackingTitle}>跟踪号</Text>
          <Text style={styles.trackingNumber}>{order.trackingNumber}</Text>
        </Card>
      )}

      {/* 客户信息 */}
      <Card style={styles.section}>
        <Card.Content style={styles.customerInfo}>
          <Text style={styles.sectionTitle}>客户信息</Text>
          <Text style={styles.customerName}>{order.customerName}</Text>
          <Text style={styles.customerContact}>{order.customerEmail}</Text>
          {order.customerPhone && (
            <Text style={styles.customerContact}>{order.customerPhone}</Text>
          )}
          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              icon="email"
              style={styles.actionButton}
              onPress={() => contactCustomer('email')}
            >
              邮件
            </Button>
            {order.customerPhone && (
              <Button
                mode="outlined"
                icon="phone"
                style={styles.actionButton}
                onPress={() => contactCustomer('phone')}
              >
                电话
              </Button>
            )}
          </View>
        </Card.Content>
      </Card>

      {/* 收货地址 */}
      <Card style={styles.section}>
        <Card.Content style={styles.addressContainer}>
          <Text style={styles.sectionTitle}>收货地址</Text>
          <Text style={styles.addressText}>
            {order.shippingAddress.street}{'\n'}
            {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}{'\n'}
            {order.shippingAddress.country}
          </Text>
        </Card.Content>
      </Card>

      {/* 订单商品 */}
      <Card style={styles.section}>
        <Card.Content>
          <Text style={styles.sectionTitle}>订单商品</Text>
          {order.items.map(renderOrderItem)}
        </Card.Content>
      </Card>

      {/* 订单汇总 */}
      <Card style={styles.section}>
        <Card.Content style={styles.summaryContainer}>
          <Text style={styles.sectionTitle}>订单汇总</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>小计</Text>
            <Text style={styles.summaryValue}>
              {order.currency} {order.subtotal.toFixed(2)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>运费</Text>
            <Text style={styles.summaryValue}>
              {order.currency} {order.shipping.toFixed(2)}
            </Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>税费</Text>
            <Text style={styles.summaryValue}>
              {order.currency} {order.tax.toFixed(2)}
            </Text>
          </View>
          <View style={styles.totalRow}>
            <Text style={styles.totalLabel}>总计</Text>
            <Text style={styles.totalValue}>
              {order.currency} {order.total.toFixed(2)}
            </Text>
          </View>
        </Card.Content>
      </Card>

      {/* 操作按钮 */}
      <View style={styles.actionButtons}>
        <Button
          mode="contained"
          icon="pencil"
          style={styles.actionButton}
          onPress={() => navigation.navigate('EditOrder', { orderId })}
        >
          编辑
        </Button>
        <Button
          mode="outlined"
          icon="printer"
          style={styles.actionButton}
          onPress={() => {/* 打印订单 */}}
        >
          打印
        </Button>
        <Button
          mode="outlined"
          icon="share"
          style={styles.actionButton}
          onPress={() => {/* 分享订单 */}}
        >
          分享
        </Button>
      </View>
    </ScrollView>
  );
};

export default OrderDetailScreen;
