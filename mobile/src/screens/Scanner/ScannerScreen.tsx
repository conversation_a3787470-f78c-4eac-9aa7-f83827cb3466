/**
 * 扫码功能屏幕
 * 提供二维码和条形码扫描功能
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Alert,
  Vibration,
  Platform,
  PermissionsAndroid,
} from 'react-native';
import {
  Text,
  Button,
  IconButton,
  Surface,
  useTheme,
} from 'react-native-paper';
import { RNCamera } from 'react-native-camera';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Logger } from '@utils/Logger';

const { width, height } = Dimensions.get('window');

interface ScanResult {
  data: string;
  type: string;
  bounds?: {
    origin: { x: number; y: number };
    size: { width: number; height: number };
  };
}

const ScannerScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  
  const [hasPermission, setHasPermission] = useState<boolean | null>(null);
  const [isScanning, setIsScanning] = useState(true);
  const [flashMode, setFlashMode] = useState(RNCamera.Constants.FlashMode.off);
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);

  useEffect(() => {
    requestCameraPermission();
  }, []);

  /**
   * 请求相机权限
   */
  const requestCameraPermission = async () => {
    try {
      if (Platform.OS === 'android') {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: '相机权限',
            message: '应用需要相机权限来扫描二维码和条形码',
            buttonNeutral: '稍后询问',
            buttonNegative: '取消',
            buttonPositive: '确定',
          }
        );
        setHasPermission(granted === PermissionsAndroid.RESULTS.GRANTED);
      } else {
        // iOS权限在RNCamera组件中自动处理
        setHasPermission(true);
      }
    } catch (error) {
      Logger.error('请求相机权限失败:', error);
      setHasPermission(false);
    }
  };

  /**
   * 处理扫码结果
   */
  const handleBarCodeRead = (scanResult: any) => {
    if (!isScanning) return;

    const result: ScanResult = {
      data: scanResult.data,
      type: scanResult.type,
      bounds: scanResult.bounds,
    };

    Logger.info('扫码成功:', result);

    // 震动反馈
    Vibration.vibrate(100);

    // 暂停扫描
    setIsScanning(false);

    // 添加到历史记录
    setScanHistory(prev => [result, ...prev.slice(0, 9)]);

    // 处理扫码结果
    handleScanResult(result);
  };

  /**
   * 处理扫码结果
   */
  const handleScanResult = (result: ScanResult) => {
    const { data, type } = result;

    // 根据扫码内容类型进行不同处理
    if (data.startsWith('http://') || data.startsWith('https://')) {
      // URL链接
      Alert.alert(
        '扫描到链接',
        data,
        [
          { text: '取消', style: 'cancel' },
          { text: '打开链接', onPress: () => openUrl(data) },
          { text: '继续扫描', onPress: () => setIsScanning(true) },
        ]
      );
    } else if (data.match(/^\d+$/)) {
      // 纯数字，可能是商品条码
      Alert.alert(
        '扫描到条码',
        `条码: ${data}`,
        [
          { text: '取消', style: 'cancel' },
          { text: '查询商品', onPress: () => searchProduct(data) },
          { text: '继续扫描', onPress: () => setIsScanning(true) },
        ]
      );
    } else {
      // 其他内容
      Alert.alert(
        '扫描结果',
        data,
        [
          { text: '复制', onPress: () => copyToClipboard(data) },
          { text: '继续扫描', onPress: () => setIsScanning(true) },
        ]
      );
    }
  };

  /**
   * 打开URL
   */
  const openUrl = (url: string) => {
    // 这里可以集成浏览器或WebView
    Logger.info('打开URL:', url);
    setIsScanning(true);
  };

  /**
   * 查询商品
   */
  const searchProduct = (barcode: string) => {
    // 导航到商品查询页面
    navigation.navigate('ProductDetail', { barcode });
  };

  /**
   * 复制到剪贴板
   */
  const copyToClipboard = (text: string) => {
    // 这里可以集成剪贴板功能
    Logger.info('复制到剪贴板:', text);
    setIsScanning(true);
  };

  /**
   * 切换闪光灯
   */
  const toggleFlash = () => {
    setFlashMode(
      flashMode === RNCamera.Constants.FlashMode.off
        ? RNCamera.Constants.FlashMode.torch
        : RNCamera.Constants.FlashMode.off
    );
  };

  /**
   * 从相册选择图片扫描
   */
  const scanFromGallery = () => {
    // 这里可以集成图片选择和二维码识别
    Logger.info('从相册扫描');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#000000',
    },
    camera: {
      flex: 1,
    },
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
    },
    topOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      paddingTop: insets.top,
    },
    middleOverlay: {
      flexDirection: 'row',
      height: 250,
    },
    leftOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
    },
    scanArea: {
      width: 250,
      height: 250,
      position: 'relative',
    },
    rightOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
    },
    bottomOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.6)',
      paddingBottom: insets.bottom,
    },
    scanFrame: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderRadius: 12,
    },
    corner: {
      position: 'absolute',
      width: 20,
      height: 20,
      borderColor: theme.colors.primary,
      borderWidth: 3,
    },
    topLeftCorner: {
      top: -2,
      left: -2,
      borderRightWidth: 0,
      borderBottomWidth: 0,
    },
    topRightCorner: {
      top: -2,
      right: -2,
      borderLeftWidth: 0,
      borderBottomWidth: 0,
    },
    bottomLeftCorner: {
      bottom: -2,
      left: -2,
      borderRightWidth: 0,
      borderTopWidth: 0,
    },
    bottomRightCorner: {
      bottom: -2,
      right: -2,
      borderLeftWidth: 0,
      borderTopWidth: 0,
    },
    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: 16,
      paddingVertical: 12,
    },
    title: {
      color: '#ffffff',
      fontSize: 18,
      fontWeight: 'bold',
    },
    controls: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      alignItems: 'center',
      paddingHorizontal: 32,
      paddingVertical: 24,
    },
    controlButton: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      borderRadius: 30,
    },
    hint: {
      color: '#ffffff',
      fontSize: 16,
      textAlign: 'center',
      marginTop: 16,
      paddingHorizontal: 32,
    },
    historyContainer: {
      position: 'absolute',
      top: insets.top + 60,
      right: 16,
      maxHeight: 200,
    },
    historyItem: {
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      padding: 8,
      marginVertical: 2,
      borderRadius: 8,
      maxWidth: 150,
    },
    historyText: {
      fontSize: 12,
      color: '#333333',
    },
  });

  if (hasPermission === null) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: '#ffffff' }}>请求相机权限中...</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
        <Text style={{ color: '#ffffff', textAlign: 'center', marginBottom: 20 }}>
          无法访问相机，请在设置中授予相机权限
        </Text>
        <Button mode="contained" onPress={requestCameraPermission}>
          重新请求权限
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <RNCamera
        style={styles.camera}
        type={RNCamera.Constants.Type.back}
        flashMode={flashMode}
        onBarCodeRead={isScanning ? handleBarCodeRead : undefined}
        barCodeTypes={[
          RNCamera.Constants.BarCodeType.qr,
          RNCamera.Constants.BarCodeType.ean13,
          RNCamera.Constants.BarCodeType.ean8,
          RNCamera.Constants.BarCodeType.code128,
          RNCamera.Constants.BarCodeType.code39,
          RNCamera.Constants.BarCodeType.code93,
          RNCamera.Constants.BarCodeType.codabar,
          RNCamera.Constants.BarCodeType.datamatrix,
          RNCamera.Constants.BarCodeType.pdf417,
        ]}
        captureAudio={false}
      />

      {/* 扫描覆盖层 */}
      <View style={styles.overlay}>
        {/* 顶部覆盖 */}
        <View style={styles.topOverlay}>
          <View style={styles.header}>
            <IconButton
              icon="arrow-left"
              iconColor="#ffffff"
              size={24}
              onPress={() => navigation.goBack()}
            />
            <Text style={styles.title}>扫一扫</Text>
            <IconButton
              icon={flashMode === RNCamera.Constants.FlashMode.off ? 'flash-off' : 'flash-on'}
              iconColor="#ffffff"
              size={24}
              onPress={toggleFlash}
            />
          </View>
        </View>

        {/* 中间扫描区域 */}
        <View style={styles.middleOverlay}>
          <View style={styles.leftOverlay} />
          <View style={styles.scanArea}>
            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeftCorner]} />
              <View style={[styles.corner, styles.topRightCorner]} />
              <View style={[styles.corner, styles.bottomLeftCorner]} />
              <View style={[styles.corner, styles.bottomRightCorner]} />
            </View>
          </View>
          <View style={styles.rightOverlay} />
        </View>

        {/* 底部覆盖 */}
        <View style={styles.bottomOverlay}>
          <Text style={styles.hint}>
            将二维码/条形码放入框内，即可自动扫描
          </Text>
          
          <View style={styles.controls}>
            <IconButton
              icon="image"
              iconColor="#ffffff"
              size={32}
              style={styles.controlButton}
              onPress={scanFromGallery}
            />
            <IconButton
              icon={isScanning ? 'pause' : 'play'}
              iconColor="#ffffff"
              size={32}
              style={styles.controlButton}
              onPress={() => setIsScanning(!isScanning)}
            />
            <IconButton
              icon="history"
              iconColor="#ffffff"
              size={32}
              style={styles.controlButton}
              onPress={() => {/* 显示历史记录 */}}
            />
          </View>
        </View>
      </View>

      {/* 扫描历史 */}
      {scanHistory.length > 0 && (
        <View style={styles.historyContainer}>
          {scanHistory.slice(0, 3).map((item, index) => (
            <Surface key={index} style={styles.historyItem} elevation={2}>
              <Text style={styles.historyText} numberOfLines={2}>
                {item.data}
              </Text>
            </Surface>
          ))}
        </View>
      )}
    </View>
  );
};

export default ScannerScreen;
