/**
 * 忘记密码屏幕
 * 提供密码重置功能
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  IconButton,
  useTheme,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { AnalyticsService } from '@services/AnalyticsService';
import { Logger } from '@utils/Logger';

const ForgotPasswordScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();

  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  useEffect(() => {
    trackScreenView();
  }, []);

  /**
   * 跟踪屏幕浏览
   */
  const trackScreenView = () => {
    AnalyticsService.trackScreenView('forgot_password_screen');
  };

  /**
   * 验证邮箱格式
   */
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  /**
   * 发送重置密码邮件
   */
  const handleSendResetEmail = async () => {
    if (!email.trim()) {
      Alert.alert('提示', '请输入邮箱地址');
      return;
    }

    if (!validateEmail(email)) {
      Alert.alert('提示', '请输入有效的邮箱地址');
      return;
    }

    setIsLoading(true);

    try {
      // 发送重置密码请求
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      });

      if (response.ok) {
        setEmailSent(true);
        
        // 跟踪重置密码事件
        AnalyticsService.trackEvent('password_reset_requested', {
          email: email.trim(),
        });

        Logger.info('密码重置邮件发送成功');
      } else {
        const errorData = await response.json();
        throw new Error(errorData.message || '发送失败');
      }
    } catch (error) {
      Logger.error('发送重置密码邮件失败:', error);
      Alert.alert(
        '发送失败',
        error instanceof Error ? error.message : '网络错误，请稍后重试'
      );
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * 返回登录
   */
  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  /**
   * 重新发送邮件
   */
  const handleResendEmail = () => {
    setEmailSent(false);
    handleSendResetEmail();
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      paddingHorizontal: 24,
      paddingTop: insets.top + 20,
      paddingBottom: insets.bottom + 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 48,
    },
    backButton: {
      position: 'absolute',
      top: 0,
      left: 0,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 24,
    },
    logoText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onPrimary,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.onBackground,
      textAlign: 'center',
      marginBottom: 8,
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      lineHeight: 24,
    },
    formContainer: {
      marginBottom: 32,
    },
    input: {
      marginBottom: 24,
      backgroundColor: theme.colors.surface,
    },
    sendButton: {
      paddingVertical: 8,
      marginBottom: 24,
    },
    successContainer: {
      alignItems: 'center',
      padding: 24,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 12,
      marginBottom: 32,
    },
    successIcon: {
      backgroundColor: theme.colors.primary,
      borderRadius: 30,
      marginBottom: 16,
    },
    successTitle: {
      fontSize: 20,
      fontWeight: 'bold',
      color: theme.colors.onSurface,
      textAlign: 'center',
      marginBottom: 8,
    },
    successText: {
      fontSize: 14,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      lineHeight: 20,
      marginBottom: 16,
    },
    resendContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    resendText: {
      fontSize: 14,
      color: theme.colors.onSurface,
    },
    resendLink: {
      fontSize: 14,
      color: theme.colors.primary,
      fontWeight: 'bold',
      marginLeft: 4,
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    loginText: {
      color: theme.colors.onSurface,
      fontSize: 14,
    },
    loginLink: {
      color: theme.colors.primary,
      fontSize: 14,
      fontWeight: 'bold',
      marginLeft: 4,
    },
  });

  if (emailSent) {
    return (
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {/* 返回按钮 */}
          <IconButton
            icon="arrow-left"
            size={24}
            onPress={handleBackToLogin}
            style={styles.backButton}
          />

          {/* 成功提示 */}
          <View style={styles.successContainer}>
            <IconButton
              icon="email-check"
              size={48}
              iconColor={theme.colors.onPrimary}
              style={styles.successIcon}
            />
            <Text style={styles.successTitle}>邮件已发送</Text>
            <Text style={styles.successText}>
              我们已向 {email} 发送了密码重置链接。请检查您的邮箱并按照说明重置密码。
            </Text>
            <View style={styles.resendContainer}>
              <Text style={styles.resendText}>没有收到邮件？</Text>
              <Text style={styles.resendLink} onPress={handleResendEmail}>
                重新发送
              </Text>
            </View>
          </View>

          {/* 返回登录 */}
          <View style={styles.loginContainer}>
            <Text style={styles.loginText}>记起密码了？</Text>
            <Text style={styles.loginLink} onPress={handleBackToLogin}>
              返回登录
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    );
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* 返回按钮 */}
        <IconButton
          icon="arrow-left"
          size={24}
          onPress={handleBackToLogin}
          style={styles.backButton}
        />

        {/* 头部 */}
        <View style={styles.header}>
          <Surface style={styles.logo} elevation={4}>
            <Text style={styles.logoText}>ERP</Text>
          </Surface>
          <Text style={styles.title}>忘记密码</Text>
          <Text style={styles.subtitle}>
            输入您的邮箱地址，我们将发送密码重置链接给您
          </Text>
        </View>

        {/* 表单 */}
        <View style={styles.formContainer}>
          <TextInput
            label="邮箱地址"
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            style={styles.input}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            textContentType="emailAddress"
            left={<TextInput.Icon icon="email" />}
          />

          <Button
            mode="contained"
            onPress={handleSendResetEmail}
            style={styles.sendButton}
            loading={isLoading}
            disabled={isLoading}
          >
            发送重置链接
          </Button>
        </View>

        {/* 返回登录 */}
        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>记起密码了？</Text>
          <Text style={styles.loginLink} onPress={handleBackToLogin}>
            返回登录
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default ForgotPasswordScreen;
