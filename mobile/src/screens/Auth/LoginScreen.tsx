/**
 * 登录屏幕
 * 提供用户登录、生物识别登录和忘记密码功能
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  IconButton,
  Checkbox,
  useTheme,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '@store/AuthContext';
import { BiometricService } from '@services/BiometricService';
import { AnalyticsService } from '@services/AnalyticsService';
import { Logger } from '@utils/Logger';

const { width, height } = Dimensions.get('window');

const LoginScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const { login, isLoading, error, clearError } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  useEffect(() => {
    checkBiometricAvailability();
    trackScreenView();
  }, []);

  useEffect(() => {
    if (error) {
      Alert.alert('登录失败', error, [
        { text: '确定', onPress: clearError }
      ]);
    }
  }, [error, clearError]);

  /**
   * 检查生物识别可用性
   */
  const checkBiometricAvailability = async () => {
    try {
      const isSupported = BiometricService.isSupported();
      const isEnabled = BiometricService.isEnabled();
      setBiometricAvailable(isSupported && isEnabled);
    } catch (error) {
      Logger.error('检查生物识别可用性失败:', error);
    }
  };

  /**
   * 跟踪屏幕浏览
   */
  const trackScreenView = () => {
    AnalyticsService.trackScreenView('login_screen');
  };

  /**
   * 处理登录
   */
  const handleLogin = async () => {
    if (!email.trim()) {
      Alert.alert('提示', '请输入邮箱地址');
      return;
    }

    if (!password.trim()) {
      Alert.alert('提示', '请输入密码');
      return;
    }

    try {
      await login(email.trim(), password);
      
      // 跟踪登录事件
      AnalyticsService.trackEvent('user_login', {
        method: 'email_password',
        remember_me: rememberMe,
      });

      Logger.info('用户登录成功');
    } catch (error) {
      Logger.error('登录失败:', error);
    }
  };

  /**
   * 生物识别登录
   */
  const handleBiometricLogin = async () => {
    try {
      const result = await BiometricService.authenticate({
        title: '生物识别登录',
        subtitle: '使用您的生物识别信息登录',
        description: '请验证您的身份以登录应用',
      });

      if (result.success) {
        // 这里应该从安全存储中获取用户凭据
        // 然后执行自动登录
        Logger.info('生物识别验证成功');
        
        // 跟踪生物识别登录事件
        AnalyticsService.trackEvent('user_login', {
          method: 'biometric',
          biometry_type: result.biometryType,
        });

        // 模拟自动登录
        // await login(savedEmail, savedPassword);
      }
    } catch (error) {
      Logger.error('生物识别登录失败:', error);
      Alert.alert('验证失败', '生物识别验证失败，请重试');
    }
  };

  /**
   * 忘记密码
   */
  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
    
    // 跟踪忘记密码事件
    AnalyticsService.trackEvent('forgot_password_clicked');
  };

  /**
   * 注册
   */
  const handleRegister = () => {
    navigation.navigate('Register');
    
    // 跟踪注册事件
    AnalyticsService.trackEvent('register_clicked');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      justifyContent: 'center',
      paddingHorizontal: 24,
      paddingTop: insets.top + 20,
      paddingBottom: insets.bottom + 20,
    },
    logoContainer: {
      alignItems: 'center',
      marginBottom: 48,
    },
    logo: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    logoText: {
      fontSize: 36,
      fontWeight: 'bold',
      color: theme.colors.onPrimary,
    },
    appName: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onBackground,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 8,
    },
    formContainer: {
      marginBottom: 32,
    },
    input: {
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
    },
    passwordContainer: {
      position: 'relative',
    },
    passwordToggle: {
      position: 'absolute',
      right: 0,
      top: 8,
    },
    optionsContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 24,
    },
    rememberContainer: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    rememberText: {
      marginLeft: 8,
      color: theme.colors.onSurface,
    },
    forgotPassword: {
      color: theme.colors.primary,
      fontSize: 14,
    },
    loginButton: {
      marginBottom: 16,
      paddingVertical: 8,
    },
    biometricContainer: {
      alignItems: 'center',
      marginBottom: 32,
    },
    biometricButton: {
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 30,
    },
    biometricText: {
      marginTop: 8,
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
    },
    divider: {
      flexDirection: 'row',
      alignItems: 'center',
      marginVertical: 24,
    },
    dividerLine: {
      flex: 1,
      height: 1,
      backgroundColor: theme.colors.outline,
    },
    dividerText: {
      marginHorizontal: 16,
      color: theme.colors.onSurfaceVariant,
      fontSize: 14,
    },
    registerContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    registerText: {
      color: theme.colors.onSurface,
      fontSize: 14,
    },
    registerLink: {
      color: theme.colors.primary,
      fontSize: 14,
      fontWeight: 'bold',
      marginLeft: 4,
    },
    versionContainer: {
      alignItems: 'center',
      marginTop: 32,
    },
    versionText: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.6,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Logo和标题 */}
        <View style={styles.logoContainer}>
          <Surface style={styles.logo} elevation={4}>
            <Text style={styles.logoText}>ERP</Text>
          </Surface>
          <Text style={styles.appName}>跨境电商ERP</Text>
          <Text style={styles.subtitle}>智能化业务管理平台</Text>
        </View>

        {/* 登录表单 */}
        <View style={styles.formContainer}>
          <TextInput
            label="邮箱地址"
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            style={styles.input}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            textContentType="emailAddress"
            left={<TextInput.Icon icon="email" />}
          />

          <View style={styles.passwordContainer}>
            <TextInput
              label="密码"
              value={password}
              onChangeText={setPassword}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              autoComplete="password"
              textContentType="password"
              left={<TextInput.Icon icon="lock" />}
            />
            <IconButton
              icon={showPassword ? 'eye-off' : 'eye'}
              onPress={() => setShowPassword(!showPassword)}
              style={styles.passwordToggle}
            />
          </View>

          {/* 选项 */}
          <View style={styles.optionsContainer}>
            <View style={styles.rememberContainer}>
              <Checkbox
                status={rememberMe ? 'checked' : 'unchecked'}
                onPress={() => setRememberMe(!rememberMe)}
              />
              <Text style={styles.rememberText}>记住我</Text>
            </View>
            <Text style={styles.forgotPassword} onPress={handleForgotPassword}>
              忘记密码？
            </Text>
          </View>

          {/* 登录按钮 */}
          <Button
            mode="contained"
            onPress={handleLogin}
            style={styles.loginButton}
            loading={isLoading}
            disabled={isLoading}
          >
            登录
          </Button>

          {/* 生物识别登录 */}
          {biometricAvailable && (
            <View style={styles.biometricContainer}>
              <IconButton
                icon="fingerprint"
                size={32}
                iconColor={theme.colors.primary}
                style={styles.biometricButton}
                onPress={handleBiometricLogin}
              />
              <Text style={styles.biometricText}>使用生物识别登录</Text>
            </View>
          )}
        </View>

        {/* 分割线 */}
        <View style={styles.divider}>
          <View style={styles.dividerLine} />
          <Text style={styles.dividerText}>或</Text>
          <View style={styles.dividerLine} />
        </View>

        {/* 注册链接 */}
        <View style={styles.registerContainer}>
          <Text style={styles.registerText}>还没有账户？</Text>
          <Text style={styles.registerLink} onPress={handleRegister}>
            立即注册
          </Text>
        </View>

        {/* 版本信息 */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>版本 1.0.0</Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default LoginScreen;
