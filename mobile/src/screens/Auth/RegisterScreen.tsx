/**
 * 注册屏幕
 * 提供用户注册功能
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  IconButton,
  Checkbox,
  useTheme,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { useAuth } from '@store/AuthContext';
import { AnalyticsService } from '@services/AnalyticsService';
import { Logger } from '@utils/Logger';

const RegisterScreen: React.FC = () => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const { register, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [agreeToPrivacy, setAgreeToPrivacy] = useState(false);

  useEffect(() => {
    trackScreenView();
  }, []);

  useEffect(() => {
    if (error) {
      Alert.alert('注册失败', error, [
        { text: '确定', onPress: clearError }
      ]);
    }
  }, [error, clearError]);

  /**
   * 跟踪屏幕浏览
   */
  const trackScreenView = () => {
    AnalyticsService.trackScreenView('register_screen');
  };

  /**
   * 更新表单数据
   */
  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  /**
   * 验证表单
   */
  const validateForm = (): boolean => {
    if (!formData.name.trim()) {
      Alert.alert('提示', '请输入姓名');
      return false;
    }

    if (!formData.email.trim()) {
      Alert.alert('提示', '请输入邮箱地址');
      return false;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      Alert.alert('提示', '请输入有效的邮箱地址');
      return false;
    }

    if (!formData.password) {
      Alert.alert('提示', '请输入密码');
      return false;
    }

    if (formData.password.length < 8) {
      Alert.alert('提示', '密码长度至少为8位');
      return false;
    }

    if (formData.password !== formData.confirmPassword) {
      Alert.alert('提示', '两次输入的密码不一致');
      return false;
    }

    if (!agreeToTerms) {
      Alert.alert('提示', '请同意用户协议');
      return false;
    }

    if (!agreeToPrivacy) {
      Alert.alert('提示', '请同意隐私政策');
      return false;
    }

    return true;
  };

  /**
   * 处理注册
   */
  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      await register(formData);
      
      // 跟踪注册事件
      AnalyticsService.trackEvent('user_register', {
        method: 'email_password',
      });

      Logger.info('用户注册成功');
      
      Alert.alert(
        '注册成功',
        '您的账户已创建成功，现在可以登录了',
        [
          { text: '确定', onPress: () => navigation.navigate('Login') }
        ]
      );
    } catch (error) {
      Logger.error('注册失败:', error);
    }
  };

  /**
   * 返回登录
   */
  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollContainer: {
      flexGrow: 1,
      paddingHorizontal: 24,
      paddingTop: insets.top + 20,
      paddingBottom: insets.bottom + 20,
    },
    header: {
      alignItems: 'center',
      marginBottom: 32,
    },
    logo: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 16,
    },
    logoText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.onPrimary,
    },
    title: {
      fontSize: 28,
      fontWeight: 'bold',
      color: theme.colors.onBackground,
      textAlign: 'center',
    },
    subtitle: {
      fontSize: 16,
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 8,
    },
    formContainer: {
      marginBottom: 24,
    },
    input: {
      marginBottom: 16,
      backgroundColor: theme.colors.surface,
    },
    passwordContainer: {
      position: 'relative',
    },
    passwordToggle: {
      position: 'absolute',
      right: 0,
      top: 8,
    },
    agreementContainer: {
      marginBottom: 24,
    },
    agreementRow: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
    },
    agreementText: {
      flex: 1,
      marginLeft: 8,
      color: theme.colors.onSurface,
      fontSize: 14,
    },
    linkText: {
      color: theme.colors.primary,
      textDecorationLine: 'underline',
    },
    registerButton: {
      marginBottom: 24,
      paddingVertical: 8,
    },
    loginContainer: {
      flexDirection: 'row',
      justifyContent: 'center',
      alignItems: 'center',
    },
    loginText: {
      color: theme.colors.onSurface,
      fontSize: 14,
    },
    loginLink: {
      color: theme.colors.primary,
      fontSize: 14,
      fontWeight: 'bold',
      marginLeft: 4,
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* 头部 */}
        <View style={styles.header}>
          <Surface style={styles.logo} elevation={4}>
            <Text style={styles.logoText}>ERP</Text>
          </Surface>
          <Text style={styles.title}>创建账户</Text>
          <Text style={styles.subtitle}>加入跨境电商ERP平台</Text>
        </View>

        {/* 注册表单 */}
        <View style={styles.formContainer}>
          <TextInput
            label="姓名"
            value={formData.name}
            onChangeText={(value) => updateFormData('name', value)}
            mode="outlined"
            style={styles.input}
            autoCapitalize="words"
            autoComplete="name"
            textContentType="name"
            left={<TextInput.Icon icon="account" />}
          />

          <TextInput
            label="邮箱地址"
            value={formData.email}
            onChangeText={(value) => updateFormData('email', value)}
            mode="outlined"
            style={styles.input}
            keyboardType="email-address"
            autoCapitalize="none"
            autoComplete="email"
            textContentType="emailAddress"
            left={<TextInput.Icon icon="email" />}
          />

          <View style={styles.passwordContainer}>
            <TextInput
              label="密码"
              value={formData.password}
              onChangeText={(value) => updateFormData('password', value)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showPassword}
              autoComplete="password-new"
              textContentType="newPassword"
              left={<TextInput.Icon icon="lock" />}
            />
            <IconButton
              icon={showPassword ? 'eye-off' : 'eye'}
              onPress={() => setShowPassword(!showPassword)}
              style={styles.passwordToggle}
            />
          </View>

          <View style={styles.passwordContainer}>
            <TextInput
              label="确认密码"
              value={formData.confirmPassword}
              onChangeText={(value) => updateFormData('confirmPassword', value)}
              mode="outlined"
              style={styles.input}
              secureTextEntry={!showConfirmPassword}
              autoComplete="password-new"
              textContentType="newPassword"
              left={<TextInput.Icon icon="lock-check" />}
            />
            <IconButton
              icon={showConfirmPassword ? 'eye-off' : 'eye'}
              onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              style={styles.passwordToggle}
            />
          </View>
        </View>

        {/* 协议同意 */}
        <View style={styles.agreementContainer}>
          <View style={styles.agreementRow}>
            <Checkbox
              status={agreeToTerms ? 'checked' : 'unchecked'}
              onPress={() => setAgreeToTerms(!agreeToTerms)}
            />
            <Text style={styles.agreementText}>
              我已阅读并同意{' '}
              <Text style={styles.linkText}>用户协议</Text>
            </Text>
          </View>

          <View style={styles.agreementRow}>
            <Checkbox
              status={agreeToPrivacy ? 'checked' : 'unchecked'}
              onPress={() => setAgreeToPrivacy(!agreeToPrivacy)}
            />
            <Text style={styles.agreementText}>
              我已阅读并同意{' '}
              <Text style={styles.linkText}>隐私政策</Text>
            </Text>
          </View>
        </View>

        {/* 注册按钮 */}
        <Button
          mode="contained"
          onPress={handleRegister}
          style={styles.registerButton}
          loading={isLoading}
          disabled={isLoading}
        >
          创建账户
        </Button>

        {/* 登录链接 */}
        <View style={styles.loginContainer}>
          <Text style={styles.loginText}>已有账户？</Text>
          <Text style={styles.loginLink} onPress={handleBackToLogin}>
            立即登录
          </Text>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

export default RegisterScreen;
