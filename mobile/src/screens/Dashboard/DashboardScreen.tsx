/**
 * 仪表板屏幕
 * 显示业务概览和关键指标
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Surface,
  useTheme,
  ActivityIndicator,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart, PieChart } from 'react-native-chart-kit';

// 导入组件
import { StatCard } from '@components/StatCard';
import { QuickActions } from '@components/QuickActions';
import { RecentOrders } from '@components/RecentOrders';
import { AlertsList } from '@components/AlertsList';

// 导入服务
import { DashboardService } from '@services/DashboardService';
import { Logger } from '@utils/Logger';

// 导入类型
interface DashboardData {
  stats: {
    totalOrders: number;
    totalRevenue: number;
    totalProducts: number;
    totalCustomers: number;
    orderGrowth: number;
    revenueGrowth: number;
  };
  charts: {
    salesTrend: number[];
    ordersByStatus: { name: string; value: number; color: string }[];
    topProducts: { name: string; sales: number }[];
  };
  recentOrders: any[];
  alerts: any[];
}

const { width: screenWidth } = Dimensions.get('window');

/**
 * 仪表板屏幕组件
 */
const DashboardScreen: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);

  useEffect(() => {
    loadDashboardData();
  }, []);

  /**
   * 加载仪表板数据
   */
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await DashboardService.getDashboardData();
      setDashboardData(data);
    } catch (error) {
      Logger.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 下拉刷新
   */
  const onRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  /**
   * 图表配置
   */
  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(${theme.colors.primary}, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(${theme.colors.onSurface}, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  };

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (!dashboardData) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Title>数据加载失败</Title>
        <Button mode="contained" onPress={loadDashboardData}>
          重试
        </Button>
      </View>
    );
  }

  return (
    <ScrollView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* 欢迎区域 */}
      <Surface style={[styles.welcomeCard, { backgroundColor: theme.colors.primary }]}>
        <View style={styles.welcomeContent}>
          <Title style={[styles.welcomeTitle, { color: theme.colors.onPrimary }]}>
            欢迎回来！
          </Title>
          <Paragraph style={[styles.welcomeText, { color: theme.colors.onPrimary }]}>
            今天是美好的一天，让我们开始工作吧
          </Paragraph>
        </View>
        <Icon name="dashboard" size={60} color={theme.colors.onPrimary} />
      </Surface>

      {/* 统计卡片 */}
      <View style={styles.statsContainer}>
        <StatCard
          title="总订单"
          value={dashboardData.stats.totalOrders.toString()}
          growth={dashboardData.stats.orderGrowth}
          icon="shopping-cart"
          color={theme.colors.primary}
        />
        <StatCard
          title="总收入"
          value={`¥${dashboardData.stats.totalRevenue.toLocaleString()}`}
          growth={dashboardData.stats.revenueGrowth}
          icon="attach-money"
          color={theme.colors.secondary}
        />
        <StatCard
          title="商品数量"
          value={dashboardData.stats.totalProducts.toString()}
          icon="inventory"
          color={theme.colors.tertiary}
        />
        <StatCard
          title="客户数量"
          value={dashboardData.stats.totalCustomers.toString()}
          icon="people"
          color={theme.colors.error}
        />
      </View>

      {/* 快速操作 */}
      <QuickActions />

      {/* 销售趋势图表 */}
      <Card style={styles.chartCard}>
        <Card.Content>
          <Title>销售趋势</Title>
          <LineChart
            data={{
              labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
              datasets: [
                {
                  data: dashboardData.charts.salesTrend,
                },
              ],
            }}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        </Card.Content>
      </Card>

      {/* 订单状态分布 */}
      <Card style={styles.chartCard}>
        <Card.Content>
          <Title>订单状态分布</Title>
          <PieChart
            data={dashboardData.charts.ordersByStatus}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            accessor="value"
            backgroundColor="transparent"
            paddingLeft="15"
            style={styles.chart}
          />
        </Card.Content>
      </Card>

      {/* 热销商品 */}
      <Card style={styles.chartCard}>
        <Card.Content>
          <Title>热销商品</Title>
          <BarChart
            data={{
              labels: dashboardData.charts.topProducts.map(p => p.name.substring(0, 8)),
              datasets: [
                {
                  data: dashboardData.charts.topProducts.map(p => p.sales),
                },
              ],
            }}
            width={screenWidth - 60}
            height={220}
            chartConfig={chartConfig}
            style={styles.chart}
          />
        </Card.Content>
      </Card>

      {/* 最近订单 */}
      <RecentOrders orders={dashboardData.recentOrders} />

      {/* 系统提醒 */}
      <AlertsList alerts={dashboardData.alerts} />

      {/* 底部间距 */}
      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

/**
 * 样式定义
 */
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  welcomeCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  welcomeContent: {
    flex: 1,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  welcomeText: {
    fontSize: 16,
    opacity: 0.9,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 8,
  },
  chartCard: {
    margin: 16,
    borderRadius: 12,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default DashboardScreen;
