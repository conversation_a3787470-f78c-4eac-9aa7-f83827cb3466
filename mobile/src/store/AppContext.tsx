/**
 * 应用状态管理Context
 * 管理应用全局状态、设置和配置
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Logger } from '@utils/Logger';

// 应用设置类型
interface AppSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'zh-CN' | 'en-US' | 'ja-JP' | 'ko-KR';
  notifications: {
    push: boolean;
    email: boolean;
    sms: boolean;
  };
  privacy: {
    analytics: boolean;
    crashReporting: boolean;
  };
  display: {
    fontSize: 'small' | 'medium' | 'large';
    density: 'compact' | 'standard' | 'comfortable';
  };
}

// 网络状态类型
interface NetworkState {
  isConnected: boolean;
  connectionType: 'wifi' | 'cellular' | 'none';
  isSlowConnection: boolean;
}

// 应用状态类型
interface AppState {
  isInitialized: boolean;
  isLoading: boolean;
  settings: AppSettings;
  network: NetworkState;
  notifications: Notification[];
  error: string | null;
}

// 通知类型
interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actions?: NotificationAction[];
}

interface NotificationAction {
  label: string;
  action: () => void;
}

// 应用操作类型
type AppAction =
  | { type: 'INITIALIZE_START' }
  | { type: 'INITIALIZE_SUCCESS'; payload: AppSettings }
  | { type: 'INITIALIZE_FAILURE'; payload: string }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<AppSettings> }
  | { type: 'UPDATE_NETWORK'; payload: NetworkState }
  | { type: 'ADD_NOTIFICATION'; payload: Notification }
  | { type: 'REMOVE_NOTIFICATION'; payload: string }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'CLEAR_ALL_NOTIFICATIONS' }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

// 默认设置
const defaultSettings: AppSettings = {
  theme: 'auto',
  language: 'zh-CN',
  notifications: {
    push: true,
    email: true,
    sms: false,
  },
  privacy: {
    analytics: true,
    crashReporting: true,
  },
  display: {
    fontSize: 'medium',
    density: 'standard',
  },
};

// 初始状态
const initialState: AppState = {
  isInitialized: false,
  isLoading: false,
  settings: defaultSettings,
  network: {
    isConnected: true,
    connectionType: 'wifi',
    isSlowConnection: false,
  },
  notifications: [],
  error: null,
};

// 状态reducer
const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'INITIALIZE_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'INITIALIZE_SUCCESS':
      return {
        ...state,
        isInitialized: true,
        isLoading: false,
        settings: action.payload,
        error: null,
      };
    case 'INITIALIZE_FAILURE':
      return {
        ...state,
        isInitialized: false,
        isLoading: false,
        error: action.payload,
      };
    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };
    case 'UPDATE_NETWORK':
      return {
        ...state,
        network: action.payload,
      };
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: [action.payload, ...state.notifications],
      };
    case 'REMOVE_NOTIFICATION':
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload),
      };
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(n =>
          n.id === action.payload ? { ...n, read: true } : n
        ),
      };
    case 'CLEAR_ALL_NOTIFICATIONS':
      return {
        ...state,
        notifications: [],
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };
    default:
      return state;
  }
};

// Context类型
interface AppContextType extends AppState {
  updateSettings: (settings: Partial<AppSettings>) => Promise<void>;
  updateNetworkState: (network: NetworkState) => void;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
  markNotificationRead: (id: string) => void;
  clearAllNotifications: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 创建Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// AppProvider组件
interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // 初始化应用设置
  useEffect(() => {
    initializeApp();
  }, []);

  /**
   * 初始化应用
   */
  const initializeApp = async () => {
    dispatch({ type: 'INITIALIZE_START' });

    try {
      // 加载保存的设置
      const savedSettings = await loadSettings();
      
      dispatch({ type: 'INITIALIZE_SUCCESS', payload: savedSettings });
      Logger.info('应用初始化成功');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '应用初始化失败';
      dispatch({ type: 'INITIALIZE_FAILURE', payload: errorMessage });
      Logger.error('应用初始化失败:', error);
    }
  };

  /**
   * 加载设置
   */
  const loadSettings = async (): Promise<AppSettings> => {
    try {
      const settingsJson = await AsyncStorage.getItem('appSettings');
      if (settingsJson) {
        const savedSettings = JSON.parse(settingsJson);
        return { ...defaultSettings, ...savedSettings };
      }
      return defaultSettings;
    } catch (error) {
      Logger.error('加载设置失败:', error);
      return defaultSettings;
    }
  };

  /**
   * 保存设置
   */
  const saveSettings = async (settings: AppSettings) => {
    try {
      await AsyncStorage.setItem('appSettings', JSON.stringify(settings));
      Logger.info('设置已保存');
    } catch (error) {
      Logger.error('保存设置失败:', error);
    }
  };

  /**
   * 更新设置
   */
  const updateSettings = async (newSettings: Partial<AppSettings>) => {
    const updatedSettings = { ...state.settings, ...newSettings };
    dispatch({ type: 'UPDATE_SETTINGS', payload: newSettings });
    await saveSettings(updatedSettings);
  };

  /**
   * 更新网络状态
   */
  const updateNetworkState = (network: NetworkState) => {
    dispatch({ type: 'UPDATE_NETWORK', payload: network });
  };

  /**
   * 添加通知
   */
  const addNotification = (notificationData: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const notification: Notification = {
      ...notificationData,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      read: false,
    };

    dispatch({ type: 'ADD_NOTIFICATION', payload: notification });
    Logger.info('添加通知:', notification.title);
  };

  /**
   * 移除通知
   */
  const removeNotification = (id: string) => {
    dispatch({ type: 'REMOVE_NOTIFICATION', payload: id });
  };

  /**
   * 标记通知为已读
   */
  const markNotificationRead = (id: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id });
  };

  /**
   * 清除所有通知
   */
  const clearAllNotifications = () => {
    dispatch({ type: 'CLEAR_ALL_NOTIFICATIONS' });
  };

  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  /**
   * 设置错误
   */
  const setError = (error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  };

  const value: AppContextType = {
    ...state,
    updateSettings,
    updateNetworkState,
    addNotification,
    removeNotification,
    markNotificationRead,
    clearAllNotifications,
    setLoading,
    setError,
  };

  return <AppContext.Provider value={value}>{children}</AppContext.Provider>;
};

// useApp Hook
export const useApp = (): AppContextType => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp必须在AppProvider内部使用');
  }
  return context;
};
