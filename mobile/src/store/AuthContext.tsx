/**
 * 认证状态管理Context
 * 管理用户登录状态、用户信息和认证相关操作
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Logger } from '@utils/Logger';

// 用户信息类型
interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  permissions: string[];
  lastLoginAt: Date;
}

// 认证状态类型
interface AuthState {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  error: string | null;
}

// 认证操作类型
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string; refreshToken: string } }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_USER'; payload: Partial<User> }
  | { type: 'CLEAR_ERROR' };

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  token: null,
  refreshToken: null,
  error: null,
};

// 状态reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        user: action.payload.user,
        token: action.payload.token,
        refreshToken: action.payload.refreshToken,
        error: null,
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        token: null,
        refreshToken: null,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    default:
      return state;
  }
};

// Context类型
interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  updateUser: (userData: Partial<User>) => void;
  clearError: () => void;
  refreshAuth: () => Promise<void>;
}

// 注册数据类型
interface RegisterData {
  email: string;
  password: string;
  name: string;
  confirmPassword: string;
}

// 创建Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// AuthProvider组件
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // 初始化时检查本地存储的认证信息
  useEffect(() => {
    checkAuthState();
  }, []);

  /**
   * 检查认证状态
   */
  const checkAuthState = async () => {
    try {
      const token = await AsyncStorage.getItem('authToken');
      const refreshToken = await AsyncStorage.getItem('refreshToken');
      const userData = await AsyncStorage.getItem('userData');

      if (token && refreshToken && userData) {
        const user = JSON.parse(userData);
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: { user, token, refreshToken },
        });
      } else {
        dispatch({ type: 'LOGOUT' });
      }
    } catch (error) {
      Logger.error('检查认证状态失败:', error);
      dispatch({ type: 'LOGOUT' });
    }
  };

  /**
   * 登录
   */
  const login = async (email: string, password: string) => {
    dispatch({ type: 'AUTH_START' });

    try {
      // 模拟API调用
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        throw new Error('登录失败');
      }

      const data = await response.json();
      const { user, token, refreshToken } = data;

      // 保存到本地存储
      await AsyncStorage.setItem('authToken', token);
      await AsyncStorage.setItem('refreshToken', refreshToken);
      await AsyncStorage.setItem('userData', JSON.stringify(user));

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user, token, refreshToken },
      });

      Logger.info('用户登录成功:', user.email);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      Logger.error('登录失败:', error);
    }
  };

  /**
   * 注册
   */
  const register = async (userData: RegisterData) => {
    dispatch({ type: 'AUTH_START' });

    try {
      if (userData.password !== userData.confirmPassword) {
        throw new Error('密码确认不匹配');
      }

      // 模拟API调用
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: userData.email,
          password: userData.password,
          name: userData.name,
        }),
      });

      if (!response.ok) {
        throw new Error('注册失败');
      }

      const data = await response.json();
      const { user, token, refreshToken } = data;

      // 保存到本地存储
      await AsyncStorage.setItem('authToken', token);
      await AsyncStorage.setItem('refreshToken', refreshToken);
      await AsyncStorage.setItem('userData', JSON.stringify(user));

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user, token, refreshToken },
      });

      Logger.info('用户注册成功:', user.email);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '注册失败';
      dispatch({ type: 'AUTH_FAILURE', payload: errorMessage });
      Logger.error('注册失败:', error);
    }
  };

  /**
   * 登出
   */
  const logout = async () => {
    try {
      // 清除本地存储
      await AsyncStorage.multiRemove(['authToken', 'refreshToken', 'userData']);

      dispatch({ type: 'LOGOUT' });
      Logger.info('用户已登出');
    } catch (error) {
      Logger.error('登出失败:', error);
    }
  };

  /**
   * 更新用户信息
   */
  const updateUser = (userData: Partial<User>) => {
    dispatch({ type: 'UPDATE_USER', payload: userData });
  };

  /**
   * 清除错误
   */
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  /**
   * 刷新认证
   */
  const refreshAuth = async () => {
    try {
      if (!state.refreshToken) {
        throw new Error('没有刷新令牌');
      }

      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${state.refreshToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('刷新认证失败');
      }

      const data = await response.json();
      const { token, refreshToken } = data;

      // 更新本地存储
      await AsyncStorage.setItem('authToken', token);
      await AsyncStorage.setItem('refreshToken', refreshToken);

      dispatch({
        type: 'AUTH_SUCCESS',
        payload: { user: state.user!, token, refreshToken },
      });

      Logger.info('认证已刷新');
    } catch (error) {
      Logger.error('刷新认证失败:', error);
      await logout();
    }
  };

  const value: AuthContextType = {
    ...state,
    login,
    logout,
    register,
    updateUser,
    clearError,
    refreshAuth,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// useAuth Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
};
