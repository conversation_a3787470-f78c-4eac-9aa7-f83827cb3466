/**
 * 移动端主题配置
 * 定义应用的颜色、字体、间距等设计系统
 */

import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

// 自定义颜色配置
const customColors = {
  primary: '#2563eb',
  primaryContainer: '#dbeafe',
  secondary: '#7c3aed',
  secondaryContainer: '#ede9fe',
  tertiary: '#059669',
  tertiaryContainer: '#d1fae5',
  surface: '#ffffff',
  surfaceVariant: '#f1f5f9',
  background: '#fafafa',
  error: '#dc2626',
  errorContainer: '#fef2f2',
  onPrimary: '#ffffff',
  onPrimaryContainer: '#1e40af',
  onSecondary: '#ffffff',
  onSecondaryContainer: '#5b21b6',
  onTertiary: '#ffffff',
  onTertiaryContainer: '#047857',
  onSurface: '#0f172a',
  onSurfaceVariant: '#64748b',
  onBackground: '#0f172a',
  onError: '#ffffff',
  onErrorContainer: '#b91c1c',
  outline: '#cbd5e1',
  outlineVariant: '#e2e8f0',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#1e293b',
  inverseOnSurface: '#f1f5f9',
  inversePrimary: '#60a5fa',
};

const customDarkColors = {
  primary: '#60a5fa',
  primaryContainer: '#1e40af',
  secondary: '#a78bfa',
  secondaryContainer: '#5b21b6',
  tertiary: '#34d399',
  tertiaryContainer: '#047857',
  surface: '#1e293b',
  surfaceVariant: '#334155',
  background: '#0f172a',
  error: '#f87171',
  errorContainer: '#b91c1c',
  onPrimary: '#1e40af',
  onPrimaryContainer: '#dbeafe',
  onSecondary: '#5b21b6',
  onSecondaryContainer: '#ede9fe',
  onTertiary: '#047857',
  onTertiaryContainer: '#d1fae5',
  onSurface: '#f1f5f9',
  onSurfaceVariant: '#94a3b8',
  onBackground: '#f1f5f9',
  onError: '#b91c1c',
  onErrorContainer: '#fef2f2',
  outline: '#475569',
  outlineVariant: '#334155',
  shadow: '#000000',
  scrim: '#000000',
  inverseSurface: '#f1f5f9',
  inverseOnSurface: '#1e293b',
  inversePrimary: '#2563eb',
};

// 字体配置
const fonts = {
  displayLarge: {
    fontFamily: 'System',
    fontSize: 57,
    fontWeight: '400' as const,
    lineHeight: 64,
    letterSpacing: -0.25,
  },
  displayMedium: {
    fontFamily: 'System',
    fontSize: 45,
    fontWeight: '400' as const,
    lineHeight: 52,
    letterSpacing: 0,
  },
  displaySmall: {
    fontFamily: 'System',
    fontSize: 36,
    fontWeight: '400' as const,
    lineHeight: 44,
    letterSpacing: 0,
  },
  headlineLarge: {
    fontFamily: 'System',
    fontSize: 32,
    fontWeight: '400' as const,
    lineHeight: 40,
    letterSpacing: 0,
  },
  headlineMedium: {
    fontFamily: 'System',
    fontSize: 28,
    fontWeight: '400' as const,
    lineHeight: 36,
    letterSpacing: 0,
  },
  headlineSmall: {
    fontFamily: 'System',
    fontSize: 24,
    fontWeight: '400' as const,
    lineHeight: 32,
    letterSpacing: 0,
  },
  titleLarge: {
    fontFamily: 'System',
    fontSize: 22,
    fontWeight: '400' as const,
    lineHeight: 28,
    letterSpacing: 0,
  },
  titleMedium: {
    fontFamily: 'System',
    fontSize: 16,
    fontWeight: '500' as const,
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  titleSmall: {
    fontFamily: 'System',
    fontSize: 14,
    fontWeight: '500' as const,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  labelLarge: {
    fontFamily: 'System',
    fontSize: 14,
    fontWeight: '500' as const,
    lineHeight: 20,
    letterSpacing: 0.1,
  },
  labelMedium: {
    fontFamily: 'System',
    fontSize: 12,
    fontWeight: '500' as const,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  labelSmall: {
    fontFamily: 'System',
    fontSize: 11,
    fontWeight: '500' as const,
    lineHeight: 16,
    letterSpacing: 0.5,
  },
  bodyLarge: {
    fontFamily: 'System',
    fontSize: 16,
    fontWeight: '400' as const,
    lineHeight: 24,
    letterSpacing: 0.5,
  },
  bodyMedium: {
    fontFamily: 'System',
    fontSize: 14,
    fontWeight: '400' as const,
    lineHeight: 20,
    letterSpacing: 0.25,
  },
  bodySmall: {
    fontFamily: 'System',
    fontSize: 12,
    fontWeight: '400' as const,
    lineHeight: 16,
    letterSpacing: 0.4,
  },
};

// 间距配置
const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// 圆角配置
const roundness = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  full: 9999,
};

// 阴影配置
const elevation = {
  level0: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  level1: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  level2: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  level3: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 3,
  },
  level4: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 4,
  },
  level5: {
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 16 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 5,
  },
};

// 浅色主题
export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    ...customColors,
  },
  fonts,
  spacing,
  roundness: roundness.md,
  elevation,
  custom: {
    spacing,
    roundness,
    elevation,
  },
};

// 深色主题
export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    ...customDarkColors,
  },
  fonts,
  spacing,
  roundness: roundness.md,
  elevation,
  custom: {
    spacing,
    roundness,
    elevation,
  },
};

// 主题类型定义
export type Theme = typeof lightTheme;

// 导出默认主题
export default lightTheme;
