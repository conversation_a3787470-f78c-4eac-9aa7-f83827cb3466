/**
 * 拍照上传组件
 * 提供拍照、选择图片和上传功能
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Platform,
  PermissionsAndroid,
  Image,
  Dimensions,
} from 'react-native';
import {
  Button,
  IconButton,
  Text,
  Surface,
  ProgressBar,
  useTheme,
} from 'react-native-paper';
import { launchCamera, launchImageLibrary, ImagePickerResponse, MediaType } from 'react-native-image-picker';
import { Logger } from '@utils/Logger';

const { width } = Dimensions.get('window');

interface CameraUploadProps {
  onUploadSuccess?: (imageUrl: string) => void;
  onUploadError?: (error: string) => void;
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  allowMultiple?: boolean;
  mediaType?: MediaType;
}

interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

export const CameraUpload: React.FC<CameraUploadProps> = ({
  onUploadSuccess,
  onUploadError,
  maxWidth = 1024,
  maxHeight = 1024,
  quality = 0.8,
  allowMultiple = false,
  mediaType = 'photo',
}) => {
  const theme = useTheme();
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<UploadProgress | null>(null);

  /**
   * 请求相机权限
   */
  const requestCameraPermission = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.CAMERA,
          {
            title: '相机权限',
            message: '应用需要相机权限来拍照',
            buttonNeutral: '稍后询问',
            buttonNegative: '取消',
            buttonPositive: '确定',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (error) {
        Logger.error('请求相机权限失败:', error);
        return false;
      }
    }
    return true;
  };

  /**
   * 拍照
   */
  const takePhoto = async () => {
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) {
      Alert.alert('权限不足', '无法访问相机，请在设置中授予相机权限');
      return;
    }

    const options = {
      mediaType,
      includeBase64: false,
      maxHeight,
      maxWidth,
      quality,
      storageOptions: {
        skipBackup: true,
        path: 'images',
      },
    };

    launchCamera(options, handleImageResponse);
  };

  /**
   * 从相册选择
   */
  const selectFromGallery = () => {
    const options = {
      mediaType,
      includeBase64: false,
      maxHeight,
      maxWidth,
      quality,
      selectionLimit: allowMultiple ? 0 : 1,
    };

    launchImageLibrary(options, handleImageResponse);
  };

  /**
   * 处理图片选择结果
   */
  const handleImageResponse = (response: ImagePickerResponse) => {
    if (response.didCancel) {
      Logger.info('用户取消了图片选择');
      return;
    }

    if (response.errorMessage) {
      Logger.error('图片选择错误:', response.errorMessage);
      Alert.alert('错误', response.errorMessage);
      return;
    }

    if (response.assets && response.assets.length > 0) {
      const imageUris = response.assets
        .filter(asset => asset.uri)
        .map(asset => asset.uri!);

      if (allowMultiple) {
        setSelectedImages(prev => [...prev, ...imageUris]);
      } else {
        setSelectedImages(imageUris);
      }

      Logger.info('选择了图片:', imageUris);
    }
  };

  /**
   * 上传图片
   */
  const uploadImages = async () => {
    if (selectedImages.length === 0) {
      Alert.alert('提示', '请先选择要上传的图片');
      return;
    }

    setUploading(true);
    setUploadProgress({ loaded: 0, total: selectedImages.length, percentage: 0 });

    try {
      const uploadedUrls: string[] = [];

      for (let i = 0; i < selectedImages.length; i++) {
        const imageUri = selectedImages[i];
        const uploadedUrl = await uploadSingleImage(imageUri, i + 1, selectedImages.length);
        uploadedUrls.push(uploadedUrl);

        // 更新进度
        const progress = {
          loaded: i + 1,
          total: selectedImages.length,
          percentage: ((i + 1) / selectedImages.length) * 100,
        };
        setUploadProgress(progress);
      }

      Logger.info('所有图片上传成功:', uploadedUrls);
      
      if (onUploadSuccess) {
        if (allowMultiple) {
          onUploadSuccess(uploadedUrls.join(','));
        } else {
          onUploadSuccess(uploadedUrls[0]);
        }
      }

      // 清空选择的图片
      setSelectedImages([]);
      Alert.alert('成功', '图片上传成功');
    } catch (error) {
      Logger.error('图片上传失败:', error);
      const errorMessage = error instanceof Error ? error.message : '上传失败';
      
      if (onUploadError) {
        onUploadError(errorMessage);
      }
      
      Alert.alert('上传失败', errorMessage);
    } finally {
      setUploading(false);
      setUploadProgress(null);
    }
  };

  /**
   * 上传单个图片
   */
  const uploadSingleImage = async (imageUri: string, current: number, total: number): Promise<string> => {
    const formData = new FormData();
    
    // 获取文件信息
    const filename = imageUri.split('/').pop() || 'image.jpg';
    const match = /\.(\w+)$/.exec(filename);
    const type = match ? `image/${match[1]}` : 'image/jpeg';

    formData.append('file', {
      uri: imageUri,
      type,
      name: filename,
    } as any);

    formData.append('folder', 'products'); // 可以根据需要设置文件夹
    formData.append('current', current.toString());
    formData.append('total', total.toString());

    const response = await fetch('/api/upload/image', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (!response.ok) {
      throw new Error(`上传失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    return result.url;
  };

  /**
   * 移除选中的图片
   */
  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
  };

  /**
   * 显示选择选项
   */
  const showImagePicker = () => {
    Alert.alert(
      '选择图片',
      '请选择获取图片的方式',
      [
        { text: '取消', style: 'cancel' },
        { text: '拍照', onPress: takePhoto },
        { text: '从相册选择', onPress: selectFromGallery },
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      padding: 16,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-around',
      marginBottom: 16,
    },
    button: {
      flex: 1,
      marginHorizontal: 4,
    },
    imageContainer: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      marginBottom: 16,
    },
    imageWrapper: {
      margin: 4,
      position: 'relative',
    },
    image: {
      width: (width - 64) / 3,
      height: (width - 64) / 3,
      borderRadius: 8,
    },
    removeButton: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: theme.colors.error,
      borderRadius: 12,
    },
    uploadContainer: {
      marginTop: 16,
    },
    progressContainer: {
      marginTop: 8,
      marginBottom: 16,
    },
    progressText: {
      textAlign: 'center',
      marginBottom: 8,
      color: theme.colors.onSurface,
    },
    emptyContainer: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 32,
      borderWidth: 2,
      borderColor: theme.colors.outline,
      borderStyle: 'dashed',
      borderRadius: 8,
      marginBottom: 16,
    },
    emptyText: {
      color: theme.colors.onSurfaceVariant,
      textAlign: 'center',
      marginTop: 8,
    },
  });

  return (
    <View style={styles.container}>
      {/* 操作按钮 */}
      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          icon="camera"
          onPress={takePhoto}
          style={styles.button}
          disabled={uploading}
        >
          拍照
        </Button>
        <Button
          mode="outlined"
          icon="image"
          onPress={selectFromGallery}
          style={styles.button}
          disabled={uploading}
        >
          相册
        </Button>
        <Button
          mode="outlined"
          icon="plus"
          onPress={showImagePicker}
          style={styles.button}
          disabled={uploading}
        >
          选择
        </Button>
      </View>

      {/* 选中的图片 */}
      {selectedImages.length > 0 ? (
        <View style={styles.imageContainer}>
          {selectedImages.map((imageUri, index) => (
            <View key={index} style={styles.imageWrapper}>
              <Image source={{ uri: imageUri }} style={styles.image} />
              <IconButton
                icon="close"
                iconColor={theme.colors.onError}
                size={16}
                style={styles.removeButton}
                onPress={() => removeImage(index)}
              />
            </View>
          ))}
        </View>
      ) : (
        <Surface style={styles.emptyContainer} elevation={0}>
          <IconButton
            icon="image-plus"
            iconColor={theme.colors.onSurfaceVariant}
            size={48}
          />
          <Text style={styles.emptyText}>
            点击上方按钮选择或拍摄图片
          </Text>
        </Surface>
      )}

      {/* 上传进度 */}
      {uploadProgress && (
        <View style={styles.progressContainer}>
          <Text style={styles.progressText}>
            上传进度: {uploadProgress.loaded}/{uploadProgress.total} ({uploadProgress.percentage.toFixed(0)}%)
          </Text>
          <ProgressBar
            progress={uploadProgress.percentage / 100}
            color={theme.colors.primary}
          />
        </View>
      )}

      {/* 上传按钮 */}
      <View style={styles.uploadContainer}>
        <Button
          mode="contained"
          icon="upload"
          onPress={uploadImages}
          disabled={selectedImages.length === 0 || uploading}
          loading={uploading}
        >
          {uploading ? '上传中...' : `上传图片 (${selectedImages.length})`}
        </Button>
      </View>
    </View>
  );
};
