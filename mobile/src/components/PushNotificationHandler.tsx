/**
 * 推送通知处理组件
 * 管理推送通知的接收、显示和处理
 */

import React, { useEffect } from 'react';
import { Alert, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NotificationService } from '@services/NotificationService';
import { AnalyticsService } from '@services/AnalyticsService';
import { useAuth } from '@store/AuthContext';
import { useApp } from '@store/AppContext';
import { Logger } from '@utils/Logger';

interface PushNotificationHandlerProps {
  children: React.ReactNode;
}

export const PushNotificationHandler: React.FC<PushNotificationHandlerProps> = ({ children }) => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { addNotification } = useApp();

  useEffect(() => {
    setupNotificationHandlers();
    
    return () => {
      // 清理通知处理器
      NotificationService.unregisterNotificationHandler('order');
      NotificationService.unregisterNotificationHandler('product');
      NotificationService.unregisterNotificationHandler('customer');
      NotificationService.unregisterNotificationHandler('system');
    };
  }, []);

  /**
   * 设置通知处理器
   */
  const setupNotificationHandlers = () => {
    // 订单相关通知
    NotificationService.registerNotificationHandler('order', handleOrderNotification);
    
    // 商品相关通知
    NotificationService.registerNotificationHandler('product', handleProductNotification);
    
    // 客户相关通知
    NotificationService.registerNotificationHandler('customer', handleCustomerNotification);
    
    // 系统通知
    NotificationService.registerNotificationHandler('system', handleSystemNotification);
    
    // 默认通知处理
    NotificationService.registerNotificationHandler('default', handleDefaultNotification);
  };

  /**
   * 处理订单通知
   */
  const handleOrderNotification = (notification: any) => {
    const { userInfo, data } = notification;
    const notificationData = userInfo || data || {};
    
    Logger.info('收到订单通知:', notificationData);
    
    // 跟踪事件
    AnalyticsService.trackEvent('notification_received', {
      category: 'order',
      type: notificationData.type,
      orderId: notificationData.orderId,
    });

    // 添加到应用内通知
    addNotification({
      type: getNotificationType(notificationData.type),
      title: notification.title || '订单通知',
      message: notification.body || notification.message,
      actions: [
        {
          label: '查看订单',
          action: () => navigateToOrder(notificationData.orderId),
        },
      ],
    });

    // 根据通知类型进行不同处理
    switch (notificationData.type) {
      case 'new_order':
        handleNewOrderNotification(notificationData);
        break;
      case 'order_paid':
        handleOrderPaidNotification(notificationData);
        break;
      case 'order_shipped':
        handleOrderShippedNotification(notificationData);
        break;
      case 'order_delivered':
        handleOrderDeliveredNotification(notificationData);
        break;
      case 'order_cancelled':
        handleOrderCancelledNotification(notificationData);
        break;
      default:
        Logger.warn('未知的订单通知类型:', notificationData.type);
    }
  };

  /**
   * 处理商品通知
   */
  const handleProductNotification = (notification: any) => {
    const { userInfo, data } = notification;
    const notificationData = userInfo || data || {};
    
    Logger.info('收到商品通知:', notificationData);
    
    // 跟踪事件
    AnalyticsService.trackEvent('notification_received', {
      category: 'product',
      type: notificationData.type,
      productId: notificationData.productId,
    });

    // 添加到应用内通知
    addNotification({
      type: getNotificationType(notificationData.type),
      title: notification.title || '商品通知',
      message: notification.body || notification.message,
      actions: [
        {
          label: '查看商品',
          action: () => navigateToProduct(notificationData.productId),
        },
      ],
    });

    // 根据通知类型进行不同处理
    switch (notificationData.type) {
      case 'low_stock':
        handleLowStockNotification(notificationData);
        break;
      case 'out_of_stock':
        handleOutOfStockNotification(notificationData);
        break;
      case 'price_change':
        handlePriceChangeNotification(notificationData);
        break;
      case 'new_review':
        handleNewReviewNotification(notificationData);
        break;
      default:
        Logger.warn('未知的商品通知类型:', notificationData.type);
    }
  };

  /**
   * 处理客户通知
   */
  const handleCustomerNotification = (notification: any) => {
    const { userInfo, data } = notification;
    const notificationData = userInfo || data || {};
    
    Logger.info('收到客户通知:', notificationData);
    
    // 跟踪事件
    AnalyticsService.trackEvent('notification_received', {
      category: 'customer',
      type: notificationData.type,
      customerId: notificationData.customerId,
    });

    // 添加到应用内通知
    addNotification({
      type: getNotificationType(notificationData.type),
      title: notification.title || '客户通知',
      message: notification.body || notification.message,
      actions: [
        {
          label: '查看客户',
          action: () => navigateToCustomer(notificationData.customerId),
        },
      ],
    });

    // 根据通知类型进行不同处理
    switch (notificationData.type) {
      case 'new_customer':
        handleNewCustomerNotification(notificationData);
        break;
      case 'customer_inquiry':
        handleCustomerInquiryNotification(notificationData);
        break;
      case 'customer_complaint':
        handleCustomerComplaintNotification(notificationData);
        break;
      default:
        Logger.warn('未知的客户通知类型:', notificationData.type);
    }
  };

  /**
   * 处理系统通知
   */
  const handleSystemNotification = (notification: any) => {
    const { userInfo, data } = notification;
    const notificationData = userInfo || data || {};
    
    Logger.info('收到系统通知:', notificationData);
    
    // 跟踪事件
    AnalyticsService.trackEvent('notification_received', {
      category: 'system',
      type: notificationData.type,
    });

    // 添加到应用内通知
    addNotification({
      type: getNotificationType(notificationData.type),
      title: notification.title || '系统通知',
      message: notification.body || notification.message,
    });

    // 根据通知类型进行不同处理
    switch (notificationData.type) {
      case 'system_maintenance':
        handleSystemMaintenanceNotification(notificationData);
        break;
      case 'app_update':
        handleAppUpdateNotification(notificationData);
        break;
      case 'security_alert':
        handleSecurityAlertNotification(notificationData);
        break;
      default:
        Logger.warn('未知的系统通知类型:', notificationData.type);
    }
  };

  /**
   * 处理默认通知
   */
  const handleDefaultNotification = (notification: any) => {
    Logger.info('收到默认通知:', notification);
    
    // 跟踪事件
    AnalyticsService.trackEvent('notification_received', {
      category: 'default',
      title: notification.title,
    });

    // 添加到应用内通知
    addNotification({
      type: 'info',
      title: notification.title || '通知',
      message: notification.body || notification.message,
    });
  };

  /**
   * 获取通知类型
   */
  const getNotificationType = (type: string): 'info' | 'success' | 'warning' | 'error' => {
    switch (type) {
      case 'new_order':
      case 'order_paid':
      case 'order_delivered':
      case 'new_customer':
        return 'success';
      case 'low_stock':
      case 'price_change':
      case 'system_maintenance':
        return 'warning';
      case 'out_of_stock':
      case 'order_cancelled':
      case 'customer_complaint':
      case 'security_alert':
        return 'error';
      default:
        return 'info';
    }
  };

  /**
   * 导航到订单详情
   */
  const navigateToOrder = (orderId: string) => {
    if (orderId) {
      navigation.navigate('OrderDetail', { orderId });
    }
  };

  /**
   * 导航到商品详情
   */
  const navigateToProduct = (productId: string) => {
    if (productId) {
      navigation.navigate('ProductDetail', { productId });
    }
  };

  /**
   * 导航到客户详情
   */
  const navigateToCustomer = (customerId: string) => {
    if (customerId) {
      navigation.navigate('CustomerDetail', { customerId });
    }
  };

  /**
   * 处理新订单通知
   */
  const handleNewOrderNotification = (data: any) => {
    // 可以在这里添加特殊处理逻辑
    // 比如播放特殊声音、显示特殊动画等
    Logger.info('处理新订单通知:', data);
  };

  /**
   * 处理订单支付通知
   */
  const handleOrderPaidNotification = (data: any) => {
    Logger.info('处理订单支付通知:', data);
  };

  /**
   * 处理订单发货通知
   */
  const handleOrderShippedNotification = (data: any) => {
    Logger.info('处理订单发货通知:', data);
  };

  /**
   * 处理订单送达通知
   */
  const handleOrderDeliveredNotification = (data: any) => {
    Logger.info('处理订单送达通知:', data);
  };

  /**
   * 处理订单取消通知
   */
  const handleOrderCancelledNotification = (data: any) => {
    Logger.info('处理订单取消通知:', data);
  };

  /**
   * 处理库存不足通知
   */
  const handleLowStockNotification = (data: any) => {
    Logger.info('处理库存不足通知:', data);
  };

  /**
   * 处理缺货通知
   */
  const handleOutOfStockNotification = (data: any) => {
    Logger.info('处理缺货通知:', data);
  };

  /**
   * 处理价格变动通知
   */
  const handlePriceChangeNotification = (data: any) => {
    Logger.info('处理价格变动通知:', data);
  };

  /**
   * 处理新评价通知
   */
  const handleNewReviewNotification = (data: any) => {
    Logger.info('处理新评价通知:', data);
  };

  /**
   * 处理新客户通知
   */
  const handleNewCustomerNotification = (data: any) => {
    Logger.info('处理新客户通知:', data);
  };

  /**
   * 处理客户咨询通知
   */
  const handleCustomerInquiryNotification = (data: any) => {
    Logger.info('处理客户咨询通知:', data);
  };

  /**
   * 处理客户投诉通知
   */
  const handleCustomerComplaintNotification = (data: any) => {
    Logger.info('处理客户投诉通知:', data);
  };

  /**
   * 处理系统维护通知
   */
  const handleSystemMaintenanceNotification = (data: any) => {
    Alert.alert(
      '系统维护通知',
      data.message || '系统将进行维护，可能会影响部分功能的使用',
      [{ text: '知道了' }]
    );
  };

  /**
   * 处理应用更新通知
   */
  const handleAppUpdateNotification = (data: any) => {
    Alert.alert(
      '应用更新',
      data.message || '发现新版本，建议您及时更新以获得更好的体验',
      [
        { text: '稍后提醒', style: 'cancel' },
        { text: '立即更新', onPress: () => {/* 跳转到应用商店 */} },
      ]
    );
  };

  /**
   * 处理安全警报通知
   */
  const handleSecurityAlertNotification = (data: any) => {
    Alert.alert(
      '安全警报',
      data.message || '检测到异常登录活动，请检查您的账户安全',
      [
        { text: '忽略', style: 'cancel' },
        { text: '查看详情', onPress: () => navigation.navigate('SecuritySettings') },
      ]
    );
  };

  return <>{children}</>;
};
