/**
 * GPS定位选择组件
 * 提供位置获取、地图选择和地址解析功能
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  Platform,
  PermissionsAndroid,
  Dimensions,
} from 'react-native';
import {
  Button,
  Text,
  Surface,
  ActivityIndicator,
  IconButton,
  useTheme,
} from 'react-native-paper';
import Geolocation from '@react-native-community/geolocation';
import MapView, { Marker, Region } from 'react-native-maps';
import { Logger } from '@utils/Logger';

const { width, height } = Dimensions.get('window');

interface LocationData {
  latitude: number;
  longitude: number;
  address?: string;
  accuracy?: number;
  timestamp?: number;
}

interface LocationPickerProps {
  onLocationSelected?: (location: LocationData) => void;
  initialLocation?: LocationData;
  showMap?: boolean;
  enableAddressLookup?: boolean;
  accuracy?: 'high' | 'medium' | 'low';
}

export const LocationPicker: React.FC<LocationPickerProps> = ({
  onLocationSelected,
  initialLocation,
  showMap = true,
  enableAddressLookup = true,
  accuracy = 'high',
}) => {
  const theme = useTheme();
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(initialLocation || null);
  const [loading, setLoading] = useState(false);
  const [mapRegion, setMapRegion] = useState<Region>({
    latitude: initialLocation?.latitude || 39.9042,
    longitude: initialLocation?.longitude || 116.4074,
    latitudeDelta: 0.01,
    longitudeDelta: 0.01,
  });
  const [address, setAddress] = useState<string>(initialLocation?.address || '');

  useEffect(() => {
    if (initialLocation) {
      setCurrentLocation(initialLocation);
      setMapRegion({
        latitude: initialLocation.latitude,
        longitude: initialLocation.longitude,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      });
      if (initialLocation.address) {
        setAddress(initialLocation.address);
      }
    }
  }, [initialLocation]);

  /**
   * 请求位置权限
   */
  const requestLocationPermission = async (): Promise<boolean> => {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: '位置权限',
            message: '应用需要位置权限来获取您的当前位置',
            buttonNeutral: '稍后询问',
            buttonNegative: '取消',
            buttonPositive: '确定',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (error) {
        Logger.error('请求位置权限失败:', error);
        return false;
      }
    }
    return true;
  };

  /**
   * 获取当前位置
   */
  const getCurrentLocation = async () => {
    const hasPermission = await requestLocationPermission();
    if (!hasPermission) {
      Alert.alert('权限不足', '无法获取位置信息，请在设置中授予位置权限');
      return;
    }

    setLoading(true);

    const options = {
      enableHighAccuracy: accuracy === 'high',
      timeout: 15000,
      maximumAge: accuracy === 'low' ? 300000 : 60000, // 低精度模式允许5分钟缓存
    };

    Geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude, accuracy: positionAccuracy } = position.coords;
        
        const locationData: LocationData = {
          latitude,
          longitude,
          accuracy: positionAccuracy,
          timestamp: position.timestamp,
        };

        // 获取地址信息
        if (enableAddressLookup) {
          try {
            const addressInfo = await reverseGeocode(latitude, longitude);
            locationData.address = addressInfo;
            setAddress(addressInfo);
          } catch (error) {
            Logger.warn('获取地址信息失败:', error);
          }
        }

        setCurrentLocation(locationData);
        setMapRegion({
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });

        if (onLocationSelected) {
          onLocationSelected(locationData);
        }

        setLoading(false);
        Logger.info('获取位置成功:', locationData);
      },
      (error) => {
        setLoading(false);
        Logger.error('获取位置失败:', error);
        
        let errorMessage = '获取位置失败';
        switch (error.code) {
          case 1:
            errorMessage = '位置权限被拒绝';
            break;
          case 2:
            errorMessage = '位置信息不可用';
            break;
          case 3:
            errorMessage = '获取位置超时';
            break;
        }
        
        Alert.alert('定位失败', errorMessage);
      },
      options
    );
  };

  /**
   * 反向地理编码 - 根据坐标获取地址
   */
  const reverseGeocode = async (latitude: number, longitude: number): Promise<string> => {
    try {
      // 使用高德地图API进行反向地理编码
      const response = await fetch(
        `https://restapi.amap.com/v3/geocode/regeo?key=YOUR_AMAP_KEY&location=${longitude},${latitude}&poitype=&radius=1000&extensions=base&batch=false&roadlevel=0`
      );
      
      const data = await response.json();
      
      if (data.status === '1' && data.regeocode) {
        return data.regeocode.formatted_address;
      }
      
      throw new Error('地址解析失败');
    } catch (error) {
      Logger.error('反向地理编码失败:', error);
      return `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;
    }
  };

  /**
   * 正向地理编码 - 根据地址获取坐标
   */
  const geocode = async (address: string): Promise<LocationData | null> => {
    try {
      const response = await fetch(
        `https://restapi.amap.com/v3/geocode/geo?key=YOUR_AMAP_KEY&address=${encodeURIComponent(address)}`
      );
      
      const data = await response.json();
      
      if (data.status === '1' && data.geocodes && data.geocodes.length > 0) {
        const location = data.geocodes[0].location.split(',');
        return {
          latitude: parseFloat(location[1]),
          longitude: parseFloat(location[0]),
          address,
        };
      }
      
      throw new Error('地址解析失败');
    } catch (error) {
      Logger.error('正向地理编码失败:', error);
      return null;
    }
  };

  /**
   * 处理地图点击
   */
  const handleMapPress = async (event: any) => {
    const { latitude, longitude } = event.nativeEvent.coordinate;
    
    const locationData: LocationData = {
      latitude,
      longitude,
      timestamp: Date.now(),
    };

    // 获取地址信息
    if (enableAddressLookup) {
      setLoading(true);
      try {
        const addressInfo = await reverseGeocode(latitude, longitude);
        locationData.address = addressInfo;
        setAddress(addressInfo);
      } catch (error) {
        Logger.warn('获取地址信息失败:', error);
      }
      setLoading(false);
    }

    setCurrentLocation(locationData);

    if (onLocationSelected) {
      onLocationSelected(locationData);
    }

    Logger.info('地图选择位置:', locationData);
  };

  /**
   * 搜索地址
   */
  const searchAddress = async (searchText: string) => {
    if (!searchText.trim()) return;

    setLoading(true);
    try {
      const location = await geocode(searchText);
      if (location) {
        setCurrentLocation(location);
        setMapRegion({
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        });
        setAddress(location.address || searchText);

        if (onLocationSelected) {
          onLocationSelected(location);
        }
      } else {
        Alert.alert('搜索失败', '未找到该地址的位置信息');
      }
    } catch (error) {
      Alert.alert('搜索失败', '地址搜索出错，请重试');
    }
    setLoading(false);
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    controlsContainer: {
      padding: 16,
      backgroundColor: theme.colors.surface,
    },
    buttonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: 12,
    },
    button: {
      flex: 1,
      marginHorizontal: 4,
    },
    locationInfo: {
      backgroundColor: theme.colors.surfaceVariant,
      padding: 12,
      borderRadius: 8,
      marginTop: 8,
    },
    coordinateText: {
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      fontFamily: 'monospace',
    },
    addressText: {
      fontSize: 14,
      color: theme.colors.onSurface,
      marginTop: 4,
    },
    mapContainer: {
      flex: 1,
      position: 'relative',
    },
    map: {
      flex: 1,
    },
    loadingOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.3)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000,
    },
    loadingContainer: {
      backgroundColor: theme.colors.surface,
      padding: 20,
      borderRadius: 8,
      alignItems: 'center',
    },
    loadingText: {
      marginTop: 8,
      color: theme.colors.onSurface,
    },
    accuracyIndicator: {
      position: 'absolute',
      top: 16,
      right: 16,
      backgroundColor: theme.colors.surface,
      padding: 8,
      borderRadius: 20,
      flexDirection: 'row',
      alignItems: 'center',
    },
    accuracyText: {
      fontSize: 12,
      marginLeft: 4,
      color: theme.colors.onSurface,
    },
  });

  return (
    <View style={styles.container}>
      {/* 控制面板 */}
      <View style={styles.controlsContainer}>
        <View style={styles.buttonRow}>
          <Button
            mode="contained"
            icon="crosshairs-gps"
            onPress={getCurrentLocation}
            style={styles.button}
            disabled={loading}
          >
            当前位置
          </Button>
          <Button
            mode="outlined"
            icon="map-search"
            onPress={() => {/* 打开地址搜索 */}}
            style={styles.button}
            disabled={loading}
          >
            搜索地址
          </Button>
        </View>

        {/* 位置信息显示 */}
        {currentLocation && (
          <Surface style={styles.locationInfo} elevation={1}>
            <Text style={styles.coordinateText}>
              经度: {currentLocation.longitude.toFixed(6)}  纬度: {currentLocation.latitude.toFixed(6)}
            </Text>
            {address && (
              <Text style={styles.addressText}>{address}</Text>
            )}
            {currentLocation.accuracy && (
              <Text style={styles.coordinateText}>
                精度: ±{currentLocation.accuracy.toFixed(0)}米
              </Text>
            )}
          </Surface>
        )}
      </View>

      {/* 地图 */}
      {showMap && (
        <View style={styles.mapContainer}>
          <MapView
            style={styles.map}
            region={mapRegion}
            onPress={handleMapPress}
            showsUserLocation={true}
            showsMyLocationButton={false}
            showsCompass={true}
            showsScale={true}
          >
            {currentLocation && (
              <Marker
                coordinate={{
                  latitude: currentLocation.latitude,
                  longitude: currentLocation.longitude,
                }}
                title="选中位置"
                description={address || '当前选择的位置'}
              />
            )}
          </MapView>

          {/* 精度指示器 */}
          {currentLocation?.accuracy && (
            <View style={styles.accuracyIndicator}>
              <IconButton
                icon="target"
                iconColor={
                  currentLocation.accuracy < 10 ? theme.colors.primary :
                  currentLocation.accuracy < 50 ? '#ff9800' : theme.colors.error
                }
                size={16}
              />
              <Text style={styles.accuracyText}>
                ±{currentLocation.accuracy.toFixed(0)}m
              </Text>
            </View>
          )}

          {/* 加载覆盖层 */}
          {loading && (
            <View style={styles.loadingOverlay}>
              <Surface style={styles.loadingContainer} elevation={4}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={styles.loadingText}>正在获取位置...</Text>
              </Surface>
            </View>
          )}
        </View>
      )}
    </View>
  );
};
