/**
 * 错误边界组件
 * 捕获和处理React组件中的JavaScript错误
 */

import React, { Component, ReactNode } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import {
  Text,
  Button,
  Card,
  IconButton,
  useTheme,
} from 'react-native-paper';
import { Logger } from '@utils/Logger';

const { width } = Dimensions.get('window');

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
  errorId: string | null;
}

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: (error: Error, errorInfo: React.ErrorInfo, retry: () => void) => ReactNode;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // 记录错误信息
    this.setState({
      errorInfo,
    });

    // 记录到日志
    Logger.error('React错误边界捕获到错误:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorId: this.state.errorId,
    });

    // 调用外部错误处理器
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // 发送错误报告到服务器
    this.sendErrorReport(error, errorInfo);
  }

  /**
   * 发送错误报告到服务器
   */
  private async sendErrorReport(error: Error, errorInfo: React.ErrorInfo) {
    try {
      const errorReport = {
        errorId: this.state.errorId,
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: 'React Native',
        url: 'mobile-app',
        userId: null, // 可以从认证状态获取
      };

      // 发送到错误报告服务
      await fetch('/api/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorReport),
      });

      Logger.info('错误报告已发送:', this.state.errorId);
    } catch (reportError) {
      Logger.error('发送错误报告失败:', reportError);
    }
  }

  /**
   * 重试操作
   */
  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
  };

  /**
   * 复制错误信息
   */
  private handleCopyError = () => {
    const { error, errorInfo, errorId } = this.state;
    const errorText = `
错误ID: ${errorId}
错误信息: ${error?.message}
错误堆栈: ${error?.stack}
组件堆栈: ${errorInfo?.componentStack}
时间: ${new Date().toISOString()}
    `.trim();

    // 这里可以集成剪贴板功能
    // Clipboard.setString(errorText);
    Logger.info('错误信息已复制到剪贴板');
  };

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback && this.state.error && this.state.errorInfo) {
        return this.props.fallback(this.state.error, this.state.errorInfo, this.handleRetry);
      }

      // 默认错误UI
      return <DefaultErrorUI {...this.state} onRetry={this.handleRetry} onCopyError={this.handleCopyError} />;
    }

    return this.props.children;
  }
}

/**
 * 默认错误UI组件
 */
interface DefaultErrorUIProps extends ErrorBoundaryState {
  onRetry: () => void;
  onCopyError: () => void;
}

const DefaultErrorUI: React.FC<DefaultErrorUIProps> = ({
  error,
  errorInfo,
  errorId,
  onRetry,
  onCopyError,
}) => {
  // 由于这是类组件内部的函数组件，我们需要手动获取主题
  // 在实际使用中，可以通过Context或其他方式获取主题
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#f5f5f5',
      justifyContent: 'center',
      alignItems: 'center',
      padding: 20,
    },
    card: {
      width: width - 40,
      maxWidth: 400,
      padding: 20,
      borderRadius: 12,
    },
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 16,
    },
    title: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#d32f2f',
      marginLeft: 8,
    },
    message: {
      fontSize: 16,
      color: '#333',
      marginBottom: 16,
      lineHeight: 24,
    },
    errorId: {
      fontSize: 12,
      color: '#666',
      marginBottom: 16,
      fontFamily: 'monospace',
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: 16,
    },
    button: {
      flex: 1,
      marginHorizontal: 4,
    },
    detailsContainer: {
      marginTop: 16,
      padding: 12,
      backgroundColor: '#f0f0f0',
      borderRadius: 8,
      maxHeight: 200,
    },
    detailsText: {
      fontSize: 12,
      fontFamily: 'monospace',
      color: '#333',
    },
  });

  const [showDetails, setShowDetails] = React.useState(false);

  return (
    <View style={styles.container}>
      <Card style={styles.card}>
        <View style={styles.header}>
          <IconButton icon="alert-circle" iconColor="#d32f2f" size={24} />
          <Text style={styles.title}>应用出现错误</Text>
        </View>

        <Text style={styles.message}>
          很抱歉，应用遇到了一个意外错误。我们已经记录了这个问题，请尝试重新加载应用。
        </Text>

        {errorId && (
          <Text style={styles.errorId}>
            错误ID: {errorId}
          </Text>
        )}

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={onRetry}
            style={styles.button}
          >
            重试
          </Button>
          <Button
            mode="outlined"
            onPress={() => setShowDetails(!showDetails)}
            style={styles.button}
          >
            {showDetails ? '隐藏详情' : '显示详情'}
          </Button>
        </View>

        {showDetails && (
          <ScrollView style={styles.detailsContainer}>
            <Text style={styles.detailsText}>
              {`错误信息: ${error?.message}\n\n`}
              {`错误堆栈:\n${error?.stack}\n\n`}
              {`组件堆栈:\n${errorInfo?.componentStack}`}
            </Text>
          </ScrollView>
        )}

        <Button
          mode="text"
          onPress={onCopyError}
          style={{ marginTop: 8 }}
        >
          复制错误信息
        </Button>
      </Card>
    </View>
  );
};

/**
 * 高阶组件：为组件添加错误边界
 */
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

/**
 * Hook：在函数组件中使用错误边界
 */
export function useErrorHandler() {
  return React.useCallback((error: Error, errorInfo?: React.ErrorInfo) => {
    // 手动触发错误边界
    throw error;
  }, []);
}
