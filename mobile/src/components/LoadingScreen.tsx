/**
 * 加载屏幕组件
 * 显示应用启动时的加载界面
 */

import React from 'react';
import {
  View,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import {
  ActivityIndicator,
  Text,
  Surface,
  useTheme,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

interface LoadingScreenProps {
  message?: string;
  showProgress?: boolean;
  progress?: number;
}

export const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = '正在加载...',
  showProgress = false,
  progress = 0,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const fadeAnim = React.useRef(new Animated.Value(0)).current;
  const scaleAnim = React.useRef(new Animated.Value(0.8)).current;

  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 50,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim]);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
      justifyContent: 'center',
      alignItems: 'center',
      paddingTop: insets.top,
      paddingBottom: insets.bottom,
    },
    content: {
      alignItems: 'center',
      justifyContent: 'center',
      padding: 32,
    },
    logoContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
      marginBottom: 32,
      elevation: 8,
      shadowColor: theme.colors.shadow,
      shadowOffset: {
        width: 0,
        height: 4,
      },
      shadowOpacity: 0.3,
      shadowRadius: 8,
    },
    logoText: {
      fontSize: 32,
      fontWeight: 'bold',
      color: theme.colors.onPrimary,
    },
    loadingContainer: {
      alignItems: 'center',
      marginBottom: 24,
    },
    message: {
      fontSize: 16,
      color: theme.colors.onBackground,
      textAlign: 'center',
      marginTop: 16,
      opacity: 0.8,
    },
    progressContainer: {
      width: width * 0.6,
      height: 4,
      backgroundColor: theme.colors.surfaceVariant,
      borderRadius: 2,
      marginTop: 16,
      overflow: 'hidden',
    },
    progressBar: {
      height: '100%',
      backgroundColor: theme.colors.primary,
      borderRadius: 2,
    },
    versionText: {
      position: 'absolute',
      bottom: insets.bottom + 32,
      fontSize: 12,
      color: theme.colors.onSurfaceVariant,
      opacity: 0.6,
    },
  });

  return (
    <View style={styles.container}>
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeAnim,
            transform: [{ scale: scaleAnim }],
          },
        ]}
      >
        {/* Logo */}
        <Surface style={styles.logoContainer} elevation={4}>
          <Text style={styles.logoText}>ERP</Text>
        </Surface>

        {/* 加载指示器 */}
        <View style={styles.loadingContainer}>
          <ActivityIndicator
            size="large"
            color={theme.colors.primary}
            animating={true}
          />
          <Text style={styles.message}>{message}</Text>
        </View>

        {/* 进度条 */}
        {showProgress && (
          <View style={styles.progressContainer}>
            <Animated.View
              style={[
                styles.progressBar,
                {
                  width: `${Math.max(0, Math.min(100, progress))}%`,
                },
              ]}
            />
          </View>
        )}
      </Animated.View>

      {/* 版本信息 */}
      <Text style={styles.versionText}>
        CBEC ERP v1.0.0
      </Text>
    </View>
  );
};
