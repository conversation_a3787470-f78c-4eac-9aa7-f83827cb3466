/**
 * Metro配置文件
 * 用于React Native项目的打包和开发服务器配置
 */

const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

/**
 * Metro配置
 * @type {import('metro-config').MetroConfig}
 */
const config = {
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: true,
      },
    }),
  },
  resolver: {
    alias: {
      '@': './src',
      '@components': './src/components',
      '@screens': './src/screens',
      '@navigation': './src/navigation',
      '@services': './src/services',
      '@utils': './src/utils',
      '@hooks': './src/hooks',
      '@store': './src/store',
      '@types': './src/types',
      '@assets': './src/assets',
      '@config': './src/config',
      '@constants': './src/constants',
      '@styles': './src/styles',
      '@themes': './src/themes',
    },
  },
};

module.exports = mergeConfig(getDefaultConfig(__dirname), config);
